package com.hvisions.hiperbase.client.fallback;

import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.common.dto.ExtendInfo;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.LocationExtendClient;
import com.hvisions.hiperbase.equipment.CellWithEquipmentDTO;
import com.hvisions.hiperbase.equipment.EquipmentDTO;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.thirdparty.common.dto.MesFactoryDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: LocationExtendFallBack</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/8/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class LocationExtendFallBack extends BaseFallbackFactory<LocationExtendClient> {

    @Override
    public LocationExtendClient getFallBack(ResultVO vo) {
        return new LocationExtendClient() {
            @Override
            public ResultVO createLocationColumn(int type, ExtendColumnInfo extendColumnInfo) {
                return vo;
            }

            @Override
            public ResultVO updateLocationExtendInfo(int type, ExtendInfo extendInfo) {
                return vo;
            }

            @Override
            public ResultVO<LocationDTO> createLocation(LocationDTO hvBmLocationDTO) {
                return vo;
            }

            @Override
            public ResultVO<LocationDTO> updateLocation(LocationDTO hvBmLocationDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteLocationColumn(String columnName, int type) {
                return vo;
            }

            @Override
            public ResultVO<List<LocationDTO>> getLocationListByType(int type) {
                return vo;
            }

            @Override
            public ResultVO<LocationDTO> getLocationById(int id) {
                return vo;
            }

            @Override
            public ResultVO<List<LocationDTO>> getLocationListByParentId(int parentId) {
                return vo;
            }

            @Override
            public ResultVO<LocationDTO> getLocationByCode(String code) {
                return vo;
            }

            @Override
            public ResultVO<LocationDTO> getHvBmLocationByCode(String code) {
                return vo;
            }

            @Override
            public ResultVO deleteLocationById(int id) {
                return vo;
            }

            @Override
            public ResultVO<List<ExtendColumnInfo>> getExtendColumnInfo(int type) {
                return vo;
            }

            @Override
            public ResultVO<List<EquipmentDTO>> getEquipmentListByCellId(int id) {
                return vo;
            }

            @Override
            public ResultVO updateCellEquipmentRelation(CellWithEquipmentDTO cellEquipmentDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteCellEquipmentRelation(int equipmentId, int cellId) {
                return vo;
            }

            @Override
            public ResultVO deleteCellEquipmentRelationBatch(CellWithEquipmentDTO cellWithEquipmentDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<LocationDTO>> getAllByCellIdList(List<Integer> idList) {
                return vo;
            }

            @Override
            public ResultVO<List<LocationDTO>> getAllByIdList(List<Integer> idList) {
                return vo;
            }

            /**
             * 获取所有工厂建模tree
             *
             * @return 工厂建模树
             */
            @Override
            public ResultVO<List<LocationDTO>> getLocationTree() {
                return vo;
            }

            @Override
            public ResultVO<Integer> saveLocationByReceiving(List<MesFactoryDTO> factoryDTOS) {
                return vo;
            }

            @Override
            public ResultVO<String> getLineByStationCode(String stationCode) {
                return vo;
            }

            @Override
            public ResultVO<LocationDTO> getLineById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<LocationDTO>  getLineInfoByLineCode(String lineCode) {
                return vo;
            }

            /**
             * 根据产线编号list查询产线信息
             *
             * @param codes
             * @return
             */
            @Override
            public ResultVO<List<LocationDTO>> getLocationsByCodes(List<String> codes) {
                return vo;
            }
        };
    }
}









