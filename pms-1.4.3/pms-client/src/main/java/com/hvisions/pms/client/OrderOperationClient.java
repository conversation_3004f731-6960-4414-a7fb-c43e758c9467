package com.hvisions.pms.client;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.dto.*;
import com.hvisions.pms.viewdto.OrderOperationNumDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: OrderOperationClient</p >
 * <p>Description: 工单工序client</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-09-02</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@FeignClient(name = "pms", path = "/operation", fallbackFactory = OrderOperationFallBack.class)
public interface OrderOperationClient {

    /**
     * 工序开始
     *
     * @param operationOperatorDTO 工序操作传输对象
     */
    @PutMapping(value = "/startOperation")
    ResultVO startOperation(@RequestBody OperationOperatorDTO operationOperatorDTO);

    /**
     * 工序批量开始
     *
     * @param operationOperatorDTOS 工序操作传输对象列表
     */
    @PutMapping(value = "/startOperationBatch")
    ResultVO startOperationBatch(@RequestBody List<OperationOperatorDTO> operationOperatorDTOS);

    /**
     * 工序中断
     *
     * @param operationOperatorDTO 工序操作传输对象
     */
    @PutMapping(value = "/breakOperation")
    ResultVO breakOperation(@RequestBody OperationOperatorDTO operationOperatorDTO);


    /**
     * 工序批量中断
     *
     * @param operationOperatorDTOS 工序操作传输对象列表
     */
    @PutMapping(value = "/breakOperationBatch")
    ResultVO breakOperationBatch(@RequestBody List<OperationOperatorDTO> operationOperatorDTOS);

    /**
     * 工序继续
     *
     * @param operationOperatorDTO 工序操作传输对象
     */
    @PutMapping(value = "/resumeOperation")
    ResultVO resumeOperation(@RequestBody OperationOperatorDTO operationOperatorDTO);

    /**
     * 工序批量继续
     *
     * @param operationOperatorDTOS 工序操作传输对象列表
     */
    @PutMapping(value = "/resumeOperationBatch")
    ResultVO resumeOperationBatch(@RequestBody List<OperationOperatorDTO> operationOperatorDTOS);

    /**
     * 工序结束
     *
     * @param operationOperatorDTO 工序操作传输对象
     */
    @PutMapping(value = "/endOperation")
    ResultVO endOperation(@RequestBody OperationOperatorDTO operationOperatorDTO);


    /**
     * 工序批量结束
     *
     * @param operationOperatorDTOS 工序操作传输对象列表
     */
    @PutMapping(value = "/endOperationBatch")
    ResultVO endOperationBatch(@RequestBody List<OperationOperatorDTO> operationOperatorDTOS);

    /**
     * 工序终止
     *
     * @param operationOperatorDTO 工序操作传输对象
     */
    @PutMapping(value = "/cancelOperation")
    ResultVO cancelOperation(@RequestBody OperationOperatorDTO operationOperatorDTO);

    /**
     * 工序批量终止
     *
     * @param operationOperatorDTOS 工序操作传输对象列表
     */
    @PutMapping(value = "/cancelOperationBatch")
    ResultVO cancelOperationBatch(@RequestBody List<OperationOperatorDTO> operationOperatorDTOS);

    /**
     * 根据当前工序查询下一道工序
     *
     * @param id 工序ID
     * @return Map  工序列表
     */
    @GetMapping(value = "/getNextOperation/{id}")
    @ApiOperation(value = "根据当前工序查询下一道工序")
    ResultVO<Map<String, Object>> getNextOperation(@PathVariable("id") int id);

    /***
     * 切换加工设备
     * @param operationId 工序ID
     * @param equipmentId  设备ID
     */
    @PutMapping(value = "/changeEquipment/{operationId}/{equipmentId}")
    ResultVO changeEquipment(@PathVariable("operationId") int operationId, @PathVariable("equipmentId") int equipmentId);

    /**
     * 批量切换加工设备
     *
     * @param changeWorkCenterDTO 切换条件DTO
     */
    @PutMapping(value = "/changeManyEquipment")
    ResultVO changeManyEquipment(@RequestBody ChangeWorkCenterDTO changeWorkCenterDTO);


    /**
     * 拆分工序
     *
     * @param operationId 工序Id
     * @param count       拆分数量
     * @param equipmentId 设备Id
     */
    @PostMapping(value = "/splitOrderOperation")
    ResultVO splitOrderOperation(@RequestParam("operationId") int operationId, @RequestParam("count") BigDecimal count,
                                 @RequestParam(required = false,value = "equipmentId") Integer equipmentId);

    /**
     * 根据工单id获取工序信息列表
     *
     * @param orderId 工单id
     * @return 工序信息列表
     */
    @GetMapping(value = "/getOperationByOrderId/{orderId}")
    ResultVO<List<OrderOperationDTO>> getOperationByOrderId(@PathVariable("orderId") int orderId);

    /**
     * 根据ID列表查询工序
     *
     * @param idIn 工序ID列表
     * @return 工序列表
     */
    @GetMapping(value = "/getOperationListByIdIn/{idIn}")
    ResultVO<List<OrderOperationDTO>> getOperationByIdIn(@PathVariable("idIn") List<Integer> idIn);

    /**
     * 根据设备ID查找工序列表
     *
     * @param equipmentId 设备ID
     * @return 工序列表
     */
    @GetMapping(value = "/getByEquipmentId/{equipmentId}")
    ResultVO<List<OrderOperationDTO>> getByEquipmentId(@PathVariable("equipmentId") int equipmentId);

    /**
     * 根据ID查询工序
     *
     * @param id 工序ID
     * @return 工序信息
     */
    @GetMapping(value = "/getOrderOperationById/{id}")
    ResultVO<OrderOperationDTO> getOperationById(@PathVariable("id") int id);

    /**
     * 根据设备id，工序状态查询工单信息
     *
     * @param operationQueryDTO 分页查询对象
     * @return 工序的分页列表
     */
    @PostMapping(value = "/getOperationPageByEquipmentIdAndOperationState")
    ResultVO<HvPage<OrderOperationDTO>> getOperationPageByEquipmentIdAndOperationState(@RequestBody OperationQueryDTO operationQueryDTO);

    /**
     * 根据工序ID查询 工序操作记录
     *
     * @param operationId 工序ID
     * @return 工序操作记录
     */
    @GetMapping(value = "/getProcedureRecordByOperationId/{operationId}")
    ResultVO<List<ProcedureRecordDTO>> getProcedureRecordByOperationId(@PathVariable("operationId") int operationId);

    /**
     * 进入工序进度查询
     *
     * @param equipmentId 设备ID
     * @return 工序进度列表
     * @throws ParseException 解析异常
     */
    @GetMapping(value = "/getOperationIdByEquipmentId/{equipmentId}")
    ResultVO<OrderOperationNumDTO> getOperationIdByEquipmentId(@PathVariable("equipmentId") int equipmentId) throws ParseException;

    /**
     * 根据设备ID统计工序数量
     *
     * @param equipmentId 设备ID
     * @return 工序进度列表
     * @throws ParseException 解析异常
     */
    @GetMapping(value = "/getWorkCountByState/{equipmentId}")
    ResultVO<OrderOperationNumDTO> getWorkCountByState(@PathVariable("equipmentId") int equipmentId) throws ParseException;
}