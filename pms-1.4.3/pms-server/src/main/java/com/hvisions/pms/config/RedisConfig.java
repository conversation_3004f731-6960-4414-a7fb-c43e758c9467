package com.hvisions.pms.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.ReadMode;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Configuration
public class RedisConfig {

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new StringRedisSerializer());
        return template;
    }


    @Bean
    public RedissonClient redissonClient(RedisProperties redisProperties) {
        Config config = new Config();
//        config.useSentinelServers()
//                .setMasterName("redis-server")  // 与配置中的 master 名称一致
//                .addSentinelAddress(
//                        "redis://**************:26379",
//                        "redis://**************:36379",
//                        "redis://**************:26379",
//                        "redis://**************:36379"
//                )
//                .setReadMode(ReadMode.MASTER);  // 关键！强制从主节点读取
        config.useSingleServer().setAddress("redis://**************:6379");
        return Redisson.create(config);
    }

}