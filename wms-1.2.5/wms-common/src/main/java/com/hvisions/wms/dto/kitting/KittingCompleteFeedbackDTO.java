package com.hvisions.wms.dto.kitting;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 集配完成反馈DTO
 */
@Data
@ApiModel(description = "集配完成反馈DTO")
public class KittingCompleteFeedbackDTO {

    /**
     * 集配任务号
     */
    @ApiModelProperty(value = "集配任务号")
    private String kittingTaskNo;

    /**
     * 关联生产工单号
     */
    @ApiModelProperty(value = "关联生产工单号")
    private String productWorkOrderCode;

    /**
     * 关联需求单号
     */
    @ApiModelProperty(value = "关联需求单号")
    private String requirementCode;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    private LocalDateTime completeTime;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 操作员ID
     */
    @ApiModelProperty(value = "操作员ID")
    private Integer operatorId;

    /**
     * 操作员姓名
     */
    @ApiModelProperty(value = "操作员姓名")
    private String operatorName;

}
