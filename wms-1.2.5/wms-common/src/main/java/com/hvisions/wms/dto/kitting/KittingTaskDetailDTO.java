package com.hvisions.wms.dto.kitting;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 集配任务明细DTO
 */
@Data
@ApiModel(description = "集配任务明细DTO")
public class KittingTaskDetailDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * 集配任务ID
     */
    @ApiModelProperty(value = "集配任务ID")
    private Integer kittingTaskId;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private Integer sequenceNo;

    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    private String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;

    /**
     * 物料规格
     */
    @ApiModelProperty(value = "物料规格")
    private String materialSpec;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 集配数量
     */
    @ApiModelProperty(value = "集配数量")
    private BigDecimal kittingQuantity;

    /**
     * 工单数量
     */
    @ApiModelProperty(value = "工单数量")
    private BigDecimal workOrderQuantity;

    /**
     * 集配状态
     */
    @ApiModelProperty(value = "集配状态")
    private Integer kittingStatus;

}
