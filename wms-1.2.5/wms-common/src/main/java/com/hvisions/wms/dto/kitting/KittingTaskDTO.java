package com.hvisions.wms.dto.kitting;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 集配任务DTO
 */
@Data
@ApiModel(description = "集配任务DTO")
public class KittingTaskDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * 集配任务号
     */
    @ApiModelProperty(value = "集配任务号")
    private String kittingTaskNo;

    /**
     * 关联生产工单号
     */
    @ApiModelProperty(value = "关联生产工单号")
    private String productWorkOrderCode;

    /**
     * 关联需求单号
     */
    @ApiModelProperty(value = "关联需求单号")
    private String requirementCode;

    /**
     * 需求时间
     */
    @ApiModelProperty(value = "需求时间")
    private LocalDateTime requirementTime;

    /**
     * 任务状态
     */
    @ApiModelProperty(value = "任务状态")
    private Integer taskStatus;

    /**
     * 目标料点
     */
    @ApiModelProperty(value = "目标料点")
    private String targetPointCode;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private Integer priority;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Integer creatorId;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private Integer updaterId;

    /**
     * 租户字段
     */
    @ApiModelProperty(value = "租户字段")
    private String siteNum;

    /**
     * 集配任务明细列表
     */
    @ApiModelProperty(value = "集配任务明细列表")
    private List<KittingTaskDetailDTO> details;

}
