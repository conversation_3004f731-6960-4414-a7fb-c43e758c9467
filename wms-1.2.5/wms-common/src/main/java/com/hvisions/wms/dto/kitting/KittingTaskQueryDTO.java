package com.hvisions.wms.dto.kitting;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 集配任务查询DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "集配任务查询DTO")
public class KittingTaskQueryDTO extends PageInfo {

    /**
     * 集配任务号
     */
    @ApiModelProperty(value = "集配任务号")
    private String kittingTaskNo;

    /**
     * 关联生产工单号
     */
    @ApiModelProperty(value = "关联生产工单号")
    private String productWorkOrderCode;

    /**
     * 关联需求单号
     */
    @ApiModelProperty(value = "关联需求单号")
    private String requirementCode;

    /**
     * 任务状态
     */
    @ApiModelProperty(value = "任务状态")
    private Integer taskStatus;

    /**
     * 目标料点
     */
    @ApiModelProperty(value = "目标料点")
    private String targetPointCode;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 需求时间开始
     */
    @ApiModelProperty(value = "需求时间开始")
    private LocalDateTime requirementTimeStart;

    /**
     * 需求时间结束
     */
    @ApiModelProperty(value = "需求时间结束")
    private LocalDateTime requirementTimeEnd;

}
