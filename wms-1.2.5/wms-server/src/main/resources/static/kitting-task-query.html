<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>集配任务查询</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .search-form {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        .form-group {
            display: flex;
            flex-direction: column;
            min-width: 150px;
        }
        .form-group label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .form-group input, .form-group select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .task-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .task-table th, .task-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .task-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .task-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-pending {
            background-color: #ffeaa7;
            color: #2d3436;
        }
        .status-processing {
            background-color: #74b9ff;
            color: white;
        }
        .status-completed {
            background-color: #00b894;
            color: white;
        }
        .pagination {
            margin-top: 20px;
            text-align: center;
        }
        .pagination button {
            margin: 0 5px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            background-color: white;
            cursor: pointer;
        }
        .pagination button.active {
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>集配任务查询</h1>
        
        <!-- 查询表单 -->
        <div class="search-form">
            <div class="form-group">
                <label for="kittingTaskNo">集配任务号</label>
                <input type="text" id="kittingTaskNo" placeholder="请输入集配任务号">
            </div>
            <div class="form-group">
                <label for="productWorkOrderCode">关联生产工单号</label>
                <input type="text" id="productWorkOrderCode" placeholder="请输入工单号">
            </div>
            <div class="form-group">
                <label for="requirementCode">关联需求单号</label>
                <input type="text" id="requirementCode" placeholder="请输入需求单号">
            </div>
            <div class="form-group">
                <label for="taskStatus">任务状态</label>
                <select id="taskStatus">
                    <option value="">全部</option>
                    <option value="0">待开始</option>
                    <option value="1">进行中</option>
                    <option value="2">已完成</option>
                </select>
            </div>
            <div class="form-group">
                <label for="targetPointCode">目标料点</label>
                <input type="text" id="targetPointCode" placeholder="请输入目标料点">
            </div>
            <div class="form-group">
                <label>&nbsp;</label>
                <button class="btn btn-primary" onclick="searchTasks()">查询</button>
            </div>
        </div>

        <!-- 任务列表表格 -->
        <table class="task-table">
            <thead>
                <tr>
                    <th>集配任务号</th>
                    <th>关联生产工单号</th>
                    <th>关联需求单号</th>
                    <th>需求时间</th>
                    <th>任务状态</th>
                    <th>目标料点</th>
                    <th>优先级</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="taskTableBody">
                <!-- 示例数据 -->
                <tr>
                    <td>JP202507140001</td>
                    <td>SC202507011</td>
                    <td>XQ202507013001</td>
                    <td>2025-07-19 08:00:00</td>
                    <td><span class="status-badge status-pending">待开始</span></td>
                    <td>TBH产线</td>
                    <td>1</td>
                    <td>待开始</td>
                    <td>
                        <button class="btn btn-success" onclick="startTask(1)">开始</button>
                        <button class="btn btn-primary" onclick="viewDetails(1)">详情</button>
                    </td>
                </tr>
                <tr>
                    <td>JP202507140002</td>
                    <td>SC202507012</td>
                    <td>XQ202507013002</td>
                    <td>2025-07-19 10:00:00</td>
                    <td><span class="status-badge status-processing">进行中</span></td>
                    <td>TBH产线</td>
                    <td>1</td>
                    <td>进行中</td>
                    <td>
                        <button class="btn btn-warning" onclick="completeTask(2)">完成</button>
                        <button class="btn btn-primary" onclick="viewDetails(2)">详情</button>
                    </td>
                </tr>
                <tr>
                    <td>JP202507140003</td>
                    <td>SC202507013</td>
                    <td>XQ202507013003</td>
                    <td>2025-07-19 14:00:00</td>
                    <td><span class="status-badge status-completed">已完成</span></td>
                    <td>TBH产线</td>
                    <td>1</td>
                    <td>已完成</td>
                    <td>
                        <button class="btn btn-primary" onclick="viewDetails(3)">详情</button>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- 分页 -->
        <div class="pagination">
            <button onclick="previousPage()">上一页</button>
            <button class="active">1</button>
            <button onclick="nextPage()">下一页</button>
        </div>
    </div>

    <script>
        // 查询任务
        function searchTasks() {
            const queryData = {
                kittingTaskNo: document.getElementById('kittingTaskNo').value,
                productWorkOrderCode: document.getElementById('productWorkOrderCode').value,
                requirementCode: document.getElementById('requirementCode').value,
                taskStatus: document.getElementById('taskStatus').value,
                targetPointCode: document.getElementById('targetPointCode').value,
                page: 0,
                pageSize: 10
            };
            
            console.log('查询条件:', queryData);
            // TODO: 调用后端API进行查询
            // fetch('/kittingTask/query', { method: 'POST', body: JSON.stringify(queryData) })
        }

        // 开始任务
        function startTask(taskId) {
            if (confirm('确定要开始执行这个集配任务吗？')) {
                console.log('开始任务:', taskId);
                // TODO: 调用后端API开始任务
                // fetch('/kittingTask/start/' + taskId, { method: 'POST' })
            }
        }

        // 完成任务
        function completeTask(taskId) {
            if (confirm('确定要完成这个集配任务吗？')) {
                console.log('完成任务:', taskId);
                // TODO: 调用后端API完成任务
                // fetch('/kittingTask/complete/' + taskId, { method: 'POST' })
            }
        }

        // 查看详情
        function viewDetails(taskId) {
            console.log('查看详情:', taskId);
            // TODO: 跳转到详情页面或弹出详情对话框
            window.open('kitting-task-detail.html?id=' + taskId, '_blank');
        }

        // 分页功能
        function previousPage() {
            console.log('上一页');
        }

        function nextPage() {
            console.log('下一页');
        }
    </script>
</body>
</html>
