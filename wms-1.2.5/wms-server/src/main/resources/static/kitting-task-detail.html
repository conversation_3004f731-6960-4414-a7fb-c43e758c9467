<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>集配任务详情</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .task-header {
            border-bottom: 2px solid #007bff;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .task-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .info-item {
            display: flex;
            align-items: center;
        }
        .info-label {
            font-weight: bold;
            color: #333;
            min-width: 120px;
        }
        .info-value {
            color: #666;
        }
        .material-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .material-table th, .material-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .material-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .material-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-pending {
            background-color: #ffeaa7;
            color: #2d3436;
        }
        .status-processing {
            background-color: #74b9ff;
            color: white;
        }
        .status-completed {
            background-color: #00b894;
            color: white;
        }
        .action-buttons {
            margin-top: 30px;
            text-align: center;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #28a745;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="task-header">
            <h1>集配任务详情</h1>
            <p>任务编号: JP202507140001</p>
        </div>

        <!-- 任务基本信息 -->
        <div class="task-info">
            <div class="info-item">
                <span class="info-label">集配任务号:</span>
                <span class="info-value">JP202507140001</span>
            </div>
            <div class="info-item">
                <span class="info-label">关联生产工单号:</span>
                <span class="info-value">SC202507011</span>
            </div>
            <div class="info-item">
                <span class="info-label">关联需求单号:</span>
                <span class="info-value">XQ202507013001</span>
            </div>
            <div class="info-item">
                <span class="info-label">需求时间:</span>
                <span class="info-value">2025-07-19 08:00:00</span>
            </div>
            <div class="info-item">
                <span class="info-label">任务状态:</span>
                <span class="info-value"><span class="status-badge status-pending">待开始</span></span>
            </div>
            <div class="info-item">
                <span class="info-label">目标料点:</span>
                <span class="info-value">TBH产线</span>
            </div>
            <div class="info-item">
                <span class="info-label">优先级:</span>
                <span class="info-value">1</span>
            </div>
            <div class="info-item">
                <span class="info-label">状态:</span>
                <span class="info-value">待开始</span>
            </div>
        </div>

        <!-- 集配进度 -->
        <div>
            <h3>集配进度</h3>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 30%"></div>
            </div>
            <p>已完成: 3/10 项物料</p>
        </div>

        <!-- 物料明细列表 -->
        <h3>物料明细</h3>
        <table class="material-table">
            <thead>
                <tr>
                    <th>序号</th>
                    <th>料号</th>
                    <th>物料名称</th>
                    <th>物料规格</th>
                    <th>数量</th>
                    <th>集配数量</th>
                    <th>工单数量</th>
                    <th>集配状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td>TBH-ERH-P1</td>
                    <td>TBH-ERH-P1</td>
                    <td>1000000.12</td>
                    <td>1</td>
                    <td>1</td>
                    <td>1</td>
                    <td><span class="status-badge status-completed">已完成</span></td>
                    <td>
                        <button class="btn btn-secondary" disabled>已完成</button>
                    </td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>TBH-ERH-P2</td>
                    <td>TBH-ERH-P2</td>
                    <td>2000000.12</td>
                    <td>1</td>
                    <td>1</td>
                    <td>1</td>
                    <td><span class="status-badge status-completed">已完成</span></td>
                    <td>
                        <button class="btn btn-secondary" disabled>已完成</button>
                    </td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>TBH-ERH-P3</td>
                    <td>TBH-ERH-P3</td>
                    <td>3000000.12</td>
                    <td>1</td>
                    <td>1</td>
                    <td>1</td>
                    <td><span class="status-badge status-completed">已完成</span></td>
                    <td>
                        <button class="btn btn-secondary" disabled>已完成</button>
                    </td>
                </tr>
                <tr>
                    <td>4</td>
                    <td>TBH-ERH-P4</td>
                    <td>TBH-ERH-P4</td>
                    <td>4000000.12</td>
                    <td>1</td>
                    <td>0</td>
                    <td>1</td>
                    <td><span class="status-badge status-pending">待集配</span></td>
                    <td>
                        <button class="btn btn-success" onclick="collectMaterial(4)">集配</button>
                    </td>
                </tr>
                <tr>
                    <td>5</td>
                    <td>TBH-ERH-P5</td>
                    <td>TBH-ERH-P5</td>
                    <td>5000000.12</td>
                    <td>1</td>
                    <td>0</td>
                    <td>1</td>
                    <td><span class="status-badge status-pending">待集配</span></td>
                    <td>
                        <button class="btn btn-success" onclick="collectMaterial(5)">集配</button>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- 操作按钮 -->
        <div class="action-buttons">
            <button class="btn btn-success" onclick="startTask()">开始任务</button>
            <button class="btn btn-warning" onclick="completeTask()">完成任务</button>
            <button class="btn btn-primary" onclick="goBack()">返回列表</button>
        </div>
    </div>

    <script>
        // 集配物料
        function collectMaterial(materialId) {
            if (confirm('确定要集配这个物料吗？')) {
                console.log('集配物料:', materialId);
                // TODO: 调用后端API进行物料集配
                // 更新界面状态
                updateMaterialStatus(materialId, 'completed');
                updateProgress();
            }
        }

        // 开始任务
        function startTask() {
            if (confirm('确定要开始执行这个集配任务吗？')) {
                console.log('开始任务');
                // TODO: 调用后端API开始任务
            }
        }

        // 完成任务
        function completeTask() {
            if (confirm('确定要完成这个集配任务吗？')) {
                console.log('完成任务');
                // TODO: 调用后端API完成任务
            }
        }

        // 返回列表
        function goBack() {
            window.history.back();
        }

        // 更新物料状态
        function updateMaterialStatus(materialId, status) {
            // TODO: 更新界面中对应物料的状态
        }

        // 更新进度条
        function updateProgress() {
            // TODO: 重新计算并更新进度条
        }

        // 页面加载时获取任务详情
        window.onload = function() {
            const urlParams = new URLSearchParams(window.location.search);
            const taskId = urlParams.get('id');
            if (taskId) {
                console.log('加载任务详情:', taskId);
                // TODO: 调用后端API获取任务详情
                // fetch('/kittingTask/' + taskId)
            }
        };
    </script>
</body>
</html>
