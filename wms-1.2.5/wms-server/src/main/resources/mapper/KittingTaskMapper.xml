<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.wms.dao.KittingTaskMapper">

    <!-- 查询集配任务列表 -->
    <select id="queryKittingTasks" resultType="com.hvisions.wms.dto.kitting.KittingTaskDTO">
        SELECT
            kt.id,
            kt.kitting_task_no as kittingTaskNo,
            kt.product_work_order_code as productWorkOrderCode,
            kt.requirement_code as requirementCode,
            kt.requirement_time as requirementTime,
            kt.task_status as taskStatus,
            kt.target_point_code as targetPointCode,
            kt.priority,
            kt.status,
            kt.create_time as createTime,
            kt.update_time as updateTime
        FROM hv_wms_kitting_task kt
        WHERE 1=1
        <if test="queryDTO.kittingTaskNo != null and queryDTO.kittingTaskNo != ''">
            AND kt.kitting_task_no LIKE CONCAT('%', #{queryDTO.kittingTaskNo}, '%')
        </if>
        <if test="queryDTO.productWorkOrderCode != null and queryDTO.productWorkOrderCode != ''">
            AND kt.product_work_order_code LIKE CONCAT('%', #{queryDTO.productWorkOrderCode}, '%')
        </if>
        <if test="queryDTO.requirementCode != null and queryDTO.requirementCode != ''">
            AND kt.requirement_code LIKE CONCAT('%', #{queryDTO.requirementCode}, '%')
        </if>
        <if test="queryDTO.taskStatus != null">
            AND kt.task_status = #{queryDTO.taskStatus}
        </if>
        <if test="queryDTO.targetPointCode != null and queryDTO.targetPointCode != ''">
            AND kt.target_point_code = #{queryDTO.targetPointCode}
        </if>
        <if test="queryDTO.status != null">
            AND kt.status = #{queryDTO.status}
        </if>
        <if test="queryDTO.requirementTimeStart != null">
            AND kt.requirement_time >= #{queryDTO.requirementTimeStart}
        </if>
        <if test="queryDTO.requirementTimeEnd != null">
            AND kt.requirement_time &lt;= #{queryDTO.requirementTimeEnd}
        </if>
        ORDER BY kt.create_time DESC
    </select>

    <!-- 根据ID获取集配任务详情 -->
    <select id="getKittingTaskById" resultType="com.hvisions.wms.dto.kitting.KittingTaskDTO">
        SELECT
            kt.id,
            kt.kitting_task_no as kittingTaskNo,
            kt.product_work_order_code as productWorkOrderCode,
            kt.requirement_code as requirementCode,
            kt.requirement_time as requirementTime,
            kt.task_status as taskStatus,
            kt.target_point_code as targetPointCode,
            kt.priority,
            kt.status,
            kt.create_time as createTime,
            kt.update_time as updateTime
        FROM hv_wms_kitting_task kt
        WHERE kt.id = #{taskId}
    </select>

    <!-- 检查任务的所有明细是否都已完成集配 -->
    <select id="checkAllDetailsCompleted" resultType="boolean">
        SELECT 
            CASE 
                WHEN COUNT(*) = 0 THEN TRUE
                WHEN COUNT(*) = SUM(CASE WHEN kitting_status = 1 THEN 1 ELSE 0 END) THEN TRUE
                ELSE FALSE
            END
        FROM hv_wms_kitting_task_detail
        WHERE kitting_task_id = #{taskId}
    </select>

</mapper>
