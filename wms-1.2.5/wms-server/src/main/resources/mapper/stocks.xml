<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.wms.dao.StocksMapper">

    <resultMap id="stock" type="com.hvisions.wms.dto.stock.StockMaterialDTO">

    </resultMap>
    <insert id="saveStocks">
        INSERT INTO
        hv_wms_stocks(
            create_time,
            creator_id,
            site_num,
            update_time,
            updater_id,
            frame_code,
            location_id,
            material_batch_num,
            material_id,
            pre_material_batch_num,
            quantity,
            supplier_id,
            location_point_id,
            ship_no,
            block_code
        ) VALUES (
            #{stocks.createTime},
            #{stocks.creatorId},
            #{stocks.siteNum},
            #{stocks.updateTime},
            #{stocks.updaterId},
            #{stocks.frameCode},
            #{stocks.locationId},
            #{stocks.materialBatchNum},
            #{stocks.materialId},
            #{stocks.preMaterialBatchNum},
            #{stocks.quantity},
            #{stocks.supplierId},
            #{stocks.locationPointId},
            #{stocks.shipNo},
            #{stocks.blockCode}
        )
    </insert>
    <update id="updateStocksByStocksId">
        UPDATE hv_wms_stocks
        <set>
            <if test="stocks.frameCode != null and stocks.frameCode != ''">
                frame_code = #{stocks.frameCode},
            </if>
            <if test="stocks.locationId != null">
                location_id = #{stocks.locationId},
            </if>
            <if test="stocks.materialBatchNum != null and stocks.materialBatchNum != ''">
                material_batch_num = #{stocks.materialBatchNum},
            </if>
            <if test="stocks.materialId != null">
                material_id = #{stocks.materialId},
            </if>
            <if test="stocks.preMaterialBatchNum != null and stocks.preMaterialBatchNum != ''">
                pre_material_batch_num = #{stocks.preMaterialBatchNum},
            </if>
            <if test="stocks.quantity != null">
                quantity = #{stocks.quantity},
            </if>
            <if test="stocks.supplierId != null">
                supplier_id = #{stocks.supplierId},
            </if>
            <if test="stocks.locationPointId != null and stocks.locationPointId != ''">
                location_point_id = #{stocks.locationPointId},
            </if>
            <if test="stocks.shipNo != null and stocks.shipNo != ''">
                ship_no = #{stocks.shipNo},
            </if>
            <if test="stocks.blockCode != null and stocks.blockCode != ''">
                block_code = #{stocks.blockCode},
            </if>
            <if test="stocks.updateTime != null">
                update_time = #{stocks.updateTime},
            </if>
            <if test="stocks.updaterId != null">
                updater_id = #{stocks.updaterId},
            </if>
        </set>
        WHERE id = #{stocks.id}
    </update>


    <select id="getStocks" resultMap="stock" parameterType="com.hvisions.wms.dto.stock.StockQueryDTO"
            databaseId="mysql">
        SELECT
        h.id,
        h.material_id,
        h.create_time,
        h.location_id,
        h.material_batch_num,
        h.pre_material_batch_num,
        l.code as locationAreaCode,
        l.name as locationDescription,
        lmp.id as materialPointId,
        lmp.material_point_code as materialPointCode,
        lmp.material_point_name as materialPointName,
        h.quantity,
        case when occupy.quantity is null then 0 else occupy.quantity end as usedCount,
        hb.description as unitName,
        m.material_name,
        m.material_code,
        s.supplier_name,
        h.frame_code,
        h.ship_no,
        h.block_code
        FROM hv_wms_stocks h
        left join framework.sys_supplier s on h.supplier_id = s.id
        left join hiper_base.hv_bm_material m on m.id = h.material_id
        left join hv_wms_wares_location l on l.id = h.location_id
        left join hv_wms_wares_location_material_point lmp on lmp.id = h.location_point_id
        left join (
        select stock_id,sum(quantity) quantity from hv_wms_stocks_occupy ol group by stock_id ) occupy on
        occupy.stock_id = h.id
        left join hiper_base.hv_bm_unit hb on m.uom = hb.id
        <where>
            <if test="dto.locationIds !=null and dto.locationIds.size >0 ">
                <foreach collection="dto.locationIds" index="index" item="item" open="and h.location_id in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.keyword != null  and dto.keyword != &apos;&apos;">
                AND (
                h.material_batch_num like concat('%',#{dto.keyword},'%')
                OR m.material_code like concat('%',#{dto.keyword},'%')
                OR m.material_name like concat('%',#{dto.keyword},'%')
                )
            </if>
            <if test="dto.materialId != null  and dto.materialId != &apos;&apos;">
                AND h.material_id = #{dto.materialId}
            </if>
            <if test="dto.createDateStart != null">
                AND h.create_time <![CDATA[ >= ]]> #{dto.createDateStart}
            </if>
            <if test="dto.createDateEnd != null ">
                AND h.create_time <![CDATA[ <= ]]> #{dto.createDateEnd}
            </if>
            <if test="dto.materialCode != null  and dto.materialCode != &apos;&apos;">
                AND m.material_code = #{dto.materialCode}
            </if>
            <!--  <if test="dto.materialCode != null  and dto.materialCode != &apos;&apos;">
                  AND m.material_code = #{dto.materialCode}
              </if>
              <if test="dto.materialCode != null  and dto.materialCode.trim() != &apos;&apos; and dto.isExactMatchMaterialCode == true">
                  AND m.material_code = #{dto.materialCode}
              </if>-->
            <if test="dto.materialBatchNum !=null  and dto.materialBatchNum != &apos;&apos; ">
                AND h.material_batch_num like concat('%',#{dto.materialBatchNum},'%')
            </if>
            <if test="dto.frameCode !=null  and dto.frameCode != &apos;&apos; ">
                AND h.frame_code = #{dto.frameCode}
            </if>
            <if test="dto.shipNo !=null  and dto.shipNo != &apos;&apos; ">
                AND h.ship_no = #{dto.shipNo}
            </if>
            <if test="dto.blockCode !=null  and dto.blockCode != &apos;&apos; ">
                AND h.block_code = #{dto.blockCode}
            </if>
        </where>
        ORDER BY h.id ASC
    </select>

    <select id="getAllByQuery" resultMap="stock" parameterType="com.hvisions.wms.dto.stock.StockQueryDTO" databaseId="mysql">
        SELECT
            h.id,
            h.material_id,
            h.create_time,
            h.location_id,
            h.material_batch_num,
            h.pre_material_batch_num,
            l.id as materialPointid,
            l.code as materialPointCode ,
            l.name as materialPointName ,
            l2.code as locationAreaCode,
            l2.name as locationDescription,
            h.quantity,
            case when occupy.quantity is null then 0 else occupy.quantity end as usedCount,
            hb.description as unitName,
            m.material_name,
            m.material_code,
            s.supplier_name,
            h.frame_code
            FROM hv_wms_stocks h
            left join framework.sys_supplier s on h.supplier_id = s.id
            left join hiper_base.hv_bm_material m on m.id = h.material_id
            left join hv_wms_wares_location l on l.id = h.location_id
            LEFT JOIN hv_wms_wares_location l2 ON l.parent_id = l2.id
            left join (
            select stock_id,sum(quantity) quantity from hv_wms_stocks_occupy ol group by stock_id ) occupy on
            occupy.stock_id = h.id
            left join hiper_base.hv_bm_unit hb on m.uom = hb.id
        <where>
            <if test="dto.locationIds !=null and dto.locationIds.size >0 ">
                <foreach collection="dto.locationIds" index="index" item="item" open="and h.location_id in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.keyword != null  and dto.keyword != &apos;&apos;">
                AND (
                h.material_batch_num like concat('%',#{dto.keyword},'%')
                OR m.material_code like concat('%',#{dto.keyword},'%')
                OR m.material_name like concat('%',#{dto.keyword},'%')
                )
            </if>
            <if test="dto.materialId != null  and dto.materialId != &apos;&apos;">
                AND h.material_id = #{dto.materialId}
            </if>
            <if test="dto.createDateStart != null">
                AND h.create_time <![CDATA[ >= ]]> #{dto.createDateStart}
            </if>
            <if test="dto.createDateEnd != null ">
                AND h.create_time <![CDATA[ <= ]]> #{dto.createDateEnd}
            </if>
            <if test="dto.materialCode != null  and dto.materialCode != &apos;&apos;">
                AND m.material_code = #{dto.materialCode}
            </if>
            <if test="dto.materialBatchNum !=null  and dto.materialBatchNum != &apos;&apos; ">
                AND h.material_batch_num like concat('%',#{dto.materialBatchNum},'%')
            </if>
            <if test="dto.frameCode !=null  and dto.frameCode != &apos;&apos; ">
                AND h.frame_code like concat('%',#{dto.frameCode},'%')
            </if>
        </where>
    </select>

    <resultMap id="stocks" type="com.hvisions.wms.dto.stock.HvWmsStocksDTO"></resultMap>
    <select id="getAllByMaterialOrLocation" resultMap="stocks" databaseId="mysql">
        SELECT h.*,me.effective_period
        from hv_wms_stocks h
        left join hiper_base.hv_bm_material_extend me on me.material_id = h.material_id
        <where>
            <if test=" dto.locationIds !=null and dto.locationIds.size >0 and dto.locationIds != null">
                <foreach collection="dto.locationIds" index="index" item="item" open="and h.location_id in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.materialId != null and dto.materialId != &apos;&apos;">
                AND h.material_id = #{dto.materialId}
            </if>
        </where>
        order by h.create_time asc
    </select>
    <select id="getAllByMaterialOrLocation" resultMap="stocks" databaseId="sqlserver">
        SELECT h.*,me.effective_period
        from hv_wms_stocks h
        left join hiper_base.dbo.hv_bm_material_extend me on me.material_id = h.material_id
        <where>
            <if test=" dto.locationIds !=null and dto.locationIds.size >0 and dto.locationIds != null">
                <foreach collection="dto.locationIds" index="index" item="item" open="and h.location_id in("
                         separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.materialId != null and dto.materialId != &apos;&apos;">
                AND h.material_id = #{dto.materialId}
            </if>
        </where>
        order by h.create_time asc
    </select>

    <resultMap id="stocksByCodeOrName" type="com.hvisions.wms.dto.stock.StockMaterialMsgDTO"></resultMap>
    <select id="getStocksByCodeOrName" resultMap="stocksByCodeOrName" parameterType="java.lang.String" databaseId="mysql">
        SELECT h.*, m.material_code, m.material_name, u.description as unitName, l.name as locationName
        from hv_wms_stocks h
                 left join hiper_base.hv_bm_material m on m.id = h.material_id
                 left join hiper_base.hv_bm_unit u on m.uom = u.id
                 left join hv_wms_wares_location l on l.id = h.location_id
        where m.material_code like concat('%', #{code}, '%')
           or m.material_name like concat('%', #{code}, '%')
           or h.material_batch_num like concat('%', #{code}, '%')
        order by h.create_time asc
    </select>
    <select id="getStocksByCodeOrName" resultMap="stocksByCodeOrName" parameterType="java.lang.String" databaseId="sqlserver">
        SELECT h.*, m.material_code, m.material_name, u.description as unitName, l.name as locationName
        from hv_wms_stocks h
                 left join hiper_base.dbo.hv_bm_material m on m.id = h.material_id
                 left join hiper_base.dbo.hv_bm_unit u on m.uom = u.id
                 left join hv_wms_wares_location l on l.id = h.location_id
        where m.material_code like concat('%', #{code}, '%')
           or m.material_name like concat('%', #{code}, '%')
           or h.material_batch_num like concat('%', #{code}, '%')
        order by h.create_time asc
    </select>

    <select id="findMaterialCount" resultType="com.hvisions.wms.dto.stock.MaterialStockCount">
        select sum(t1.quantity) as quantity, t1.material_id
        from hv_wms_stocks t1
        <where>
            <foreach collection="ids" index="index" item="item" open="material_id in("
                     separator=","
                     close=")">
                #{item}
            </foreach>
        </where>
        group by material_id
    </select>

    <sql id="stockSql" databaseId="mysql">
        SELECT
        t5.location_id AS location_id,
        t5.quantity AS quantity,
        t5.material_id AS material_id,
        t6.name AS location_name,
        t6.code AS location_code,
        t7.id AS area_id,
        t7.name AS area_name,
        t7.code AS area_code,
        t8.id AS warehouse_id,
        t8.name AS warehouse_name,
        t8.code AS warehouse_code,
        t9.material_name AS material_name,
        t9.material_code AS material_code,
        t10.description AS unit_name,
        t9.specs
        FROM ( SELECT
        t1.location_id AS location_id,
        t1.material_id AS material_id,
        SUM(t1.quantity) AS quantity
        FROM hv_wms_stocks t1
        LEFT JOIN hv_wms_wares_location t2 ON t1.location_id = t2.id
        LEFT JOIN hv_wms_wares_location t3 ON t2.parent_id = t3.id
        LEFT JOIN hv_wms_wares_location t4 ON t3.parent_id = t4.id

        <where>
            t4.parent_id = 0
            <if test="query.warehouseId != null and query.warehouseId != ''">
                AND t4.id = #{query.warehouseId}
            </if>
            <if test="query.areaId != null and query.areaId != ''">
                AND t3.id = #{query.areaId}
            </if>
            <!-- 修改为支持多个库位 ID -->
            <if test="query.locationId != null and query.locationId.size > 0">
                AND t2.id IN
                <foreach item="lId" collection="query.locationId" separator="," open="(" close=")">
                    #{lId}
                </foreach>
            </if>
            <if test="query.materialId != null and query.materialId != ''">
                AND t1.material_id = #{query.materialId}
            </if>
        </where>
        GROUP BY t1.material_id, t1.location_id ) t5
        LEFT JOIN hv_wms_wares_location t6 ON t5.location_id = t6.id
        LEFT JOIN hv_wms_wares_location t7 ON t6.parent_id = t7.id
        LEFT JOIN hv_wms_wares_location t8 ON t7.parent_id = t8.id
        LEFT JOIN hiper_base.hv_bm_material t9 ON t5.material_id = t9.id
        LEFT JOIN hiper_base.hv_bm_unit t10 ON t9.uom = t10.id
    </sql>
    <sql id="stockSql" databaseId="sqlserver">
        SELECT
        t4.location_id AS location_id,
        t4.quantity AS quantity,
        t4.material_id AS material_id,
        t5.name AS location_name,
        t5.code AS location_code,
        t6.id AS warehouse_id,
        t6.name AS warehouse_name,
        t6.code AS warehouse_code,
        t7.material_name AS material_name,
        t7.material_code AS material_code,
        t8.description AS unit_name
        FROM ( SELECT
        t1.location_id AS location_id,
        t1.material_id AS material_id,
        SUM(t1.quantity) AS quantity
        FROM hv_wms_stocks t1
        LEFT JOIN hv_wms_wares_location t2 ON t1.location_id = t2.id
        LEFT JOIN hv_wms_wares_location t3 ON t2.parent_id = t3.id
        <where>
            t3.parent_id = 0
            <if test="query.warehouseId != null">
                AND t3.id = #{query.warehouseId}
            </if>
            <if test="query.locationId != null">
                AND t2.id = #{query.locationId}
            </if>
            <if test="query.materialId != null">
                AND t1.material_id = #{query.materialId}
            </if>
        </where>
        GROUP BY t1.material_id,t1.location_id ) t4
        LEFT JOIN hv_wms_wares_location t5 ON t4.location_id = t5.id
        LEFT JOIN hv_wms_wares_location t6 ON t5.parent_id = t6.id
        LEFT JOIN hiper_base.dbo.hv_bm_material t7 ON t4.material_id = t7.id
        LEFT JOIN hiper_base.dbo.hv_bm_unit t8 ON t7.uom = t8.id
    </sql>
    <select id="getMaterialStock" resultType="com.hvisions.wms.dto.stock.StockInfoDTO">
        <include refid="stockSql" />
        ORDER BY t8.id,t7.id,t6.id
    </select>
    <select id="getLocationStock" resultType="com.hvisions.wms.dto.stock.StockInfoDTO">
        <include refid="stockSql" />
        ORDER BY t5.material_id
    </select>
    <select id="getSum" resultType="java.math.BigDecimal">
        select sum(t1.quantity) from hv_wms_stocks t1 where t1.material_id = #{materialId}
    </select>


    <select id="getStockPallet" resultType="java.lang.String">
        SELECT DISTINCT frame_code
        FROM hv_wms_stocks
        WHERE frame_code IS NOT NULL
          AND frame_code != ''
        ORDER BY
            frame_code ASC;
    </select>
    <select id="getQuantityByMaterialCodeAndLocationCode"
            resultType="com.hvisions.wms.dto.stock.StockMaterialPreparationDTO">
        SELECT
            h.location_id  as locationId,
            h.material_id as materialId,
            l.CODE as locationCode,
            l.NAME as locationDescription,
            m.material_name as materialName,
            m.material_code as materialCode,
            SUM( h.quantity )  as quantity
        FROM
            `hv_wms_stocks` h
                LEFT JOIN hiper_base.hv_bm_material m ON m.id = h.material_id and m.eigenvalue=1
                LEFT JOIN hv_wms_wares_location l ON l.id = h.location_id
        WHERE
            m.material_code =#{materialCode}
          AND l.CODE =#{locationCode}
        GROUP BY
            h.material_id,
            h.location_id,
            l.CODE,
            l.NAME,
            m.material_name,
            m.material_code
    </select>

    <select id="getUsedCountByMaterialCodeAndLocationCode"
            resultType="com.hvisions.wms.dto.stock.StockMaterialPreparationDTO">
        SELECT
            so.location_id  as locationId,
            so.material_id as materialId,
            l.CODE as locationCode,
            l.NAME as locationDescription,
            m.material_name as materialName,
            m.material_code as materialCode,
            SUM( so.quantity )  as quantity
        FROM
            `hv_wms_stocks_occupy` so
                LEFT JOIN hiper_base.hv_bm_material m ON m.id = so.material_id and m.eigenvalue=1
                LEFT JOIN hv_wms_wares_location l ON l.id = so.location_id
        WHERE
            m.material_code = #{materialCode}
          AND l.CODE = #{locationCode}
        GROUP BY
            so.material_id,
            so.location_id,
            l.CODE,
            l.NAME,
            m.material_name,
            m.material_code
    </select>
    <select id="getStocksByFrame" resultType="com.hvisions.wms.dto.stock.StockMaterialDTO">
        SELECT
            hs.material_id,
            hb.material_code,
            hb.material_name,
            hs.quantity,
            hs.create_time
        FROM
            `hv_wms_stocks` hs
                JOIN hiper_base.hv_bm_material hb ON hb.id = hs.material_id
        WHERE
            hs.frame_code = #{frameCode}
        GROUP BY
            hs.material_id,
            hb.material_code,
            hb.material_name,
            hs.create_time
    </select>
    <select id="getLocationList" resultType="com.hvisions.wms.dto.stock.StockMaterialDTO">
        SELECT
            DISTINCT
            h.location_id as locationId,
            l.code as locationCode,
            l.name as locationDescription
        FROM hv_wms_stocks h
        LEFT JOIN hv_wms_wares_location l on l.id = h.location_id
    </select>
    <select id="getListByFrameCode" resultType="com.hvisions.wms.dto.stock.StockMaterialDTO">
        SELECT
            h.id,
            h.material_id,
            h.create_time,
            h.location_id,
            h.material_batch_num,
            h.pre_material_batch_num,
            l.code as locationCode,
            l.code as locationAreaCode,
            l.name as locationDescription,
            lmp.id as materialPointId,
            lmp.material_point_code as materialPointCode,
            lmp.material_point_name as materialPointName,
            h.quantity,
            case when occupy.quantity is null then 0 else occupy.quantity end as usedCount,
            hb.description as unitName,
            m.material_name,
            m.material_code,
            s.supplier_name,
            h.frame_code
        FROM hv_wms_stocks h
                 left join framework.sys_supplier s on h.supplier_id = s.id
                 left join hiper_base.hv_bm_material m on m.id = h.material_id
                 left join hv_wms_wares_location l on l.id = h.location_id
                 left join hv_wms_wares_location_material_point lmp on lmp.id = h.location_point_id
                 left join (
            select stock_id,sum(quantity) quantity from hv_wms_stocks_occupy ol group by stock_id ) occupy on
            occupy.stock_id = h.id
                 left join hiper_base.hv_bm_unit hb on m.uom = hb.id
        WHERE
            h.frame_code = #{frameCode}
    </select>
    <select id="getStockOccupyByOrderCodeAndMaterialCode"
            resultType="com.hvisions.wms.dto.stock.StockOccupyDTO">
        SELECT
            o.*
        FROM
            hv_wms_stocks_occupy o
                LEFT JOIN hiper_base.hv_bm_material m ON m.id = o.material_id
        WHERE
            o.order_code =   #{orderCode} AND
            m.material_code =   #{materialCode}
    </select>
    <select id="getStockMaterialByFrameCodeAndMaterialId"
            resultType="com.hvisions.wms.dto.stock.MaterialSortingShowDTO">
        SELECT
            *
        FROM
        hv_wms_stocks
        <where>
            frame_code =   #{frameCode}
            AND
            material_id =   #{materialId}
        </where>
    </select>

    <delete id="deleteStockOccupyByOrderCode">
        DELETE FROM hv_wms_stocks_occupy where  order_code = #{orderCode}
    </delete>
</mapper>
