<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.wms.dao.KittingTaskDetailMapper">

    <!-- 根据集配任务ID获取明细列表 -->
    <select id="getDetailsByTaskId" resultType="com.hvisions.wms.dto.kitting.KittingTaskDetailDTO">
        SELECT 
            ktd.id,
            ktd.kitting_task_id as kittingTaskId,
            ktd.sequence_no as sequenceNo,
            ktd.material_code as materialCode,
            ktd.material_name as materialName,
            ktd.material_spec as materialSpec,
            ktd.quantity,
            ktd.kitting_quantity as kittingQuantity,
            ktd.work_order_quantity as workOrderQuantity,
            ktd.kitting_status as kittingStatus,
            ktd.create_time as createTime,
            ktd.update_time as updateTime
        FROM hv_wms_kitting_task_detail ktd
        WHERE ktd.kitting_task_id = #{kittingTaskId}
        ORDER BY ktd.sequence_no ASC, ktd.id ASC
    </select>

    <!-- 批量更新集配状态 -->
    <update id="batchUpdateKittingStatus">
        UPDATE hv_wms_kitting_task_detail 
        SET kitting_status = #{kittingStatus},
            update_time = NOW()
        WHERE id IN
        <foreach collection="detailIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
