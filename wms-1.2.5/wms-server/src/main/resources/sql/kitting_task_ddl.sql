-- =============================================
-- 物料集配功能建表语句
-- 创建时间: 2025-01-14
-- 说明: 包含集配任务主表和明细表
-- 基类: KittingBaseEntity (使用LocalDateTime时间类型)
-- =============================================

-- 1. 集配任务主表
DROP TABLE IF EXISTS `hv_wms_kitting_task`;
CREATE TABLE `hv_wms_kitting_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `kitting_task_no` varchar(50) NOT NULL COMMENT '集配任务号',
  `product_work_order_code` varchar(50) DEFAULT NULL COMMENT '关联生产工单号',
  `requirement_code` varchar(50) DEFAULT NULL COMMENT '关联需求单号',
  `requirement_time` datetime DEFAULT NULL COMMENT '需求时间',
  `task_status` int(11) NOT NULL COMMENT '任务状态',
  `target_point_code` varchar(50) DEFAULT NULL COMMENT '目标料点',
  `priority` int(11) DEFAULT NULL COMMENT '优先级',
  `status` int(11) NOT NULL COMMENT '状态',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `creator_id` int(11) DEFAULT NULL COMMENT '创建人',
  `updater_id` int(11) DEFAULT NULL COMMENT '修改人',
  `site_num` varchar(50) DEFAULT NULL COMMENT '租户字段',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_kitting_task_no` (`kitting_task_no`),
  KEY `idx_product_work_order_code` (`product_work_order_code`),
  KEY `idx_requirement_code` (`requirement_code`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_status` (`status`),
  KEY `idx_target_point_code` (`target_point_code`),
  KEY `idx_requirement_time` (`requirement_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='WMS集配任务主表';

-- 2. 集配任务明细表
DROP TABLE IF EXISTS `hv_wms_kitting_task_detail`;
CREATE TABLE `hv_wms_kitting_task_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `kitting_task_id` int(11) NOT NULL COMMENT '集配任务ID',
  `sequence_no` int(11) DEFAULT NULL COMMENT '序号',
  `material_code` varchar(50) NOT NULL COMMENT '料号',
  `material_name` varchar(200) DEFAULT NULL COMMENT '物料名称',
  `material_spec` varchar(200) DEFAULT NULL COMMENT '物料规格',
  `quantity` decimal(18,6) NOT NULL COMMENT '数量',
  `kitting_quantity` decimal(18,6) DEFAULT 0.000000 COMMENT '集配数量',
  `work_order_quantity` decimal(18,6) DEFAULT NULL COMMENT '工单数量',
  `kitting_status` int(11) NOT NULL COMMENT '集配状态',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `creator_id` int(11) DEFAULT NULL COMMENT '创建人',
  `updater_id` int(11) DEFAULT NULL COMMENT '修改人',
  `site_num` varchar(50) DEFAULT NULL COMMENT '租户字段',
  PRIMARY KEY (`id`),
  KEY `idx_kitting_task_id` (`kitting_task_id`),
  KEY `idx_material_code` (`material_code`),
  KEY `idx_kitting_status` (`kitting_status`),
  KEY `idx_sequence_no` (`sequence_no`),
  CONSTRAINT `fk_kitting_task_detail_task_id` FOREIGN KEY (`kitting_task_id`) REFERENCES `hv_wms_kitting_task` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='WMS集配任务明细表';

-- 3. 初始化数据字典（任务状态）
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
('集配任务状态', 'kitting_task_status', '0', 'admin', NOW(), '集配任务状态字典');

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '待开始', '0', 'kitting_task_status', '', 'warning', 'Y', '0', 'admin', NOW(), '集配任务待开始状态'),
(2, '进行中', '1', 'kitting_task_status', '', 'primary', 'N', '0', 'admin', NOW(), '集配任务进行中状态'),
(3, '已完成', '2', 'kitting_task_status', '', 'success', 'N', '0', 'admin', NOW(), '集配任务已完成状态');

-- 4. 初始化数据字典（集配状态）
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) VALUES
('集配状态', 'kitting_status', '0', 'admin', NOW(), '物料集配状态字典');

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '待集配', '0', 'kitting_status', '', 'warning', 'Y', '0', 'admin', NOW(), '物料待集配状态'),
(2, '已集配', '1', 'kitting_status', '', 'success', 'N', '0', 'admin', NOW(), '物料已集配状态');

-- 5. 创建索引优化查询性能
-- 复合索引：按状态和时间查询
CREATE INDEX `idx_status_time` ON `hv_wms_kitting_task` (`status`, `requirement_time`);

-- 复合索引：按任务状态和优先级查询
CREATE INDEX `idx_task_status_priority` ON `hv_wms_kitting_task` (`task_status`, `priority`);

-- 复合索引：明细表按任务ID和集配状态查询
CREATE INDEX `idx_task_kitting_status` ON `hv_wms_kitting_task_detail` (`kitting_task_id`, `kitting_status`);

-- 6. 添加表注释说明
ALTER TABLE `hv_wms_kitting_task` COMMENT = 'WMS集配任务主表-存储集配任务的基本信息';
ALTER TABLE `hv_wms_kitting_task_detail` COMMENT = 'WMS集配任务明细表-存储集配任务中具体的物料信息';

-- 7. 创建视图（可选）- 用于查询集配任务及其完成进度
CREATE OR REPLACE VIEW `v_kitting_task_progress` AS
SELECT 
    kt.id,
    kt.kitting_task_no,
    kt.product_work_order_code,
    kt.requirement_code,
    kt.requirement_time,
    kt.task_status,
    kt.target_point_code,
    kt.priority,
    kt.status,
    COUNT(ktd.id) as total_materials,
    SUM(CASE WHEN ktd.kitting_status = 1 THEN 1 ELSE 0 END) as completed_materials,
    ROUND(SUM(CASE WHEN ktd.kitting_status = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(ktd.id), 2) as completion_rate,
    kt.create_time,
    kt.update_time
FROM `hv_wms_kitting_task` kt
LEFT JOIN `hv_wms_kitting_task_detail` ktd ON kt.id = ktd.kitting_task_id
GROUP BY kt.id, kt.kitting_task_no, kt.product_work_order_code, kt.requirement_code, 
         kt.requirement_time, kt.task_status, kt.target_point_code, kt.priority, 
         kt.status, kt.create_time, kt.update_time;

-- 建表语句执行完成
SELECT 'WMS集配任务表创建完成' as message;
