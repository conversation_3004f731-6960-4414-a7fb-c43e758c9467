package com.hvisions.wms.repository;

import com.hvisions.wms.entity.HvWmsTransferBox;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: TransferBoxRepository</p>
 * <p>Description: 中转箱仓储层</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/8/26</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface TransferBoxRepository extends JpaRepository<HvWmsTransferBox, Integer> {

    /**
     * 根据编码查询中转箱详情
     *
     * @param code 中转箱编码
     * @return 中转箱详情
     */
    HvWmsTransferBox findByCode(String code);

    /**
     * 分页查询中转箱信息
     * @param code 编码
     * @param name 名称
     * @param pageable 分页信息
     * @return 中装箱分页信息
     */
    Page<HvWmsTransferBox> findAllByCodeContainsOrNameContaining(String code,String name, Pageable pageable);
}









