package com.hvisions.wms.repository;

import com.hvisions.wms.entity.stockcheck.HvWmsStockCheckDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: StockCheckDetailRepository</p >
 * <p>Description: 详情数据</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/9/8</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Repository
public interface StockCheckDetailRepository extends JpaRepository<HvWmsStockCheckDetail, Integer> {
    /**
     * @param batchNumber 批次号 lineId 行id
     * @return 详情数据
     */

    HvWmsStockCheckDetail findByBatchNumberAndLineId(String batchNumber, Integer lineId);

    /**
     * @param stockId 库存数据id
     * @param lineId  行数据id
     * @return 详情数据
     */
    HvWmsStockCheckDetail findByStockIdAndLineId(Integer stockId, Integer lineId);

    /**
     * 根据lineid删除数据
     *
     * @param lineId 行表id
     */
    void deleteAllByLineId(Integer lineId);
}
