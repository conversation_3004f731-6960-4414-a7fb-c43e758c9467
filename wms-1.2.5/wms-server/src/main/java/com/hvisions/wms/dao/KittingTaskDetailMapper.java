package com.hvisions.wms.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.wms.dto.kitting.KittingTaskDetailDTO;
import com.hvisions.wms.entity.HvWmsKittingTaskDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 集配任务明细Mapper接口
 */
@Mapper
public interface KittingTaskDetailMapper extends BaseMapper<HvWmsKittingTaskDetail> {

    /**
     * 根据集配任务ID获取明细列表
     *
     * @param kittingTaskId 集配任务ID
     * @return 明细列表
     */
    List<KittingTaskDetailDTO> getDetailsByTaskId(@Param("kittingTaskId") Integer kittingTaskId);

    /**
     * 批量更新集配状态
     *
     * @param detailIds 明细ID列表
     * @param kittingStatus 集配状态
     * @return 更新数量
     */
    int batchUpdateKittingStatus(@Param("detailIds") List<Integer> detailIds, 
                                @Param("kittingStatus") Integer kittingStatus);

}
