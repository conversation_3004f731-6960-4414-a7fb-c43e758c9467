package com.hvisions.wms.controller;

import com.hvisions.wms.dto.receipt.ShelfMaterialDTO;
import com.hvisions.wms.service.ShelfMaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Title: ShelfMaterialController</p >
 * <p>Description: 收货单物料上架控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/28</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/shelfMaterial")
@Api(description = "收货单物料上架控制器")
public class ShelfMaterialController {

    @Autowired
    ShelfMaterialService shelfMaterialService;

    /**
     * 修改原材料收货单状态
     *
     * @param id    主键
     * @param state 收货单状态
     */
    @GetMapping(value = "/setShelfState")
    @ApiOperation(value = "修改上架状态")
    public void setShelfState(@RequestParam int id, @RequestParam int state) {
        shelfMaterialService.setShelfMaterialState(id, state);
    }

    /**
     * 根据库位编号、上架数量做库存验证
     *
     * @param materialBatchNum 物料批次号
     * @param receiptCount     上架数量
     * @return 验证结果 0可以上架，1抵达仓库上限
     */
    @GetMapping(value = "validStock")
    @ApiOperation(value = "根据物料批次号、上架数量做仓储上限验证")
    public Boolean validStock(@RequestParam String materialBatchNum, @RequestParam BigDecimal receiptCount,
                              @RequestParam Integer receiptId) {
        return shelfMaterialService.validStock(materialBatchNum, receiptCount, receiptId);
    }

    /**
     * 上架、批量上架
     *
     * @param shelfMaterialDTOS 上架物料信息
     */
    @PostMapping(value = "/putShelf")
    @ApiOperation(value = "上架")
    public void putShelf(@RequestBody ShelfMaterialDTO shelfMaterialDTOS) {
        shelfMaterialService.putShelf(shelfMaterialDTOS);
    }

    /**
     * 上架、批量上架
     *
     * @param shelfMaterialDTOS 上架物料信息
     */
    @PostMapping(value = "/putAllShelf")
    @ApiOperation(value = "批量上架")
    public void putShelf(@RequestBody List<ShelfMaterialDTO> shelfMaterialDTOS) {
        shelfMaterialService.putAllShelf(shelfMaterialDTOS);
    }


    /**
     * 全部上架
     *
     * @param locationId 库位Id
     * @param receiptId  收货单ID
     */
    @PostMapping(value = "/allPut")
    @ApiOperation(value = "全部上架")
    public void allPut(@RequestParam Integer locationId, @RequestParam Integer receiptId) {
        shelfMaterialService.allPut(locationId, receiptId);
    }

    /**
     * 完成上架
     *
     * @param shelfId 上架物料Id
     */
    @PutMapping(value = "/finishShelf")
    @ApiOperation(value = "完成上架")
    public void finishShelf(@RequestParam Integer shelfId) {
        shelfMaterialService.finishShelf(shelfId);
    }


    /**
     * 完成上架入库
     *
     * @param receiptId 收货单Id
     */
    @PutMapping(value = "/finishShelfAndInStock")
    @ApiOperation(value = "完成上架入库")
    public void finishShelfAndInStock(@RequestParam Integer receiptId) {
        shelfMaterialService.finishShelfAndInStock(receiptId);
    }

    /**
     * 清楚上架物料信息
     *
     * @param shelfId 上架物料Id
     */
    @DeleteMapping(value = "/deleteById/{shelfId}")
    @ApiOperation(value = "清楚上架物料信息")
    public void deleteById(@PathVariable Integer shelfId) {
        shelfMaterialService.deleteById(shelfId);
    }

    /**
     * 根据收货单id查询上架物料信息
     *
     * @param receiptId 收货单Id
     * @return 上架物料信息
     */
    @GetMapping(value = "/getAllByReceiptId/{receiptId}")
    @ApiOperation(value = "根据收货单id查询上架物料信息")
    public List<ShelfMaterialDTO> getAllByReceiptId(@PathVariable int receiptId) {
        return shelfMaterialService.getAllByReceiptId(receiptId);
    }

    /**
     * 上架重置
     *
     * @param receiptId 收货单Id
     */
    @PutMapping(value = "/resetShelf/{receiptId}")
    @ApiOperation(value = "上架重置")
    public void resetShelf(@PathVariable Integer receiptId) {
        shelfMaterialService.resetShelf(receiptId);
    }

    /**
     * 自动上架
     *
     * @param receiptId 收货单Id
     */
    @PutMapping(value = "/autoShelf/{receiptId}")
    @ApiOperation(value = "自动上架分配库位")
    public void autoShelf(@PathVariable Integer receiptId) {
        shelfMaterialService.autoShelf(receiptId);
    }
}