package com.hvisions.wms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.MaterialPointClient;
import com.hvisions.wms.dto.location.WaresLocationDTO;
import com.hvisions.wms.dto.location.WaresLocationMaterialPointSynchronizationDTO;
import com.hvisions.wms.entity.HvWmsWaresLocationMaterialPoint;
import com.hvisions.wms.enums.PageControllerTypeEnum;
import com.hvisions.wms.excelTemplate.WaresLocationMaterialPointExcelTemplate;
import com.hvisions.wms.service.WaresLocationMaterialPointService;
import com.hvisions.wms.service.WaresLocationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-24 16:41
 */

@RestController
@RequestMapping(value = "/waresLocationMaterialPoint")
@Api(description = "库位/料点配置")
public class WaresLocationMaterialPointController {
    @Autowired
    private WaresLocationMaterialPointService waresLocationMaterialPointService;
    @Autowired
    private MaterialPointClient materialPointClient;
    @Autowired
    private WaresLocationService waresLocationService;

    /**
     * 根据料点区域获取下面料点数据
     */
    @ApiOperation("根据料点区域获取下面料点数据")
    @GetMapping("/getMaterialPointByLocationId/{locationId}")
    public List<HvWmsWaresLocationMaterialPoint> getMaterialPointByLocationId(@PathVariable Integer locationId) {
        QueryWrapper<HvWmsWaresLocationMaterialPoint> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("location_id",locationId);
        List<HvWmsWaresLocationMaterialPoint> list = waresLocationMaterialPointService.list(queryWrapper);
        return list;
    }

    /**
     * 导出
     *
     * @param
     */
    @ApiOperation(value = "导出")
    @PostMapping(value = "/exportData")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> exportData() {
        QueryWrapper<HvWmsWaresLocationMaterialPoint> queryWrapper = new QueryWrapper<>();

        List<HvWmsWaresLocationMaterialPoint> list = waresLocationMaterialPointService.list(queryWrapper);
        return ResultVO.success(EasyExcelUtil.getExcel(null, WaresLocationMaterialPointExcelTemplate.class, System.currentTimeMillis() + "调度记录.xlsx"));

    }

    /**
     * 下载模版
     *
     */
    @ApiOperation(value = "下载模版")
    @PostMapping(value = "/downloadTemplate")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> downloadTemplate() {
        List<WaresLocationMaterialPointExcelTemplate> list = new ArrayList<>();
        return ResultVO.success(EasyExcelUtil.getExcel(list, WaresLocationMaterialPointExcelTemplate.class, System.currentTimeMillis() + "库区主数据导入模版.xlsx"));

    }

    /**
     * 导入
     *
     * @param file 文件
     */
    @PostMapping("/import")
    @ApiOperation("导入")
    public void importData(MultipartFile file, @UserInfo @ApiIgnore UserInfoDTO userInfo) {

        waresLocationMaterialPointService.importData(file, userInfo);

    }

    /**
     * 添加
     *
     * @param waresLocationMaterialPoint
     */
    @ApiOperation(value = "添加")
    @PostMapping(value = "/add")
    @Transactional(rollbackFor = Exception.class)
    public boolean add(@RequestBody HvWmsWaresLocationMaterialPoint waresLocationMaterialPoint,@UserInfo @ApiIgnore UserInfoDTO userInfo) {
        //查询是否存在
        QueryWrapper<HvWmsWaresLocationMaterialPoint> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("material_point_code",waresLocationMaterialPoint.getMaterialPointCode());
        HvWmsWaresLocationMaterialPoint isExist = waresLocationMaterialPointService.getOne(queryWrapper);
        if(isExist != null){
            throw new BaseKnownException("料点编号" + waresLocationMaterialPoint.getMaterialPointCode() + "已存在");
        }

        waresLocationMaterialPoint.setCreateTime(new Date());
        if (userInfo != null) {
            waresLocationMaterialPoint.setCreatorId(userInfo.getId());
        }
        //同步至base模块
        WaresLocationDTO waresLocationDTO = waresLocationService.getLocationById(waresLocationMaterialPoint.getLocationId());
        WaresLocationMaterialPointSynchronizationDTO waresLocationMaterialPointSynchronizationDTO = new WaresLocationMaterialPointSynchronizationDTO();
        waresLocationMaterialPointSynchronizationDTO.setMaterialPointCode(waresLocationMaterialPoint.getMaterialPointCode());
        waresLocationMaterialPointSynchronizationDTO.setMaterialPointName(waresLocationMaterialPoint.getMaterialPointName());
        waresLocationMaterialPointSynchronizationDTO.setWarehouseAreaCode(waresLocationDTO.getCode());
        waresLocationMaterialPointSynchronizationDTO.setWarehouseAreaName(waresLocationDTO.getName());
        List<WaresLocationMaterialPointSynchronizationDTO> dtos = new ArrayList<>();
        dtos.add(waresLocationMaterialPointSynchronizationDTO);
        ResultVO<Boolean> resultVO = materialPointClient.synchronizationWmsModule(dtos, PageControllerTypeEnum.ADDorEDIT.getCode());
        if(!resultVO.isSuccess()){
            throw new BaseKnownException("同步base模块异常,请联系管理员");
        }
        return waresLocationMaterialPointService.save(waresLocationMaterialPoint);
    }

    /**
     * 删除
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除")
    @DeleteMapping(value = "/delete/{id}")
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(@PathVariable Long id) {
        HvWmsWaresLocationMaterialPoint materialPoint = waresLocationMaterialPointService.getById(id);
        WaresLocationMaterialPointSynchronizationDTO waresLocationMaterialPointSynchronizationDTO = new WaresLocationMaterialPointSynchronizationDTO();
        waresLocationMaterialPointSynchronizationDTO.setMaterialPointCode(materialPoint.getMaterialPointCode());
        List<WaresLocationMaterialPointSynchronizationDTO> dtos = new ArrayList<>();
        dtos.add(waresLocationMaterialPointSynchronizationDTO);
        ResultVO<Boolean> resultVO = materialPointClient.synchronizationWmsModule(dtos, PageControllerTypeEnum.DELETE.getCode());
        if(!resultVO.isSuccess()){
            throw new BaseKnownException("同步base模块异常,请联系管理员");
        }
        return waresLocationMaterialPointService.removeById(id);
    }

    /**
     * 修改
     *
     * @param waresLocationMaterialPoint
     */
    @ApiOperation(value = "修改")
    @PutMapping(value = "/update")
    @Transactional(rollbackFor = Exception.class)
    public boolean update(@RequestBody HvWmsWaresLocationMaterialPoint waresLocationMaterialPoint,@UserInfo @ApiIgnore UserInfoDTO userInfo) {
        waresLocationMaterialPoint.setUpdaterId(userInfo.getId());
        waresLocationMaterialPoint.setUpdateTime(new Date());
        //同步至base模块
        WaresLocationDTO waresLocationDTO = waresLocationService.getLocationById(waresLocationMaterialPoint.getLocationId());
        WaresLocationMaterialPointSynchronizationDTO waresLocationMaterialPointSynchronizationDTO = new WaresLocationMaterialPointSynchronizationDTO();
        waresLocationMaterialPointSynchronizationDTO.setMaterialPointCode(waresLocationMaterialPoint.getMaterialPointCode());
        waresLocationMaterialPointSynchronizationDTO.setMaterialPointName(waresLocationMaterialPoint.getMaterialPointName());
        waresLocationMaterialPointSynchronizationDTO.setWarehouseAreaCode(waresLocationDTO.getCode());
        waresLocationMaterialPointSynchronizationDTO.setWarehouseAreaName(waresLocationDTO.getName());
        List<WaresLocationMaterialPointSynchronizationDTO> dtos = new ArrayList<>();
        dtos.add(waresLocationMaterialPointSynchronizationDTO);
        ResultVO<Boolean> resultVO = materialPointClient.synchronizationWmsModule(dtos, PageControllerTypeEnum.ADDorEDIT.getCode());
        if(!resultVO.isSuccess()){
            throw new BaseKnownException("同步base模块异常,请联系管理员");
        }
        return waresLocationMaterialPointService.updateById(waresLocationMaterialPoint);
    }

    /**
     * 根据id获取
     *
     * @param id 主键
     * @return HvWmsWaresLocationMaterialPoint hvWmsWaresLocationMaterialPoint
     */
    @ApiOperation(value = "根据id获取")
    @GetMapping(value = "/get/{id}")
    public HvWmsWaresLocationMaterialPoint getById(@PathVariable Long id) {
        return waresLocationMaterialPointService.getById(id);
    }

    /**
     * 查询全部
     *
     * @return 列表
     */
    @ApiOperation(value = "查询全部")
    @GetMapping(value = "/getAll")
    public List<HvWmsWaresLocationMaterialPoint> getAll() {
        return waresLocationMaterialPointService.list();
    }

    /**
     * 根据库区id和库位编号查询
     */
    @ApiOperation("根据库区id和库位编号查询")
    @PostMapping("/getMaterialPointByLocationIdAndMaterialPointCode")
    public HvWmsWaresLocationMaterialPoint getMaterialPointByLocationIdAndMaterialPointCode(@RequestBody HvWmsWaresLocationMaterialPoint condition) {
        QueryWrapper<HvWmsWaresLocationMaterialPoint> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("location_id",condition.getLocationId());
        queryWrapper.eq("material_point_code",condition.getMaterialPointCode());
        HvWmsWaresLocationMaterialPoint materialPoint = waresLocationMaterialPointService.getOne(queryWrapper);
        return materialPoint;
    }


    /**
     * 同步Base模块库区主数据
     *
     * @param synchronizationData 传入的对象
     * @return 添加后的实体Id
     */
    @ApiOperation(value = "同步Base模块库区主数据")
    @PutMapping(value = "/synchronizationBaseModule")
    public boolean synchronizationBaseModule(@RequestBody List<WaresLocationMaterialPointSynchronizationDTO> synchronizationData,@RequestParam("typeCode") Integer typeCode) {
        return waresLocationMaterialPointService.synchronizationBaseModule(synchronizationData,typeCode);
    }
}
