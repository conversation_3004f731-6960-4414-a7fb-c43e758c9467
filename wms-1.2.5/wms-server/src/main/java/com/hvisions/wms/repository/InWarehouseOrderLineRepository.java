package com.hvisions.wms.repository;

/**
 * <p>Title: HvWmsInWarehouseOrderDetail</p>
 * <p>Description: 入库单详情表</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/9/3</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

import com.hvisions.wms.entity.HvWmsInWarehouseOrderLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InWarehouseOrderLineRepository extends JpaRepository<HvWmsInWarehouseOrderLine, Integer> {
    /**
     * 获取明细
     *
     * @param headerId 头表id
     * @return 明细
     */
    List<HvWmsInWarehouseOrderLine> findByHeaderId(Integer headerId);

    /**
     * 根据物料编码查询行表
     * @param materialCode 物料编码
     * @return 行表
     */
    List<HvWmsInWarehouseOrderLine> findAllByMaterialCode(String materialCode);
}









