package com.hvisions.wms.controller;

import com.hvisions.common.vo.ResultVO;


import com.hvisions.wms.dto.rcs.HvWmRcsReportedTaskRecord;
import com.hvisions.wms.service.HvWmRcsReportedTaskService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Api(tags = "rcs任务记录")
@RestController
@RequestMapping("/rcsReport")
public class HvWmRcsReportedTaskController {
    @Autowired
    private HvWmRcsReportedTaskService rcsReportedTaskService;

    @PostMapping("/rcs/findRecord")
    public HvWmRcsReportedTaskRecord searchById(@RequestParam("id") String id){
        return rcsReportedTaskService.searchById(id);
    }

    @PostMapping("/rcs/addRecord")
    public void addRecord(@RequestBody HvWmRcsReportedTaskRecord hvWmRcsReportedTaskRecord){
         rcsReportedTaskService.addRecord(hvWmRcsReportedTaskRecord);
    }

    @PostMapping("/rcs/updateRecord")
    public void updateRecord(@RequestBody HvWmRcsReportedTaskRecord hvWmRcsReportedTaskRecord){
        rcsReportedTaskService.updateRecord(hvWmRcsReportedTaskRecord);
    }

    @PostMapping("/rcs/delRecord")
    public void delRecord(@RequestBody HvWmRcsReportedTaskRecord hvWmRcsReportedTaskRecord){
        rcsReportedTaskService.delRecord(hvWmRcsReportedTaskRecord);
    }
}
