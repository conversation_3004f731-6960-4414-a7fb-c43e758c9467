package com.hvisions.wms.controller;

import com.hvisions.common.utils.EnumUtil;
import com.hvisions.wms.dto.purchase.PurchaseHeaderDTO;
import com.hvisions.wms.dto.purchase.PurchaseHeaderQueryDTO;
import com.hvisions.wms.dto.purchase.PurchaseLineDTO;
import com.hvisions.wms.enums.ProcurementEmum;
import com.hvisions.wms.service.PurchaseOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * <p>Title:PurchaseOrderController</p>
 * <p>Description:仓储采购控制器</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2020/8/24</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@RestController
@RequestMapping(value = "/purchaseOrder")
@Slf4j
@Api(description = "仓储采购控制器")
public class PurchaseOrderController {

    /**
     * 采购service
     */
    private PurchaseOrderService purchaseOrderService;

    @Autowired
    public PurchaseOrderController(PurchaseOrderService purchaseOrderService) {
        this.purchaseOrderService = purchaseOrderService;
    }

    /**
     * 新增采购单
     *
     * @param purchaseHeaderDTO 采购单dto
     * @return 新增质检单ID
     */
    @ApiOperation(value = "新增采购单")
    @PostMapping(value = "/createPurchase")
    public PurchaseHeaderDTO createPurchase(@RequestBody PurchaseHeaderDTO purchaseHeaderDTO) {
        return purchaseOrderService.savePurchaseOrder(purchaseHeaderDTO);
    }

    /**
     * 修改采购单
     *
     * @param purchaseHeaderDTO 采购单dto
     * @return id
     */
    @ApiOperation(value = "修改采购单")
    @PutMapping(value = "/updatePurchase")
    public PurchaseHeaderDTO updatePurchase(@RequestBody PurchaseHeaderDTO purchaseHeaderDTO) {
        return purchaseOrderService.savePurchaseOrder(purchaseHeaderDTO);
    }

    /**
     * 删除采购单
     *
     * @param purchaseHeaderId 采购单dto
     */
    @ApiOperation(value = "删除采购单")
    @DeleteMapping(value = "/deletePurchase/{purchaseHeaderId}")
    public void deletePurchase(@PathVariable Integer purchaseHeaderId) {
        purchaseOrderService.deletePurchase(purchaseHeaderId);
    }

    /**
     * 通过查询条件获取 采购单
     *
     * @param purchaseHeaderQueryDTO 采购单查询DTO
     * @return page
     */
    @ApiOperation(value = "通过查询条件获取 采购单")
    @PostMapping(value = "/getPurchase")
    public Page<PurchaseHeaderDTO> getPurchase(@RequestBody PurchaseHeaderQueryDTO purchaseHeaderQueryDTO) {
        return purchaseOrderService.getPurchase(purchaseHeaderQueryDTO);
    }

    /**
     * 通过采购单ID获取采购单
     *
     * @param purchaseHeaderId 采购单ID
     * @return 采购单
     */
    @ApiOperation(value = "通过ID获取 采购单")
    @GetMapping(value = "/getPurchaseById/{purchaseHeaderId}")
    public PurchaseHeaderDTO getPurchaseById(@PathVariable Integer purchaseHeaderId) {
        return purchaseOrderService.getPurchaseById(purchaseHeaderId);
    }

    /**
     * 添加采购单行表
     *
     * @param purchaseLineDTO 行表
     * @return 行表信息
     */
    @ApiOperation(value = "添加采购单行表")
    @PostMapping(value = "/createPurchaseLine")
    public PurchaseHeaderDTO createPurchaseLine(@RequestBody @Valid PurchaseLineDTO purchaseLineDTO) {
        return purchaseOrderService.createPurchaseLine(purchaseLineDTO);
    }

    /**
     * 删除采购单行表
     *
     * @param lineId 行表ID
     */
    @ApiOperation(value = "删除采购单行表")
    @DeleteMapping(value = "/deletePurchaseLine/{lineId}")
    public void deletePurchaseLine(@PathVariable Integer lineId) {
        purchaseOrderService.deletePurchaseLine(lineId);
    }

    /**
     * 通过采购单ID生成入库单
     *
     * @param purchaseHeaderId 采购单
     * @return 入库单ID
     */
    @ApiOperation(value = "创建入库单")
    @GetMapping(value = "/createReceiptByPurchaseId/{purchaseHeaderId}")
    public Integer createReceiptByPurchaseId(@PathVariable Integer purchaseHeaderId) {
        return purchaseOrderService.createReceiptByPurchaseId(purchaseHeaderId);
    }

    /**
     * 通过采购审批
     *
     * @return id
     */
    @ApiOperation(value = "通过采购审批")
    @GetMapping(value = "/purchaseApproval/{purchaseHeaderId}")
    public Integer purchaseApproval(@PathVariable Integer purchaseHeaderId) {
        return purchaseOrderService.purchaseApproval(purchaseHeaderId);
    }

    /**
     * 获取采购单类型
     *
     * @return 采购单状态键值对
     */
    @GetMapping("/getProcurementType")
    @ApiOperation(value = "获取采购单类型")
    public Map<Integer, String> getProcurementType() {
        return EnumUtil.enumToMap(ProcurementEmum.class);
    }

    /**
     * 下发工单
     * @param orderId 工单id
     */
    @ApiOperation(value = "下发")
    @PutMapping(value = "/deliverOrder/{orderId}")
    public void deliverOrder(@PathVariable Integer orderId){
        purchaseOrderService.deliverOder(orderId);
    }
}
