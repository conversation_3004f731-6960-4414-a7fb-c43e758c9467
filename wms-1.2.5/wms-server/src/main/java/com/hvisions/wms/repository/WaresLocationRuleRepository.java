package com.hvisions.wms.repository;

import com.hvisions.wms.dto.location.WaresLocationRuleDTO;
import com.hvisions.wms.entity.HvWmsWaresLocationRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: WaresLocationRuleRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/7/20</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface WaresLocationRuleRepository extends JpaRepository<HvWmsWaresLocationRule, Integer> {
    /**
     * 根据仓储位置信息查询仓储规则
     *
     * @param locationId 仓储位置信息
     * @return 仓储规则
     */
    List<HvWmsWaresLocationRule> findByLocationId(int locationId);

    /**
     * 根据物料Id查询仓储规则
     *
     * @param materialId 物料
     * @return 仓储规则
     */
    List<HvWmsWaresLocationRule> getRuleByMaterialId(int materialId);

    /**
     * 根据物料Id查询仓储规则
     *
     * @param materialIds 物料
     * @return 仓储规则
     */
    List<HvWmsWaresLocationRule> getRuleByMaterialIdIn(List<Integer> materialIds);
}