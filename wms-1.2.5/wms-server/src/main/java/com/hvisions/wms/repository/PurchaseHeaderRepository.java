package com.hvisions.wms.repository;

import com.hvisions.wms.entity.HvWmsPurchaseHeader;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * <p>Title:PurchaseHeaderRepository</p>
 * <p>Description:采购单头表jpa</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2020/9/8</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Repository
public interface PurchaseHeaderRepository extends JpaRepository<HvWmsPurchaseHeader, Integer> {

    @Query(value = "SELECT * FROM hv_bm_purchase_header pur WHERE 1=1 " +
            "and if(?1 is not null and ?2 isnot null,pur.delivery_date between ?1 and ?2, 1=1 )" +
            "and pur.receipt_number like %?3% " +
            "and if(?4 is not null ,pur.supplier_id = ?4 ,1=1)" +
            "and if(?5 is not null ,pur.department_id = ?5,1=1) " +
            "and if(?6 is not null ,applicant_id = ?6 ,1=1)" +
            "and if(?7 is not null ,pur.purchase_state = ?7,1=1) "+
            "order by ?#{#pageable}", nativeQuery = true)
    Page<HvWmsPurchaseHeader> findByQuery(Date startTime, Date endTime, String receiptNumber, Integer supplierId, Integer departmentId, Integer applicantId, Integer purchaseState, Pageable pageable);


    /**
     * 根据采购单号查询采购单
     * @param receiptNumber 采购单号
     * @return 采购单头信息
     */
    HvWmsPurchaseHeader findByReceiptNumber(String receiptNumber);
}
