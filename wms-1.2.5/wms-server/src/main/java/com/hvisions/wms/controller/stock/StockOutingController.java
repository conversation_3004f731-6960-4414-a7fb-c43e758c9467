package com.hvisions.wms.controller.stock;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.utils.EnumUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.dto.stockouting.*;
import com.hvisions.wms.enums.StockOutingEnum;
import com.hvisions.wms.service.StockOutingBatchService;
import com.hvisions.wms.service.StockOutingLineService;
import com.hvisions.wms.service.StockOutingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: StockOutingController</p >
 * <p>Description: 出库单头表操作</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/9/3</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/stockout")
@Api(description = "出库控制器")
public class StockOutingController {

    @Autowired
    private StockOutingService stockOutingService;

    @Autowired
    private StockOutingLineService stockOutingLineService;

    @Autowired
    private StockOutingBatchService stockOutingBatchService;

    @ApiOperation(value = "根据条件查询出库单")
    @PostMapping("/getPage")
    public Page<StockOutingHeaderDto> getPageByQuery(@RequestBody StockOutingQuery stockOutingQuery) {
        return stockOutingService.getPageByQuery(stockOutingQuery);
    }

    @ApiOperation(value = "根据出库单出库")
    @PutMapping("/complete/{owId}")
    public void complete(@PathVariable Integer owId) {
        stockOutingService.completeOuting(owId);
    }

    @ApiOperation(value = "根据出库单id删除出库单")
    @DeleteMapping("/delete/{owId}")
    public void deleteOuting(@PathVariable Integer owId) {
        stockOutingService.deleteOuting(owId);
    }

    @ApiOperation(value = "根据出库单查询出库详情")
    @GetMapping("/getByOwId/{owId}")
    public StockOutingHeaderDto getOuting(@PathVariable Integer owId) {
        return stockOutingService.getOutingById(owId);
    }

    @ApiOperation(value = "根据出库单id下载出库单")
    @GetMapping("/download/{owId}")
    public ResponseEntity<byte[]> download(@PathVariable Integer owId) throws IOException, IllegalAccessException {
        return stockOutingService.download(owId);
    }

    @ApiOperation(value = "根据出库单id下载出库单")
    @GetMapping(value = "/downloadDetail/{owId}")
    public ResultVO<ExcelExportDto> downloadDetail(@PathVariable Integer owId) throws IOException, IllegalAccessException {
        return stockOutingService.downloadDetail(owId);
    }

    @ApiOperation(value = "创建出库单")
    @GetMapping("/create")
    public StockOutingHeaderDto createOutingHeader() {
        return stockOutingService.createOutingHeader();
    }

    @ApiOperation(value = "保存出库单")
    @PostMapping("/save")
    public Integer saveOutingHeader(@RequestBody StockOutingHeaderDto stockOutingHeaderDto) {
        return stockOutingService.saveOutingHeader(stockOutingHeaderDto);
    }

    @ApiOperation(value = "出库")
    @PostMapping("/out")
    public Integer outStock(@Valid @RequestBody StockOutingForm stockOutingForm) {
        return stockOutingLineService.outStock(stockOutingForm);
    }

    @ApiOperation(value = "删除出库清单")
    @DeleteMapping("/deleteDetail/{outingLineId}")
    public void deleteOutLine(@PathVariable Integer outingLineId) {
        stockOutingLineService.deleteOutLine(outingLineId);
    }

    @ApiOperation(value = "删除批次详情")
    @DeleteMapping("/deleteBatch/{batchId}")
    public void deleteOutBatch(@PathVariable Integer batchId) {
        stockOutingBatchService.deleteOutBatch(batchId);
    }

    @ApiOperation(value = "出库状态")
    @GetMapping("/status")
    public Map<Integer, String> getStatus() {
        return EnumUtil.enumToMap(StockOutingEnum.class);
    }

    @ApiOperation(value = "创建出库清单")
    @PostMapping("/createLine")
    public Integer createLine(@Valid @RequestBody StockLineFormDto stockLineFormDto) {
        return stockOutingLineService.createLine(stockLineFormDto);
    }

    @ApiOperation(value = "根据出库单行表id查询出库清单")
    @GetMapping("/getBatchByLineId/{lineId}")
    public List<StockOutingBatchDto> getBatchByLineId(@PathVariable Integer lineId) {
        return stockOutingBatchService.getBatchByLineId(lineId);
    }
}
