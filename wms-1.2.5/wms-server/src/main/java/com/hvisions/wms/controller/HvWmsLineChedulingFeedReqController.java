package com.hvisions.wms.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.thirdparty.common.dto.*;
import com.hvisions.wms.dao.HvWmsLineChedulingFeedReqMapper;
import com.hvisions.wms.dao.HvWmsLineSchedulingMaterialMapper;
import com.hvisions.wms.dto.rcs.*;

import com.hvisions.wms.service.HvWmsLineSchedulingFeedReqService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Api(tags = "产线调度请求")
@RestController
@RequestMapping("/lineChedulingFeedReq")
public class HvWmsLineChedulingFeedReqController {
    @Autowired
    private HvWmsLineSchedulingFeedReqService hvWmsLineSchedulingFeedReqService;
    @Autowired
    HvWmsLineChedulingFeedReqMapper mapper;
    @Autowired
    private HvWmsLineSchedulingMaterialMapper materialMapper;

    @PostMapping("/addLineChedulingFeedReq")
    @ApiOperation(value = "添加产线调度请求")
    public void addLineChedulingFeedReq(@RequestBody HvWmsLineSchedulingFeedReq hvWmsLineSchedulingFeedReq) {
        hvWmsLineSchedulingFeedReqService.addFeedReqRecord(hvWmsLineSchedulingFeedReq);
    }

    @PostMapping("/updateLineChedulingFeedReq")
    @ApiOperation(value = "更新产线调度请求")
    public void updateLineChedulingFeedReq(@RequestBody HvWmsLineSchedulingFeedReq hvWmsLineSchedulingFeedReq) {
        hvWmsLineSchedulingFeedReqService.updateFeedReqRecord(hvWmsLineSchedulingFeedReq);
    }

    @PostMapping("/deleteLineChedulingFeedReq")
    @ApiOperation(value = "删除产线调度请求")
    public void deleteLineChedulingFeedReqByRequestCode(String requestCode) {
        hvWmsLineSchedulingFeedReqService.deleteByRequestCode(requestCode);
    }

    @PostMapping("/getLineChedulingFeedReqByRequestCode")
    @ApiOperation(value = "根据请求编号获取产线调度请求")
    public HvWmsLineSchedulingFeedReq getLineChedulingFeedReqByRequestCode(String requestCode) {
        HvWmsLineSchedulingFeedReq feedReqRecordByRequestCode = hvWmsLineSchedulingFeedReqService.getFeedReqRecordByRequestCode(requestCode);
        return feedReqRecordByRequestCode;
    }

    @PostMapping("/getPageLineSchedulingReceiveReq")
    @ApiOperation(value = "产线调度请求分页-新")
    public Page<LineSchedulingReceiveReqPageDTO> getPageLineSchedulingReceiveReq(@RequestBody LineSchedulingReceiveReqQueryDTO queryDTO){
        return hvWmsLineSchedulingFeedReqService.getLineSchedulingReceiveReqPage(queryDTO);
    }

    @PostMapping("/getPageLineSchedulingMaterialList")
    @ApiOperation(value = "产线调度请求物料列表分页-新")
    public Page<HvWmsLineSchedulingMatListDTO> getPageLineSchedulingMaterialList(@RequestBody LineSchedulingMatListQueryDTO queryDTO){
        return PageHelperUtil.getPage(materialMapper::getPage, queryDTO);
    }

    @PostMapping("/addLineSchedulingMaterialList")
    @ApiOperation(value = "产线调度请求物料列表新增-新")
    public ResultVO addLineSchedulingMaterialList(@RequestBody LineSchedulingMatListDTO matListDTO){
        return ResultVO.success(materialMapper.insert(DtoMapper.convert(matListDTO, HvWmsLineSchedulingMaterialList.class)));
    }

    @PostMapping("/addLineSchedulingReceiveReq")
    @ApiOperation(value = "添加产线调度请求-新")
    void addLineSchedulingReceiveReq(@RequestBody LineSchedulingReceiveReqDTO lineSchedulingReceiveReqDTO){
        hvWmsLineSchedulingFeedReqService.addLineSchedulingReceiveReq(lineSchedulingReceiveReqDTO);
    }

    @PostMapping("/updateLineSchedulingReceiveReq")
    @ApiOperation(value = "更新产线调度请求-新")
    void updateLineSchedulingReceiveReq(@RequestBody LineSchedulingReceiveReqDTO lineSchedulingReceiveReqDTO){
        hvWmsLineSchedulingFeedReqService.updateLineSchedulingReceiveReq(lineSchedulingReceiveReqDTO);
    }

    @PostMapping("/getLineSchedulingReceiveReqByRequestCode")
    @ApiOperation(value = "根据请求编号获取产线调度请求-新")
    LineSchedulingReceiveReqDTO getLineSchedulingReceiveReqByRequestCode(@RequestParam("RequestCode") String RequestCode){
       return hvWmsLineSchedulingFeedReqService.getLineSchedulingReceiveReqByRequestCode(RequestCode);
    }

    @PostMapping("/deleteLineSchedulingReceiveReqByRequestCode")
    @ApiOperation(value = "删除产线调度请求-新")
    void deleteLineSchedulingReceiveReqByRequestCode(@RequestParam String RequestCode){
        hvWmsLineSchedulingFeedReqService.deleteLineSchedulingReceiveReqByRequestCode(RequestCode);
    }
    @PostMapping("/generateSchedulingTasks")
    @ApiOperation(value = "根据线体请求生成调度任务")
    public ResultVO generateSchedulingTasks(@RequestBody HvWmsLineSchedulingReceiveReqDTO lineSchedulingReceiveReqDTO){
        ResultVO resultVO = hvWmsLineSchedulingFeedReqService.generateSchedulingTasks(lineSchedulingReceiveReqDTO);
        return resultVO;
    }

    /**
     * 导出
     *
     * @param queryDTO
     */
    @ApiOperation(value = "导出")
    @PostMapping(value = "/exportData")
    @ApiResultIgnore
    public ResultVO<ExcelExportDto> exportData(@RequestBody LineSchedulingReceiveReqQueryDTO queryDTO) {
        List<LineSchedulingReceiveReqPageDTO> listByCondition = hvWmsLineSchedulingFeedReqService.findListByCondition(queryDTO);
        return ResultVO.success(EasyExcelUtil.getExcel(listByCondition, LineSchedulingReceiveReqPageDTO.class, System.currentTimeMillis() + "产线调度数据.xlsx"));
    }

}
