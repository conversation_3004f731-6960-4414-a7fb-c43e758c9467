package com.hvisions.wms.repository;

import com.hvisions.wms.entity.HvWmsStocks;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: HistoryService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/24</p >
 *
 * <AUTHOR> liuwei
 * @version :1.0.0
 */
@Repository
public interface StocksRepository extends JpaRepository<HvWmsStocks, Integer> {
    /**
     * 根据储位id查找下面所有的库存信息
     *
     * @param locationId 储位id
     * @return 库存信息列表
     */
    List<HvWmsStocks> findAllByLocationId(Integer locationId);

    /**
     * 根据物料id和库房id查询库存
     *
     * @param materialId 物料id
     * @param locationId 库房id
     * @param batchNum 物料批次号
     * @return 库存信息
     */
    List<HvWmsStocks> findAllByMaterialIdAndLocationIdAndMaterialBatchNum(Integer materialId, Integer locationId,String batchNum);

    /**
     * 根据物料id和库房id查询库存
     *
     * @param materialId 物料id
     * @param locationId 库房id
     * @return 库存信息
     */
    HvWmsStocks findByMaterialIdAndLocationId(Integer materialId, Integer locationId);

    /**
     * 根据物料id和库房id查询库存
     *
     * @param materialId      物料id
     * @param locationId      库房id
     * @param locationPointId 库区id
     * @return 库存信息
     */
    HvWmsStocks findByMaterialIdAndLocationIdAndLocationPointId(Integer materialId, Integer locationId, Integer locationPointId);

    /**
     * 根据物料id和库房id查询库存
     *
     * @param materialId 物料id
     * @param locationId 库房id
     * @param frameCode
     * @return 库存信息
     */
    HvWmsStocks findByMaterialIdAndLocationIdAndFrameCodeAndBlockCodeAndShipNo(Integer materialId, Integer locationId, String frameCode, String blockCode, String shipNo);

    /**
     * 根据物料id和库房id查询库存
     *
     * @param materialId 物料id
     * @param locationId 库房id
     * @param frameCode
     * @return 库存信息
     */
    HvWmsStocks findByMaterialIdAndLocationIdAndFrameCode(Integer materialId, Integer locationId, String frameCode);

    void removeByFrameCode(String frameCode);

}
