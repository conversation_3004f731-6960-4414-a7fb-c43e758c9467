package com.hvisions.wms.repository;

import com.hvisions.wms.entity.HvWmsOrderLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: HistoryService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/24</p >
 *
 * <AUTHOR> liuwei
 * @version :1.0.0
 */
@Repository
public interface OutOrderLineRepository extends JpaRepository<HvWmsOrderLine,Integer> {

    /**
     * 根据出库单id查询详情
     * @param orderId 出库单id
     * @return 出库单详情
     */
    List<HvWmsOrderLine> findAllByOrderId(Integer orderId);

    /**
     * 根据出库单id和物料id查找数据
     * @param orderId 出库单id
     * @param materialId 物料id
     * @return 出库单详情
     */
    HvWmsOrderLine findByOrderIdAndAndMaterialId(Integer orderId,Integer materialId);
}
