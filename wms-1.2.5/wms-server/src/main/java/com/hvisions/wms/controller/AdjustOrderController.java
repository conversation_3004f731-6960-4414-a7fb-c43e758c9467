package com.hvisions.wms.controller;

import com.hvisions.wms.dto.adjust.AdjustOrderDTO;
import com.hvisions.wms.dto.adjust.AdjustOrderLineDTO;
import com.hvisions.wms.dto.adjust.AdjustOrderQuery;
import com.hvisions.wms.dto.adjust.SummaryDTO;
import com.hvisions.wms.service.AdjustOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: AdjustOrderController</p>
 * <p>Description: 调整单控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/9/8</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/adjustOrder")
@Api(description = "调整单控制器")
public class AdjustOrderController {
    @Autowired
    AdjustOrderService adjustOrderService;

    /**
     * 添加修改调整单
     *
     * @param adjustOrderDTO 调整单信息
     * @return 调整单id
     */
    @PostMapping("/create")
    @ApiOperation(value = "添加修改调整单")
    public AdjustOrderDTO create(@RequestBody AdjustOrderDTO adjustOrderDTO) {
        //只能修改“新建"状态下的调整单
        return adjustOrderService.createOrder(adjustOrderDTO);
    }


    /**
     * 删除调整单
     *
     * @param id 调整单id
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除调整单")
    public void delete(@PathVariable int id) {
        //只能删除新建状态下的调整单
        adjustOrderService.deleteOrderById(id);
    }

    /**
     * 查询调整单分页数据信息
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/getPage")
    @ApiOperation(value = "查询调整单分页信息")
    public Page<AdjustOrderDTO> getPage(@RequestBody AdjustOrderQuery query) {
        //注意使用mybatis连物料数据库查询
        return adjustOrderService.getOrderPage(query);
    }

    /**
     * 查询调整单明细信息
     *
     * @param id 调整单id
     * @return 明细信息
     */
    @GetMapping("/getLine/{id}")
    @ApiOperation(value = "查询调整单明细信息")
    public List<AdjustOrderLineDTO> getLine(@PathVariable int id) {
        //使用mybatis连物料数据库查询
        return adjustOrderService.getLine(id);
    }

    /**
     * 添加修改调整单明细信息
     *
     * @param lineDTO 明细信息
     * @return 明细id
     */
    @PostMapping("/createLine")
    @ApiOperation(value = "添加修改明细信息")
    public Integer createLine(@RequestBody @Valid AdjustOrderLineDTO lineDTO) {
        //只能再新建状态下的调整单才可以操作
        return adjustOrderService.createLine(lineDTO);
    }

    /**
     * 删除明细信息
     *
     * @param id 明细id
     */
    @DeleteMapping("/deleteLine/{id}")
    @ApiOperation(value = "删除明细信息")
    public void deleteLine(@PathVariable int id) {
        //只能再新建状态下的调整单才可以操作
        adjustOrderService.deleteLineById(id);
    }

    /**
     * 确认调整单明细信息，并提交调整单，调整库存
     *
     * @param id 调整单id
     */
    @PutMapping("/confirm/{id}")
    @ApiOperation(value = "确认调整单信息，并调整库存")
    public void confirm(@PathVariable int id) {
        //事务操作
        //只能操作“新建状态”
        //修改调整单状态为完成
        //增加的库存调用增加库存接口，（注意汇总后提交一次）
        //减少的库存调用减少的库存接口,（注意汇总后提交一次)
        adjustOrderService.confirm(id);
    }

    /**
     * 查询调整概览
     *
     * @param id 调整单id
     */
    @GetMapping("/summary/{id}")
    @ApiOperation(value = "查询调整概览")
    public List<SummaryDTO> summary(@PathVariable Integer id) {
        return adjustOrderService.summary(id);
    }

    @GetMapping("/getHeader/{id}")
    @ApiOperation(value = "根据id查询")
    public AdjustOrderDTO getHeaderById(@PathVariable Integer id){
        return adjustOrderService.getHeaderById(id);
    }
}









