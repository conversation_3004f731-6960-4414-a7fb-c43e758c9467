package com.hvisions.wms.repository;

import com.hvisions.wms.entity.quantity.HvWmsQuality;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: QualityRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/9/3</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface QualityRepository extends JpaRepository<HvWmsQuality, Integer> {


    /**
     * 根据收货单Id查询请验单
     *
     * @param receiptId 收货单ID
     * @return 请验单信息
     */
    List<HvWmsQuality> getAllByReceiptId(Integer receiptId);
}