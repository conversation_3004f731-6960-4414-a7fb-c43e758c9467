package com.hvisions.wms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * WMS集配任务实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TableName("hv_wms_kitting_task")
public class HvWmsKittingTask extends SysBase {


    /**
     * 集配任务号
     */
    @NotBlank(message = "集配任务号不能为空")
    private String kittingTaskNo;

    /**
     * 关联生产工单号
     */
    private String productWorkOrderCode;

    /**
     * 关联需求单号
     */
    private String requirementCode;

    /**
     * 需求时间
     */
    private LocalDateTime requirementTime;

    /**
     * 任务状态
     */
    @NotNull(message = "任务状态不能为空")
    private Integer taskStatus;

    /**
     * 目标料点
     */
    private String targetPointCode;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 状态
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

}