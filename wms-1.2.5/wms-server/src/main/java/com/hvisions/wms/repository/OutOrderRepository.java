package com.hvisions.wms.repository;

import com.hvisions.wms.entity.HvWmsOutOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: HistoryService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/24</p >
 *
 * <AUTHOR> liuwei
 * @version :1.0.0
 */
@Repository
public interface OutOrderRepository extends JpaRepository<HvWmsOutOrder,Integer> {

    HvWmsOutOrder findByCode(String code);

    @Modifying
    void deleteByCode(String code);
}
