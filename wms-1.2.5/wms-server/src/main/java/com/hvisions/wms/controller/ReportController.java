package com.hvisions.wms.controller;

import com.hvisions.wms.dto.report.ReportSummaryDTO;
import com.hvisions.wms.service.ReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>Title: ReportController</p>
 * <p>Description: 报表控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/9/8</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@Api(description = "报表控制器")
@RequestMapping("/report")
public class ReportController {
    @Autowired
    ReportService reportService;

    /**
     * 仓库总览
     *
     * @return 仓库总览数据
     */
    @GetMapping("/summary")
    @ApiOperation("仓库总览")
    public ReportSummaryDTO summary() {
        return reportService.summary();
    }
}









