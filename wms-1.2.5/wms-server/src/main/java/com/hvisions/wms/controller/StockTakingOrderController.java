package com.hvisions.wms.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.dto.stocktaking.StockTakingOrderDTO;
import com.hvisions.wms.dto.stocktaking.StockTakingOrderLineDTO;
import com.hvisions.wms.dto.stocktaking.StockTakingQuery;
import com.hvisions.wms.service.StockTakingOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Title: StockTakingOrderController</p>
 * <p>Description: 盘点控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/9/7</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/stockTaskingOrder")
@Api(description = "盘点单控制器")
public class StockTakingOrderController {
    private final StockTakingOrderService stockTakingOrderService;

    @Autowired
    public StockTakingOrderController(StockTakingOrderService stockTakingOrderService) {
        this.stockTakingOrderService = stockTakingOrderService;
    }

    /**
     * 创建盘点单
     *
     * @param orderDTO 盘点单信息
     * @return 盘点单id
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建盘点单")
    public Integer create(@RequestBody StockTakingOrderDTO orderDTO) {
        //创建完为新建状态，库位和物料id必须要存在一个,不能同时为空
        return stockTakingOrderService.create(orderDTO);
    }

    /**
     * 删除盘点单
     *
     * @param id 盘点单id
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除盘点单")
    public void delete(@PathVariable int id) {
        //只能删除“新建”状态的盘点单。
        stockTakingOrderService.delete(id);
    }

    /**
     * 查找盘点单分页数据
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/findPage")
    @ApiOperation(value = "查找盘点单分页数据")
    public Page<StockTakingOrderDTO> findPage(@RequestBody StockTakingQuery query) {
        return stockTakingOrderService.findPage(query);
    }


    /**
     * 查询明细
     *
     * @param id 盘点单id
     * @return 明细列表
     */
    @GetMapping("/findLine/{id}")
    @ApiOperation(value = "查询明细")
    public List<StockTakingOrderLineDTO> findLine(@PathVariable Integer id) {
        return stockTakingOrderService.findLine(id);
    }


    /**
     * 排列盘点明细表
     *
     * @param id 盘点单id
     */
    @PostMapping("/sortOrderLine")
    @ApiOperation(value = "排列盘点明细表")
    public void sortOrderLine(@RequestParam Integer id) {
        //将明细表根据库位进行排列
        //要求：
        // 1：相同库位的明细，应该是连续的
        // 2: 归属相同库位的库位，应该是连续的
        stockTakingOrderService.sortOrderLine(id);

    }


    /**
     * 开始盘点
     *
     * @param id 盘点单号
     */
    @PutMapping("/startTaking")
    @ApiOperation(value = "开始盘点")
    public void startTaking(@RequestParam Integer id, @UserInfo UserInfoDTO userInfoDTO) {
        //根据盘点明细冻结库存
        //修改盘点单状态(盘点中)
        //修改盘点明细状态 (盘点中)
        stockTakingOrderService.startTaking(id,userInfoDTO);
    }

    /**
     * 设置明细检查无误(快捷操作)
     *
     * @param lineId 明细id
     */
    @PutMapping("/checkLine")
    @ApiOperation(value = "设置明细检查无误")
    public void checkLine(@RequestParam Integer lineId) {
        //设置明细完成的快捷操作，预计数量和实际数量相等，并且状态改完（已盘点）
        stockTakingOrderService.checkLine(lineId);
    }

    /**
     * 设置明细检查
     *
     * @param lineId   明细id
     * @param quantity 实际数量
     */
    @PutMapping("/updateLine")
    @ApiOperation(value = "设置明细检查无误")
    public void updateLine(@RequestParam Integer lineId, @RequestParam BigDecimal quantity) {
        //设置明细的数量，并且状态更改成（已盘点）
        stockTakingOrderService.updateLine(lineId, quantity);
    }

    /**
     * 结束盘点
     *
     * @param id 盘点单id
     */
    @PostMapping("/finishTaking")
    @ApiOperation(value = "结束盘点")
    public void finishTaking(@RequestParam Integer id) {
        //解除占用
        //修改盘点单状态（完成）
        //盘点单明细必须全部为（已盘点）
        stockTakingOrderService.finishTaking(id);
    }


    /**
     * 导出盘点明细表
     *
     * @param id 盘点单Id
     * @return 盘点明细表
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportOrderLine/{id}")
    @ApiOperation(value = "导出盘点明细表")
    public ResponseEntity<byte[]> export(@PathVariable Integer id) throws IOException, IllegalAccessException {
        //作用是，用户可以拿着打印好的纸去盘点。
        return stockTakingOrderService.export(id);
    }


    /**
     * 导出盘点明细表
     *
     * @param id 盘点单Id
     * @return 盘点明细表
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportStockLine/{id}")
    @ApiOperation(value = "导出盘点明细表")
    public ResultVO<ExcelExportDto> exportStockLine(@PathVariable Integer id) throws IOException, IllegalAccessException {
        return stockTakingOrderService.exportStockLine(id);
    }

}









