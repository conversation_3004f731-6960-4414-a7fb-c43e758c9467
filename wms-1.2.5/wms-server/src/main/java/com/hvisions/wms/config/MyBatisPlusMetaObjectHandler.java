package com.hvisions.wms.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * MyBatis-Plus 自动填充处理器
 * 用于自动填充审计字段（创建时间、修改时间、创建人、修改人）
 */
@Slf4j
@Component
public class MyBatisPlusMetaObjectHandler implements MetaObjectHandler {

    /**
     * 插入时自动填充
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        log.info("开始插入填充...");
        
        // 自动填充创建时间
        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
        
        // 自动填充修改时间
        this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
        
        // 自动填充创建人ID（这里可以从当前登录用户获取，暂时设为null）
        // TODO: 集成用户上下文，获取当前登录用户ID
        this.strictInsertFill(metaObject, "creatorId", Integer.class, getCurrentUserId());
        
        // 自动填充修改人ID
        this.strictInsertFill(metaObject, "updaterId", Integer.class, getCurrentUserId());
    }

    /**
     * 更新时自动填充
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        log.info("开始更新填充...");
        
        // 自动填充修改时间
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
        
        // 自动填充修改人ID
        this.strictUpdateFill(metaObject, "updaterId", Integer.class, getCurrentUserId());
    }

    /**
     * 获取当前用户ID
     * TODO: 实现获取当前登录用户ID的逻辑
     * 
     * @return 用户ID
     */
    private Integer getCurrentUserId() {
        // 这里应该从Spring Security或其他认证框架中获取当前用户ID
        // 暂时返回null，后续可以根据实际的用户认证方式进行实现
        
        // 示例实现（需要根据实际情况调整）：
        // Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        // if (authentication != null && authentication.getPrincipal() instanceof UserDetails) {
        //     UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        //     return getUserIdFromUserDetails(userDetails);
        // }
        
        return null;
    }

}
