package com.hvisions.wms.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.dto.stock.*;
import com.hvisions.wms.entity.HvWmsStocks;
import com.hvisions.wms.service.StocksService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title: StockController</p >
 * <p>Description: 库存信息</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/24</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/stock")
@Api(description = "库存信息")
public class StockController {

    @Autowired
    StocksService stocksService;

    /**
     * 查询库存信息
     *
     * @param stockQueryDTO 库存信息查询条件
     * @return 库存信息
     */
    @PostMapping(value = "/getAllByQuery")
    @ApiOperation(value = "查询库存信息")
    public Page<StockMaterialDTO> getAllByQuery(@RequestBody StockQueryDTO stockQueryDTO) {
        return stocksService.getAllByQuery(stockQueryDTO);
    }

    /**
     * 查询库存信息
     *
     * @param frameCode 库存信息查询条件
     * @return 库存信息
     */
    @PostMapping(value = "/getStocksByFrame")
    @ApiOperation(value = "查询库存信息")
    public List<StockMaterialDTO> getStocksByFrame(@RequestParam String frameCode) {
        return stocksService.getStocksByFrame(frameCode);
    }


    /**
     * 查询库存信息
     *
     * @param stockQueryDTO 库存信息查询条件
     * @return 库存信息
     */
    @PostMapping(value = "/getStocks")
    @ApiOperation(value = "查询库存信息")
    public List<StockMaterialDTO> getStocks(@RequestBody StockQueryDTO stockQueryDTO) {
        return stocksService.getStocks(stockQueryDTO);
    }


    /**
     * 导出库存信息
     *
     * @return 库存信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/export")
    @ApiOperation(value = "导出库存信息 支持超链接")
    public ResponseEntity<byte[]> export() throws IOException, IllegalAccessException {
        return stocksService.export();
    }

    /**
     * 导出库房查询
     *
     * @return 导出库存信息 Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @PostMapping(value = "/exportLinker")
    @ApiOperation(value = "导出库房查询 ")
    public ResultVO<ExcelExportDto> exportLinker(@RequestBody StockQueryDTO stockQueryDTO) throws IOException, IllegalAccessException {
        return stocksService.exportLinker(stockQueryDTO);
    }


    /**
     * 导出库区库存信息(以库区为统计口径）
     *
     * @return 导出库区库存信息 Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @PostMapping(value = "/exportLocationStock")
    @ApiOperation(value = "导出库区库存信息 ")
    public ResultVO<ExcelExportDto> exportLocationStock(@RequestBody StockBriefQuery stockBriefQuery) throws IOException, IllegalAccessException {
        return stocksService.exportLocationStock(stockBriefQuery);
    }


    /**
     * 根据库存id查询物料占用信息
     *
     * @param stockId 库存id
     * @return 物料占用信息
     */
    @GetMapping(value = "/getOccupy/{stockId}")
    @ApiOperation(value = "查询物料占用信息")
    public List<StockOccupyDTO> getOccupy(@PathVariable Integer stockId) {
        return stocksService.getOccupy(stockId);
    }


    /**
     * 增加库存
     *
     * @param adjustStorageDTOS 库存增加信息列表
     */
    @PostMapping(value = "/addStorage")
    @ApiOperation(value = "批量增加库存")
    public void addStorageList(@RequestBody List<AdjustStorageDTO> adjustStorageDTOS) {
        stocksService.addStorageList(adjustStorageDTOS);
    }

    /**
     * 减少库存
     *
     * @param adjustStorageDTOS 库存减少信息
     */
    @PostMapping(value = "/removeStorage")
    @ApiOperation(value = "批量减少库存")
    public void removeStorage(@RequestBody @Valid List<AdjustStorageDTO> adjustStorageDTOS) {
        stocksService.removeStorage(adjustStorageDTOS);
    }

    /**
     * 占用库存
     *
     * @param adjustStorageDTOS 库存信息
     */
    @PostMapping(value = "/occupyStorage")
    @ApiOperation(value = "批量占用库存")
    public void occupyStorage(@RequestBody @Valid List<AdjustStorageDTO> adjustStorageDTOS) {
        stocksService.occupyStorageList(adjustStorageDTOS);
    }

    /**
     * 取消占用，外部业务占用后，取消操作使用
     *
     * @param operation 操作
     * @param orderCode 业务号
     */
    @PostMapping(value = "/occupyCancel")
    @ApiOperation(value = "取消占用")
    public void occupyCancel(@RequestParam String operation, String orderCode) {
        stocksService.occupyCancel(operation, orderCode);
    }

    /**
     * 核销占用,外部业务占用库存，走完业务后，进行库存扣减
     *
     * @param operation 操作
     * @param orderCode 业务号
     */
    @PostMapping(value = "/occupyWriteOff")
    @ApiOperation(value = "核销库存")
    public void occupyWriteOff(@RequestParam String operation, String orderCode) {
        stocksService.occupyWriteOff(operation, orderCode);
    }

    /**
     * 根据物料编码或者物料名称查询库存信息
     *
     * @param codeOrName 编码或者名称
     * @return 库存信息
     */
    @ApiOperation(value = "根据物料编码或者物料名称查询库存信息")
    @GetMapping(value = "/getStocksByCodeOrName/{codeOrName}")
    public List<StockMaterialMsgDTO> getStocksByCodeOrName(@PathVariable String codeOrName) {
        return stocksService.findStocksByCodeOrName(codeOrName);
    }


    /**
     * 获取库存概览(以物料为统计口径）
     *
     * @param query 查询条件
     * @return 分页信息
     */
    @ApiOperation(value = "库存概览")
    @PostMapping(value = "/materialStock")
    public Page<StockInfoDTO> materialStock(@RequestBody StockBriefQuery query) {
        return stocksService.materialStock(query);
    }

    /**
     * 获取库存概览(以库区为统计口径）
     *
     * @param query 查询条件
     * @return 分页信息
     */
    @ApiOperation(value = "库存概览")
    @PostMapping(value = "/locationStock")
    public Page<StockInfoDTO> locationStock(@RequestBody StockBriefQuery query) {
        return stocksService.locationStock(query);
    }


    /**
     * 物料库存更新（2024/4/30）
     */
    @ApiOperation(value = "物料库存新增")
    @PutMapping(value = "/updateMaterialStock")
    public void updateMaterialStock(@RequestParam String orderCode,@RequestBody List<StockMaterialDTO> stockMaterials) {
        stocksService.updateMaterialStock(orderCode,stockMaterials);
    }

    /**
     * 删除物料库存（2024/4/30）
     */
    @ApiOperation(value = "删除物料库存")
    @DeleteMapping(value = "/deleteStock")
    public void deleteStock(@RequestParam String orderCode,@RequestBody List<StockMaterialDTO> stockMaterials) {
        stocksService.deleteStock(orderCode,stockMaterials);
    }


    /**
     * 删除删除物料占用（2024/7/30）
     */
    @ApiOperation(value = "删除物料占用")
    @DeleteMapping(value = "/occupyRemove")
    public void occupyRemove(@RequestParam Integer occupyId) {
        stocksService.occupyRemove(occupyId);
    }


    /**
     * 批量新增占用库存
     *
     * @param stockOccupyDTOS 库存信息
     */
    @PostMapping(value = "/batchAddOccupyStorage")
    @ApiOperation(value = "批量新增占用库存")
    public void batchAddOccupyStorage(@RequestBody @Valid List<StockOccupyDTO> stockOccupyDTOS) {
        stocksService.batchAddOccupyStorage(stockOccupyDTOS);
    }

    /**
     * 批量修改占用库存
     *
     * @param stockOccupyDTOS 库存信息
     */
    @PostMapping(value = "/batchUpOccupyStorage")
    @ApiOperation(value = "批量修改占用库存")
    public void batchUpOccupyStorage(@RequestBody @Valid List<StockOccupyDTO> stockOccupyDTOS) {
        stocksService.batchUpOccupyStorage(stockOccupyDTOS);
    }

    /**
     * 查询库存中的所有料框
     */
    @GetMapping(value = "/getStockPallet")
    @ApiOperation(value = "查询库存中的所有料框")
    public List<String> getStockPallet() {
        return stocksService.getStockPallet();
    }

    /**
     * 根据库存id查询库存
     *
     * @param id 库存ID
     */
    @PostMapping(value = "/getStocksById")
    @ApiOperation(value = "根据库存id查询库存")
    public StockMaterialDTO  getStocksById(@RequestParam("id") Integer id){
        return stocksService.getStocksById(id);
    };


    /**
     *
     * @param materialCode
     * @param locationCode
     * @return
     */
    @PostMapping(value = "/getQuantityByMaterialCodeAndLocationCode")
    @ApiOperation(value = "根据库区编码和物料编码查询库存数量")
    public StockMaterialPreparationDTO  getQuantityByMaterialCodeAndLocationCode(@RequestParam("materialCode")  String materialCode, @RequestParam("locationCode") String locationCode){
        return stocksService.getQuantityByMaterialCodeAndLocationCode(materialCode,locationCode);
    }

    /**
     *
     * @param materialCode
     * @param locationCode
     * @return
     */
    @PostMapping(value = "/getUsedCountByMaterialCodeAndLocationCode")
    @ApiOperation(value = "根据库区编码和物料编码查询库存占用数量")
    public StockMaterialPreparationDTO getUsedCountByMaterialCodeAndLocationCode(@RequestParam("materialCode")  String materialCode, @RequestParam("locationCode") String locationCode){
        return stocksService.getUsedCountByMaterialCodeAndLocationCode(materialCode,locationCode);
    }

    /**
     * 新增库存占用
     * @param stockOccupyDTO
     * @return
     */
    @PostMapping(value = "/addOccupyStock")
    @ApiOperation(value = "新增库存占用")
    public void addOccupyStock(@RequestBody StockOccupyDTO stockOccupyDTO){
        stocksService.addOccupyStock(stockOccupyDTO);
    }

    /**
     * 修改库存占用
     * @param stockOccupyDTO
     * @return
     */
    @PostMapping(value = "/upOccupyStock")
    @ApiOperation(value = "修改库存占用")
    public void upOccupyStock(@RequestBody StockOccupyDTO stockOccupyDTO){
        stocksService.upOccupyStock(stockOccupyDTO);
    }

    /**
     * 查询库存中的所有库区
     */
    @GetMapping(value = "/getLocationList")
    @ApiOperation(value = "查询库存中的所有库区")
    public List<StockMaterialDTO> getLocationList() {
        return stocksService.getLocationList();
    }


    @GetMapping(value = "/getListByFrameCode")
    @ApiOperation(value = "根据料框编码获取库存数据")
    public List<StockMaterialDTO> getListByFrameCode(@RequestParam("frameCode") String frameCode) {
        return stocksService.getListByFrameCode(frameCode);
    }


    @GetMapping(value = "/getStockOccupyByOrderCodeAndMaterialCode/{orderCode}/{materialCode}")
    @ApiOperation(value = "根据料框编码获取库存数据")
    public  List<StockOccupyDTO> getStockOccupyByOrderCodeAndMaterialCode(@PathVariable("orderCode")  String orderCode, @PathVariable("materialCode") String materialCode){
        return stocksService.getStockOccupyByOrderCodeAndMaterialCode(orderCode,materialCode);
    }

    @ApiOperation(value = "根据工单号删除库存占用")
    @DeleteMapping(value = "/deleteStockOccupyByOrderCode")
    public void deleteStockOccupyByOrderCode(@RequestParam String orderCode){
         stocksService.deleteStockOccupyByOrderCode(orderCode);
    }

    @ApiOperation(value = "根据料框删除对应库存")
    @DeleteMapping(value = "/deleteStockByFrameCode")
    public void deleteStockByFrameCode(@RequestParam String palletCode) {
        stocksService.deleteStockByFrameCode(palletCode);
    }


    @ApiOperation(value = "物料分拣")
    @PostMapping(value = "/materialSorting")
    @Transactional
    public ResultVO materialSorting(@RequestBody MaterialSortingDTO materialSortingDTO) {
        List<MaterialSortingSelectDTO> stockMaterials = materialSortingDTO.getStockMaterials();
        for (MaterialSortingSelectDTO stockMaterialDTO :stockMaterials) {
            MaterialSortingShowDTO modify = stocksService.getStockMaterialByFrameCodeAndMaterialId(materialSortingDTO.getOriginFrameCode(), stockMaterialDTO.getMaterialId());
//            之前是否存在这么一个料框库存
            MaterialSortingShowDTO add = stocksService.getStockMaterialByFrameCodeAndMaterialId(materialSortingDTO.getTargetFrameCode(), stockMaterialDTO.getMaterialId());
//          自身数量=自身数量-选择数量
            modify.setQuantity(modify.getQuantity().subtract(stockMaterialDTO.getQuantity()));
            if (!stocksService.updateStocksByStocksId(modify)){
                return ResultVO.error(500,"分拣失败");
            }
//            目标料框中没有该物料的情况，需要添加库存信息
            if (add == null){
                modify.setFrameCode(materialSortingDTO.getTargetFrameCode());
                modify.setQuantity(stockMaterialDTO.getQuantity());
                if (!stocksService.saveStocks(modify)){
                    return ResultVO.error(500,"分拣失败");
                }
            } else {
//                目标料框中有该物料情况，需要更新库存信息
                add.setQuantity(add.getQuantity().add(stockMaterialDTO.getQuantity()));
                if (!stocksService.updateStocksByStocksId(add)){
                    return ResultVO.error(500,"分拣失败");
                }
            }
        }
        return ResultVO.success("分拣成功");
    }

}