package com.hvisions.wms.controller;

import com.hvisions.common.utils.EnumUtil;
import com.hvisions.wms.dto.receipt.BaseReceiptDTO;
import com.hvisions.wms.dto.receipt.ReceiptDTO;
import com.hvisions.wms.dto.receipt.ReceiptProductDTO;
import com.hvisions.wms.dto.receipt.ReceiptQueryDTO;
import com.hvisions.wms.enums.ReceiptStateEnum;
import com.hvisions.wms.enums.ReceiptTypeEnum;
import com.hvisions.wms.service.ReceiptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: HistoryService</p >
 * <p>Description:收货单控制器 </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/24</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/receipt")
@Api(description = "收货单控制器")
public class ReceiptController {
    @Autowired
    ReceiptService receiptService;

    /**
     * 创建收货单
     *
     * @param baseReceiptDTO 收货单信息
     */
    @PostMapping(value = "/createReceipt")
    @ApiOperation(value = "创建收货单")
    public Integer createReceipt(@RequestBody BaseReceiptDTO baseReceiptDTO) {
        return receiptService.createReceipt(baseReceiptDTO);
    }

    /**
     * 修改收货单
     *
     * @param baseReceiptDTO
     */
    @PutMapping(value = "/updateReceipt")
    @ApiOperation(value = "修改收货单")
    public void updateReceipt(@RequestBody BaseReceiptDTO baseReceiptDTO) {
        receiptService.updateReceipt(baseReceiptDTO);
    }

    /**
     * 查询收货单
     *
     * @param receiptQueryDTO
     * @return 收货单列表
     */
    @PostMapping(value = "/getReceiptByQuery")
    @ApiOperation(value = "查询收货单")
    public Page<ReceiptDTO> getReceiptByQuery(@RequestBody ReceiptQueryDTO receiptQueryDTO) {
        return receiptService.getReceiptByQuery(receiptQueryDTO);
    }

    /**
     * 删除收货单
     *
     * @param id 收货单ID
     */
    @DeleteMapping(value = "/deleteReceiptById/{id}")
    @ApiOperation(value = "根据Id删除收货单")
    public void deleteReceiptById(@PathVariable int id) {
        receiptService.deleteReceiptById(id);
    }


    /**
     * 获取收货单状态
     *
     * @return 收货单状态键值对
     */
    @GetMapping("/getReceiptState")
    @ApiOperation(value = "获取收货单状态")
    public Map<Integer, String> getReceiptState() {
        return EnumUtil.enumToMap(ReceiptStateEnum.class);
    }

    /**
     * 获取收货单类型
     *
     * @return 收货单状态键值对
     */
    @GetMapping("/getReceiptType")
    @ApiOperation(value = "获取收货单类型")
    public Map<Integer, String> getReceiptType() {
        return EnumUtil.enumToMap(ReceiptTypeEnum.class);
    }


    /**
     * 根据物料信息收货
     */
    @PostMapping(value = "/receiptByMaterial")
    @ApiOperation(value = "根据物料信息收货")
    public void receiptByMaterial(@RequestBody List<ReceiptProductDTO> productDTOS) {
        receiptService.receiptByMaterial(productDTOS);
    }



}
