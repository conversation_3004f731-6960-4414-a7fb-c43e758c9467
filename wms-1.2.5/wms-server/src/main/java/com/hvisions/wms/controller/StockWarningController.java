package com.hvisions.wms.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.dto.stockWarning.BaseStockWarning;
import com.hvisions.wms.service.StockWarningService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

/**
 * <p>Title: StockWarningController</p >
 * <p>Description: 储位库存报警</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date:  2020/9/7</p >
 *
 * <AUTHOR> liuwei
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/stockWarning")
@Api(description = "储位库存报警")
public class StockWarningController {
    @Autowired
    StockWarningService stockWarningService;

    /**
     * 获取库位库存警告详细信息
     *
     * @return BaseStockWarning
     */
    @GetMapping(value = "/getStockWarning")
    @ApiOperation(value = "获取库位库存警告详细信息")
    public List<BaseStockWarning> getStockWarning() {
        return stockWarningService.getStockWarning();
    }

    /**
     * 导出报警库存信息
     *
     * @return 报警库存信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/export")
    @ApiOperation(value = "导出报警库存信息 支持超链接")
    public ResponseEntity<byte[]> export() throws IOException, IllegalAccessException {
        return stockWarningService.export();
    }

    /**
     * 导出报警库存信息
     *
     * @return 报警库存信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportLinker")
    @ApiOperation(value = "导出报警库存信息")
    public ResultVO<ExcelExportDto> exportLinker() throws IOException, IllegalAccessException {
        return stockWarningService.exportLinker();
    }
}
