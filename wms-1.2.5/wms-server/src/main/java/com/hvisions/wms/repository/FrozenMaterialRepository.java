package com.hvisions.wms.repository;

import com.hvisions.wms.entity.HvWmsFrozenMaterial;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>Title: FrozenMaterialRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/9/2</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface FrozenMaterialRepository extends JpaRepository<HvWmsFrozenMaterial, Integer> {

    /**
     * 查询冻结的数据
     *
     * @param operation 业务类型
     * @param orderCode 业务号
     * @return 冻结的数据列表
     */
    List<HvWmsFrozenMaterial> findAllByOperationAndOrderCode(String operation, String orderCode);


    /**
     * 根据Id列表删除
     *
     * @param frozenIds 冻结Id列表
     */
    @Transactional(rollbackFor = Exception.class)
    void deleteAllByIdIn(List<Integer> frozenIds);

}