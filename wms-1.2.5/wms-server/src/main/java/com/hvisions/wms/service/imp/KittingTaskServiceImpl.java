package com.hvisions.wms.service.imp;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.dao.KittingTaskMapper;
import com.hvisions.wms.dto.kitting.KittingTaskDTO;
import com.hvisions.wms.dto.kitting.KittingTaskQueryDTO;
import com.hvisions.wms.entity.HvWmsKittingTask;
import com.hvisions.wms.service.KittingTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 集配任务服务实现类
 */
@Slf4j
@Service
public class KittingTaskServiceImpl extends ServiceImpl<KittingTaskMapper, HvWmsKittingTask> 
        implements KittingTaskService {

    @Autowired
    private KittingTaskMapper kittingTaskMapper;

    @Override
    public Page<KittingTaskDTO> queryKittingTasks(KittingTaskQueryDTO queryDTO) {
        return PageHelperUtil.getPage(kittingTaskMapper::queryKittingTasks, queryDTO);
    }

    @Override
    public KittingTaskDTO getKittingTaskById(Integer taskId) {
        return kittingTaskMapper.getKittingTaskById(taskId);
    }

    @Override
    @Transactional
    public ResultVO startKittingTask(Integer taskId, Integer operatorId) {
        HvWmsKittingTask task = this.getById(taskId);
        if (task == null) {
            return ResultVO.error("集配任务不存在");
        }

        if (task.getStatus() != 0) {
            return ResultVO.error("任务状态不允许开始执行");
        }

        // 更新任务状态为进行中
        task.setStatus(1);
        task.setUpdateTime(LocalDateTime.now());
        this.updateById(task);

        return ResultVO.success("集配任务开始执行");
    }

    @Override
    @Transactional
    public ResultVO completeKittingTask(Integer taskId, Integer operatorId) {
        HvWmsKittingTask task = this.getById(taskId);
        if (task == null) {
            return ResultVO.error("集配任务不存在");
        }

        if (task.getStatus() != 1) {
            return ResultVO.error("任务状态不允许完成");
        }

        // 验证所有明细是否都已集配完成
        boolean allCompleted = kittingTaskMapper.checkAllDetailsCompleted(taskId);
        if (!allCompleted) {
            return ResultVO.error("还有物料未完成集配");
        }

        // 更新任务状态为已完成
        task.setStatus(2);
        task.setUpdateTime(LocalDateTime.now());
        this.updateById(task);

        // TODO: 向MES系统反馈完成信息

        return ResultVO.success("集配任务完成");
    }

    @Override
    @Transactional
    public ResultVO generateKittingTasksFromRequirement(String requirementCode) {
        // TODO: 实现根据物料需求单生成集配任务的逻辑
        // 1. 获取物料需求单信息
        // 2. 查询物料存放区域
        // 3. 按集配区域生成集配任务
        // 4. 根据分段、流向、组立规则优化分配

        return ResultVO.success("集配任务生成成功");
    }

}
