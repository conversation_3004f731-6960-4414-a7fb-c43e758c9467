package com.hvisions.wms.service.imp;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.dao.KittingTaskDetailMapper;
import com.hvisions.wms.dao.KittingTaskMapper;
import com.hvisions.wms.dto.kitting.KittingCompleteFeedbackDTO;
import com.hvisions.wms.dto.kitting.KittingTaskDTO;
import com.hvisions.wms.dto.kitting.KittingTaskQueryDTO;
import com.hvisions.wms.entity.HvWmsAuditableTask;
import com.hvisions.wms.entity.HvWmsAuditableTaskDetail;
import com.hvisions.wms.service.KittingTaskService;
import com.hvisions.wms.service.MESFeedbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 集配任务服务实现类
 */
@Slf4j
@Service
public class KittingTaskServiceImpl extends ServiceImpl<KittingTaskMapper, HvWmsAuditableTask>
        implements KittingTaskService {

    @Autowired
    private KittingTaskMapper kittingTaskMapper;

    @Autowired
    private KittingTaskDetailMapper kittingTaskDetailMapper;

    @Autowired
    private MESFeedbackService mesFeedbackService;

    @Override
    public Page<KittingTaskDTO> queryKittingTasks(KittingTaskQueryDTO queryDTO) {
        // 使用MyBatis-Plus的分页功能
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<KittingTaskDTO> page =
            new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(queryDTO.getPage() + 1, queryDTO.getPageSize());

        // 设置排序
        if (queryDTO.isSort() && queryDTO.getSortCol() != null) {
            if (queryDTO.isDirection()) {
                page.addOrder(com.baomidou.mybatisplus.core.conditions.query.QueryWrapper.orderByAsc(queryDTO.getSortCol()));
            } else {
                page.addOrder(com.baomidou.mybatisplus.core.conditions.query.QueryWrapper.orderByDesc(queryDTO.getSortCol()));
            }
        }

        // 执行分页查询
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<KittingTaskDTO> result =
            kittingTaskMapper.queryKittingTasksPage(page, queryDTO);

        // 转换为Spring Data的Page对象
        return convertToSpringDataPage(result);
    }

    @Override
    public KittingTaskDTO getKittingTaskById(Integer taskId) {
        KittingTaskDTO taskDTO = kittingTaskMapper.getKittingTaskById(taskId);
        if (taskDTO != null) {
            // 获取任务明细
            taskDTO.setDetails(kittingTaskDetailMapper.getDetailsByTaskId(taskId));
        }
        return taskDTO;
    }

    @Override
    @Transactional
    public ResultVO startKittingTask(Integer taskId, Integer operatorId) {
        HvWmsAuditableTask task = this.getById(taskId);
        if (task == null) {
            return ResultVO.error("集配任务不存在");
        }

        if (task.getStatus() != 0) {
            return ResultVO.error("任务状态不允许开始执行");
        }

        // 更新任务状态为进行中
        task.setStatus(1);
        // updateTime 会由 MyBatis-Plus 自动填充
        this.updateById(task);

        return ResultVO.success("集配任务开始执行");
    }

    @Override
    @Transactional
    public ResultVO completeKittingTask(Integer taskId, Integer operatorId) {
        HvWmsAuditableTask task = this.getById(taskId);
        if (task == null) {
            return ResultVO.error("集配任务不存在");
        }

        if (task.getStatus() != 1) {
            return ResultVO.error("任务状态不允许完成");
        }

        // 验证所有明细是否都已集配完成
        boolean allCompleted = kittingTaskMapper.checkAllDetailsCompleted(taskId);
        if (!allCompleted) {
            return ResultVO.error("还有物料未完成集配");
        }

        // 更新任务状态为已完成
        task.setStatus(2);
        // updateTime 会由 MyBatis-Plus 自动填充
        this.updateById(task);

        // 向MES系统反馈完成信息
        try {
            KittingCompleteFeedbackDTO feedbackDTO = new KittingCompleteFeedbackDTO();
            feedbackDTO.setKittingTaskNo(task.getKittingTaskNo());
            feedbackDTO.setProductWorkOrderCode(task.getProductWorkOrderCode());
            feedbackDTO.setRequirementCode(task.getRequirementCode());
            feedbackDTO.setCompleteTime(LocalDateTime.now());
            feedbackDTO.setStatus("COMPLETED");
            feedbackDTO.setOperatorId(operatorId);

            mesFeedbackService.feedbackKittingComplete(feedbackDTO);
        } catch (Exception e) {
            log.error("向MES系统反馈集配完成信息失败", e);
            // 不影响主流程，只记录日志
        }

        return ResultVO.success("集配任务完成");
    }

    @Override
    @Transactional
    public ResultVO generateKittingTasksFromRequirement(String requirementCode) {
        // TODO: 实现根据物料需求单生成集配任务的逻辑
        // 1. 获取物料需求单信息
        // 2. 查询物料存放区域
        // 3. 按集配区域生成集配任务
        // 4. 根据分段、流向、组立规则优化分配

        return ResultVO.success("集配任务生成成功");
    }

    @Override
    @Transactional
    public ResultVO collectMaterial(Integer detailId, Integer operatorId) {
        HvWmsAuditableTaskDetail detail = kittingTaskDetailMapper.selectById(detailId);
        if (detail == null) {
            return ResultVO.error("集配明细不存在");
        }

        if (detail.getKittingStatus() == 1) {
            return ResultVO.error("该物料已完成集配");
        }

        // 更新集配状态
        detail.setKittingStatus(1);
        detail.setKittingQuantity(detail.getQuantity());
        // updateTime 会由 MyBatis-Plus 自动填充
        kittingTaskDetailMapper.updateById(detail);

        return ResultVO.success("物料集配完成");
    }

    /**
     * 转换MyBatis-Plus的Page为Spring Data的Page
     */
    private Page<KittingTaskDTO> convertToSpringDataPage(com.baomidou.mybatisplus.extension.plugins.pagination.Page<KittingTaskDTO> mybatisPlusPage) {
        return new org.springframework.data.domain.PageImpl<>(
            mybatisPlusPage.getRecords(),
            org.springframework.data.domain.PageRequest.of(
                (int) mybatisPlusPage.getCurrent() - 1,
                (int) mybatisPlusPage.getSize()
            ),
            mybatisPlusPage.getTotal()
        );
    }

}
