package com.hvisions.wms.repository;

import com.hvisions.wms.entity.HvWmsTransferBoxDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: TransferBoxRepository</p>
 * <p>Description: 中转箱仓储层</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/8/26</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface TransferBoxDetailRepository extends JpaRepository<HvWmsTransferBoxDetail, Integer> {
    /**
     * 根据中转箱id查询详情
     * @param boxId 中转箱详情
     * @return 详情列表
     */
    List<HvWmsTransferBoxDetail> findAllByBoxId(Integer boxId);
}









