package com.hvisions.wms.controller;

import com.hvisions.wms.dto.quality.MaterialControlDTO;
import com.hvisions.wms.dto.quality.QualityControlDTO;
import com.hvisions.wms.dto.quality.QualityDTO;
import com.hvisions.wms.dto.quality.QualityQueryDTO;
import com.hvisions.wms.service.QualityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.models.auth.In;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: QualityController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/9/3</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "quality")
@Api(description = "收货单货物质检")
public class QualityController {


    @Autowired
    QualityService qualityService;


    /**
     * 创建请验单
     *
     * @param receiptId 收货单Id
     */
    @PostMapping(value = "/createQuality")
    @ApiOperation(value = "创建请验单")
    public void createQuality(@RequestParam Integer receiptId, @RequestBody List<Integer> receiptMaterialIds) {
        qualityService.createQuality(receiptId, receiptMaterialIds);
    }

    /**
     * 质检
     */
    @PutMapping(value = "/qualityControl")
    @ApiOperation(value = "质检")
    public void qualityControl(@RequestBody List<QualityControlDTO> qualityControlDTOS) {
        qualityService.qualityControl(qualityControlDTOS);
    }

    /**
     * 物料质检项修改
     *
     * @param materialControlDTO 物料质检项信息
     */
    @PutMapping(value = "/qualityMaterialControl")
    @ApiOperation(value = "物料质检项修改")
    public void qualityMaterialControl(@RequestBody MaterialControlDTO materialControlDTO) {
        qualityService.qualityMaterialControl(materialControlDTO);
    }

    /**
     * 质检完成
     *
     * @param qualityId 请验单Id
     */
    @PutMapping(value = "/finishQuality/{qualityId}/{inspector}")
    @ApiOperation(value = "提交质检结果(质检完成)")
    public void finishQuality(@PathVariable Integer qualityId, @PathVariable Integer inspector) {
        qualityService.finishQuality(qualityId, inspector);
    }


    /**
     * 根据请验单查询项目
     *
     * @param qualityId 请验单ID
     * @return 检验货物信息
     */
    @GetMapping(value = "/getAllByQualityId/{qualityId}")
    @ApiOperation(value = "根据请验单查询项目")
    public List<QualityControlDTO> getAllByQualityId(@PathVariable Integer qualityId) {
        return qualityService.getAllByQualityId(qualityId);
    }

    /**
     * 查询请验单
     *
     * @param qualityQueryDTO 查询条件
     * @return 请验单
     */
    @PostMapping(value = "/getAllByQuery")
    @ApiOperation(value = "查询请验单")
    public Page<QualityDTO> getAllByQuery(@RequestBody QualityQueryDTO qualityQueryDTO) {
        return qualityService.getAllByQuery(qualityQueryDTO);
    }

}