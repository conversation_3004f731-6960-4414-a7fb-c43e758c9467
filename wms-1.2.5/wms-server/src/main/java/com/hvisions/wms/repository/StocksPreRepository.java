package com.hvisions.wms.repository;

import com.hvisions.wms.entity.HvWmsStocksPre;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: StocksPreRepository</p>
 * <p>Description: 预入库仓储层</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/3/2</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface StocksPreRepository extends JpaRepository<HvWmsStocksPre,Integer> {
    /**
     * 查询相关的预入库数据
     * @param operation 操作类型
     * @param orderCode 业务编号
     * @return 预入库数据
     */
    List<HvWmsStocksPre> findAllByOperationAndOrderCode(String operation,String orderCode);
}

    
    
    
    