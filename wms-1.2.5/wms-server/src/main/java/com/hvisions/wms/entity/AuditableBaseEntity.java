package com.hvisions.wms.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 公共实体基类，包含审计（创建/修改时间、人）和租户字段
 * 使用LocalDateTime类型的时间字段
 * 技术选型：MyBatis-Plus
 */
@Data
public class AuditableBaseEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    protected Integer id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    protected LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    protected LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    protected Integer creatorId;

    /**
     * 修改人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    protected Integer updaterId;

    /**
     * 用于后续SaaS服务租户字段
     * 通常用于区分不同租户的数据，实现数据隔离
     */
    protected String siteNum;
}