package com.hvisions.wms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 公共实体基类，包含审计（创建/修改时间、人）和租户字段
 * 使用LocalDateTime类型的时间字段
 */
@Data // Lombok 注解，自动生成 getter、setter、equals、hashCode 和 toString 方法
public class AuditableBaseEntity { // 将类名从 KittingBaseEntity 修改为 AuditableBaseEntity

    /**
     * 主键
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY) // JPA 注解，指定主键的生成策略为数据库自增
    @TableId(type = IdType.AUTO) // 注意：这个注解通常是 MyBatis-Plus 框架的，与 JPA 的 @GeneratedValue 冲突。
    protected Integer id;

    /**
     * 创建时间
     */
    @Column(updatable = false) // JPA 注解，表示该字段在更新操作时不应被修改
    @CreatedDate // Spring Data JPA 审计注解，在实体被创建时自动填充当前时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // Spring 格式化注解，定义日期时间字符串的解析和格式化模式
    protected LocalDateTime createTime;

    /**
     * 修改时间
     */
    @LastModifiedDate // Spring Data JPA 审计注解，在实体被创建或更新时自动填充当前时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") // Spring 格式化注解，定义日期时间字符串的解析和格式化模式
    protected LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    @Column(updatable = false) // JPA 注解，表示该字段在更新操作时不应被修改
    @CreatedBy // Spring Data JPA 审计注解，在实体被创建时自动填充当前操作用户的 ID
    protected Integer creatorId;

    /**
     * 修改人ID
     */
    @LastModifiedBy // Spring Data JPA 审计注解，在实体被创建或更新时自动填充当前操作用户的 ID
    protected Integer updaterId;

    /**
     * 用于后续SaaS服务租户字段
     * 通常用于区分不同租户的数据，实现数据隔离
     */
    protected String siteNum; // 站点编号或租户标识
}