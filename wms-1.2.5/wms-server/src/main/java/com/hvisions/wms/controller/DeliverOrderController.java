package com.hvisions.wms.controller;

import com.hvisions.wms.dto.deliver.DeliverOrderDTO;
import com.hvisions.wms.dto.deliver.DeliverOrderLineDTO;
import com.hvisions.wms.dto.deliver.DeliverOrderQuery;
import com.hvisions.wms.dto.deliver.MergeDeliverOrderDTO;
import com.hvisions.wms.service.DeliverOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: DeliverOrderController</p>
 * <p>Description: 移库单控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/9/8</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/deliverOrder")
@Api(description = "移库单控制器")
public class DeliverOrderController {

    @Autowired
    DeliverOrderService deliverOrderService;

    /**
     * 创建修改移库单(普通），只能选择“普通移库，或者其他类型”，具体的明细要手动录入
     *
     * @param deliverOrderDTO 移库单信息
     * @return 移库单id
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建移库单")
    public Integer create(@RequestBody DeliverOrderDTO deliverOrderDTO) {
        //只能创建修改普通或者其他类型
        //状态为新建

        return deliverOrderService.createOrder(deliverOrderDTO);
    }

    /**
     * 查询移库单单分页数据信息
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/getPage")
    @ApiOperation(value = "查询移库单单分页数据信息")
    public Page<DeliverOrderDTO> getPage(@RequestBody DeliverOrderQuery query) {
        return deliverOrderService.getOrderPage(query);
    }

    /**
     * 创建库存合并移库单
     *
     * @param mergeDeliverOrderDTO 库存合并移库单信息
     * @return 移库单id
     */
    @PostMapping("/createCombineOrder")
    @ApiOperation(value = "创建库存合并移库单")
    public Integer createCombineOrder(@RequestBody MergeDeliverOrderDTO mergeDeliverOrderDTO) {
        //只能创建业务类型为“库存合并”类型的移库单
        //查询库存情况并且自动生成明细
        //明细不能进行修改
        //状态为新建
        //这种类型的移库单只能全部删除重新建立，不允许进行修改操作
        return deliverOrderService.createCombineOrder(mergeDeliverOrderDTO);
    }

    /**
     * 删除移库单
     *
     * @param id 移库单id
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除移库单")
    public void delete(@PathVariable int id) {
        //只能删除“新建状态”
        deliverOrderService.deleteOrder(id);
    }

    /**
     * 获取移库单明细
     *
     * @param id 移库单id
     * @return 移库单明细列表
     */
    @GetMapping("/getLine/{id}")
    @ApiOperation(value = "获取移库单明细")
    public List<DeliverOrderLineDTO> getLine(@PathVariable int id) {
        return deliverOrderService.getLine(id);
    }

    /**
     * 创建或者修改明细信息
     *
     * @param dto 明细信息
     * @return 明细id
     */
    @PostMapping("/createLine")
    @ApiOperation(value = "创建或者修改明细信息")
    public Integer createLine(@RequestBody DeliverOrderLineDTO dto) {
        //只能修改新建状态
        //不能修改库存合并类型的明细
        return deliverOrderService.createLine(dto);
    }

    /**
     * 删除明细信息
     *
     * @param id 明细id
     */
    @DeleteMapping("/deleteLine/{id}")
    @ApiOperation(value = "删除明细")
    public void deleteLine(@PathVariable int id) {
        deliverOrderService.deleteLine(id);
    }

    /**
     * 锁定库存，开始移库操作
     *
     * @param id 移库单id
     */
    @PutMapping("/lockStock/{id}")
    @ApiOperation(value = "锁定库存，开始移库操作")
    public void lockStock(@PathVariable int id) {
        //事务操作
        //修改移库单状态为移库中
        //调用锁定库存接口
        //明细状态改为“移库中”
        deliverOrderService.lockStock(id);
    }

    /**
     * 标记明细已经完成移库操作
     *
     * @param id 明细id
     */
    @PutMapping("/finishLine/{id}")
    @ApiOperation(value = "标记明细已经完成移库操作")
    public void finishLine(@PathVariable int id) {
        //只能再“移库中”状态下操作
        //作为工作中记录哪些工作已经完成
        //标记明细状态为“完成”
        deliverOrderService.finishLine(id);
    }

    /**
     * 完成移库单,提交数据，更新库存信息
     *
     * @param id 移库单id
     */
    @PutMapping("/finishOrder/{id}")
    @ApiOperation(value = "完成移库单操作")
    public void finishOrder(@PathVariable int id) {
        //事务操作
        //更改所有明细状态为“完成”
        //更改移库单状态为“完成”
        //调用占用核销接口，完成库存改动
        deliverOrderService.finishOrder(id);
    }

}









