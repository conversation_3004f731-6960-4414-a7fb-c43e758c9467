package com.hvisions.wms.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.wms.dto.kitting.KittingTaskDTO;
import com.hvisions.wms.dto.kitting.KittingTaskQueryDTO;
import com.hvisions.wms.entity.HvWmsKittingTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 集配任务Mapper接口
 */
@Mapper
public interface KittingTaskMapper extends BaseMapper<HvWmsKittingTask> {

    /**
     * 查询集配任务列表
     *
     * @param queryDTO 查询条件
     * @return 集配任务列表
     */
    List<KittingTaskDTO> queryKittingTasks(@Param("queryDTO") KittingTaskQueryDTO queryDTO);

    /**
     * 根据ID获取集配任务详情
     *
     * @param taskId 任务ID
     * @return 集配任务详情
     */
    KittingTaskDTO getKittingTaskById(@Param("taskId") Integer taskId);

    /**
     * 检查任务的所有明细是否都已完成集配
     *
     * @param taskId 任务ID
     * @return 是否全部完成
     */
    boolean checkAllDetailsCompleted(@Param("taskId") Integer taskId);

}
