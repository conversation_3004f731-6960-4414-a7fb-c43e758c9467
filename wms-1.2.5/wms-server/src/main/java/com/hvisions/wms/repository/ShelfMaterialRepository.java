package com.hvisions.wms.repository;

import com.hvisions.wms.entity.receipt.HvWmsShelfMaterial;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>Title: ShelfMaterialRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/29</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface ShelfMaterialRepository extends JpaRepository<HvWmsShelfMaterial, Integer> {

    /**
     * 根据物料批次号查询上架信息
     *
     * @param materialBatchNum 物料批次号
     * @return 上架物料信息
     */
    List<HvWmsShelfMaterial> getAllByOldMaterialBatchNumAndReceiptId(String materialBatchNum, Integer receiptId);


    /**
     * 根据收货单ID查询上架物料信息
     *
     * @param receiptId 收货单Id
     * @return 上架物料信息
     */
    List<HvWmsShelfMaterial> getAllByReceiptId(Integer receiptId);


    /**
     * 根据上架物料信息ID列表删除
     *
     * @param shelfIdList 上架物料信息ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    void deleteAllByIdIn(List<Integer> shelfIdList);
}