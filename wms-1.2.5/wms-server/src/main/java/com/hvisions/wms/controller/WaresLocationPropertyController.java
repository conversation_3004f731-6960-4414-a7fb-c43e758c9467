package com.hvisions.wms.controller;

import com.hvisions.wms.dto.location.AddWaresLocationPropertyDTO;
import com.hvisions.wms.dto.location.WaresLocationPropertyDTO;
import com.hvisions.wms.service.WaresLocationPropertyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: WaresLocationPropertyContonller</p >
 * <p>Description:仓储属性 </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/7/21</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/wares_property")
@Api(description = "仓储属性")
public class WaresLocationPropertyController {

    @Autowired
    WaresLocationPropertyService service;

    /**
     * 修改仓储属性
     *
     * @param propertyDTOs 属性
     */
    @PutMapping(value = "/update")
    @ApiOperation(value = "修改仓储属性")
    public void update(@RequestBody List<WaresLocationPropertyDTO> propertyDTOs) {
        service.update(propertyDTOs);
    }

    /**
     * 获取仓储属性
     *
     * @param id 仓储id
     * @return 仓储属性列表
     */
    @GetMapping(value = "/findByLocationId/{id}")
    @ApiOperation(value = "获取仓储属性")
    public List<WaresLocationPropertyDTO> findByLocationId(@PathVariable Integer id) {
        return service.findByLocationId(id);
    }


    /**
     * 添加仓储属性
     *
     * @param propertyDTO 仓储属性
     */
    @ApiOperation(value = "添加仓储属性")
    @PostMapping(value = "/addPropertyToLocations")
    public void addPropertyToLocations(@RequestBody AddWaresLocationPropertyDTO propertyDTO) {
        service.addPropertyToLocations(propertyDTO);
    }

    /**
     * 根据Id删除仓储属性
     *
     * @param id 仓储id
     */
    @DeleteMapping(value = "/deletePropertyById/{id}")
    @ApiOperation(value = "根据Id删除仓储属性")
    public void deletePropertyById(@PathVariable int id) {
        service.deletePropertyById(id);
    }
}