package com.hvisions.wms.controller;

import com.hvisions.common.utils.EnumUtil;
import com.hvisions.wms.dto.receipt.ReceiptMaterialDTO;
import com.hvisions.wms.dto.receipt.ScanningReceiptDTO;
import com.hvisions.wms.enums.ReceiptMaterialTypeEnum;
import com.hvisions.wms.service.ReceiptMaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: HistoryService</p >
 * <p>Description:原材料收货单控制器 </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/24</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/receiptMaterial")
@Api(description = "收货单货物信息控制器")
public class ReceiptMaterialController {
    @Autowired
    ReceiptMaterialService receiptMaterialService;

    /**
     * 创建原材料收货单
     *
     * @param receiptMaterialDTO 收货单信息
     */
    @PostMapping(value = "/createReceiptMaterial")
    @ApiOperation(value = "创建原材料收货单")
    public Integer createReceiptMaterial(@RequestBody ReceiptMaterialDTO receiptMaterialDTO) {
        return receiptMaterialService.createReceiptMaterial(receiptMaterialDTO);
    }


    /**
     * 扫码添加收货信息
     *
     * @param scanningReceiptDTO 原材料收货信息
     * @return Id
     */
    @PostMapping(value = "/scanningReceiptMaterial")
    @ApiOperation(value = "扫码添加收货信息")
    public Integer scanningReceiptMaterial(@RequestBody ScanningReceiptDTO scanningReceiptDTO) {
        return receiptMaterialService.scanningReceiptMaterial(scanningReceiptDTO);
    }

    /**
     * 新增原材料收货信息
     *
     * @param receiptMaterialDTOS 收货单物料信息列表
     */
    @PostMapping(value = "/createReceiptMaterialList")
    @ApiOperation(value = "批量创建原材料收货单")
    public void createReceiptMaterialList(@RequestBody List<ReceiptMaterialDTO> receiptMaterialDTOS) {
        receiptMaterialService.createReceiptMaterialList(receiptMaterialDTOS);
    }

    /**
     * 修改原材料收货单
     *
     * @param receiptMaterialDTO 收货单信息
     */
    @PutMapping(value = "/updateReceiptMaterial")
    @ApiOperation(value = "修改原材料收货单")
    public void updateReceiptMaterial(@RequestBody ReceiptMaterialDTO receiptMaterialDTO) {
        receiptMaterialService.updateReceiptMaterial(receiptMaterialDTO);
    }


    /**
     * 根据主键删除原材料收货单
     *
     * @param id 主键
     */
    @DeleteMapping(value = "deleteById/{id}")
    @ApiOperation(value = "根据主键删除原材料收货单")
    public void deleteById(@PathVariable Integer id) {
        receiptMaterialService.deleteById(id);
    }

    /**
     * 根据主键查询原材料收货单
     *
     * @param id 主键
     * @return 收货单物料
     */
    @GetMapping(value = "getAllByReceiptId/{id}")
    @ApiOperation(value = "根据收货单Id查询原材料收货单")
    public List<ReceiptMaterialDTO> getAllByReceiptId(@PathVariable Integer id) {
        return receiptMaterialService.getAllByReceiptId(id);
    }

    /**
     * 获取收货单类型
     *
     * @return 收货单状态键值对
     */
    @GetMapping("/getReceiptMaterialType")
    @ApiOperation(value = "获取收货单物料类型")
    public Map<Integer, String> getReceiptMaterialType() {
        return EnumUtil.enumToMap(ReceiptMaterialTypeEnum.class);
    }


    /**
     * 上架
     *
     * @param receiptId 收货单物料Id
     */
    @PostMapping(value = "/goShelf")
    @ApiOperation(value = "收货单物料准备上架")
    public void goShelf(@RequestParam Integer receiptId) {
        receiptMaterialService.goShelf(receiptId);
    }


}
