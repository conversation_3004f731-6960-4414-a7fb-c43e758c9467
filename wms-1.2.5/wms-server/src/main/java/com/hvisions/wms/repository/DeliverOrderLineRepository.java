package com.hvisions.wms.repository;

import com.hvisions.wms.entity.HvWmsDeliverOrderLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: DeliverOrderLineRepository</p >
 * <p>Description: 移库单详情</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date:  2020/9/9</p >
 *
 * <AUTHOR> liuwei
 * @version :1.0.0
 */
@Repository
public interface DeliverOrderLineRepository extends JpaRepository<HvWmsDeliverOrderLine,Integer> {

    /**
     * 根据移库单id查询明细
     * @param deliverOrderId
     * @return
     */
    List<HvWmsDeliverOrderLine> findAllByDeliverOrderId(Integer deliverOrderId);

    /**
     * 根据移库单id删除
     * @param deliverId 移库单id
     */
    @Modifying
    void deleteAllByDeliverOrderId(Integer deliverId);
}
