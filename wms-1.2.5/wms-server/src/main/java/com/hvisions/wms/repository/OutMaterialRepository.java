package com.hvisions.wms.repository;

import com.hvisions.wms.entity.HvWmsOutMaterial;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <p>Title: OutMaterialRepository</p >
 * <p>Description: 出库单物料详情</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date:  2020/9/11</p >
 *
 * <AUTHOR> liuwei
 * @version :1.0.0
 */
@Repository
public interface OutMaterialRepository extends JpaRepository<HvWmsOutMaterial,Integer> {

    /**
     * 根据出库单详情删除
     * @param orderLineId 出库单详情id
     */
    @Modifying
    void deleteAllByOrderLineId(Integer orderLineId);

    /**
     * 根据出库单详情id查询
     * @param lineId 出库单详情id
     * @return 出库单原材料列表
     */
    List<HvWmsOutMaterial> findAllByOrderLineId(Integer lineId);


}
