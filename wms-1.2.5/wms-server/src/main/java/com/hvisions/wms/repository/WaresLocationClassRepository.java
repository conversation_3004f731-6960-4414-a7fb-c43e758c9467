package com.hvisions.wms.repository;

import com.hvisions.wms.entity.HvWmsWaresLocationClass;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: WaresLocationClassRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/7/20</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface WaresLocationClassRepository extends JpaRepository<HvWmsWaresLocationClass, Integer> {

    /**
     * 根据编码查询
     *
     * @param code 类型编码
     * @return 属性类型
     */
    HvWmsWaresLocationClass findByCode(String code);

    /**
     * 查询分页
     *
     * @param code     编码
     * @param name     名称
     * @param pageable 分页信息
     * @return 分页数据
     */
    Page<HvWmsWaresLocationClass> findAllByCodeContainsOrNameContaining(String code, String name, Pageable pageable);
}