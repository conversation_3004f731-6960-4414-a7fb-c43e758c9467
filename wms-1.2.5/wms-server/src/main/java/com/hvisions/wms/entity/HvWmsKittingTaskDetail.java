package com.hvisions.wms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * WMS集配任务明细实体类
 * 技术选型：MyBatis-Plus
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hv_wms_kitting_task_detail")
public class HvWmsKittingTaskDetail extends AuditableBaseEntity {

    /**
     * 集配任务ID
     */
    @NotNull(message = "集配任务ID不能为空")
    private Integer kittingTaskId;

    /**
     * 序号
     */
    private Integer sequenceNo;

    /**
     * 料号
     */
    @NotBlank(message = "料号不能为空")
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料规格
     */
    private String materialSpec;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    private BigDecimal quantity;

    /**
     * 集配数量
     */
    private BigDecimal kittingQuantity;

    /**
     * 工单数量
     */
    private BigDecimal workOrderQuantity;

    /**
     * 集配状态
     */
    @NotNull(message = "集配状态不能为空")
    private Integer kittingStatus;

}
