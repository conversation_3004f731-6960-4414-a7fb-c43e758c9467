package com.hvisions.wms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * WMS集配任务明细实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@TableName("hv_wms_kitting_task_detail")
public class HvWmsKittingTaskDetail extends SysBase {

    /**
     * 集配任务ID
     */
    @NotNull(message = "集配任务ID不能为空")
    private Integer kittingTaskId;

    /**
     * 序号
     */
    private Integer sequenceNo;

    /**
     * 料号
     */
    @NotBlank(message = "料号不能为空")
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    private BigDecimal quantity;

    private String materialParentCode;//组立物料

    private String workOrderCode;//工单编号

    private String parentWorkOrderCode;//组立工单号

}
