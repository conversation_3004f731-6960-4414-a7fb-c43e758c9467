package com.hvisions.wms.controller;

import com.hvisions.wms.dto.location.WaresLocationClassPropertyDTO;
import com.hvisions.wms.service.WaresLocationClassPropertyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: WaresLocationClassPropertyController</p >
 * <p>Description:仓储位置属性控制器 </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/7/21</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/wares_class_property")
@Api(description = "仓储位置属性控制器")
public class WaresLocationClassPropertyController {


    @Autowired
    WaresLocationClassPropertyService classPropertyService;

    /**
     * 创建属性
     *
     * @param propertyDTO 属性
     * @return 主键
     */
    @PostMapping(value = "/create")
    @ApiOperation(value = "创建属性")
    public Integer create(@RequestBody WaresLocationClassPropertyDTO propertyDTO) {
        return classPropertyService.create(propertyDTO);
    }

    /**
     * 更新属性
     *
     * @param propertyDTO 属性
     */
    @PutMapping(value = "/update")
    @ApiOperation(value = "更新属性")
    public void update(@RequestBody WaresLocationClassPropertyDTO propertyDTO) {
        classPropertyService.update(propertyDTO);
    }

    /**
     * 删除属性
     *
     * @param id 主键
     */
    @DeleteMapping(value = "/deleteById/{id}")
    @ApiOperation(value = "删除属性")
    public void deleteById(@PathVariable Integer id) {
        classPropertyService.deleteById(id);
    }

    /**
     * 获取属性
     *
     * @param id 仓储位置类型id
     * @return 仓储位置属性类型属性列表
     */
    @GetMapping(value = "/findByLocationClassId/{id}")
    @ApiOperation(value = "获取属性")
    public List<WaresLocationClassPropertyDTO> findByLocationClassId(@PathVariable Integer id) {
        return classPropertyService.findByLocationClassId(id);
    }
}