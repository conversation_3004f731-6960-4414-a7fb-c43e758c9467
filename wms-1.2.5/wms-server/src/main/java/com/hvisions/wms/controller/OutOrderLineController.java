package com.hvisions.wms.controller;

import com.hvisions.wms.dto.outstock.MaterialCheckDTO;
import com.hvisions.wms.dto.outstock.OutLineDTO;
import com.hvisions.wms.service.OutOrderLineService;
import com.hvisions.wms.service.OutOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Title: HistoryService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/24</p >
 *
 * <AUTHOR> liuwei
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "outMaterial")
@Api(description = "原材料出库单控制器")
public class OutOrderLineController {
    @Autowired
    OutOrderLineService outOrderLineService;
    @Autowired
    OutOrderService outOrderService;

    /**
     * 新增出库单原料
     *
     * @param outLineDTO 出料单原材料新增对象
     * @return id 出料单原料主键
     */
    @PostMapping(value = "createReceiptMaterial")
    @ApiOperation(value = "新增出库单原料")
    public Integer createReceiptMaterial(@RequestBody OutLineDTO outLineDTO) {
        return outOrderLineService.createReceiptMaterial(outLineDTO);
    }

    /**
     * 批量新增出库单原料
     *
     * @param outLineDTOS 出料单原材料新增对象
     */
    @PostMapping(value = "createOutLine")
    @ApiOperation(value = "批量新增出库单原料")
    public void createOutLine(@RequestBody List<OutLineDTO> outLineDTOS) {
        outOrderLineService.createOutLine(outLineDTOS);
    }

    /**
     * 根据库存id、出库单id、出库单数量生成明细表
     *
     * @param stockId 库位id
     * @param orderId 出库单id
     * @param quality 出库数量
     */
    @PutMapping(value = "autoCreateLine")
    @ApiOperation(value = "根据库存id、出库单id、出库单数量生成明细表")
    public void autoCreateLine(@RequestParam Integer stockId, @RequestParam Integer orderId,
                               @RequestParam BigDecimal quality) {
        outOrderLineService.autoCreateLine(stockId, orderId, quality);
    }

    /**
     * 修改出库单原料
     *
     * @param outLineDTO 出料单原材料修改对象
     * @return id
     */
    @PutMapping(value = "updateReceiptMaterial")
    @ApiOperation(value = "修改出库单原料")
    public Integer updateReceiptMaterial(@RequestBody OutLineDTO outLineDTO) {
        return outOrderLineService.updateReceiptMaterial(outLineDTO);
    }

    /**
     * 删除出库单原料
     *
     * @param id 主键
     */
    @DeleteMapping(value = "deleteById/{id}")
    @ApiOperation(value = "删除出库单原料")
    public void deleteById(@PathVariable int id) {
        outOrderLineService.deleteById(id);
    }

    /**
     * 根据主键出库单id查找物料信息
     *
     * @param id 出库单id
     * @return 出料单原材料新增
     */
    @GetMapping(value = "findStock/{id}")
    @ApiOperation(value = "根据主键出库单id查找物料信息")
    public List<OutLineDTO> findStock(@PathVariable int id) {
        return outOrderLineService.findStock(id);
    }


    /**
     * 根据主键修改物料状态
     *
     * @param id 出料单主键
     */
    @GetMapping(value = "setStockState/{id}")
    @ApiOperation(value = "物料出库完成")
    public void setStockState(@PathVariable int id) {
        outOrderLineService.setMaterialState(id);
    }

    /**
     * 捡料
     *
     * @param id 出料单主键
     */
    @GetMapping(value = "pickMaterial/{id}")
    @ApiOperation(value = "捡料")
    public void pickMaterial(@PathVariable int id) {
        outOrderService.pickMaterial(id);
    }

    /**
     * 重置储位
     *
     * @param id 出料单主键
     */
    @GetMapping(value = "resetStock/{id}")
    @ApiOperation(value = "重置储位")
    public void resetStock(@PathVariable int id) {
        outOrderLineService.resetStock(id);
    }

    /**
     * 根据主键删除物料详情
     *
     * @param id 物料详情主键
     */
    @DeleteMapping(value = "deleteMaterialById/{id}")
    @ApiOperation(value = "根据主键删除物料详情")
    public void deleteMaterialById(@PathVariable int id) {
        outOrderLineService.deleteMaterialById(id);
    }

    /**
     * 根据物料批次号进行出库单出库
     *
     * @param materialCheckDTO 物料批次号,出库单id
     */
    @PostMapping(value = "finishByBatchNumber")
    @ApiOperation(value = "根据物料批次号进行出库单出库")
    public void finishByBatchNumber(@RequestBody MaterialCheckDTO materialCheckDTO) {
        outOrderLineService.finishByBatchNumber(materialCheckDTO.getOrderId(), materialCheckDTO.getMaterialBatchNumber());
    }


}
