package com.hvisions.wms.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.bom.dto.FrameMaterialItemDTO;
import com.hvisions.thirdparty.common.dto.LesTransportTaskApplyDTO;
import com.hvisions.wms.enums.LESCarCssStatusEnum;
import com.hvisions.thridparty.client.LesClient;
import com.hvisions.wms.dto.factoryLesCar.FactoryLesCarDTO;
import com.hvisions.wms.dto.factoryLesCar.FactoryLesCarMaterialItemQueryDTO;
import com.hvisions.wms.dto.factoryLesCar.FactoryLesCarQueryDTO;
import com.hvisions.wms.entity.factoryLesCar.HvWmsFactoryLesCar;
import com.hvisions.wms.service.FactoryLesCarService;
import com.hvisions.wms.utils.SerialCodeUtilsV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping(value = "/factoryLesCarController")
@Api(description = "工厂LES车辆")
public class FactoryLesCarController {

    @Autowired
    private FactoryLesCarService factoryLesCarService;
    @Autowired
    private SerialCodeUtilsV2 serialCodeUtilsV2;
    @Autowired
    private LesClient lesClient;

    @PostMapping("/getPage")
    @ApiOperation("分页查询")
    public Page<FactoryLesCarDTO> getPage(@RequestBody FactoryLesCarQueryDTO factoryLesCarQueryDTO) {
        return factoryLesCarService.getPage(factoryLesCarQueryDTO);
    }

    @PostMapping("/getMaterialPage")
    @ApiOperation("分页查询物料")
    public Page<FrameMaterialItemDTO> getMaterialPage(@RequestBody FactoryLesCarMaterialItemQueryDTO queryDTO) {
        return factoryLesCarService.getPageMaterial(queryDTO);
    }

    @PostMapping("/addCar")
    @ApiOperation("添加LES车辆")
    public ResultVO addCar(@RequestBody FactoryLesCarDTO factoryLesCarDTO) {
        factoryLesCarDTO.setTaskNo(serialCodeUtilsV2.generateCode("FactoryLesCarTaskNo"));
        LesTransportTaskApplyDTO lesTransportTaskApplyDTO = factoryLesCarConvertLesTransportTask(factoryLesCarDTO);
        lesTransportTaskApplyDTO.setCssStatus(LESCarCssStatusEnum.ADD.getCode());
//        ResultVO resultVO = lesClient.transportTaskApplySend(lesTransportTaskApplyDTO);
//        if (!resultVO.isSuccess()){
//            return resultVO;
//        }
        return ResultVO.success(factoryLesCarService.save(DtoMapper.convert(factoryLesCarDTO, HvWmsFactoryLesCar.class)));
    }

    @PutMapping("/updateCarById")
    @ApiOperation("更新LES车辆")
    public ResultVO updateCarById(@RequestBody FactoryLesCarDTO factoryLesCarDTO) {
        LesTransportTaskApplyDTO lesTransportTaskApplyDTO = factoryLesCarConvertLesTransportTask(factoryLesCarDTO);
        lesTransportTaskApplyDTO.setCssStatus(LESCarCssStatusEnum.UPDATE.getCode());
//        ResultVO resultVO = lesClient.transportTaskApplySend(lesTransportTaskApplyDTO);
//        if (!resultVO.isSuccess()){
//            return resultVO;
//        }
        return ResultVO.success(factoryLesCarService.updateById(DtoMapper.convert(factoryLesCarDTO, HvWmsFactoryLesCar.class)));
    }

    @PutMapping("/updateCarByTaskCode")
    @ApiOperation("根据任务号更新LES车辆")
    public ResultVO updateCarByCode(@RequestBody FactoryLesCarDTO factoryLesCarDTO) {
        LesTransportTaskApplyDTO lesTransportTaskApplyDTO = factoryLesCarConvertLesTransportTask(factoryLesCarDTO);
        lesTransportTaskApplyDTO.setCssStatus(LESCarCssStatusEnum.UPDATE.getCode());
//        ResultVO resultVO = lesClient.transportTaskApplySend(lesTransportTaskApplyDTO);
//        if (!resultVO.isSuccess()){
//            return resultVO;
//        }
        return ResultVO.success(factoryLesCarService.update(DtoMapper.convert(factoryLesCarDTO, HvWmsFactoryLesCar.class)
                ,new LambdaQueryWrapper<HvWmsFactoryLesCar>().eq(HvWmsFactoryLesCar::getTaskNo, factoryLesCarDTO.getTaskNo())));
    }

    @PutMapping("/cancelCar")
    @ApiOperation("取消LES车辆任务")
    public ResultVO cancelCar(@RequestBody FactoryLesCarDTO factoryLesCarDTO) {
        LesTransportTaskApplyDTO lesTransportTaskApplyDTO = factoryLesCarConvertLesTransportTask(factoryLesCarDTO);
        lesTransportTaskApplyDTO.setCssStatus(LESCarCssStatusEnum.CANCEL.getCode());
//        ResultVO resultVO = lesClient.transportTaskApplySend(lesTransportTaskApplyDTO);
//        if (!resultVO.isSuccess()){
//            return resultVO;
//        }
        return ResultVO.success(factoryLesCarService.update(DtoMapper.convert(factoryLesCarDTO, HvWmsFactoryLesCar.class)
                ,new LambdaQueryWrapper<HvWmsFactoryLesCar>().eq(HvWmsFactoryLesCar::getTaskNo, factoryLesCarDTO.getTaskNo())));
    }

    private LesTransportTaskApplyDTO factoryLesCarConvertLesTransportTask (FactoryLesCarDTO factoryLesCarDTO) {
        LesTransportTaskApplyDTO lesTransportTaskApplyDTO = new LesTransportTaskApplyDTO();
        lesTransportTaskApplyDTO.setTaskCode(factoryLesCarDTO.getTaskNo());
        lesTransportTaskApplyDTO.setApplyStartDate(convertUtcToOffset8(factoryLesCarDTO.getApplyStartTime()));
        lesTransportTaskApplyDTO.setApplyEndDate(convertUtcToOffset8(factoryLesCarDTO.getApplyEndTime()));
        lesTransportTaskApplyDTO.setCarTypeId(factoryLesCarDTO.getCarType());
        lesTransportTaskApplyDTO.setCarJobType(factoryLesCarDTO.getCarWorkType());
        lesTransportTaskApplyDTO.setJobContent(factoryLesCarDTO.getWorkContent());
        lesTransportTaskApplyDTO.setDeptId(factoryLesCarDTO.getApplyCarDept());
        lesTransportTaskApplyDTO.setProjectId(factoryLesCarDTO.getProjectNo());
        lesTransportTaskApplyDTO.setDistributionStartId(factoryLesCarDTO.getStartPoint());
        lesTransportTaskApplyDTO.setDistributionEndId(factoryLesCarDTO.getEndPoint());
        lesTransportTaskApplyDTO.setDeliveryUserId(factoryLesCarDTO.getDeliverManNo());
        lesTransportTaskApplyDTO.setContactUserId(factoryLesCarDTO.getReceiveManNo());
        lesTransportTaskApplyDTO.setApplyUserId(factoryLesCarDTO.getApplyManNo());
        lesTransportTaskApplyDTO.setSectionNumber(factoryLesCarDTO.getSegmentationCode());
        lesTransportTaskApplyDTO.setRemarks(factoryLesCarDTO.getRemark());
        return lesTransportTaskApplyDTO;
    }

    private String convertUtcToOffset8(LocalDateTime dateTime) {
// 将 LocalDateTime 转换为带时区的 ZonedDateTime（使用系统默认时区）
        ZonedDateTime zonedDateTime = dateTime.atZone(ZoneId.systemDefault());

        // 定义目标格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");

        // 格式化输出
        return zonedDateTime.format(formatter);
    }


}