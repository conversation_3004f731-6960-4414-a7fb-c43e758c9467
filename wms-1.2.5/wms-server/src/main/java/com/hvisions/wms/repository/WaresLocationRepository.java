package com.hvisions.wms.repository;

import com.hvisions.wms.entity.HvWmsWaresLocation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: WarehouseLocationRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/7/19</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface WaresLocationRepository extends JpaRepository<HvWmsWaresLocation, Integer> {

    /**
     * 根据父级id查询子集
     * @param parentId 父级id
     * @return 子集
     */
    List<HvWmsWaresLocation> findAllByParentId(Integer parentId);

    @Query(value = "select h.id  from  HvWmsWaresLocation h where h.parentId = ?1")
    List<Integer> findAllIdByParentId(Integer code);

    @Query(value = "select h.id  from  HvWmsWaresLocation h where h.code = ?1")
    Integer findIdByCode(String code);

    @Query(value = "select h  from  HvWmsWaresLocation h where h.code = ?1")
    HvWmsWaresLocation findByCode(String code);

    List<HvWmsWaresLocation> getAllByIdIn(List<Integer> integers);

    List<HvWmsWaresLocation> getAllByParentIdNot(Integer parentId);

    HvWmsWaresLocation getById(Integer id);
}