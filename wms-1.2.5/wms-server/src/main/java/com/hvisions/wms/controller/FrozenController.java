package com.hvisions.wms.controller;

import com.hvisions.wms.dto.stock.FrozenInfo;
import com.hvisions.wms.dto.stock.FrozenMaterialDTO;
import com.hvisions.wms.dto.stock.FrozenMaterialQueryDTO;
import com.hvisions.wms.service.FrozenMaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: FrozenController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/9/2</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RequestMapping(value = "/frozen")
@RestController
@Api(description = "库存冻结控制器")
public class FrozenController {


    @Autowired
    FrozenMaterialService frozenMaterialService;

    /**
     * 库存冻结
     *
     * @param materialDTO 冻结信息
     */
    @PostMapping(value = "/frozenMaterial")
    @ApiOperation(value = "库存冻结")
    public void frozenMaterial(@RequestBody FrozenMaterialDTO materialDTO) {
        frozenMaterialService.frozenMaterial(materialDTO);
    }

    /**
     * 解除冻结（根据业务进行）
     *
     * @param operation 业务类型
     * @param orderCode 业务号
     */
    @PostMapping(value = "/unFrozenByOrderCode")
    @ApiOperation(value = "解除冻结")
    public void unFrozenByOrderCode(@RequestParam String operation, @RequestParam String orderCode) {
        frozenMaterialService.deleteFrozenMaterial(operation, orderCode);
    }


    /**
     * 解冻物料
     *
     * @param id 冻结信息Id
     */
    @ApiOperation(value = "解除库存冻结")
    @DeleteMapping(value = "/deleteFrozenMaterial/{id}")
    public void deleteFrozenMaterial(@PathVariable int id) {
        frozenMaterialService.deleteFrozenMaterial(id);
    }


    /**
     * 批量解冻物料
     *
     * @param idList id列表
     */
    @ApiOperation(value = "批量解冻物料")
    @DeleteMapping(value = "/deleteFrozenMaterialByIdIn")
    public void deleteFrozenMaterialByIdIn(@RequestBody List<Integer> idList) {
        frozenMaterialService.deleteFrozenMaterialByIdIn(idList);
    }

    /**
     * 库存冻结查询
     *
     * @param frozenMaterialQueryDTO 查询条件
     * @return 返回值
     */
    @ApiOperation(value = "库存冻结查询")
    @PostMapping(value = "/getAllFrozenMaterialByQuery")
    public Page<FrozenMaterialDTO> getAllFrozenMaterialByQuery(@RequestBody FrozenMaterialQueryDTO frozenMaterialQueryDTO) {
        return frozenMaterialService.getAllFrozenMaterialByQuery(frozenMaterialQueryDTO);
    }

    /**
     * 查询库存冻结信息
     *
     * @param warehouseId 仓库id
     * @param locationId  库位id
     * @return 库存冻结信息
     */
    @ApiOperation(value = "查询库存冻结信息")
    @GetMapping(value = "/getFrozenInfo")
    public List<FrozenInfo> getFrozenInfo(@RequestParam(required = false) @ApiParam(value = "仓库id") Integer warehouseId,
                                          @RequestParam(required = false) @ApiParam(value = "库区id") Integer wareAreaId,
                                          @RequestParam(required = false) @ApiParam(value = "库位id") Integer locationId) {
        return frozenMaterialService.getFrozenInfo(warehouseId,wareAreaId,locationId);
    }


}
