package com.hvisions.wms.controller;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.MaterialPointClient;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.wms.dto.location.*;
import com.hvisions.wms.entity.HvWmsWaresLocation;
import com.hvisions.wms.enums.PageControllerTypeEnum;
import com.hvisions.wms.service.WaresLocationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title: WareHouseLocationController</p >
 * <p>Description: 库存位置控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/7/19</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/warehouse")
@Slf4j
@Api(description = "库存位置控制器")
public class WaresLocationController {

    @Autowired
    WaresLocationService waresLocationService;
    @Autowired
    private MaterialPointClient materialPointClient;

    /**
     * 创建仓储位置
     *
     * @param waresLocationDTO 仓储位置信息
     */
    @PostMapping(value = "/createWaresLocation")
    @ApiOperation(value = "创建仓储位置")
    @Transactional(rollbackFor = Exception.class)
    public WaresLocationDTO createWaresLocation(@RequestBody WaresLocationDTO waresLocationDTO) {
        //同步至base模块
        if(waresLocationDTO.getParentId() != 0){
            //库区才同步
            WaresLocationMaterialPointSynchronizationDTO waresLocationMaterialPointSynchronizationDTO = new WaresLocationMaterialPointSynchronizationDTO();
            waresLocationMaterialPointSynchronizationDTO.setWarehouseAreaCode(waresLocationDTO.getCode());
            waresLocationMaterialPointSynchronizationDTO.setWarehouseAreaName(waresLocationDTO.getName());
            List<WaresLocationMaterialPointSynchronizationDTO> dtos = new ArrayList<>();
            dtos.add(waresLocationMaterialPointSynchronizationDTO);
            ResultVO<Boolean> resultVO = materialPointClient.synchronizationWmsModule(dtos, PageControllerTypeEnum.ADDorEDIT.getCode());
            if(!resultVO.isSuccess()){
                throw new BaseKnownException("同步base模块异常,请联系管理员");
            }
        }
        return waresLocationService.createWaresLocation(waresLocationDTO);
    }

    /**
     * 更新仓储位置
     *
     * @param waresLocationDTO 仓储位置信息
     */
    @PutMapping(value = "/updateWaresLocation")
    @ApiOperation(value = "更新仓储位置")
    @Transactional(rollbackFor = Exception.class)
    public WaresLocationDTO updateWaresLocation(@RequestBody WaresLocationDTO waresLocationDTO) {
        //同步至base模块
        if(waresLocationDTO.getParentId() != 0){
            //库区才同步
            WaresLocationMaterialPointSynchronizationDTO waresLocationMaterialPointSynchronizationDTO = new WaresLocationMaterialPointSynchronizationDTO();
            waresLocationMaterialPointSynchronizationDTO.setWarehouseAreaCode(waresLocationDTO.getCode());
            waresLocationMaterialPointSynchronizationDTO.setWarehouseAreaName(waresLocationDTO.getName());
            List<WaresLocationMaterialPointSynchronizationDTO> dtos = new ArrayList<>();
            dtos.add(waresLocationMaterialPointSynchronizationDTO);
            ResultVO<Boolean> resultVO = materialPointClient.synchronizationWmsModule(dtos, PageControllerTypeEnum.ADDorEDIT.getCode());
            if(!resultVO.isSuccess()){
                throw new BaseKnownException("同步base模块异常,请联系管理员");
            }
        }
        return waresLocationService.updateWaresLocation(waresLocationDTO);
    }

    /**
     * 分页查询库存位置
     *
     * @param waresLocationQueryDTO 查询条件
     * @return 库存位置信息
     */
    @PostMapping(value = "/getLocationByQuery")
    @ApiOperation(value = "分页查询库存位置")
    public Page<WaresLocationDTO> getLocationByQuery(@RequestBody WaresLocationQueryDTO waresLocationQueryDTO) {
        return waresLocationService.getLocationByQuery(waresLocationQueryDTO);
    }


    /**
     * 查询所有库位
     *
     * @return 库位信息
     */
    @PostMapping(value = "/findAllByQuery")
    @ApiOperation(value = "查询所有库位")
    public List<WaresLocationDTO> findAllByQuery() {
        return waresLocationService.findAllByQuery();
    }

    /**
     * 同步储位的储存规则到相同层级下面
     *
     * @param locationId 储位id
     */
    @PutMapping(value = "/synchronize")
    @ApiOperation(value = "同步分支下的储位物料存储规则")
    public void synchronize(@RequestParam @ApiParam(value = "储位id") Integer locationId) {
        waresLocationService.synchronize(locationId);
    }

    /**
     * 删除仓储位置
     *
     * @param id 仓储位置信息ID
     */
    @DeleteMapping(value = "/deleteWareLocation/{id}")
    @ApiOperation(value = "删除仓储位置 同时删除库位储存规则")
    @Transactional(rollbackFor = Exception.class)
    public void deleteWareLocation(@PathVariable int id) {
        //同步至base模块
        WaresLocationDTO waresLocationDTO = waresLocationService.getLocationById(id);
        WaresLocationMaterialPointSynchronizationDTO waresLocationMaterialPointSynchronizationDTO = new WaresLocationMaterialPointSynchronizationDTO();
        waresLocationMaterialPointSynchronizationDTO.setWarehouseAreaCode(waresLocationDTO.getCode());
        List<WaresLocationMaterialPointSynchronizationDTO> dtos = new ArrayList<>();
        dtos.add(waresLocationMaterialPointSynchronizationDTO);
        ResultVO<Boolean> resultVO = materialPointClient.synchronizationWmsModule(dtos, PageControllerTypeEnum.DELETE.getCode());
        if(!resultVO.isSuccess()){
            throw new BaseKnownException("同步base模块异常,请联系管理员");
        }
        waresLocationService.deleteWareLocation(id);
    }


    /**
     * 新增库位储存物料
     *
     * @param waresLocationRuleDTO 新增信息
     */
    @PostMapping(value = "/createLocationRule")
    @ApiOperation(value = "新增库位储存物料")
    public void createLocationRule(@RequestBody WaresLocationRuleDTO waresLocationRuleDTO) {
        waresLocationService.createLocationRule(waresLocationRuleDTO);
    }

    /**
     * 更新库位储存物料
     *
     * @param waresLocationRuleDTO 库位储存信息信息
     */
    @PutMapping(value = "/updateLocationRule")
    @ApiOperation(value = "更新库位储存物料")
    public void updateLocationRule(@RequestBody WaresLocationRuleDTO waresLocationRuleDTO) {
        waresLocationService.updateLocationRule(waresLocationRuleDTO);
    }


    /**
     * 根据库存id查询库存可储存物料
     *
     * @param locationId 库存id
     * @return 库存可储存物料列表
     */
    @ApiOperation(value = "根据库存id查询库存可储存物料")
    @GetMapping(value = "/getRuleByLocationId/{locationId}")
    public List<WaresLocationRuleDTO> getRuleByLocationId(@PathVariable int locationId) {
        return waresLocationService.getRuleByLocationId(locationId);
    }

    /**
     * 删除仓储位置可存储物料
     *
     * @param id id
     */
    @DeleteMapping(value = "/deleteLocationRule/{id}")
    @ApiOperation(value = "删除仓储位置可存储物料")
    public void deleteLocationRule(@PathVariable Integer id) {
        waresLocationService.deleteLocationRule(id);
    }

    /**
     * 递归查询仓储位置
     *
     * @param locationId 仓库位置
     * @return HvWmsWaresLocation
     */
    @ApiOperation(value = "递归查询仓储位置")
    @GetMapping(value = "/getStockLocation/{locationId}")
    public List<Integer> getStockLocation(@PathVariable Integer locationId) {
        return waresLocationService.getStockLocation(locationId);
    }

    /**
     * 查询所有父节点不为0的库存
     * @return
     */
    @ApiOperation(value = "查询所有父节点不为0的库存")
    @GetMapping(value = "/getAllNotInZero")
    public List<WaresLocationDTO> getAllNotInZero(){
        return waresLocationService.findAllByNotInZero();
    }


    /**
     * 仓储
     *
     * @param locationCode 仓库位置
     * @return HvWmsWaresLocation
     */
    @ApiOperation(value = "查询仓储位置Id")
    @GetMapping(value = "/getLocationId/{locationCode}")
    public Integer getLocationId(@PathVariable String locationCode) {
        return waresLocationService.getLocationCode(locationCode);
    }

    @ApiOperation(value = "根据库位编码查询库位库区信息")
    @GetMapping(value = "/getWareLocationInfoByLocationCode/{locationCode}")
    public WareLocationInfoDTO getWareLocationInfoByLocationCode(@PathVariable String locationCode) {
        return waresLocationService.getWareLocationInfoByLocationCode(locationCode);
    }


    /**
     * 仓储
     *
     * @param code 仓库
     * @return HvWmsWaresLocation
     */
    @ApiOperation(value = "查询仓储信息")
    @GetMapping(value = "/getByCode/{code}")
    public HvWmsWaresLocation getByCode(@PathVariable String code) {
        return waresLocationService.getByCode(code);
    }

    @ApiOperation(value = "根据仓库id查询仓储信息")
    @GetMapping(value = "/getById/{Id}")
    public WaresLocationDTO getById(@PathVariable Integer Id) {
        return waresLocationService.getById(Id);
    }

    /**
     * 获取所有库位建模tree
     *
     * @return 库位建模树
     */
    @GetMapping(value = "/getWaresLocationTree")
    @ApiOperation(value = "获取所有库位存储建模tree")
    public List<WaresLocationTreeDTO> getWaresLocationTree() {
        return waresLocationService.getWaresLocationTree();
    }

}
