package com.hvisions.wms.controller;

import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.wms.dto.outstock.*;
import com.hvisions.wms.service.OutOrderLineService;
import com.hvisions.wms.service.OutOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>Title: HistoryService</p >
 * <p>Description:出库单控制器 </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/24</p >
 *
 * <AUTHOR> liuwei
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "outStock")
@Api(description = "出库单控制器")
public class OutOrderController {
    @Autowired
    OutOrderService outOrderService;

    @Autowired
    OutOrderLineService outOrderLineService;

    /**
     * 新建出库单
     * @param baseOutStockDto 出库单新增对象
     * @return 主键
     */
    @PostMapping(value = "createOutStock")
    @ApiOperation(value = "新建出库单")
    public Integer createOutStock(@RequestBody BaseOutStockDTO baseOutStockDto) {
        return outOrderService.createOutStock(baseOutStockDto);
    }

    /**
     * 出库单和出库单详情新增
     * @param outOrderAndLineDto 出库单和出库单详情新增对象
     * @return 出库单id
     */
    @PostMapping(value = "createOutStockAndMaterial")
    @ApiOperation(value = "出库单和出库单详情新增")
    public Integer createOutStockAndMaterial(@RequestBody OutOrderAndLineDTO outOrderAndLineDto) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        return outOrderService.createOutStockAndMaterial(outOrderAndLineDto);
    }

    /**
     *修改
     * @param baseOutStockDto  出库单修改对象
     * @return 主键
     */
    @PutMapping(value = "updateOutStock")
    @ApiOperation(value = "修改出库单")
    public Integer updateOutStock(@RequestBody BaseOutStockDTO baseOutStockDto) {
        return outOrderService.updateOutStock(baseOutStockDto);
    }

    /**
     * 查询出库单
     * @param outStockQueryDTO 出库单查询对象
     * @return 出库单
     */
    @PostMapping(value = "getOutStock")
    @ApiOperation(value = "查询出库单")
    public Page<OutStockDTO> getOutStock(@RequestBody OutStockQueryDTO outStockQueryDTO) {
        return outOrderService.getOutStock(outStockQueryDTO);
    }

    /**
     * 自动查找库位
     * @param id 主键
     */
    @PutMapping(value = "autoLocate")
    @ApiOperation(value = "自动查找库位")
    public void autoLocate(@RequestParam int id,@ApiParam(value = "是否库存报警,0:报警，1:不报警 ") @RequestParam int stockWarning) {
         outOrderLineService.autoLocate(id,stockWarning);
    }


    /**
     * 根据id查询
     * @param id 主键
     * @return 出库单
     */
    @GetMapping(value = "findById/{id}")
    @ApiOperation(value = "根据id查询")
    public OutStockDTO findById(@PathVariable int id) {
        return outOrderService.findById(id);
    }


    /**
     * 根据出库单号查询
     * @param code 出库单号
     * @return 出库单
     */
    @GetMapping(value = "findByCode/{code}")
    @ApiOperation(value = "根据出库单号查询")
    public OutStockDTO findByCode(@PathVariable String code) {
        return outOrderService.findByCode(code);
    }

    /**
     * 根据出库单号删除
     * @param code 出库单号
     */
    @DeleteMapping(value = "deleteByCode/{code}")
    @ApiOperation(value = "根据出库单号删除")
    public void deleteByCode(@PathVariable String code) {
        outOrderService.deleteByCode(code);
    }

    /**
     * 根据id删除
     * @param id 主键
     */
    @DeleteMapping(value = "deleteById/{id}")
    @ApiOperation(value = "根据id删除")
    public void deleteById(@PathVariable Integer id) {
        outOrderService.deleteById(id);
    }

    /**
     * 完成出库
     * @param id 出料单id
     */
    @GetMapping(value = "finishStock/{id}")
    @ApiOperation(value = "完成出库")
    public void finishStock(@PathVariable Integer id) {
        outOrderService.finishStock(id);
    }

    /**
     * 出库完成后修改出库状态
     * @param id 主键
     * @param state 状态
     */
    @PutMapping(value = "updateState")
    @ApiOperation(value = "修改出库状态")
    public void updateState(@RequestParam Integer id,
                     @ApiParam(value = "类型(1:创建; 2:捡料中; 3:完成出库;4:审批完成;5:收料完成)") @RequestParam String state){
        outOrderService.updateState(id,state);
    }

    /**
     * 修改审批状态
     * @param id 主键
     * @param state 状态
     */
    @PutMapping(value = "approveOutOrder")
    @ApiOperation(value = "修改审批状态")
    public void approveOutOrder(@RequestParam Integer id,
                                @ApiParam(value = "类型(审批通过;审批不通过;提交审批)") @RequestParam String state,
                                @UserInfo UserInfoDTO userInfoDTO){
        outOrderService.approveOutOrder(id,state,userInfoDTO);
    }
}
