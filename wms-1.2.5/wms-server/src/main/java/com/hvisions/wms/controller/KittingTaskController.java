package com.hvisions.wms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.dto.kitting.KittingTaskDTO;
import com.hvisions.wms.dto.kitting.KittingTaskQueryDTO;
import com.hvisions.wms.service.KittingTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 集配任务控制器
 */
@Slf4j
@RestController
@RequestMapping("/kittingTask")
@Api(description = "物料集配任务")
public class KittingTaskController {

    @Autowired
    private KittingTaskService kittingTaskService;

    /**
     * 查询集配任务列表
     */
    @PostMapping("/query")
    @ApiOperation("查询集配任务列表")
    public Page<KittingTaskDTO> queryTasks(@RequestBody KittingTaskQueryDTO queryDTO) {
        return kittingTaskService.queryKittingTasks(queryDTO);
    }

    /**
     * 根据ID获取集配任务详情
     */
    @GetMapping("/{taskId}")
    @ApiOperation("获取集配任务详情")
    public KittingTaskDTO getTaskById(@PathVariable Integer taskId) {
        return kittingTaskService.getKittingTaskById(taskId);
    }

    /**
     * 开始执行集配任务
     */
    @PostMapping("/start/{taskId}")
    @ApiOperation("开始执行集配任务")
    public ResultVO startTask(@PathVariable Integer taskId, 
                             @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        return kittingTaskService.startKittingTask(taskId, userInfo.getId());
    }

    /**
     * 完成集配任务
     */
    @PostMapping("/complete/{taskId}")
    @ApiOperation("完成集配任务")
    public ResultVO completeTask(@PathVariable Integer taskId, 
                                @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        return kittingTaskService.completeKittingTask(taskId, userInfo.getId());
    }

    /**
     * 根据物料需求单生成集配任务
     */
    @PostMapping("/generate/{requirementCode}")
    @ApiOperation("根据物料需求单生成集配任务")
    public ResultVO generateTasks(@PathVariable String requirementCode) {
        return kittingTaskService.generateKittingTasksFromRequirement(requirementCode);
    }

}
