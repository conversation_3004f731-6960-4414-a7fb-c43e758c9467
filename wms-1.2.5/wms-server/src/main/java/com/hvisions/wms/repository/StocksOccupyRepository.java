package com.hvisions.wms.repository;

import com.hvisions.wms.entity.HvWmsStocksOccupy;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>Title: StocksOccupyRepository</p>
 * <p>Description: 库存占用仓储层</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/8/28</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface StocksOccupyRepository extends JpaRepository<HvWmsStocksOccupy,Integer> {
    /**
     * 查找占用的库存
     * @param stockId 库位id
     * @return 占用的库存信息
     */
    List<HvWmsStocksOccupy> findAllByStockId(Integer stockId);

    /**
     * 根据operation和OrderCode找到此项业务的所有库存占用信息
     * @param operation 业务类型
     * @param orderCode 业务唯一编号
     * @return 占用信息列表
     */
    List<HvWmsStocksOccupy> findAllByOperationAndOrderCode(String operation,String orderCode);

    /**
     * 查询是否存在operation和code的业务
     * @param operation 业务类型
     * @param orderCode 业务号
     * @return 是否存在
     */
    Boolean existsByOperationAndOrderCode(String operation,String orderCode);

    /**
     * 根据删除库存主表id删除占用的库存信息
     * @param stockIds id
     *
     */
    @Modifying
    @Transactional
    @Query("DELETE FROM HvWmsStocksOccupy s WHERE s.stockId IN :stockIds")
    void deleteByStockIdIn(@Param("stockIds") List<Integer> stockIds);
}

    
    
    
    