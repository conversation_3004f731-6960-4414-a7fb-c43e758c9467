package com.hvisions.wms.repository;

import com.hvisions.wms.entity.HvWmsPurchaseLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title:PurchaseLineRepository</p>
 * <p>Description:采购单行表jpa</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2020/9/8</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Repository
public interface PurchaseLineRepository extends JpaRepository<HvWmsPurchaseLine,Integer> {

    /**
     * 通过头表ID获取所有行表
     * @param headerId 头表ID
     * @return 全部行表
     */
    List<HvWmsPurchaseLine> findByHeaderId(Integer headerId);
}
