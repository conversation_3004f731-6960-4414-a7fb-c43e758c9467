package com.hvisions.wms.repository;

import com.hvisions.wms.entity.HvWmsAdjustOrderLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: AdjustOrderLineRepository</p >
 * <p>Description: 移库单明细仓储层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date:  2020/9/9</p >
 *
 * <AUTHOR> liuwei
 * @version :1.0.0
 */
@Repository
public interface AdjustOrderLineRepository extends JpaRepository<HvWmsAdjustOrderLine,Integer> {
    /**
     * 根据调整单id查询明细信息
     * @param id
     * @return
     */
    List<HvWmsAdjustOrderLine> findAllByDeliverOrderId(Integer id);


    /**
     * 根据调整单地删除
     * @param deliverId 移库单id
     */
    @Modifying
    void deleteAllByDeliverOrderId(Integer deliverId);
}
