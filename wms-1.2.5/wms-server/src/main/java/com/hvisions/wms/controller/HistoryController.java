package com.hvisions.wms.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.thirdparty.common.dto.StockInfoDTO;
import com.hvisions.wms.dto.history.HistoryDTO;
import com.hvisions.wms.dto.history.QueryHistory;
import com.hvisions.wms.service.HistoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * <p>Title: HistoryController</p >
 * <p>Description: 库存历史单控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/24</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@RequestMapping(value = "/history")
@RestController
@Api(description = "库存历史")
public class HistoryController {

    @Autowired
    HistoryService historyService;

    /**
     * 获取历史记录
     * @param queryHistory 历史记录查询条件
     * @return 历史记录
     */
    @PostMapping(value = "/getHistoryByQuery")
    @ApiOperation(value = "获取历史记录")
    public Page<HistoryDTO> getHistoryByQuery(@RequestBody QueryHistory queryHistory) {

        return historyService.getHistoryByQuery(queryHistory);
    }

    /**
     * 手动库存同步
     * @return 历史记录
     */
    @GetMapping(value = "/synchronizationStockSend")
    @ApiOperation(value = "手动库存同步")
    public void synchronizationStockSend() {
        historyService.synchronizationStockSend();
    }

    /**
     * 库存同步异常信息存入redis
     * @return 历史记录
     */
    @PostMapping(value = "/stockSendFailedSaveRedis")
    @ApiOperation(value = "库存同步异常信息存入redis")
    public void stockSendFailedSaveRedis(@RequestBody StockInfoDTO stockInfoDTO) {
        historyService.stockSendFailedSaveRedis(stockInfoDTO);
    }

    @ApiResultIgnore
    @PostMapping("/exportStockLog")
    @ApiOperation("导出库存日志")
    public ResultVO<ExcelExportDto> exportStockLog(@RequestBody QueryHistory queryHistory) throws IOException, IllegalAccessException {
        return historyService.exportStockLog(queryHistory);
    }


}