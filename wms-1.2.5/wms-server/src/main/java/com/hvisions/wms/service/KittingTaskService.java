package com.hvisions.wms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.dto.kitting.KittingTaskDTO;
import com.hvisions.wms.dto.kitting.KittingTaskQueryDTO;
import com.hvisions.wms.entity.HvWmsKittingTask;
import org.springframework.data.domain.Page;

/**
 * 集配任务服务接口
 */
public interface KittingTaskService extends IService<HvWmsKittingTask> {

    /**
     * 查询集配任务列表
     *
     * @param queryDTO 查询条件
     * @return 集配任务分页列表
     */
    Page<KittingTaskDTO> queryKittingTasks(KittingTaskQueryDTO queryDTO);

    /**
     * 根据ID获取集配任务详情
     *
     * @param taskId 任务ID
     * @return 集配任务详情
     */
    KittingTaskDTO getKittingTaskById(Integer taskId);

    /**
     * 开始执行集配任务
     *
     * @param taskId 任务ID
     * @param operatorId 操作员ID
     * @return 操作结果
     */
    ResultVO startKittingTask(Integer taskId, Integer operatorId);

    /**
     * 完成集配任务
     *
     * @param taskId 任务ID
     * @param operatorId 操作员ID
     * @return 操作结果
     */
    ResultVO completeKittingTask(Integer taskId, Integer operatorId);

    /**
     * 根据物料需求单生成集配任务
     *
     * @param requirementCode 需求单号
     * @return 操作结果
     */
    ResultVO generateKittingTasksFromRequirement(String requirementCode);

}
