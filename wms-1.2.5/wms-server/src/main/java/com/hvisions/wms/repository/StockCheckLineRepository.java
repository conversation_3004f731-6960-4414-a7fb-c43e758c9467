package com.hvisions.wms.repository;

import com.hvisions.wms.entity.stockcheck.HvWmsStockCheckLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: StockCheckLineRepository</p >
 * <p>Description: 盘点单行表</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/9/8</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Repository
public interface StockCheckLineRepository extends JpaRepository<HvWmsStockCheckLine, Integer> {
    /**
     *
     * @param headerId 头表id
     * @return 行数据
     */
    List<HvWmsStockCheckLine> findByHeaderId(Integer headerId);

    /**
     *
     * @param headerId 头表id
     * @param materialId 物料id
     * @return 行数据
     */
    HvWmsStockCheckLine findByHeaderIdAndMaterialId(Integer headerId,Integer materialId);
}
