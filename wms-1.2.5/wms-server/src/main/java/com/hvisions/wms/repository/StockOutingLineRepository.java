package com.hvisions.wms.repository;

import com.hvisions.wms.entity.stock.HvWmsStockOutingLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: StockOutingLineRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/9/3</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Repository
public interface StockOutingLineRepository extends JpaRepository<HvWmsStockOutingLine, Integer> {
    /**
     * 根据头表id查询数据
     *
     * @param id 头表id
     * @return 入库清单数据
     */
    HvWmsStockOutingLine getByHeaderId(Integer id);

    /**
     * 根据出库单id删除出库清单数据
     *
     * @param owId 出库单id
     */
    void deleteByHeaderId(Integer owId);

    /**
     * 根据物料id,编码和头表id查询数据
     *
     * @param materialId   物料id
     * @param materialCode 物料编码
     * @param headerId     出库单id
     * @return 根据物料id, 物料编码, 出库单id查询出库清单
     */
    HvWmsStockOutingLine findByMaterialIdAndMaterialCodeAndHeaderId(Integer materialId, String materialCode, Integer headerId);

    /**
     * 根据物料id和头表id查询是否存在物料
     *
     * @param headerId   头表id
     * @param materialId 物料id
     * @return 是否存在
     */
    Boolean existsByHeaderIdAndMaterialId(Integer headerId, Integer materialId);
}
