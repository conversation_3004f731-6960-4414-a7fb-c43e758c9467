package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.activiti.lub.ItemsLubDTO;
import com.hvisions.eam.activiti.sapre.ItemsSpareDTO;
import com.hvisions.eam.dto.publicstore.ActualUseDTO;
import com.hvisions.eam.dto.publicstore.ApplyDTO;
import com.hvisions.eam.query.publicstore.ActualUseQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title:ActualUseClient</p>
 * <p>Description:备件实际使用列表</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "eam", path = "/ActualUseController", fallbackFactory = ActualUseClientFallBack.class)
public interface ActualUseClient {

    /**
     * 增加备件实际使用列表
     *
     * @param actualUseDTO 使用列表DTO
     * @return id
     */
    @Deprecated
    @ApiOperation(value = "增加备件实际使用列表")
    @PostMapping(value = "/createActualUse")
    ResultVO<Integer> createActualUse(@RequestBody ActualUseDTO actualUseDTO);

    /**
     * 批量增加备件实际使用列表
     *
     * @param actualUseDTOs 使用列表DTO List 集合
     * @return id集合
     */
    @ApiOperation(value = "批量增加备件实际使用列表")
    @PostMapping(value = "/createActualUseList")
    ResultVO<List<Integer>> createActualUseList(@RequestBody List<ActualUseDTO> actualUseDTOs);

    /**
     * 修改备件实际使用列表
     *
     * @param actualUseDTO 使用清单DTO列表
     * @return id
     */
    @ApiOperation(value = "修改备件实际使用列表")
    @PostMapping(value = "/updateActualUse")
    ResultVO<Integer> updateActualUse(@RequestBody ActualUseDTO actualUseDTO);

    /**
     * 通过id删除备件实际使用列表
     *
     * @param id 列表id
     * @return void
     */
    @Deprecated
    @ApiOperation(value = "通过id删除备件实际使用列表")
    @GetMapping(value = "/deleteById/{id}")
    ResultVO deleteById(@PathVariable Integer id);

    /**
     * 通过Id list批量删除
     *
     * @param ids id列表
     * @return void
     */
    @ApiOperation(value = "通过Id list批量删除")
    @DeleteMapping(value = "/deleteByListId")
    ResultVO deleteByListId(@RequestBody List<Integer> ids);


    /**
     * 通过 processInstance identifierKey type获取当前的流程所实际使用的备件
     *
     * @param actualUseQueryDTO 关联id
     * @return 分页
     */
    @ApiOperation(value = "查询备件实际申请(返回分页)（数据库）")
    @PostMapping(value = "/getSpareActual")
    ResultVO<HvPage<ActualUseDTO>> getSpareActual(@RequestBody ActualUseQueryDTO actualUseQueryDTO);

    /**
     * 通过 processInstance identifierKey type获取当前的流程所实际使用的油品
     *
     * @param actualUseQueryDTO 关联id
     * @return 分页
     */
    @ApiOperation(value = "查询油品实际申请(返回分页)（数据库）")
    @PostMapping(value = "/getLubActual")
    ResultVO<HvPage<ActualUseDTO>> getLubActual(@RequestBody ActualUseQueryDTO actualUseQueryDTO);


    /**
     * 获取备件全部出库的申请项
     *
     * @param actualUseQueryDTO 查询条件
     * @return 全部信息
     */
    @Deprecated
    @ApiOperation(value = "获取备件全部申请项（Activiti）")
    @PostMapping(value = "/getSpareApplyThrough")
    ResultVO<List<ActualUseDTO>> getSpareApplyThrough(@RequestBody ActualUseQueryDTO actualUseQueryDTO);

    /**
     * 获取备件全部出库的申请项-维修（Activiti）
     *
     * @param actualUseQueryDTO 查询条件
     * @return 全部信息
     */
    @Deprecated
    @ApiOperation(value = "获取备件全部申请项-维修（Activiti）")
    @PostMapping(value = "/getSpareApplyThroughEquipment")
    ResultVO<List<ActualUseDTO>> getSpareApplyThroughEquipment(@RequestBody ActualUseQueryDTO actualUseQueryDTO);

    /**
     * 获取油品全部申请项
     *
     * @param actualUseQueryDTO 查询条件
     * @return 全部信息
     */
    @Deprecated
    @ApiOperation(value = "获取油品全部申请项（Activiti）")
    @PostMapping(value = "/getLubApplyThrough")
    ResultVO<List<ActualUseDTO>> getLubApplyThrough(@RequestBody ActualUseQueryDTO actualUseQueryDTO);

    /**
     * 获取油品全部申请项
     *
     * @param actualUseQueryDTO 查询条件
     * @return 全部信息
     */
    @Deprecated
    @ApiOperation(value = "获取油品全部申请项-维修（Activiti）")
    @PostMapping(value = "/getLubApplyThroughEquipment")
    ResultVO<List<ActualUseDTO>> getLubApplyThroughEquipment(@RequestBody ActualUseQueryDTO actualUseQueryDTO);

    /**
     * 通过BusinessKey获取备件申请表
     *
     * @param businessKey 唯一标识符
     * @return 申请单list
     */
    @Deprecated
    @ApiOperation(value = "通过BusinessKey获取备件申请表")
    @GetMapping(value = "/getSpareApply/{businessKey}")
    ResultVO<List<ApplyDTO>> getSpareApply(@PathVariable String businessKey);

    /**
     * 通过BusinessKey获取油品申请表
     *
     * @param businessKey 唯一标识符
     * @return 申请单list
     */
    @Deprecated
    @ApiOperation(value = "通过BusinessKey获取油品申请表")
    @GetMapping(value = "/getLubApply/{businessKey}")
    ResultVO<List<ApplyDTO>> getLubApply(@PathVariable String businessKey);

    /**
     * 通过BusinessKey获取备件申请通过的申请项数据
     *
     * @param businessKey 唯一标识符
     * @return 申请项 list
     */
    @Deprecated
    @ApiOperation(value = "通过BusinessKey获取全部申请通过的申请项数据")
    @GetMapping(value = "/getSpareApplyList/{businessKey}")
    ResultVO<List<ItemsSpareDTO>> getSpareApplyList(@PathVariable String businessKey);

    /**
     * 通过BusinessKey获取油品申请通过的申请项数据
     *
     * @param businessKey 唯一标识符
     * @return 申请项 list
     */
    @Deprecated
    @ApiOperation(value = "通过BusinessKey获取油品申请通过的申请项数据")
    @GetMapping(value = "/getLubApplyList/{businessKey}")
    ResultVO<List<ItemsLubDTO>> getLubApplyList(@PathVariable String businessKey);

    /**
     * 通过processInstanceIds 获取实际使用获取油品申请通过的申请项数据
     *
     * @param processInstanceIds 流程实例
     * @return 申请项 list
     */
    @Deprecated
    @ApiOperation(value = "通过processInstanceIds 获取实际使用获取油品申请通过的申请项数据")
    @PostMapping(value = "/getActualUseByProcessInstanceIds")
    ResultVO<List<ActualUseDTO>> getActualUseByProcessInstanceIds(@RequestBody List<String> processInstanceIds);
}
