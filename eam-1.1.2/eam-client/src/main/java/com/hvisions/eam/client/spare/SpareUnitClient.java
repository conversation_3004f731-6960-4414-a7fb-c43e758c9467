package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.publicstore.UnitDTO;
import com.hvisions.eam.query.spare.SpareUnitQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <p>Title:SpareUnitClient</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "eam", path = "/spareUnit", fallbackFactory = SpareUnitClientFallBack.class)
public interface SpareUnitClient {
    /**
     * 增加备件单位  改 通过 备件单位DTO
     *
     * @param unitDTO 备件单位DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "增加备件单位")
    @PostMapping(value = "/createSpareUnit")
    ResultVO<Integer> createSpareUnit(@RequestBody UnitDTO unitDTO);

    /**
     * 修改备件单位 改 通过 备件单位DTO
     *
     * @param unitDTO 备件单位DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "修改备件单位")
    @PutMapping(value = "/updateSpareUnit")
    ResultVO<Integer> updateSpareUnit(@RequestBody UnitDTO unitDTO);

    /**
     * 查询备件单位 通过ID
     *
     * @param id 备件单位DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "查询备件单位 通过ID")
    @GetMapping(value = "/getSpareUnitById/{id}")
    ResultVO<UnitDTO> getSpareUnitById(@PathVariable Integer id);

    /**
     * 删除通过ID
     *
     * @param id 单位ID
     * @return void
     */
    @ApiOperation(value = "删除通过ID")
    @DeleteMapping(value = "/deleteSpareUnitById/{id}")
    ResultVO deleteSpareUnitById(@PathVariable Integer id);

    /**
     * 分页查询 通过单位名称
     *
     * @param spareUnitQueryDTO 单位DTO
     * @return 分页信息
     */
    @ApiOperation(value = "分页查询 通过单位DTO")
    @PostMapping(value = "/getSpareUnitPageBySpareName")
    ResultVO<HvPage<UnitDTO>> getSpareUnitPageBySpareName(@RequestBody SpareUnitQueryDTO spareUnitQueryDTO);

    /**
     * 通过单位名称查询单位
     *
     * @param unitName 单位名称
     * @return 单位
     */
    @ApiOperation(value = "分页查询 通过单位DTO 如果没查到返回null")
    @GetMapping(value = "/getSpareUnitByUnitName/{unitName}")
    ResultVO<UnitDTO> getSpareUnitByUnitName(@PathVariable String unitName);
}
