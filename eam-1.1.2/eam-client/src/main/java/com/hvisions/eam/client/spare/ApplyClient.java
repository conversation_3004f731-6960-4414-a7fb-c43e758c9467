package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.activiti.lub.RootLubDTO;
import com.hvisions.eam.activiti.sapre.RootSpareDTO;
import com.hvisions.eam.dto.publicstore.HeaderDTO;
import com.hvisions.eam.dto.publicstore.LineDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title:ApplyClient</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "eam", path = "/apply", fallbackFactory = ApplyClientFallBack.class)
public interface ApplyClient {

    //----------------------------------= 入库申请 =---------------------------------------

    /**
     * 发起备件出/入库申请
     *
     * @param rootSpareDTO 出库申请申请单
     * @return 出库申请单
     */
    @ApiOperation(value = "发起备件出/入库申请")
    @PostMapping(value = "/applySpare")
    ResultVO<RootSpareDTO> applySpare(@RequestBody @Valid RootSpareDTO rootSpareDTO);

    /**
     * 发起油品出/入库申请
     *
     * @param rootLubDTO 申请单
     * @return 申请单
     */

    @ApiOperation(value = "发起油品出/入库申请")
    @PostMapping(value = "/applyLub")
    ResultVO<RootLubDTO> applyLub(@RequestBody @Valid RootLubDTO rootLubDTO);

    //----------------------------------= 审批 =------------------------------------------

    /**
     * 备件出/入库申请审批 当状态为通过时isPass 为2 驳回时isPass 为 3
     *
     * @param taskId 任务ID
     * @param isPass 是否通过
     * @return void
     */
    @Deprecated
    @ApiOperation(value = "备件 油品出/入库申请审批 当状态为通过时isPass 为2 驳回时isPass 为 3")
    @GetMapping(value = "/spareAndLubIsThrough/{taskId}/{isPass}")
    ResultVO spareAndLubIsThrough(@PathVariable String taskId, @PathVariable Integer isPass);

    /**
     * 备件出/入库申请审批 当状态为通过时isPass 为2 驳回时isPass 为 3
     *
     * @param taskId 任务ID
     * @param isPass 是否通过
     * @return void
     */
    @ApiOperation(value = "备件 出/入库申请审批 当状态为通过时isPass 为2 驳回时isPass 为 3")
    @GetMapping(value = "/spareIsThrough/{taskId}/{isPass}")
    ResultVO spareIsThrough(@PathVariable String taskId, @PathVariable Integer isPass);

    /**
     * 油品 出/入库申请审批 当状态为通过时isPass 为2 驳回时isPass 为 3
     *
     * @param taskId 任务ID
     * @param isPass 是否通过
     * @return void
     */
    @ApiOperation(value = "油品 出/入库申请审批 当状态为通过时isPass 为2 驳回时isPass 为 3")
    @GetMapping(value = "/lubIsThrough/{taskId}/{isPass}")
    ResultVO lubIsThrough(@PathVariable String taskId, @PathVariable Integer isPass);

    //------------------------------------= 出库 =--------------------------------------------------

    /**
     * 备件出库 库管
     *
     * @param processInstanceID 流程ID
     * @param isPass            是否通过
     * @return id
     */
    @Deprecated
    @ApiOperation(value = " 备件出库 库管 ")
    @GetMapping("/spareOut/{processInstanceID}/{isPass}")
    ResultVO<Integer> spareOut(@PathVariable String processInstanceID, @PathVariable Boolean isPass);

    /**
     * 油品出库 库管
     *
     * @param processInstanceID 流程id
     * @param isPass            是否出库
     * @return id
     */
    @Deprecated
    @ApiOperation(value = " 油品出库 库管 ")
    @GetMapping("/lubOut/{processInstanceID}/{isPass}")
    ResultVO<Integer> lubOut(@PathVariable String processInstanceID, @PathVariable Boolean isPass);

    /**
     * 出库 当前单号出库 (全部)
     *
     * @param headerId 头表id
     * @return void
     */
    @ApiOperation(value = "出库 全部出库")
    @GetMapping("/deliveryOfCargoFromStorage/{headerId}")
    ResultVO deliveryOfCargoFromStorage(Integer headerId);


    //------------------------------------= 过期 =--------------------------------------------------

    /**
     * 生成唯一不重复的单号 生成规则  年 月 日 流水号（四位）共计12位
     *
     * @param service 服务名
     * @return 唯一单号
     */
    @Deprecated
    @ApiOperation(value = "生成唯一不重复的单号 生成规则  年 月 日 流水号（四位）共计12位")
    @GetMapping(value = "/getSerialNumber/{service}")
    ResultVO<String> getSerialNumber(@PathVariable String service);

    /**
     * 生成唯一不重复的单号 生成规则  年 月 日 时 分 秒 随机数（四位）共计18位
     *
     * @return 唯一单号
     */
    @Deprecated
    @ApiOperation(value = "生成唯一不重复的单号 生成规则  年 月 日 时 分 秒 随机数（四位）共计18位")
    @GetMapping(value = "/getGodownReceiptNumber")
    ResultVO<String> getGodownReceiptNumber();

    //--------------------------------------= 库管查询 =------------------------------------------------

    /**
     * 获取全部待出库信息
     *
     * @param headerDTO 行表
     * @return 待出库信息
     */
    @PostMapping("/getHeader")
    @ApiOperation(" 获取全部待出库信息")
    ResultVO<HvPage<HeaderDTO>> getHeader(@RequestBody HeaderDTO headerDTO);

    /**
     * 修改出库信息数据
     *
     * @param lineDTO 行表
     * @return 行表id
     */
    @ApiOperation("修改出库信息数据")
    @PutMapping("/updateHeaderLine")
    ResultVO<Integer> updateHeaderLine(@RequestBody LineDTO lineDTO);

    /**
     * 增加出库信息
     *
     * @param lineDTO 行表
     * @return 行表id
     */
    @ApiOperation("修改出库信息数据")
    @PostMapping("/addHeaderLine")
    ResultVO<Integer> addHeaderLine(@RequestBody LineDTO lineDTO);

    /**
     * 修改备件出库信息
     *
     * @param lineDTOId 备件信息
     * @return void
     */
    @ApiOperation("修改出库信息数据")
    @DeleteMapping("/deleteHeaderLine/{lineDTOId}")
    ResultVO deleteHeaderLine(@PathVariable Integer lineDTOId);

    /**
     * 获取全部来源
     *
     * @return 来源集合
     */
    @ApiOperation(" 获取全部来源 ")
    @GetMapping("/getAllSource")
    ResultVO<List<String>> getAllSource();


}
