package com.hvisions.eam.service.autonomy.imp;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.eam.dao.ProjectGroupMapper;
import com.hvisions.eam.dto.autonomy.ProjectGroupCreateOrUpdateDTO;
import com.hvisions.eam.dto.autonomy.ProjectGroupDTO;
import com.hvisions.eam.entity.autonomy.HvAmInspectionProject;
import com.hvisions.eam.entity.autonomy.HvAmProjectGroup;
import com.hvisions.eam.enums.HExceptionEnum;
import com.hvisions.eam.repository.autonomy.InspectionProjectRepository;
import com.hvisions.eam.repository.autonomy.ProjectGroupRepository;
import com.hvisions.eam.service.autonomy.ProjectGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
@Service
public class ProjectGroupServiceImpl extends ServiceImpl<ProjectGroupMapper, HvAmProjectGroup> implements ProjectGroupService {

    /**
     * jpa仓储层
     */
    private final ProjectGroupRepository projectGroupRepository;
    private final InspectionProjectRepository inspectionProjectRepository;

    @Autowired
    public ProjectGroupServiceImpl(ProjectGroupRepository projectGroupRepository, InspectionProjectRepository inspectionProjectRepository) {
        this.projectGroupRepository = projectGroupRepository;
        this.inspectionProjectRepository = inspectionProjectRepository;
    }

    @Override
    public int add(ProjectGroupCreateOrUpdateDTO dto) {

        if (dto.getParentId()!=null){
            if (dto.getParentId()>0){
                HvAmProjectGroup temp = this.getById(dto.getParentId());
                if (temp==null){
                    throw new BaseKnownException(HExceptionEnum.PARENT_GROUP_DOES_NOT_EXIST);
                }
            }
        }
        HvAmProjectGroup hvAmProjectGroup = DtoMapper.convert(dto, HvAmProjectGroup.class);
        return projectGroupRepository.save(hvAmProjectGroup).getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Integer id) {

        //判断其下级是否还有组
        List<HvAmProjectGroup> temp = projectGroupRepository.findAllByParentId(id);
        if (temp.size() > 0){
            throw new BaseKnownException(HExceptionEnum.DELETE_ITEM_ERROR);
        }
        //判断其下级是否还有检查项目
        List<HvAmInspectionProject> list = inspectionProjectRepository.findAllByGroupId(id);
        if (temp.size() > 0){
            throw new BaseKnownException(HExceptionEnum.DELETE_ITEM_ERROR);
        }

        projectGroupRepository.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(ProjectGroupCreateOrUpdateDTO dto) {

        HvAmProjectGroup temp = this.getById(dto.getId());
        if (temp==null){
            throw new BaseKnownException(HExceptionEnum.DATA_IN_WRONG_FORMAT);
        }
        HvAmProjectGroup hvAmProjectGroup = DtoMapper.convert(dto, HvAmProjectGroup.class);
        hvAmProjectGroup.setParentId(temp.getParentId());

        return projectGroupRepository.save(hvAmProjectGroup).getId();
    }

    @Override
    public List<ProjectGroupDTO> queryList() {

        List<ProjectGroupDTO> list = DtoMapper.convertList(projectGroupRepository.findAllByParentIdIsNull(),ProjectGroupDTO.class);

        return queryById(list);
    }

    /**
     * 递归查询子级信息
     * <AUTHOR>
     * @param list 查询条件
     * @return 分组数据list
     */
    @Override
    public List<ProjectGroupDTO> queryById(List<ProjectGroupDTO> list) {

        list.forEach(r -> {
            List<ProjectGroupDTO> childNodeList = DtoMapper.convertList(projectGroupRepository.findAllByParentId(r.getId()),ProjectGroupDTO.class);
            if (CollectionUtils.isNotEmpty(childNodeList)){
                r.setChildNode(childNodeList);
                queryById(childNodeList);
            }

        });

        return list;
    }
}
