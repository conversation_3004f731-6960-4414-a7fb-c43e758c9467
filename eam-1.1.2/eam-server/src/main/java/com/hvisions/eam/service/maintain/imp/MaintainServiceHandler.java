package com.hvisions.eam.service.maintain.imp;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.lub.LubricatingDTO;
import com.hvisions.eam.dto.spare.SpareDTO;
import com.hvisions.eam.service.lub.LubService;
import com.hvisions.eam.service.spare.SpareService;
import com.hvisions.hiperbase.client.EquipmentFeignClient;
import com.hvisions.hiperbase.client.EquipmentTypeClient;
import com.hvisions.hiperbase.equipment.EquipmentDTO;
import com.hvisions.hiperbase.equipment.EquipmentTypeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title: ServiceHandler</p>
 * <p>Description: 处理接口问题</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2022/5/18</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class MaintainServiceHandler {
    private final EquipmentFeignClient equipmentFeignClient;
    private final EquipmentTypeClient equipmentTypeClient;
    private final SpareService spareService;
    private final LubService lubService;

    @Autowired
    public MaintainServiceHandler(EquipmentFeignClient equipmentFeignClient,
                                  EquipmentTypeClient equipmentTypeClient,
                                  SpareService spareService,
                                  LubService lubService) {
        this.equipmentFeignClient = equipmentFeignClient;
        this.equipmentTypeClient = equipmentTypeClient;
        this.spareService = spareService;
        this.lubService = lubService;
    }

    /**
     * 获取设备数据
     *
     * @param id id
     * @return 设备信息
     */
    public EquipmentDTO findEquipment(Integer id) {
        ResultVO<EquipmentDTO> result = equipmentFeignClient.getEquipmentById(id);
        if (!result.isSuccess()) {
            log.warn("设备接口查询错误,{}", result.getMessage());
            return null;
        }
        return Optional.ofNullable(result.getData()).orElse(null);
    }

    /**
     * 获取设备数据
     *
     * @param ids id列表
     * @return 设备信息
     */
    public List<EquipmentDTO> findEquipmentDTO(List<Integer> ids) {
        ResultVO<List<EquipmentDTO>> result = equipmentFeignClient.getEquipmentById(ids);
        if (!result.isSuccess()) {
            log.warn("设备接口查询错误,{}", result.getMessage());
            return Collections.emptyList();
        }
        return Optional.ofNullable(result.getData()).orElse(Collections.emptyList());
    }


    /**
     * 获取设备类型
     *
     * @param ids 类型id列表
     * @return 类型信息列表
     */
    public List<EquipmentTypeDTO> findEquipmentType(List<Integer> ids) {
        ResultVO<List<EquipmentTypeDTO>> result = equipmentTypeClient.getTypeAllByIdIn(ids);
        if (!result.isSuccess()) {
            log.warn("设备接口查询错误,{}", result.getMessage());
            return Collections.emptyList();
        }
        return Optional.ofNullable(result.getData()).orElse(Collections.emptyList());
    }

    /**
     * 获取子类型
     *
     * @param parentId 父级id
     * @return 子类型
     */
    public List<EquipmentTypeDTO> findAllChildTypeByParentId(Integer parentId) {
        ResultVO<List<EquipmentTypeDTO>> result =
            equipmentTypeClient.findAllChildTypeByParentId(parentId);
        if (!result.isSuccess()) {
            log.warn("设备接口查询错误,{}", result.getMessage());
            return Collections.emptyList();
        }
        return Optional.ofNullable(result.getData()).orElse(Collections.emptyList());
    }

    /**
     * 获取设备数据
     *
     * @param codes 编码列表
     * @return 设备信息
     */
    public List<EquipmentDTO> getAllEquipmentByCodeList(List<String> codes) {
        ResultVO<List<EquipmentDTO>> result = equipmentFeignClient.getAllEquipmentsByCodeIn(codes);
        if (!result.isSuccess()) {
            log.warn("设备接口查询错误,{}", result.getMessage());
            return Collections.emptyList();
        }
        return Optional.ofNullable(result.getData()).orElse(Collections.emptyList());
    }

    /**
     * 获取设备数据
     *
     * @param codes 编码列表
     * @return 设备信息
     */
    public List<EquipmentTypeDTO> getAllEquipmentTypeByCodeList(List<String> codes) {
        ResultVO<List<EquipmentTypeDTO>> result = equipmentTypeClient.getAllByEquipmentTypeCodeIn(codes);
        if (!result.isSuccess()) {
            log.warn("设备接口查询错误,{}", result.getMessage());
            return Collections.emptyList();
        }
        return Optional.ofNullable(result.getData()).orElse(Collections.emptyList());
    }

    /**
     * 获取设备数据
     *
     * @param codes 编码列表
     * @return 设备信息
     */
    public List<SpareDTO> getAllSpareByCodeList(List<String> codes) {
        List<SpareDTO> result = spareService.getSpareBySpareCodeList(codes);
        if (CollectionUtils.isEmpty(result)) {
            return Collections.emptyList();
        }
        return Optional.of(result).orElse(Collections.emptyList());
    }

    /**
     * 获取设备数据
     *
     * @param codes 编码列表
     * @return 设备信息
     */
    public List<LubricatingDTO> getAllLubByCodeList(List<String> codes) {
        List<LubricatingDTO> result = lubService.getLubByCodeList(codes);
        if (CollectionUtils.isEmpty(result)) {
            return Collections.emptyList();
        }
        return Optional.of(result).orElse(Collections.emptyList());
    }

}









