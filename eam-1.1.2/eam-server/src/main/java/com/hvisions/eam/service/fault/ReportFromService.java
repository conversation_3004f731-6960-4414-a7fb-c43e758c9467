package com.hvisions.eam.service.fault;


import com.hvisions.eam.reportfrom.FaultDrawingBoardDTO;
import com.hvisions.eam.reportfrom.ReportFromDTO;
import com.hvisions.eam.reportfrom.ReportGroupDTO;

import java.util.Date;
import java.util.List;

/**
 * <p>Title:ReportFromService</p>
 * <p>Description: 报表</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/4</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
public interface ReportFromService {

    /**
     * 通过 设备名称 故障等级 故障类型名称 开始时间 结束时间 获取设备信息
     *
     * @param reportFromDTO 报表信息
     * @return 报表信息
     */
    List<ReportFromDTO> getReportFromDTO(ReportFromDTO reportFromDTO);

    /**
     * 今日故障次数
     *
     * @return 故障次数
     */
    Integer getTodayEquipmentNumber();

    /**
     * 本周故障次数
     *
     * @return 故障次数
     */
    Integer getWeekEquipmentNumber();

    /**
     * 通过 设备名称 故障等级 故障类型名称 开始时间 结束时间 获取设备信息 分组
     *
     * @param reportFromDTO 报表信息
     * @return 报表信息
     */
    List<ReportGroupDTO> getReportGroup(ReportFromDTO reportFromDTO);

    /**
     * 获取设备维修看板
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 看板
     */
    List<FaultDrawingBoardDTO> getFaultDrawingBoardDTO(Date startTime, Date endTime);
}
