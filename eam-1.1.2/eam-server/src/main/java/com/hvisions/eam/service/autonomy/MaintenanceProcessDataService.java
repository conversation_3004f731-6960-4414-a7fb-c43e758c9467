package com.hvisions.eam.service.autonomy;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.eam.entity.autonomy.HvAmAutonomyMaintenanceProcessData;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-11
 */
public interface MaintenanceProcessDataService extends IService<HvAmAutonomyMaintenanceProcessData> {

    //查询时间范围内的计划
    List<HvAmAutonomyMaintenanceProcessData> getDataList(String startTime, String endTime);

    List<HvAmAutonomyMaintenanceProcessData> getByIdS(List<Integer> idList);
}
