package com.hvisions.eam.service.autonomy;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.eam.dto.autonomy.InspectionPlanCreateOrUpdateDTO;
import com.hvisions.eam.dto.autonomy.InspectionPlanDTO;
import com.hvisions.eam.dto.autonomy.InspectionPlanQueryDTO;
import com.hvisions.eam.dto.autonomy.InspectionProjectDTO;
import com.hvisions.eam.entity.autonomy.HvAmInspectionPlan;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-11
 */
public interface InspectionPlanService extends IService<HvAmInspectionPlan> {

    /**
     * 新增
     * <AUTHOR>
     * @param dto 新增dto
     * @return int 主键
     */
    int add(InspectionPlanCreateOrUpdateDTO dto);

    /**
     * 删除单个
     * <AUTHOR>
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 批量删除
     * <AUTHOR>
     * @param idList 主键list
     */
    void deleteList(List<Integer> idList);

    /**
     * 修改
     * <AUTHOR>
     * @param dto 入参dto
     * @return int
     */
    int update(InspectionPlanCreateOrUpdateDTO dto);

    /**
     * 分页查询
     * <AUTHOR>
     * @param dto 入参dto
     * @return 分页对象
     */
    Page<InspectionPlanDTO> queryList(InspectionPlanQueryDTO dto);

    /**
     * 获取计划详情
     * <AUTHOR>
     * @param id 主键
     * @return InspectionPlanDTO
     */
    InspectionPlanDTO getById(Integer id);

    /**
     *通过计划id手动触发计划
     * <AUTHOR>
     * @param planId 计划id
     */
    void triggerPlanById(Integer planId);

    /**
     *创建临时任务
     * <AUTHOR>
     * @param dto 入参dto
     */
    void createTemporaryTask(InspectionPlanCreateOrUpdateDTO dto);

    /**
     * 通过 计划ID 查询出对应的 项目详细信息
     * <AUTHOR>
     * @param planId 计划id
     * @return 项目信息
     */
    List<InspectionProjectDTO> getProjectList(Integer planId);
}
