package com.hvisions.eam.service.maintain;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.eam.dto.maintain.MaintainItemDTO;
import com.hvisions.eam.dto.maintain.MaintainItemQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>Title: MaintainItemService</p >
 * <p>Description: 保养项目Service接口</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/18</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface MaintainItemService {
    /**
     * 删除单个保养项目
     *
     * @param id 保养项目id
     */
    void deleteMaintainItemById(Integer id);

    /**
     * 批量删除保养项目
     *
     * @param idList 要删除的保养项目id集合
     */
    void deleteMaintainItemByIdList(List<Integer> idList);

    /**
     * 根据设备型号，保养部位查询保养项目信息
     *
     * @param maintainItemQueryDTO 查询条件
     * @return 保养项目信息
     */
    Page<MaintainItemDTO> getMaintainByQuery(MaintainItemQueryDTO maintainItemQueryDTO);


    /**
     * 新增保养项目信息
     *
     * @param maintainItemCreateOrUpdateDTO 保养项目DTO
     * @return 新增保养项目信息id
     */
    Integer save(MaintainItemDTO maintainItemCreateOrUpdateDTO);

    /**
     * 是否启用保养项目
     *
     * @param id 被操作的保养项目id
     * @return 返回被操作的保养项目id
     */
    Boolean toggle(Integer id);


    /**
     * 获取所有周期
     *
     * @return 周期集合
     */
    List<String> getAllCycles();


    /**
     * 获取保养项目信息
     *
     * @param ids id列表
     * @return 保养项目信息列表
     */
    List<MaintainItemDTO> getList(List<Integer> ids);


    /**
     * excel导出模板下载
     *
     * @return 导出对象
     */
    ExcelExportDto exportTemplate();

    /**
     * 导入保养项目
     *
     * @param file 文件对象
     * @return 导入结果
     */
    ImportResult importData(MultipartFile file);

    /**
     * 导出保养项目
     *
     * @param ids 保养项目ID
     * @return 导出结果对象
     */
    ExcelExportDto export(List<Integer> ids);

    /**
     * 获取保养项目详情
     *
     * @param id 保养项目id
     * @return 保养项目信息
     */
    MaintainItemDTO getById(Integer id);
    /**
     * 获取保养项目详情
     *
     * @param itemCode 保养项目id
     * @return 保养项目信息
     */
    MaintainItemDTO getByCode(String itemCode);
}
