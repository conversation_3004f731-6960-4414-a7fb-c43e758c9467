package com.hvisions.eam.service.spare;

import com.hvisions.eam.dto.spare.SpareToShelveDTO;
import com.hvisions.eam.query.spare.SpareToShelveQueryDTO;
import io.vavr.Tuple2;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Set;

/**
 * <p>Title:SpareToShelveService</p>
 * <p>Description:备件库位关系</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
public interface SpareToShelveService {

    /**
     * 增加型号  改 通过 型号DTO
     *
     * @param spareToShelveDTO 型号DTO
     * @return 新增数据的ID
     */
    Integer save(SpareToShelveDTO spareToShelveDTO);

    /**
     * 删除型号 通过 ID
     *
     * @param id 型号ID
     */
    void deleteById(Integer id);

    /**
     * 查询型号 通过型号ID
     *
     * @param id 型号ID
     * @return 型号DTO
     */
    SpareToShelveDTO findById(Integer id);

    /**
     * 通过 备件ID 查询 库位ID
     *
     * @param spareId 备件ID
     * @return 库位ID集合
     */
    Set<Integer> findShelveIdBySpareId(Integer spareId);

    /**
     * 通过 库位ID 查询 备件ID
     *
     * @param spareId 库位ID
     * @return 备件ID集合
     */
    Set<Integer> findSpareIdByShelveId(Integer spareId);

    /**
     * 通过备件查询 当前备件总数量
     *
     * @param spareId 备件ID
     * @return 备件的所有数量
     */
    Integer sumSpareNumBySpareId(Integer spareId);

    /**
     * 查询库存备件ID 通过备件ID
     *
     * @param spareId 备件ID
     * @return 库存备件ID集合
     */
    Set<Integer> findAllBySpareId(Integer spareId);


    /**
     * 查询库存备件ID 通过库存ID
     *
     * @param shelveId 库存ID
     * @return 库存备件ID集合
     */
    Set<Integer> findAllByShelveId(Integer shelveId);

    /**
     * 通过库存ID获取当前仓库的所有备件数量
     *
     * @param shelveId 库存Id
     * @return 当前仓库的总数量
     */
    Integer findAllByShelveIds(Integer shelveId);

    /**
     * 查询全部 通过 备件编码 备件名称 库房编码 库房名称 批次号
     *
     * @param spareToShelveQueryDTO 备件库房分页DTO
     * @return 分页信息
     */
    Page<SpareToShelveQueryDTO> findAllBySpareCodeAndSpareNameAndShelveCodeAndShelveNameAndBatchNumber(SpareToShelveQueryDTO spareToShelveQueryDTO);

    /**
     * 备件库房查询
     *
     * @param spareToShelveQueryDTO 备件库房
     * @return 分页
     */
    Page<SpareToShelveDTO> getSpareToShelve(SpareToShelveQueryDTO spareToShelveQueryDTO);


    /**
     * 判断当前DTO数据是否在数据中存在
     *
     * @param spareToShelveDTO 判断DTO
     * @return true 存在   false不存在
     */
    boolean isExist(SpareToShelveDTO spareToShelveDTO);


    /**
     * 修改库存数量
     *
     * @param spareToShelveDTO 库存信息
     * @return id
     */
    Integer update(SpareToShelveDTO spareToShelveDTO);

    /**
     * 获取备件相关的库存信息
     *
     * @param spareId 备件id
     * @return 库存分组的库存信息
     */
    List<Tuple2<String, List<SpareToShelveDTO>>> getSpareStoreInfo(Integer spareId);
}
