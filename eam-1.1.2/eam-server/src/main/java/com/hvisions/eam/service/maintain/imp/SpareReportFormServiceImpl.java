package com.hvisions.eam.service.maintain.imp;

import com.hvisions.activiti.client.HistoryClient;
import com.hvisions.eam.dao.SpareReportMapper;
import com.hvisions.eam.dto.repair.spare.*;
import com.hvisions.eam.service.maintain.SpareReportFormService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Title: SpareReportFormServiceImpl</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/1/4</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Service
public class SpareReportFormServiceImpl implements SpareReportFormService {


    @Autowired
    SpareReportMapper spareReportMapper;

    @Autowired
    HistoryClient client;

    /**
     * 备件使用统计
     *
     * @param sparePartUsageQueryDTO 备件使用统计查询条件
     * @return 备件使用统计记录
     */
    @Override
    public List<SparePartUsageDTO> sparePartUsage(SparePartUsageQueryDTO sparePartUsageQueryDTO) {
        return spareReportMapper.sparePartUsage(sparePartUsageQueryDTO);
    }

    /**
     * 备件周转率
     *
     * @param turnoverQueryDTO 周转率查询条件
     * @return 周转率信息
     */
    @Override
    public List<MonthTurnoverDTO> spareTurnover(TurnoverQueryDTO turnoverQueryDTO) {

        List<MonthTurnoverDTO> monthTurnoverDTOS = spareReportMapper.spareTurnover(turnoverQueryDTO);
        List<StoreNumDTO> storeNum = spareReportMapper.getStoreNum(turnoverQueryDTO);
        List<MonthTurnoverDTO> objects = new ArrayList<>();

        for (int i = 1; i < turnoverQueryDTO.getMonthTime(); i++) {
            TurnoverQueryDTO turnoverQueryDTO1 = new TurnoverQueryDTO();
            turnoverQueryDTO1.setMonthTime(i);
            turnoverQueryDTO1.setYearTime(turnoverQueryDTO.getYearTime());
            List<MonthTurnoverDTO> monthTurnoverDTOS1 = spareReportMapper.spareTurnover(turnoverQueryDTO);
            objects.addAll(monthTurnoverDTOS1);
        }


        for (MonthTurnoverDTO monthTurnoverDTO : monthTurnoverDTOS) {
            for (StoreNumDTO storeNumDTO : storeNum) {
                if (monthTurnoverDTO.getSparePartCode().equals(storeNumDTO.getSpareCode())) {
                    monthTurnoverDTO.setSparePartUsage(storeNumDTO.getNumber());
                }
            }

            //计算上年库存余粮
            TurnoverQueryDTO lastYear = new TurnoverQueryDTO();
            lastYear.setYearTime(turnoverQueryDTO.getYearTime() - 1);
            List<StoreNumDTO> storeNum1 = spareReportMapper.getStoreNum(lastYear);
            List<BigDecimal> collect = storeNum1.stream().map(StoreNumDTO::getNumber).collect(Collectors.toList());
            BigDecimal lastYearNum = new BigDecimal(0);
            for (BigDecimal bigDecimal : collect) {
                lastYearNum.add(bigDecimal);
            }
            monthTurnoverDTO.setLastYearStock(lastYearNum);
            //计算往月月底库存总和
            List<MonthTurnoverDTO> lastMothSumStockList =
                    objects.stream().filter(t -> t.getSparePartCode()
                            .equals(monthTurnoverDTO.getSparePartCode())).collect(Collectors.toList());
            BigDecimal lastMothSumStock = new BigDecimal(0);
            BigDecimal usage = new BigDecimal(0);
            for (MonthTurnoverDTO turnoverDTO : lastMothSumStockList) {
                lastMothSumStock.add(turnoverDTO.getMonthStock() == null ? new BigDecimal(0) : turnoverDTO.getMonthStock());
                usage.add(turnoverDTO.getSparePartUsage() == null ? new BigDecimal(0) : turnoverDTO.getSparePartUsage());
                if (turnoverDTO.getMonthTime().equals(turnoverQueryDTO.getMonthTime() - 1)) {
                    //加上月月底库存
                    monthTurnoverDTO.setLastMonthStock(turnoverDTO.getMonthStock());
                }
            }
            monthTurnoverDTO.setLastMonthStock(lastMothSumStock);
            monthTurnoverDTO.setLastSparePartUsage(usage);
        }

        return monthTurnoverDTOS;
    }

    /**
     * 当月故障统计报表
     *
     * @param equipmentFailureQueryDTO 查询条件
     * @return 统计记录
     */
    @Override
    public List<EquipmentFailureDTO> equipmentFailure(EquipmentFailureQueryDTO equipmentFailureQueryDTO) {
        List<EquipmentFailureDTO> equipmentFailureDTOS = spareReportMapper.equipmentFailure(equipmentFailureQueryDTO);
        List<FaultNumDTO> faultNum = spareReportMapper.getFaultNum(equipmentFailureQueryDTO);
        for (EquipmentFailureDTO equipmentFailureDTO : equipmentFailureDTOS) {
            for (FaultNumDTO faultNumDTO : faultNum) {
                if ((equipmentFailureDTO.getEquipmentId()).equals(faultNumDTO.getEquipmentId())) {
                    equipmentFailureDTO.setFailureNum(faultNumDTO.getFaultNumber());
                }
            }
        }
        return equipmentFailureDTOS;
    }
}