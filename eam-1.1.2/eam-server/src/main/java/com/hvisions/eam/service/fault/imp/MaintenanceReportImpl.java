package com.hvisions.eam.service.fault.imp;

import com.hvisions.activiti.client.ActivitiClient;
import com.hvisions.activiti.dto.instance.ProcessInstanceStartDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.activiti.fault.RootEquipmentFaultDTO;
import com.hvisions.eam.activiti.fault.VariablesFaultDTO;
import com.hvisions.eam.entity.HvEamSerialNumber;
import com.hvisions.eam.enums.ObjectTypeEnum;
import com.hvisions.eam.repository.fault.SerialNumberRepository;
import com.hvisions.eam.service.fault.MaintenanceReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;

/**
 * <p>Title:MaintenanceReportImpl</p>
 * <p>Description:设备报修</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/1</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Slf4j
@Service
public class MaintenanceReportImpl implements MaintenanceReportService {

    /**
     * activitiClient 接口
     */
    private final ActivitiClient activitiClient;

    private final SerialNumberRepository serialNumberRepository;


    @Autowired
    @SuppressWarnings("all")
    public MaintenanceReportImpl(ActivitiClient activitiClient,
                                 SerialNumberRepository serialNumberRepository) {
        this.activitiClient = activitiClient;
        this.serialNumberRepository = serialNumberRepository;
    }

    /**
     * 设备报修申请
     *
     * @param rootEquipmentFaultDTO 报修申请单
     * @return 报修申请单
     */
    @Override
    public ResultVO applyEquipmentFault(RootEquipmentFaultDTO rootEquipmentFaultDTO) {
        //获取维修信息
        VariablesFaultDTO variables = rootEquipmentFaultDTO.getVariables();
        if (variables == null) {
            throw new BaseKnownException(10000, "参数不能为空");
        }
        //获取单号
        variables.setReceiptNumber(getSerialNumber("equipment-fault"));

        //创建新类型DTO
        ProcessInstanceStartDTO processInstanceStartDTO = new ProcessInstanceStartDTO();
        processInstanceStartDTO.setBusinessKey(rootEquipmentFaultDTO.getVariables().getEquipmentId().toString());
        processInstanceStartDTO.setProcessDefinitionKey("equipment-maintenance");
        try {
            //dto转map
            Map<String, Object> map = DtoMapper.convertMap(rootEquipmentFaultDTO.getVariables());
            //设置候选组
            if (rootEquipmentFaultDTO.getVariables().getCandidateGroups() == null) {
                map.put("candidateGroups", new ArrayList<>());
            }
            processInstanceStartDTO.setVariables(map);
        } catch (Exception e) {
            log.info("DTO转换error :" + e.toString());
            throw new BaseKnownException(ObjectTypeEnum.DTO_CONVERT_MAP_EXCEPTION);
        }
        //创建流程
        return activitiClient.startProcessInstance(processInstanceStartDTO);
    }

    /**
     * 获取单号
     *
     * @param service 服务名
     * @return 单号
     */
    @Override
    public String getSerialNumber(String service) {
        //获取当前天数
        Calendar cal1 = Calendar.getInstance();
        //月
        String month = 1 + cal1.get(Calendar.MONTH) + "";
        //位数不足 用0补齐
        if (month.length() < 2) {
            month = "0" + month;
        }
        //日
        String newDate = cal1.get(Calendar.DATE) + "";
        //位数不足 用0补齐
        if (newDate.length() < 2) {
            newDate = "0" + newDate;
        }
        //时间拼装 年 月 日
        String date = cal1.get(Calendar.YEAR) + month + newDate;
        //通过服务获取该服务的
        HvEamSerialNumber byService = serialNumberRepository.findByService(service);

        //当查询的服务不存在的情况下 添加当前数据
        if (byService == null) {
            byService = new HvEamSerialNumber();
            byService.setNumber(1);
            byService.setId(0);
            byService.setService(service);
            byService.setDay(new Date(System.currentTimeMillis()));
            serialNumberRepository.save(byService);
            return date + "0001";
        }

        //取出数据中的天数
        Calendar cal = Calendar.getInstance();
        cal.setTime(byService.getDay());
        int date1 = cal.get(Calendar.DATE);

        //如果天数不等 表示是第二天
        if (date1 != cal1.get(Calendar.DATE)) {
            //日期更新
            byService.setDay(new Date(System.currentTimeMillis()));
            //流水号更新
            byService.setNumber(1);
            serialNumberRepository.save(byService);
            return date + "0001";
        } else {
            //获取当前流水号
            Integer number = byService.getNumber();
            //流水号自增
            byService.setNumber(++number);
            //记录数据库
            serialNumberRepository.save(byService);
            //位数补齐操作
            String a = "0000" + number;
            return date + a.substring(a.length() - 4);
        }
    }

}
