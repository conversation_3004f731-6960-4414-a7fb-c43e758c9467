package com.hvisions.eam.service.spare;


import com.hvisions.eam.dto.spare.SpareDTO;
import com.hvisions.eam.query.spare.SpareQueryDTO;
import com.hvisions.eam.entity.spare.HvEamSpare;
import org.springframework.data.domain.Page;

import java.util.List;


/**
 * 备件服务
 *
 * <AUTHOR>
 */
public interface SpareService {


    /**
     * 增加备件 改 通过 备件DTO
     *
     * @param spareDTO 备件DTO
     * @return 新增数据的ID
     */
    Integer save(SpareDTO spareDTO);

    /**
     * 删除备件 通过 ID
     *
     * @param id 备件ID
     */
    void deleteById(Integer id);

    /**
     * 查询备件 通过备件ID
     *
     * @param id 备件ID
     * @return 备件DTO
     */
    SpareDTO findById(Integer id);

    /**
     * 分页查询
     * 通过 备件编码 备件名称 小类ID 是否关键部位  联合查询
     *
     * @param spareQueryDTO 备件分页DTO
     * @return 分页信息
     */
    Page<SpareDTO> findAllBySpareCodeAndSpareNameAndSpareTypeSub(SpareQueryDTO spareQueryDTO);

    /**
     * 通过ID列表查询备件列表
     *
     * @param ids id列表
     * @return 备件列表
     */
    List<SpareDTO> getSpareListByIdList(List<Integer> ids);

    /**
     * 通过批次号 和 解析规则中的服务名获取 备件信息
     *
     * @param service     解析规则中的服务名获取
     * @param batchNumber a
     * @return 备件基础信息
     */
    SpareDTO getSpareByBatchNumber(String service, String batchNumber);


    /**
     * 通过备件code获取备件基础信息
     *
     * @param spareCods 备件code
     * @return 备件基础信息
     */
    SpareDTO getSpareBySpareCode(String spareCods);

    /**
     * 根据id列表查询
     * @param spareIds 备件id列表
     * @return 备件信息
     */
    List<SpareDTO> findAllById(List<Integer> spareIds);


    /**
     * 分页查询
     *
     * @param spareQueryDTO 查询条件
     * @return 备件信息
     */
    Page<SpareDTO> getSparePageByQuery(SpareQueryDTO spareQueryDTO);

    /**
     * 通过备件编码code 查询备件
     *
     * @param spareCodeList 编码列表
     * @return 备件
     */
    List<SpareDTO> getSpareBySpareCodeList(List<String> spareCodeList);
}