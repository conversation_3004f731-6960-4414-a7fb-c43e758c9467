package com.hvisions.eam.service.inspect;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.eam.dto.inspect.item.InspectItemDTO;
import com.hvisions.eam.dto.inspect.item.InspectItemQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <p>Title: InspectItemService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/27</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface InspectItemService {
    /**
     * 新增点检项目
     *
     * @param inspectItemCreateOrUpdateDTO 点检项目DTO
     * @return 新增点检项目id
     */
    Integer save(InspectItemDTO inspectItemCreateOrUpdateDTO);


    /**
     * 删除单个
     *
     * @param id id
     */
    void deleteById(Integer id);

    /**
     * 批量删除
     *
     * @param idList id集合
     */
    void deleteByIdList(List<Integer> idList);



    /**
     * 条件分页查询点巡检
     *
     * @param itemQueryDTO 查询条件
     * @return 点检项目分页数据
     */
    Page<InspectItemDTO> getInspectByQuery(InspectItemQueryDTO itemQueryDTO);

    /**
     * 是否启用
     *
     * @param id 被操作的项目id
     * @return 被操作的id
     */
    Boolean toggle(Integer id);



    /**
     * excel导出模板下载
     *
     * @return 导出对象
     */
    ExcelExportDto exportTemplate();

    /**
     * 导入保养项目
     *
     * @param file 文件对象
     * @return 导入结果
     */
    ImportResult importData(MultipartFile file) throws IllegalAccessException, ParseException, IOException;

    /**
     * 导出保养项目
     *
     * @param ids 保养项目ID
     * @return 导出结果对象
     */
    ExcelExportDto exportData(List<Integer> ids);

    List<String> getAllCycle();
    /**
     * 根据id获取保养项目详情
     *
     * @param id 保养项目id
     * @return 保养项目信息
     */
    InspectItemDTO getById(Integer id);
    /**
     * 根据编码获取保养项目详情
     *
     * @param itemCode 保养项目编码
     * @return 保养项目信息
     */
    InspectItemDTO getByCode(String itemCode);
}
