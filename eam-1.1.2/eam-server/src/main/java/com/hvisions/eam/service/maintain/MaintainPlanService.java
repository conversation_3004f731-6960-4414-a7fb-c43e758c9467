package com.hvisions.eam.service.maintain;

import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.eam.dto.maintain.MaintainPlanDTO;
import com.hvisions.eam.dto.maintain.MaintainPlanQuery;
import com.hvisions.eam.dto.maintain.MaintainPlanRejectDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <p>Title: MaintainPlanService</p >
 * <p>Description: 保养计划Service接口</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/19</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface MaintainPlanService {

    /**
     * 查询保养计划
     *
     * @param maintainPlanQuery 查询条件
     * @return 保养计划分页数据
     */
    Page<MaintainPlanDTO> getAll(MaintainPlanQuery maintainPlanQuery);

    /**
     * 获取计划详情
     *
     * @param planId 计划id
     * @return 计划详情
     */
    MaintainPlanDTO getById(Integer planId);

    /**
     * 删除单个保养计划
     *
     * @param id 保养计划id
     */
    void deleteMaintainPlanById(Integer id);

    /**
     * 批量删除保养计划
     *
     * @param idList id集合
     */
    void deleteMaintainPlanByIdList(List<Integer> idList);



    /**
     * 审批通过计划
     *  @param taskIds 任务ids
     * @param userInfo 用户信息
     * @param token token
     */
    void startPlanById(List<String> taskIds, UserInfoDTO userInfo,String token);

    /**
     * 驳回计划
     *
     * @param maintainPlanRejectDTO 驳回DTO
     * @param userInfo 用户信息
     * @param token token
     */
    void refusePlanById(MaintainPlanRejectDTO maintainPlanRejectDTO, UserInfoDTO userInfo,String token);

    /**
     * 新增无内容计划
     *
     * @param maintainPlanCreateOrUpdateDTO 计划DTO
     * @return 新增的计划id
     */
    Integer createMaintainPlan(MaintainPlanDTO maintainPlanCreateOrUpdateDTO);

    /**
     * 编辑保养计划（无保养内容）
     *
     * @param maintainPlanCreateOrUpdateDTO 保养计划DTO
     * @return 编辑的计划id
     */
    Integer updateMaintainPlan(MaintainPlanDTO maintainPlanCreateOrUpdateDTO);

    /**
     * 强制停用计划
     *
     * @param ids id列表
     */
    void forcedStopMaintainPlan(List<Integer> ids);

    /**
     * 根据timerId查看绑定个数
     *
     * @param timerId timerId
     * @return 次数
     */
    Integer checkBindingByTimerId(Integer timerId);

    /**
     * 通过计划id手动触发计划
     *
     * @param planId 任务id
     */
    void triggerPlanById(Integer planId);



    /**
     * 根据ItmerId触发计划
     * @param id timerId
     */
    void triggerPlanByTimerId(Integer id);
    /**
     * 创建临时任务
     *
     * @param dto 保养计划信息
     */
    void createTemporaryTask(MaintainPlanDTO dto);
}
