package com.hvisions.eam.service.spare.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.eam.configuration.spare.StoreConfig;
import com.hvisions.eam.dto.SysBaseDTO;
import com.hvisions.eam.enums.StoreExceptionEnum;
import com.hvisions.eam.dao.SpareToShelveMapper;
import com.hvisions.eam.dto.spare.SpareToShelveDTO;
import com.hvisions.eam.query.spare.SpareToShelveQueryDTO;
import com.hvisions.eam.entity.spare.HvEamSpare;
import com.hvisions.eam.entity.spare.HvEamSpareToShelve;
import com.hvisions.eam.entity.spare.HvEamSpareToShelveView;
import com.hvisions.eam.entity.spare.HvEamSpareType;
import com.hvisions.eam.repository.sprare.SpareEntityRepository;
import com.hvisions.eam.repository.sprare.SpareToShelveEntityRepository;
import com.hvisions.eam.repository.sprare.SpareToShelveViewEntityRepository;
import com.hvisions.eam.repository.sprare.SpareTypeEntityRepository;
import com.hvisions.eam.service.spare.SpareToShelveService;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title:SpareToShelveServiceImpl</p>
 * <p>Description:备件库位关系</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Slf4j
@Service
public class SpareToShelveServiceImpl implements SpareToShelveService {
    @Value(value = "${spring.datasource.url}")
    private String url;
    /**
     * 关系jpa
     */
    private final SpareToShelveEntityRepository spareToShelveEntityRepository;
    /**
     * 30
     * 联表jpa
     */
    private final SpareToShelveViewEntityRepository spareToShelveViewEntityRepository;

    /**
     * 备件
     */
    private final SpareEntityRepository spareEntityRepository;

    /**
     * 备件类型
     */
    private final SpareTypeEntityRepository spareTypeEntityRepository;

    /**
     * 备件库房mapper
     */
    private final SpareToShelveMapper spareToShelveMapper;


    @Autowired
    public SpareToShelveServiceImpl(SpareToShelveEntityRepository spareToShelveEntityRepository,
                                    SpareToShelveViewEntityRepository spareToShelveViewEntityRepository, SpareEntityRepository spareEntityRepository, SpareTypeEntityRepository spareTypeEntityRepository, SpareToShelveMapper spareToShelveMapper) {
        this.spareToShelveEntityRepository = spareToShelveEntityRepository;
        this.spareToShelveViewEntityRepository = spareToShelveViewEntityRepository;
        this.spareEntityRepository = spareEntityRepository;
        this.spareTypeEntityRepository = spareTypeEntityRepository;
        this.spareToShelveMapper = spareToShelveMapper;
    }


    /**
     * 判断当前DTO数据是否在数据中存在
     *
     * @param spareToShelveDTO 判断DTO
     * @return true 存在   false不存在
     */
    @Override
    public boolean isExist(SpareToShelveDTO spareToShelveDTO) {
        HvEamSpareToShelve spareToShelveOpt = spareToShelveEntityRepository.findAllBySpareIdAndShelveIdAndBatchNumber(
                spareToShelveDTO.getSpareId(),
                spareToShelveDTO.getShelveId(),
                spareToShelveDTO.getBatchNumber());
        return spareToShelveOpt != null;
    }


    @Override
    public Integer update(SpareToShelveDTO spareToShelveDTO) {
        HvEamSpareToShelve entity = spareToShelveEntityRepository.findById(spareToShelveDTO.getId())
                .orElseThrow(() -> new BaseKnownException(10000, "库存信息不存在，请检查id"));
        HvEamSpare spare = spareEntityRepository.findById(entity.getSpareId())
                .orElseThrow(() -> new BaseKnownException(10000, "备件信息缺失，请检查库存中的备件信息"));
        HvEamSpareType type = spareTypeEntityRepository.findById(spare.getSpareTypeId())
                .orElseThrow(() -> new BaseKnownException(10000, "备件类型信息缺失，请检查备件类型数据id:" + spare.getSpareTypeId()));
        if (1 == type.getBatchManagementOrNot()) {
            throw new BaseKnownException(10000, "单个管理的备件不允许修改数量");
        }
        entity.setNumber(spareToShelveDTO.getNumber());
        return spareToShelveEntityRepository.save(entity).getId();
    }

    @Override
    public List<Tuple2<String, List<SpareToShelveDTO>>> getSpareStoreInfo(Integer spareId) {
        SpareToShelveQueryDTO query = new SpareToShelveQueryDTO();
        query.setSpareId(spareId);
        List<SpareToShelveDTO> result = spareToShelveMapper.getSpareToShelve(query);
        Map<String, List<SpareToShelveDTO>> map = new HashMap<>();
        for (SpareToShelveDTO dto : result) {
            if (dto.getShelveName() == null) {
                continue;
            }
            map.computeIfAbsent(dto.getShelveName(), k -> new ArrayList<>());
            map.get(dto.getShelveName()).add(dto);
        }
        List<Tuple2<String, List<SpareToShelveDTO>>> tuple = new ArrayList<>();
        for (String key : map.keySet()) {
            tuple.add(new Tuple2<>(key, map.get(key)));
        }
        return tuple;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public Integer save(SpareToShelveDTO spareToShelveDTO) {
        //dto转换
        HvEamSpareToShelve spareToShelve = DtoMapper.convert(spareToShelveDTO, HvEamSpareToShelve.class);
        spareToShelve.setCreateTime(new Date());
        spareToShelve.setUpdateTime(new Date());
        Integer batchManagementOrNot = getBatchManagementOrNot(spareToShelve.getSpareId());
        if (batchManagementOrNot == null) {
            batchManagementOrNot = 0;
        }
        //如果类型为单一管理
        if (batchManagementOrNot == 1) {
            //判断是否为出库
            //判断是否可以修改
            Integer spareToShelveDTOId = spareToShelveDTO.getId();
            //判断为修改
            if (spareToShelveDTOId != null) {
                Optional<HvEamSpareToShelve> optional = spareToShelveEntityRepository.findById(spareToShelveDTOId);
                if (optional.isPresent()) {
                    //唯一管理的数量 只有 0 和 1
                    if (!(0 == spareToShelveDTO.getNumber() || 1 == spareToShelveDTO.getNumber())) {
                        //当前类型数量不可修改
                        throw new BaseKnownException(StoreExceptionEnum.CURRENT_NUMBER_OF_TYPES_CANNOT_BE_MODIFIED);
                    }
                    if (!optional.get().getBatchNumber().equals(spareToShelveDTO.getBatchNumber())) {
                        //当前类型批次号不可修改
                        throw new BaseKnownException(StoreExceptionEnum.CURRENT_TYPE_BATCH_NUMBER_CANNOT_BE_MODIFIED);
                    }
                    return spareToShelveEntityRepository.save(spareToShelve).getId();
                }
            }
            int id = 0;
            //查询出批次号
            List<HvEamSpareToShelve> spareToShelve1 = spareToShelveEntityRepository.findBySpareIdAndBatchNumberContains(spareToShelve.getSpareId(), spareToShelve.getBatchNumber());
            if (spareToShelve1.size() > 0) {
                //当前批次号已经存在
                Optional<HvEamSpareToShelve> spare = spareToShelve1.stream().max(Comparator.comparing(HvEamSpareToShelve::getSpareNo));

                Integer min = spare.get().getSpareNo();
                Integer spareNumber = spareToShelve.getNumber();
                String batchNumber = spareToShelve.getBatchNumber();
                for (int i = min + 1; i < min + spareNumber + 1; i++) {
                    //更改批次号
                    spareToShelve.setId(0);
                    spareToShelve.setNumber(1);
                    spareToShelve.setBatchNumber(batchNumber + getNumber(i));
                    spareToShelve.setSpareNo(i);
                    id = spareToShelveEntityRepository.save(spareToShelve).getId();
                }
            } else {
                //第一次录入
                spareToShelve.setSpareNo(1);
                spareToShelve.setNumber(1);
                //获取数量
                Integer spareNumber = spareToShelve.getNumber();
                String batchNumber = spareToShelve.getBatchNumber();
                for (int i = 1; i <= spareNumber; i++) {
                    //更改批次号
                    spareToShelve.setId(0);
                    spareToShelve.setBatchNumber(batchNumber + getNumber(i));
                    spareToShelve.setSpareNo(i);
                    id = spareToShelveEntityRepository.save(spareToShelve).getId();
                }
                int i = spareToShelveDTO.getNumber() - 1;
                if (i > 0) {
                    spareToShelveDTO.setNumber(i);
                    this.save(spareToShelveDTO);
                }
            }
            //返回
            return id;
        }
        return addSpareToShelve(spareToShelve);
    }

    private Integer addSpareToShelve(HvEamSpareToShelve spareToShelve) {
        HvEamSpareToShelve spare = spareToShelveEntityRepository.findAllBySpareIdAndShelveIdAndBatchNumber(spareToShelve.getSpareId(), spareToShelve.getShelveId(), spareToShelve.getBatchNumber());
        if (spare == null) {
            return spareToShelveEntityRepository.save(spareToShelve).getId();
        } else {
            spare.setNumber(spareToShelve.getNumber());
            return spareToShelveEntityRepository.save(spare).getId();
        }
    }

    /**
     * 传入一个备件id 返回一个当前备件所对应的管理类型
     *
     * @param spareId 备件id
     * @return 管理类型
     */
    private Integer getBatchManagementOrNot(Integer spareId) {
        //通过备件id获取备件
        Optional<HvEamSpare> spare = spareEntityRepository.findById(spareId);
        //判断是否存在
        if (spare.isPresent()) {
            //获取备件类型
            Optional<HvEamSpareType> spareType = spareTypeEntityRepository.findById(spare.get().getSpareTypeId());
            if (spareType.isPresent()) {
                return spareType.get().getBatchManagementOrNot();
            }
        }
        return -1;
    }

    /**
     * 给定一个数字生成一个四位数的序列号
     *
     * @param num 数字
     * @return 四位数的序列号
     */
    private String getNumber(Integer num) {
        String number = num.toString();
        number += "0000" + number;
        return number.substring(number.length() - 4, number.length());
    }

    /**
     * 删除关系通过备件库存号关系ID
     *
     * @param id 型号ID
     */
    @Override
    public void deleteById(Integer id) {
        //当前库存不为0不能删除
        Optional<HvEamSpareToShelve> spareToShelve = spareToShelveEntityRepository.findById(id);
        if (!spareToShelve.isPresent()) {
            //数据错误
            log.info("库房 id：" + id + " 不存在");
            throw new BaseKnownException(StoreExceptionEnum.DATE_ERROR_EXCEPTION);
        }
        HvEamSpareToShelve newSpareToShelve = spareToShelve.get();
        newSpareToShelve.setNumber(0);
        HvEamSpareToShelve save = spareToShelveEntityRepository.save(newSpareToShelve);
        log.info("修改：" + save.toString());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SpareToShelveDTO findById(Integer id) {
        try {
            return DtoMapper.convert(spareToShelveEntityRepository.getOne(id), SpareToShelveDTO.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Set<Integer> findShelveIdBySpareId(Integer spareId) {
        List<HvEamSpareToShelve> spareToShelves = spareToShelveEntityRepository.findShelveIdBySpareId(spareId);
        Set<Integer> shelveIds = new HashSet<>();
        for (HvEamSpareToShelve s : spareToShelves) {
            shelveIds.add(s.getShelveId());
        }
        return shelveIds;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Set<Integer> findSpareIdByShelveId(Integer spareId) {
        List<HvEamSpareToShelve> spareToShelves = spareToShelveEntityRepository.findSpareIdByShelveId(spareId);
        Set<Integer> spareIds = new HashSet<>();
        for (HvEamSpareToShelve s : spareToShelves) {
            spareIds.add(s.getShelveId());
        }
        return spareIds;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public Integer sumSpareNumBySpareId(Integer spareId) {
        Integer sumNumber = 0;
        //查询当前备件所在的备件库存关系表
        List<HvEamSpareToShelve> spareIds = spareToShelveEntityRepository.findShelveIdBySpareId(spareId);
        for (HvEamSpareToShelve s : spareIds) {
            sumNumber += s.getNumber();
        }
        return sumNumber;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Set<Integer> findAllBySpareId(Integer spareId) {
        List<HvEamSpareToShelve> spareToShelves = spareToShelveEntityRepository.findAllBySpareId(spareId);
        Set<Integer> ids = new HashSet<>();
        for (HvEamSpareToShelve s : spareToShelves) {
            ids.add(s.getId());
        }
        return ids;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public Set<Integer> findAllByShelveId(Integer shelveId) {
        List<HvEamSpareToShelve> spareToShelves = spareToShelveEntityRepository.findAllByShelveId(shelveId);
        Set<Integer> ids = new HashSet<>();
        for (HvEamSpareToShelve s : spareToShelves) {
            ids.add(s.getId());
        }
        return ids;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer findAllByShelveIds(Integer shelveId) {
        List<HvEamSpareToShelve> spareToShelves = spareToShelveEntityRepository.findAllByShelveId(shelveId);
        Integer sum = 0;
        for (HvEamSpareToShelve s : spareToShelves) {
            sum += s.getNumber();
        }
        return sum;
    }

    /**
     * {@inheritDoc}
     */
    @Deprecated
    @Override
    public Page<SpareToShelveQueryDTO> findAllBySpareCodeAndSpareNameAndShelveCodeAndShelveNameAndBatchNumber(SpareToShelveQueryDTO spareToShelveQueryDTO) {

        //把DTO转换成普通类
        HvEamSpareToShelveView spareToShelveDto = DtoMapper.convert(spareToShelveQueryDTO, HvEamSpareToShelveView.class);

        //匹配方式       xxx 采用开始匹配的方式查询 忽略大小写
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
                .withMatcher("spareCode", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("spareName", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("shelveCode", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("shelveName", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("spareTypeCode", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("batchNumber", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("spareId", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());

        //匹配器
        Example<HvEamSpareToShelveView> example = Example.of(spareToShelveDto, exampleMatcher);

        Page<HvEamSpareToShelveView> page = spareToShelveViewEntityRepository.findAll(example, spareToShelveQueryDTO.getRequest());
        return DtoMapper.convertPage(page, SpareToShelveQueryDTO.class);
    }
    /**
     * SuppressWarnings() 注解的压制规则 all 全部
     */
    /**
     * 分页查询
     *
     * @param spareToShelveQueryDTO 备件库房
     * @return 分页条件
     */
    @SuppressWarnings(StoreConfig.RULES)
    @Override
    public Page<SpareToShelveDTO> getSpareToShelve(SpareToShelveQueryDTO spareToShelveQueryDTO) {
        Page<SpareToShelveDTO>
                page = PageHelperUtil.getPage(this.spareToShelveMapper::getSpareToShelve, spareToShelveQueryDTO);
        List<HvEamSpare> spare = spareEntityRepository.findAllById(page.stream().map(SysBaseDTO::getId).collect(Collectors.toList()));

        for (SpareToShelveDTO spareToShelveDTO : page) {
            for (HvEamSpare hvEamSpare : spare) {
                if (spareToShelveDTO.getSpareId().equals(hvEamSpare.getId())) {
                    spareToShelveDTO.setSpareCode(hvEamSpare.getSpareCode());
                }
            }
            //当前数量状态
            spareToShelveDTO.setInventoryQuantityStatus(getInventoryQuantityStatus(spareToShelveDTO.getSpareId()));
        }
        return page;
    }

    /**
     * 通过备件id获取当前数量的状态
     * 库存数量状态 0 数据错误或为填写最大最小值  1 库存不足 2 库存超限
     */
    private Integer getInventoryQuantityStatus(Integer spareId) {
        Optional<HvEamSpare> opt = spareEntityRepository.findById(spareId);
        if (!opt.isPresent()) {
            log.info("备件id 找不到");
            throw new BaseKnownException(StoreExceptionEnum.DATE_ERROR_EXCEPTION);
        }
        //获取备件定义
        HvEamSpare spare = opt.get();
        //当前总库存数量
        Double inventoryQuantity;
        //获取当前备件在库房的全部信息
        List<HvEamSpareToShelve> spareToShelveList = spareToShelveEntityRepository.findAllBySpareId(spareId);
        //计算求和X
        double sum = 0.0;
        for (HvEamSpareToShelve spareToShelve : spareToShelveList) {
            double number = spareToShelve.getNumber();
            sum += number;
        }
        inventoryQuantity = sum;
        if (spare.getCargoMax() == 0 && spare.getCargoMin() == 0) {
            return 0;
        }
        if (spare.getCargoMin() == null || spare.getCargoMax() == null) {
            log.info("备件定义中最大最小数量发生异常 :" + spare.toString());
            return 0;
        }
        //库存不足
        if (inventoryQuantity < spare.getCargoMin()) {
            return 1;
        }
        //库存超出
        if (inventoryQuantity > spare.getCargoMax()) {
            return 2;
        }

        return 0;
    }
}