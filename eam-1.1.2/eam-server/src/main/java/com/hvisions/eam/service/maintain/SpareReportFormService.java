package com.hvisions.eam.service.maintain;

import com.hvisions.eam.dto.repair.spare.*;

import java.util.List;

/**
 * <p>Title: SpareReportFormService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/1/4</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public interface SpareReportFormService {


    /**
     * 备件使用统计
     *
     * @param sparePartUsageQueryDTO 备件使用统计查询条件
     * @return 备件使用统计记录
     */
    List<SparePartUsageDTO> sparePartUsage(SparePartUsageQueryDTO sparePartUsageQueryDTO);

    /**
     * 备件周转率
     *
     * @param turnoverQueryDTO 周转率查询条件
     * @return 周转率信息
     */
    List<MonthTurnoverDTO> spareTurnover(TurnoverQueryDTO turnoverQueryDTO);

    /**
     * 当月故障统计报表
     *
     * @param equipmentFailureQueryDTO 查询条件
     * @return 统计记录
     */
    List<EquipmentFailureDTO> equipmentFailure(EquipmentFailureQueryDTO equipmentFailureQueryDTO);
}