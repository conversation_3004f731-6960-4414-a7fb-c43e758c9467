package com.hvisions.eam.service.publicstore;

import com.hvisions.eam.dto.publicstore.ShelveDTO;
import com.hvisions.eam.query.spare.ShelveQueryDTO;
import org.springframework.data.domain.Page;

/**
 * <p>Title: ShelveService</p>
 * <p>Description: 库房表</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface ShelveService {

    /**
     * 增加库房  改 通过 库房DTO
     *
     * @param shelveDTO 库房DTO
     * @return 新增数据的ID
     */
    Integer save(ShelveDTO shelveDTO);

    /**
     * 删除库房 通过 ID
     *
     * @param id 库位ID
     */
    void deleteById(Integer id);

    /**
     * 查询库房 通过库房ID
     *
     * @param id 库房ID
     * @return 库房DTO
     */
    ShelveDTO findById(Integer id);

    /**
     * 分页查询 备件申请表 通过状态 出库单号 标题 出入类型 出入库
     *
     * @param shelveQueryDTO 备件申请表DTO
     * @return 分页
     */
    Page<ShelveDTO> findAllByShelveCodeAndShelveNameAndShelvePace(ShelveQueryDTO shelveQueryDTO);

}