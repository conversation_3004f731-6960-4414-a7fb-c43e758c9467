package com.hvisions.eam.service.maintain;

import com.hvisions.eam.dto.maintain.RepairMaintainCalendarCard;

import java.text.ParseException;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>Title: RepairMaintainCalendarService</p >
 * <p>Description: 维修保养日历service</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2022/2/7</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
public interface RepairMaintainCalendarService {

    /**
     * @return 维修保养任务详情数据
     */
    List<RepairMaintainCalendarCard> getRepairMaintainCalendar(LocalDate queryDate) throws ParseException;
}
