package com.hvisions.eam.service.publicstore.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.eam.enums.StoreExceptionEnum;
import com.hvisions.eam.entity.lub.HvEamLubricating;
import com.hvisions.eam.repository.lub.LubEntityRepository;
import com.hvisions.eam.dto.publicstore.UnitDTO;
import com.hvisions.eam.entity.publicstore.HvEamSpareUnit;
import com.hvisions.eam.repository.publicstore.SpareUnitEntityRepository;
import com.hvisions.eam.service.publicstore.SpareUnitService;
import com.hvisions.eam.query.spare.SpareUnitQueryDTO;
import com.hvisions.eam.entity.spare.HvEamSpare;
import com.hvisions.eam.repository.sprare.SpareEntityRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>Title:SpareUnitServiceImpl</p>
 * <p>Description:备件单位</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Service
public class SpareUnitServiceImpl implements SpareUnitService {

    /**
     * 备件单位jpa
     */
    private final SpareUnitEntityRepository spareUnitEntityRepository;


    /**
     * 备件jpa
     */
    private final SpareEntityRepository spareEntityRepository;

    /**
     * 油品jpa
     */
    private final LubEntityRepository lubEntityRepository;

    @Autowired
    public SpareUnitServiceImpl(SpareUnitEntityRepository spareUnitEntityRepository,
                                SpareEntityRepository spareEntityRepository,
                                LubEntityRepository lubEntityRepository) {
        this.spareEntityRepository = spareEntityRepository;
        this.spareUnitEntityRepository = spareUnitEntityRepository;
        this.lubEntityRepository = lubEntityRepository;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer save(UnitDTO unitDTO) {
        HvEamSpareUnit spareUnit = DtoMapper.convert(unitDTO, HvEamSpareUnit.class);
        return spareUnitEntityRepository.save(spareUnit).getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void deleteById(Integer id) {
        //检查备件中是否应用 被应用删除失败
        List<HvEamSpare> spare = spareEntityRepository.findByUnitId(id);
        if (spare != null && spare.size() == 0) {
            List<HvEamLubricating> lub = lubEntityRepository.findByUnitId(id);
            if (lub != null && lub.size() == 0) {
                spareUnitEntityRepository.deleteById(id);
            }else {
                throw new BaseKnownException(StoreExceptionEnum.IN_USE);
            }
        }else {
            throw new BaseKnownException(StoreExceptionEnum.IN_USE);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UnitDTO findById(Integer id) {
        return DtoMapper.convert(spareUnitEntityRepository.getOne(id), UnitDTO.class);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Page<UnitDTO> findAllByUnitName(SpareUnitQueryDTO spareUnitQueryDTO) {
        //把DTO转换成普通类
        HvEamSpareUnit spareUnit = DtoMapper.convert(spareUnitQueryDTO, HvEamSpareUnit.class);

        //匹配方式       xxx 采用开始匹配的方式查询 忽略大小写
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
                .withMatcher("unitName", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());

        //匹配器
        Example<HvEamSpareUnit> example = Example.of(spareUnit, exampleMatcher);
        Page<HvEamSpareUnit> page = spareUnitEntityRepository.findAll(example, spareUnitQueryDTO.getRequest());
        return DtoMapper.convertPage(page, UnitDTO.class);
    }

    /**
     * 通过单位名称获取单位
     *
     * @param unitName 单位名称
     * @return 单位
     */
    @Override
    public UnitDTO findByUnitName(String unitName) {
        HvEamSpareUnit byUnitName = spareUnitEntityRepository.findByUnitName(unitName);
        if (byUnitName != null) {
            return DtoMapper.convert(byUnitName, UnitDTO.class);
        }
        return null;
    }
}