package com.hvisions.eam.service.autonomy.imp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.hvisions.activiti.client.ActivitiClient;
import com.hvisions.activiti.dto.instance.ProcessInstanceStartDTO;
import com.hvisions.activiti.dto.process.ProcessInstanceDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.client.autonomy.ActivitiIdentityClient;
import com.hvisions.eam.client.autonomy.AuthClient;
import com.hvisions.eam.dao.InspectionPlanMapper;
import com.hvisions.eam.dao.InspectionProjectMapper;
import com.hvisions.eam.dao.ProjectAndPlanRelationMapper;
import com.hvisions.eam.dto.autonomy.*;
import com.hvisions.eam.dto.autonomy.activitiIdentity.Content;
import com.hvisions.eam.dto.autonomy.activitiIdentity.JsonGetGroup;
import com.hvisions.eam.dto.autonomy.activitiIdentity.QueryDTO;
import com.hvisions.eam.entity.autonomy.HvAmInspectionPlan;
import com.hvisions.eam.entity.autonomy.HvAmProjectAndPlanRelation;
import com.hvisions.eam.entity.autonomy.HvAmProjectGroup;
import com.hvisions.eam.enums.HExceptionEnum;
import com.hvisions.eam.repository.autonomy.InspectionPlanRepository;
import com.hvisions.eam.repository.autonomy.ProjectAndPlanRelationRepository;
import com.hvisions.eam.repository.autonomy.ProjectGroupRepository;
import com.hvisions.eam.service.autonomy.InspectionPlanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-11
 */
@Service
@Slf4j
public class InspectionPlanServiceImpl extends ServiceImpl<InspectionPlanMapper, HvAmInspectionPlan> implements InspectionPlanService {

    private final ActivitiClient activitiClient;
    private final AuthClient authClient;
    private final ActivitiIdentityClient activitiIdentityClient;

    /**
     * jpa仓储层
     */
    private final InspectionPlanRepository inspectionPlanRepository;
    private final ProjectAndPlanRelationRepository projectAndPlanRelationRepository;
    private final ProjectGroupRepository projectGroupRepository;
    /**
     * mybatis仓储层
     */
    private final ProjectAndPlanRelationMapper projectAndPlanRelationMapper;
    private final InspectionProjectMapper inspectionProjectMapper;

    @Autowired
    public InspectionPlanServiceImpl(ActivitiClient activitiClient, AuthClient authClient, ActivitiIdentityClient activitiIdentityClient, InspectionPlanRepository inspectionPlanRepository, ProjectAndPlanRelationRepository projectAndPlanRelationRepository, ProjectGroupRepository projectGroupRepository, ProjectAndPlanRelationMapper projectAndPlanRelationMapper, InspectionProjectMapper inspectionProjectMapper) {
        this.activitiClient = activitiClient;
        this.authClient = authClient;
        this.activitiIdentityClient = activitiIdentityClient;
        this.inspectionPlanRepository = inspectionPlanRepository;
        this.projectAndPlanRelationRepository = projectAndPlanRelationRepository;
        this.projectGroupRepository = projectGroupRepository;
        this.projectAndPlanRelationMapper = projectAndPlanRelationMapper;
        this.inspectionProjectMapper = inspectionProjectMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int add(InspectionPlanCreateOrUpdateDTO dto) {

        //校验检查项目是否为空
        if (dto.getProjectIdList().size() <= 0) {
            throw new BaseKnownException(HExceptionEnum.ITEM_IS_NULL);
        }
        //验证计划编码是否存在
        if (inspectionPlanRepository.existsByNumber(dto.getNumber())) {
            throw new BaseKnownException(HExceptionEnum.INSERT_CODE_ERROR);
        }
        //插入计划
        Integer planId = inspectionPlanRepository.save(DtoMapper.convert(dto,HvAmInspectionPlan.class)).getId();
        //插入项目和计划关系
        List<HvAmProjectAndPlanRelation> list = new ArrayList<>();
        for (Integer integer:dto.getProjectIdList()) {

            HvAmProjectAndPlanRelation temp = new HvAmProjectAndPlanRelation();
            temp.setPlanId(planId);
            temp.setProjectId(integer);
            list.add(temp);
        }
        projectAndPlanRelationRepository.saveAll(list);

        return planId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Integer id) {

        //先根据；计划ID，删除项目和计划表数据
        projectAndPlanRelationRepository.deleteByPlanId(id);
        //再删除计划
        inspectionPlanRepository.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteList(List<Integer> idList) {

        for (Integer id : idList) {
            //先根据；计划ID，删除项目和计划表数据
            projectAndPlanRelationRepository.deleteByPlanId(id);
            //再删除计划
            inspectionPlanRepository.deleteById(id);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(InspectionPlanCreateOrUpdateDTO dto) {

        if (dto.getProjectIdList().size() <= 0) {
            throw new BaseKnownException(HExceptionEnum.ITEM_IS_NULL);
        }
        //先根据；计划ID，删除项目和计划表数据
        projectAndPlanRelationRepository.deleteByPlanId(dto.getId());
        //插入新 项目和计划关系
        List<HvAmProjectAndPlanRelation> list = new ArrayList<>();
        for (Integer integer:dto.getProjectIdList()) {
            HvAmProjectAndPlanRelation temp = new HvAmProjectAndPlanRelation();
            temp.setPlanId(dto.getId());
            temp.setProjectId(integer);
            list.add(temp);
        }
        projectAndPlanRelationRepository.saveAll(list);

        //修改计划
        return inspectionPlanRepository.save(DtoMapper.convert(dto,HvAmInspectionPlan.class)).getId();
    }

    @Override
    public Page<InspectionPlanDTO> queryList(InspectionPlanQueryDTO dto) {

        //匹配查询
        Page<HvAmInspectionPlan> hvAmInspectionPlans;
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
                //包含，忽略大小写
                .withMatcher("number", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("name", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());
        Example<HvAmInspectionPlan> example = Example.of(DtoMapper.convert(dto, HvAmInspectionPlan.class), exampleMatcher);
        hvAmInspectionPlans = inspectionPlanRepository.findAll(example, dto.getRequest());
        Page<InspectionPlanDTO> inspectionPlanDTOS = DtoMapper.convertPage(hvAmInspectionPlans, InspectionPlanDTO.class);

//        for (InspectionPlanDTO temp: inspectionPlanDTOS) {
//            //查询每个计划对应的检查项目
//            LambdaQueryWrapper<HvAmProjectAndPlanRelation> queryWrapper = new LambdaQueryWrapper<>();
//            queryWrapper.eq(HvAmProjectAndPlanRelation::getPlanId,temp.getId())
//                    .select(HvAmProjectAndPlanRelation::getProjectId);
//            List<HvAmProjectAndPlanRelation> projectList = projectAndPlanRelationMapper.selectList(queryWrapper);
//            List<Integer> list = new ArrayList<>();
//            for (HvAmProjectAndPlanRelation hvAmProjectAndPlanRelation:projectList) {
//                list.add(hvAmProjectAndPlanRelation.getProjectId());
//            }
//            temp.setProjectIdList(list);
//        }

        return inspectionPlanDTOS;
    }

    @Override
    public InspectionPlanDTO getById(Integer id) {

        //查询计划主体信息
        HvAmInspectionPlan hvAmInspectionPlan = inspectionPlanRepository.getOne(id);
        InspectionPlanDTO inspectionPlanDTO = DtoMapper.convert(hvAmInspectionPlan, InspectionPlanDTO.class);

        //查询计划对应的检查项目信息
        LambdaQueryWrapper<HvAmProjectAndPlanRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HvAmProjectAndPlanRelation::getPlanId,id)
                .select(HvAmProjectAndPlanRelation::getProjectId);
        List<HvAmProjectAndPlanRelation> projectList = projectAndPlanRelationMapper.selectList(queryWrapper);

        List<Integer> list = new ArrayList<>();
        for (HvAmProjectAndPlanRelation hvAmProjectAndPlanRelation:projectList) {
            list.add(hvAmProjectAndPlanRelation.getProjectId());
        }
        //通过 检查项目ID 查询出检查项目的详细信息
        List<InspectionProjectDTO> projectDTOList = DtoMapper.convertList(inspectionProjectMapper.selectBatchIds(list),InspectionProjectDTO.class);
        inspectionPlanDTO.setProjectIdList(projectDTOList);

        return inspectionPlanDTO;
    }

    @Override
    public void triggerPlanById(Integer planId) {

        Optional<HvAmInspectionPlan> result = inspectionPlanRepository.findById(planId);
        if (!result.isPresent()) {
            return;
        }
        InspectionPlanDTO plan = getById(planId);

        List<InspectionProjectDTO> temp = plan.getProjectIdList();
        List<InspectionProjectDTO> projectDTOList = new ArrayList<>();
        //排除已经停用的项目 并查询组ID
        for (InspectionProjectDTO inspectionProjectDTO:temp) {
            if ("0".equals(inspectionProjectDTO.getEnableFlag())){
                HvAmProjectGroup allById = projectGroupRepository.findAllById(inspectionProjectDTO.getGroupId());
                if (null == allById){
                    throw new BaseKnownException(HExceptionEnum.SERVER_ERROR);
                }else {
                    inspectionProjectDTO.setGroupName(allById.getName());
                    projectDTOList.add(inspectionProjectDTO);
                }
            }
        }
        plan.setProjectIdList(projectDTOList);
        triggerPlan(plan);
    }

    private void triggerPlan(InspectionPlanDTO plan) {

        //一个计划生成一条任务
        ProcessInstanceStartDTO processInstanceStartDTO = new ProcessInstanceStartDTO();
        Map<String, Object> map = Maps.newHashMap();
        String businessKey = plan.getId() == null ? "" : plan.getId().toString();
        processInstanceStartDTO.setBusinessKey(businessKey);
        processInstanceStartDTO.setProcessDefinitionKey("autonomymaintenance");

        //设置任务的各种参数
        map.put("number", plan.getNumber());//检查计划编码
        map.put("taskName", plan.getName());//检查计划名称
        map.put("typesOfLiability",plan.getTypesOfLiability());//责任类型;0-责任人,1-责任组
        if ("0".equals(plan.getTypesOfLiability())){
            map.put("personLiable", plan.getPersonLiable());//责任人
            try{
                JsonByUserId jsonByUserId = authClient.getUserById(Integer.parseInt(plan.getPersonLiable()));
                Data data = jsonByUserId.getData();
                map.put("personLiableName", data.getUserName());//责任人-名称
            }catch (Exception e){
                map.put("personLiableName", plan.getPersonLiable());//责任人-名称 获取失败 展示id
            }
            map.put("responsibilityGroup", null);//责任组
            map.put("responsibilityGroupName", null);//责任组-名称
        }else {
            map.put("personLiable", null);//责任人
            map.put("personLiableName", null);//责任人-名称
            map.put("responsibilityGroup", plan.getResponsibilityGroup());//责任组
            try{
                QueryDTO queryDTO = new QueryDTO();
                queryDTO.setId(plan.getResponsibilityGroup());
                queryDTO.setSortCol("id");
                JsonGetGroup groupByQuery = activitiIdentityClient.getGroupByQuery(queryDTO);
                List<Content> content = groupByQuery.getData().getContent();
                map.put("responsibilityGroupName", content.get(0).getName());//责任组-名称
            }catch (Exception e){
                map.put("responsibilityGroupName", plan.getResponsibilityGroup());//responsibilityGroupName 获取失败 展示id
            }
        }
        map.put("verifier", plan.getVerifier());//验证人员
        try{
            JsonByUserId jsonByUserId = authClient.getUserById(Integer.parseInt(plan.getVerifier()));
            Data data = jsonByUserId.getData();
            map.put("verifierName", data.getUserName());//验证人员-名称
        }catch (Exception e){
            map.put("verifierName", plan.getVerifier());//验证人员-名称 获取失败 展示id
        }
        map.put("foreman", plan.getForeman());//班组长
        try{
            JsonByUserId jsonByUserId = authClient.getUserById(Integer.parseInt(plan.getForeman()));
            Data data = jsonByUserId.getData();
            map.put("foremanName", data.getUserName());//班组长-名称
        }catch (Exception e){
            map.put("foremanName", plan.getForeman());//班组长-名称 获取失败 展示id
        }
        map.put("leader", plan.getLeader());//主管领导
        try{
            JsonByUserId jsonByUserId = authClient.getUserById(Integer.parseInt(plan.getLeader()));
            Data data = jsonByUserId.getData();
            map.put("leaderName", data.getUserName());//主管领导-名称
        }catch (Exception e){
            map.put("leaderName", plan.getLeader());//主管领导-名称 获取失败 展示id
        }
        map.put("descr",plan.getName()+"验证人员："+map.get("verifierName")+",班组长："+map.get("foremanName"));
        map.put("timerId",plan.getTimerId());//timerId
        map.put("projectDTOList", plan.getProjectIdList());

        processInstanceStartDTO.setVariables(map);

        ResultVO<ProcessInstanceDTO> processInstanceDTOResultVO = new ResultVO<>();
        try {
            processInstanceDTOResultVO = activitiClient.startProcessInstance(processInstanceStartDTO);
            if (processInstanceDTOResultVO.isSuccess()){
                String processInstanceId = processInstanceDTOResultVO.getData().getProcessInstanceId();
                log.info("启动流程实例成功，流程实例ID为：" + processInstanceId);
            }else{
                log.error("流程启动失败");
            }
        }catch (Exception e){
            log.error("启动流程实例失败,异常信息:{} , 入参为：{} ", processInstanceDTOResultVO.getMessage(),processInstanceStartDTO.toString());
            throw new BaseKnownException(HExceptionEnum.PROCESS_INSTANCE_START_FAILED);
        }
    }

    @Override
    public void createTemporaryTask(InspectionPlanCreateOrUpdateDTO dto) {

        //校验检查项目是否为空
        if (dto.getProjectIdList().size() <= 0) {
            throw new BaseKnownException(HExceptionEnum.ITEM_IS_NULL);
        }

        InspectionPlanDTO plan = DtoMapper.convert(dto, InspectionPlanDTO.class);
        //通过 检查项目ID 查询出检查项目的详细信息
        List<InspectionProjectDTO> temp = DtoMapper.convertList(inspectionProjectMapper.selectBatchIds(dto.getProjectIdList()),InspectionProjectDTO.class);
        List<InspectionProjectDTO> projectDTOList = new ArrayList<>();
        //排除已经停用的项目 并查询组ID
        for (InspectionProjectDTO inspectionProjectDTO:temp) {
            if ("0".equals(inspectionProjectDTO.getEnableFlag())){
                inspectionProjectDTO.setGroupName(projectGroupRepository.findAllById(inspectionProjectDTO.getGroupId()).getName());
                projectDTOList.add(inspectionProjectDTO);
            }
        }
        plan.setProjectIdList(projectDTOList);
        triggerPlan(plan);

    }

    /**
     * 通过 计划ID 查询出对应的 项目详细信息
     * <AUTHOR>
     * @param planId 计划id
     * @return 项目list
     */
    @Override
    public List<InspectionProjectDTO> getProjectList(Integer planId){

        //查询计划对应的检查项目信息
        LambdaQueryWrapper<HvAmProjectAndPlanRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HvAmProjectAndPlanRelation::getPlanId,planId)
                .select(HvAmProjectAndPlanRelation::getProjectId);
        List<HvAmProjectAndPlanRelation> projectList = projectAndPlanRelationMapper.selectList(queryWrapper);

        List<Integer> list = new ArrayList<>();
        for (HvAmProjectAndPlanRelation hvAmProjectAndPlanRelation:projectList) {
            list.add(hvAmProjectAndPlanRelation.getProjectId());
        }
        //通过 检查项目ID 查询出检查项目的详细信息
        List<InspectionProjectDTO> projectDTOList = DtoMapper.convertList(inspectionProjectMapper.selectBatchIds(list),InspectionProjectDTO.class);

        return projectDTOList;
    }
}
