package com.hvisions.eam.service.maintain.imp;

import com.hvisions.eam.client.spare.ActivitiHistoryClient;
import com.hvisions.eam.dao.DataMapper;
import com.hvisions.eam.dto.repair.process.RepairData;
import com.hvisions.eam.entity.maintain.HvEamRepairData;
import com.hvisions.eam.repository.maintain.RepaireDataRepository;
import com.hvisions.eam.service.maintain.RepairDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.support.ListenerExecutionFailedException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title: RepairDataServiceImpl</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/11/5</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class RepairDataServiceImpl implements RepairDataService {
    private final RepaireDataRepository repaireDataRepository;
    private final ActivitiHistoryClient client;
    private final DataMapper dataMapper;

    @Autowired
    public RepairDataServiceImpl(RepaireDataRepository repaireDataRepository,
                                 ActivitiHistoryClient client,
                                 DataMapper dataMapper) {
        this.repaireDataRepository = repaireDataRepository;
        this.client = client;
        this.dataMapper = dataMapper;
    }


    /**
     * 保留流程数据
     *
     * @param processInstanceId 流程实例id
     */
    @Override
    public void save(String processInstanceId) {
        if (repaireDataRepository.existsByProcessInstanceId(processInstanceId)) {
            log.info("已经存在流程实例信息，维修数据不做同步,流程实例id为:{}", processInstanceId);
            return;
        }
        //获取流程数据
        log.info("开始同步历史流程实例数据，流程实例Id为：{}", processInstanceId);
        RepairData repairData = client.getRepairProcessInstance(processInstanceId);
        if (repairData.getCode() == 200) {
            if (repairData.getData() != null) {
                //查询设备对应的产线。如果返回多个。需要循环建立
                List<Integer> lineIds = dataMapper.getLineIdsByEquipmentId(repairData.getData().getProcessInstanceVariables().getEquipmentId());
                if (lineIds == null || lineIds.size() == 0) {
                    lineIds = new ArrayList<>();
                    lineIds.add(0);
                }
                for (Integer lineId : lineIds) {
                    HvEamRepairData data = new HvEamRepairData();
                    BeanUtils.copyProperties(repairData.getData(), data);
                    if (repairData.getData().getProcessInstanceVariables() != null) {
                        BeanUtils.copyProperties(repairData.getData().getProcessInstanceVariables(), data);
                        data.setLineId(lineId);
                        List<Integer> userMessage =
                            repairData.getData().getProcessInstanceVariables().getUserMessage();
                        if (userMessage != null) {
                            String userIdJoin = "";
                            for (Integer integer : userMessage) {
                                userIdJoin = StringUtils.join(userIdJoin, ",", integer.toString());
                            }
                            data.setUserIdJoin(userIdJoin);
                        }
                    }
                    repaireDataRepository.save(data);
                }
                log.info("同步历史流程实例数据成功，流程实例id为：{}", processInstanceId);
            }
        } else {
            log.info("同步历史流程实例数据异常,流程实例id为：{}", processInstanceId);
        }
    }

    /**
     * 处理异常
     *
     * @param message 消息信息
     * @param e       异常
     */
    @Override
    public void handleError(Message message, ListenerExecutionFailedException e) {
        log.error(e.getMessage(), e);
    }
}









