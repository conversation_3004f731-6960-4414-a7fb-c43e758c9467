package com.hvisions.eam.service.maintain.imp;

import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.HvImportUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.eam.dao.ReportMapper;
import com.hvisions.eam.dao.ReportStatisticalMapper;
import com.hvisions.eam.dto.maintain.TaskSummaryDTO;
import com.hvisions.eam.dto.report.*;
import com.hvisions.eam.entity.maintain.HvEamMaintainReport;
import com.hvisions.eam.repository.maintain.MaintainReportRepository;
import com.hvisions.eam.service.maintain.ReportStatisticalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <p>Title: ReportStatisticalServiceImpl</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/1/6</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class ReportStatisticalServiceImpl implements ReportStatisticalService {

    private final ReportStatisticalMapper reportStatisticalMapper;

    private final MaintainReportRepository maintainReportRepository;

    private final ReportMapper reportMapper;

    @Autowired
    public ReportStatisticalServiceImpl(ReportStatisticalMapper reportStatisticalMapper, MaintainReportRepository maintainReportRepository, ReportMapper reportMapper) {
        this.reportStatisticalMapper = reportStatisticalMapper;
        this.maintainReportRepository = maintainReportRepository;
        this.reportMapper = reportMapper;
    }


    @Override
    public List<ReportUrgentRepairParetoAnalysisDTO> urgentRepairParetoAnalysis(ReportUrgentRepairParetoAnalysisQuery reportUrgentRepairParetoAnalysisQuery) {
        List<UrgentRepairParetoInfo> infos = reportStatisticalMapper.urgentMainParetoAnalysis(reportUrgentRepairParetoAnalysisQuery);
        if (infos == null || infos.size() == 0) {
            log.info("急维修Pareto分析查询没有数据");
            return new ArrayList<>();
        }
        BigDecimal sumRepairTime = new BigDecimal("0");
        List<ReportUrgentRepairParetoAnalysisDTO> result = new ArrayList<>();
        for (UrgentRepairParetoInfo info : infos) {
            ReportUrgentRepairParetoAnalysisDTO dto = new ReportUrgentRepairParetoAnalysisDTO();
            dto.setEquipmentName(info.getEquipmentName());
            dto.setRepairTime(info.getRepairTime());
            sumRepairTime = sumRepairTime.add(info.getRepairTime());
            result.add(dto);
        }
        BigDecimal everyRepairTime = new BigDecimal("0");
        for (ReportUrgentRepairParetoAnalysisDTO info : result) {
            everyRepairTime = everyRepairTime.add(info.getRepairTime());
            info.setCumulativePercentage(everyRepairTime
                .multiply(new BigDecimal(100))
                .divide(sumRepairTime, 2, BigDecimal.ROUND_HALF_UP));

        }
        return result;
    }

    @Override
    public List<ReportEquipmentFaultStatisticalDTO> equipmentFaultStatistical(ReportEquipmentFaultStatisticalQuery reportEquipmentFaultStatisticalQuery) {
        List<EquipmentFaultInfo> infos =
            reportStatisticalMapper.equipmentFaultStatistical(reportEquipmentFaultStatisticalQuery);
        if (infos == null || infos.size() == 0) {
            log.info("设备故障统计表没有数据");
            return new ArrayList<>();
        }
        Map<String, BigDecimal> productLineTimeMap = new HashMap<>();
        List<ReportEquipmentFaultStatisticalDTO> result = new ArrayList<>();
        for (EquipmentFaultInfo info : infos) {
            //产线信息
            if (!productLineTimeMap.containsKey(info.getProductionLine())) {
                productLineTimeMap.put(info.getProductionLine(), info.getFaultTime());
            } else {
                productLineTimeMap.put(info.getProductionLine()
                    , productLineTimeMap.get(info.getProductionLine()).add(info.getFaultTime()));
            }
            //设备信息
            Optional<ReportEquipmentFaultStatisticalDTO> findResult = result.stream()
                .filter(t -> t.getEquipmentCode().equals(info.getEquipmentCode())
                    && t.getProductionLine().equals(info.getProductionLine()))
                .findFirst();
            if (!findResult.isPresent()) {
                ReportEquipmentFaultStatisticalDTO dto = new ReportEquipmentFaultStatisticalDTO();
                dto.setProductionLine(info.getProductionLine());
                dto.setEquipmentCode(info.getEquipmentCode());
                dto.setEquipmentName(info.getEquipmentName());
                dto.setRepairTime(BigDecimal.ZERO);
                dto.setMainFault(info.getFault());
                findResult = Optional.of(dto);
                result.add(dto);
            }
            ReportEquipmentFaultStatisticalDTO productEquipment = findResult.get();
            productEquipment.setRepairTime(productEquipment.getRepairTime().add(info.getFaultTime()));
            if (info.getFault().equals(productEquipment.getMainFault())) {
                productEquipment.setDownTimeOfMainFault(info.getFaultTime());
            }
        }
        for (ReportEquipmentFaultStatisticalDTO info : result) {
            info.setProportionOfTotalTime(info.getRepairTime().multiply(new BigDecimal(100))
                .divide(productLineTimeMap.get(info.getProductionLine()), 2, BigDecimal.ROUND_HALF_UP));
            info.setProportionOfMainFaultTime(info.getDownTimeOfMainFault().multiply(new BigDecimal(100))
                .divide(productLineTimeMap.get(info.getProductionLine()), 2, BigDecimal.ROUND_HALF_UP));
        }
        return result;
    }

    @Override
    public List<ReportRepairUserJobStatisticalDTO> repairUserJobStatistical(ReportRepairUserJobStatisticalQuery reportRepairUserJobStatisticalQuery) {
        List<RepairUserJobInfo> repairInfos =
            reportStatisticalMapper.repairUserJobFromRepair(reportRepairUserJobStatisticalQuery);
        List<RepairUserJobInfo> maintainInfos =
            reportStatisticalMapper.repairUserJobFromMaintain(reportRepairUserJobStatisticalQuery);
        List<ReportRepairUserJobStatisticalDTO> result = new ArrayList<>();
        if ((repairInfos == null || repairInfos.size() == 0)) {
            if ((maintainInfos == null || maintainInfos.size() == 0)) {
                log.info("维修人员工作统计表没有数据");
                return new ArrayList<>();
            }
        }
        //把两个list转换成一个list，这里转成repairInfos
        repairInfos.addAll(maintainInfos);
        for (RepairUserJobInfo repairInfo : repairInfos) {
            Optional<ReportRepairUserJobStatisticalDTO> findResult = result.stream()
                .filter(t -> t.getUserName().equals(repairInfo.getUserName())).findFirst();
            if (!findResult.isPresent()) {
                ReportRepairUserJobStatisticalDTO dto = new ReportRepairUserJobStatisticalDTO();
                dto.setUserName(repairInfo.getUserName());
                dto.setSumRepairTime(BigDecimal.ZERO);
                dto.setRepairCount(0);
                dto.setAvgRepairTime(BigDecimal.ZERO);
                dto.setSumMaintainTime(BigDecimal.ZERO);
                dto.setMaintainCount(0);
                dto.setAvgMaintainTime(BigDecimal.ZERO);
                dto.setSparePartUseAmount(BigDecimal.ZERO);
                findResult = Optional.of(dto);
                result.add(dto);
            }
            ReportRepairUserJobStatisticalDTO repairUserJob = findResult.get();
            repairUserJob.setSumRepairTime(repairUserJob.getSumRepairTime().add(repairInfo.getSumRepairTime()));
            repairUserJob.setRepairCount(Integer.sum(repairUserJob.getRepairCount(), repairInfo.getRepairCount()));
            repairUserJob.setAvgRepairTime(repairUserJob.getAvgRepairTime().add(repairInfo.getAvgRepairTime()));
            repairUserJob.setSumMaintainTime(repairUserJob.getSumMaintainTime().add(repairInfo.getSumMaintainTime()));
            repairUserJob.setMaintainCount(Integer.sum(repairUserJob.getMaintainCount(), repairInfo.getMaintainCount()));
            repairUserJob.setAvgMaintainTime(repairUserJob.getAvgMaintainTime().add(repairInfo.getAvgMaintainTime()));
            repairUserJob.setSparePartUseAmount(repairUserJob.getSparePartUseAmount().add(repairInfo.getSparePartUseAmount()));
        }
        return result;
    }

    /**
     * 设备维修查询
     *
     * @param reportQuery 查询条件
     * @return 维修信息
     */
    @Override
    public Page<MaintainReportDTO> getReportPageByQuery(ReportQuery reportQuery) {
        return PageHelperUtil.getPage(reportStatisticalMapper::getReportByQuery, reportQuery);
    }

    /**
     * 导入维修项目
     *
     * @param file 文件对象
     * @return 导入结果
     */
    @Override
    public ImportResult importMaintainReport(MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        maintainReportRepository.deleteAllInBatch();
        return HvImportUtil.importEntity(file, MaintainReportDTO.class, this);
    }

    /**
     * 获取设备模块整体的 汇总信息
     *
     * @return 汇总信息
     */
    @Override
    public SummaryDTO getSummary() {
        SummaryDTO result = new SummaryDTO();
        //获取配置的计划个数
        try {
            Integer maintainPlanCount = reportMapper.getMaintainPlanCount();
            result.setMaintainPlanCount(maintainPlanCount);
        } catch (Exception ex) {
            result.setMaintainPlanCount(0);
        }
        try {
            Integer inspectionPlanCount = reportMapper.getInspectionPlanCount();
            result.setInspectPlanCount(inspectionPlanCount);
        } catch (Exception ex) {
            result.setInspectPlanCount(0);
        }
        try {
            Integer autoPlanCount = reportMapper.getAutoPlanCount();
            result.setAutoPlanCount(autoPlanCount);
        } catch (Exception ex) {
            result.setAutoPlanCount(0);
        }
        try {
            Integer faultKnowledgeCount = reportMapper.getFaultKnowledge();
            result.setKnowledgeCount(faultKnowledgeCount);
        } catch (Exception ex) {
            result.setKnowledgeCount(0);
        }
        //获取当前月份开始
        LocalDate date = LocalDate.now();
        //获取数据
        LocalDateTime monthStartTime = date.atStartOfDay().minusDays(date.getDayOfMonth()-1);
        List<TaskSummaryDTO> taskSummary = reportMapper.getSummary(monthStartTime);
        //维修数据
        Optional<TaskSummaryDTO> faultInfo =
            taskSummary.stream()
                .filter(t -> "equipment-maintenance".equals(t.getDefinitionKey()))
                .findFirst();
        result.setFaultFinished(faultInfo.map(TaskSummaryDTO::getFinishedCount).orElse(0));
        result.setFaultTotal(faultInfo.map(TaskSummaryDTO::getTotalCount).orElse(0));
        //保养数据
        Optional<TaskSummaryDTO> maintainInfo =
            taskSummary.stream()
                .filter(t -> "equipment-maintain".equals(t.getDefinitionKey()))
                .findFirst();
        result.setMaintainFinished(maintainInfo.map(TaskSummaryDTO::getFinishedCount).orElse(0));
        result.setMaintainTotal(maintainInfo.map(TaskSummaryDTO::getTotalCount).orElse(0));
        //点巡检数据
        Optional<TaskSummaryDTO> inspectInfo =
            taskSummary.stream()
                .filter(t -> "equipment-inspect".equals(t.getDefinitionKey()))
                .findFirst();
        result.setInspectFinished(inspectInfo.map(TaskSummaryDTO::getFinishedCount).orElse(0));
        result.setInspectTotal(inspectInfo.map(TaskSummaryDTO::getTotalCount).orElse(0));
        //自主性维护数据
        Optional<TaskSummaryDTO> autoInfo =
            taskSummary.stream()
                .filter(t -> "autonomymaintenance".equals(t.getDefinitionKey()))
                .findFirst();
        result.setAutoFinished(autoInfo.map(TaskSummaryDTO::getFinishedCount).orElse(0));
        result.setAutoTotal(autoInfo.map(TaskSummaryDTO::getTotalCount).orElse(0));
        //返回数据
        return result;
    }

    @Override
    public void saveOrUpdate(MaintainReportDTO maintainReportDTO) {
        maintainReportRepository.save(DtoMapper.convert(maintainReportDTO, HvEamMaintainReport.class));
    }

}
