package com.hvisions.eam.service.maintain;

import com.hvisions.common.interfaces.EntitySaver;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.eam.dto.report.*;
import com.hvisions.eam.dto.report.SummaryDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <p>Title: ReportStatisticalService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/1/6</p >
 *
 * <AUTHOR> fengfeng
 * @version :1.0.0
 */
public interface ReportStatisticalService extends EntitySaver<MaintainReportDTO> {
    /**
     * 急维修 Pareto 分析的明细
     *
     * @param reportUrgentRepairParetoAnalysisQuery 急维修 Pareto 查询条件
     * @return 急维修 Pareto 统计记录
     */
    List<ReportUrgentRepairParetoAnalysisDTO> urgentRepairParetoAnalysis(ReportUrgentRepairParetoAnalysisQuery reportUrgentRepairParetoAnalysisQuery);

    /**
     * 设备故障统计表
     *
     * @param reportEquipmentFaultStatisticalQuery 设备故障查询条件
     * @return 设备故障统计记录
     */
    List<ReportEquipmentFaultStatisticalDTO> equipmentFaultStatistical(ReportEquipmentFaultStatisticalQuery reportEquipmentFaultStatisticalQuery);

    /**
     * 维修人员工作统计
     *
     * @param reportRepairUserJobStatisticalQuery 维修人员工作统计查询条件
     * @return 维修人员工作统计记录
     */
    List<ReportRepairUserJobStatisticalDTO> repairUserJobStatistical(ReportRepairUserJobStatisticalQuery reportRepairUserJobStatisticalQuery);

    /**
     * 设备维修查询
     *
     * @param reportQuery 查询条件
     * @return 维修信息
     */
    Page<MaintainReportDTO> getReportPageByQuery(ReportQuery reportQuery);

    /**
     * 导入维修
     *
     * @param file 文件对象
     * @return 导入结果
     */
    ImportResult importMaintainReport(MultipartFile file) throws IllegalAccessException, ParseException, IOException;
    /**
     * 获取设备模块整体的 汇总信息
     *
     * @return 汇总信息
     */
    SummaryDTO getSummary();
}