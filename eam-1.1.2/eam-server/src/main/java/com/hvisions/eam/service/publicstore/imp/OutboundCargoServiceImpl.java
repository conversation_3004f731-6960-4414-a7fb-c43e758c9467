package com.hvisions.eam.service.publicstore.imp;

import com.hvisions.eam.entity.publicstore.HvEamOutboundCargo;
import com.hvisions.eam.repository.publicstore.OutboundCargoRepository;
import com.hvisions.eam.service.publicstore.OutboundCargoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>Title:OutboundCargoService</p>
 * <p>Description:出库记录表</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/22</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Service
public class OutboundCargoServiceImpl implements OutboundCargoService {

    private OutboundCargoRepository outboundCargoRepository;

    @Autowired
    public OutboundCargoServiceImpl(OutboundCargoRepository outboundCargoRepository) {
        this.outboundCargoRepository = outboundCargoRepository;
    }

    @Override
    public Boolean isOut(String processInstanceid) {
        HvEamOutboundCargo byProcessInstanceID = outboundCargoRepository.findByProcessInstanceId(processInstanceid);
        if (byProcessInstanceID != null) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public HvEamOutboundCargo findByProcessInsttnceId(String processInstanceId) {
        return outboundCargoRepository.findByProcessInstanceId(processInstanceId);
    }

    @Override
    public HvEamOutboundCargo save(HvEamOutboundCargo hvEamOutboundCargo) {
        return outboundCargoRepository.save(hvEamOutboundCargo);
    }
}
