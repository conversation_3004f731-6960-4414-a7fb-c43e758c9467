package com.hvisions.eam.service.inspect.impl;

import com.hvisions.activiti.dto.process.ProcessDefinationQuery;
import com.hvisions.activiti.dto.process.ProcessDefinitionDTO;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.client.inspect.InspectActivitiClient;
import com.hvisions.eam.service.inspect.InspectCheckActivitiBpmnService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <p>Title: CheckActivitiBpmnServiceImpl</p >
 * <p>Description: 检查bpmn文件是否存在</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/10</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class InspectCheckActivitiBpmnServiceImpl implements InspectCheckActivitiBpmnService {
    private final InspectActivitiClient inspectActivitiClient;
    /**
     * 流程文件发布配置
     */
    @Value("${h-visions.bpmn-update}")
    private Boolean bpmnUpdate;

    @Autowired
    public InspectCheckActivitiBpmnServiceImpl(@Qualifier("inspectActivitiClient") InspectActivitiClient inspectActivitiClient) {
        this.inspectActivitiClient = inspectActivitiClient;
    }

    /**
     * 检查流程定义
     */
    @Override
    public void checkActivitiBpmn() {
        String inspectKey = "equipment-inspect";
        String inspectApproveKey = "inspect-approve";
        String inspectResourceName = "inspect.bpmn20.xml";
        String approveResourceName = "点巡检计划审批.bpmn20.xml";
        //强制发布
        if (bpmnUpdate) {
            log.info("强制发布流程文件开始");
            //点检任务流程
            deployProcess(inspectResourceName);
            //计划审批流程
            deployProcess(approveResourceName);
        } else {
            //判断是否存在，不存在则发布
            //点检任务流程
            checkBpmnAndDeploy(inspectKey, inspectResourceName);
            //审批流程
            checkBpmnAndDeploy(inspectApproveKey, approveResourceName);
        }
    }


    /**
     * 读取文件
     *
     * @param inputStream 输入流
     * @return 文件内容
     * @throws IOException io
     */
    private String readFile(InputStream inputStream) throws IOException {
        StringBuilder builder = new StringBuilder();
        try {
            InputStreamReader reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
            BufferedReader bfReader = new BufferedReader(reader);
            String tmpContent = null;
            while ((tmpContent = bfReader.readLine()) != null) {
                builder.append(tmpContent);
            }
            bfReader.close();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return this.filter(builder.toString());
    }


    /**
     * 过滤输入字符串, 剔除多行注释以及替换掉反斜杠
     *
     * @param input 内容
     * @return 替换后的内容
     */
    private String filter(String input) {
        return input.replaceAll("/\\*[\\s\\S]*?\\*/", "");
    }

    /**
     * 检查流程定义是否存在，不存在则发布
     *
     * @param key                 key
     * @param inspectResourceName 文件名
     */
    private void checkBpmnAndDeploy(String key, String inspectResourceName) {
        ProcessDefinationQuery processDefinationQuery = new ProcessDefinationQuery();
        processDefinationQuery.setProcessDefinitionKey(key);
        ResultVO<List<ProcessDefinitionDTO>> inspectProcess = inspectActivitiClient.getAllProcess(processDefinationQuery);
        if (inspectProcess.isSuccess()) {
            List<ProcessDefinitionDTO> data = inspectProcess.getData();
            if (data.size() > 0) {
                //取第一个进行判断
                ProcessDefinitionDTO processDefinitionDTO = data.get(0);
                if (processDefinitionDTO.getKey().equals(key)) {
                    log.info(key + "定义文件存在");
                } else {
                    //发布流程
                    deployProcess(inspectResourceName);
                }
            } else {
                //发布流程
                deployProcess(inspectResourceName);
            }
        } else {
            log.error(key + "流程定义查询失败{}", inspectProcess);
        }
    }

    /**
     * 发布流程
     *
     * @param inspectResourceName resourceName
     */
    private void deployProcess(String inspectResourceName) {
        InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("process/" + inspectResourceName);
        String configContent = null;
        try {
            configContent = readFile(inputStream);
        } catch (IOException e) {
            e.printStackTrace();
        }
        ResultVO resultVO = inspectActivitiClient.deployByText(inspectResourceName, configContent);
        if (resultVO.isSuccess()) {
            log.info(inspectResourceName + "流程定义发布成功");
        } else {
            log.info(inspectResourceName + "流程定义发布失败{}", resultVO);
        }
    }
}