package com.hvisions.eam.service.spare;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.EntitySaver;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.spare.SpareExtendDTO;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;

/**
 * <p>Title:SpareExtendService</p>
 * <p>Description:a</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/5/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
public interface SpareExtendService extends EntitySaver<SpareExtendDTO> {


    /**
     * 导出备件信息 (文件流)
     *
     * @return 备件信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    ResponseEntity<byte[]> exportEquipmentLink() throws IOException, IllegalAccessException;


    /**
     * 导出所有备件信息 (excel)
     *
     * @return 设备信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    ResultVO<ExcelExportDto> exportSparePart() throws IOException, IllegalAccessException;

    /**
     * 导入所有设备信息
     *
     * @param file 设备信息excel
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    ImportResult importEquipment(MultipartFile file) throws IllegalAccessException, ParseException, IOException;


}
