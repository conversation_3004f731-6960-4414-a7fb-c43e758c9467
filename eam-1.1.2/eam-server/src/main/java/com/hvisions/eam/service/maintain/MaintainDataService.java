package com.hvisions.eam.service.maintain;

import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.support.ListenerExecutionFailedException;

/**
 * <p>Title: MaintainDataService</p>
 * <p>Description: 处理设备保养的相关数据</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/9/28</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface MaintainDataService {
    /**
     * 处理流程实例信息
     *
     * @param processInstanceId 流程实例id
     */
    void save(String processInstanceId);

    /**
     * 处理业务异常
     * @param message 消息
     * @param e 异常
     */
    void handleError(Message message, ListenerExecutionFailedException e);
}









