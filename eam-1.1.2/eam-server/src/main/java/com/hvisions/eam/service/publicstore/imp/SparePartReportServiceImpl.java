package com.hvisions.eam.service.publicstore.imp;

import com.hvisions.eam.dto.publicstore.SparePartInfo;
import com.hvisions.eam.dao.ReportDao;
import com.hvisions.eam.service.publicstore.SparePartReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>Title: SparePartReportServiceImpl</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2022/5/16</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
public class SparePartReportServiceImpl implements SparePartReportService {
    private final ReportDao reportDao;

    @Autowired
    public SparePartReportServiceImpl(ReportDao reportDao) {
        this.reportDao = reportDao;
    }

    /**
     * 获取设备使用的备件信息
     *
     * @param equipmentId 设备id
     * @return 备件统计数据
     */
    @Override
    public List<SparePartInfo> equipmentSparePart(Integer equipmentId) {
        return reportDao.getEquipmentSparePart(equipmentId);
    }
}









