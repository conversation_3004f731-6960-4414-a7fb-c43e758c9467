package com.hvisions.eam.service.publicstore.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.eam.configuration.spare.StoreConfig;
import com.hvisions.eam.enums.StoreExceptionEnum;
import com.hvisions.eam.entity.lub.HvEamLubType;
import com.hvisions.eam.entity.lub.HvEamLubricating;
import com.hvisions.eam.repository.lub.LubEntityRepository;
import com.hvisions.eam.repository.lub.LubTypeEntityRepository;
import com.hvisions.eam.dto.publicstore.UseListingLubDTO;
import com.hvisions.eam.dto.publicstore.UseListingSpareDTO;
import com.hvisions.eam.query.publicstore.UseListingQueryDTO;
import com.hvisions.eam.entity.publicstore.HvEamUseListing;
import com.hvisions.eam.repository.publicstore.UseListingRepository;
import com.hvisions.eam.service.publicstore.UseListingService;
import com.hvisions.eam.entity.spare.HvEamSpare;
import com.hvisions.eam.entity.spare.HvEamSpareType;
import com.hvisions.eam.repository.sprare.SpareEntityRepository;
import com.hvisions.eam.repository.sprare.SpareTypeEntityRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title:UseListingServiceImpl</p>
 * <p>Description:备件使用清单</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/5/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Slf4j
@Service
public class UseListingServiceImpl implements UseListingService {

    /**
     * 备件类型
     */
    private final static Integer TYPE_SPARE = 1;

    /**
     * 油品类型
     */
    private final static Integer TYPE_LUB = 2;

    /**
     * 使用清单jpa
     */
    private final UseListingRepository useListingRepository;

    /**
     * 备件jpa
     */
    private final SpareEntityRepository spareEntityRepository;

    /**
     * 备件类型
     */
    private final SpareTypeEntityRepository spareTypeEntityRepository;

    /**
     * 油品类型
     */
    private final LubTypeEntityRepository lubTypeEntityRepository;
    /**
     * 油品jpa
     */
    private final LubEntityRepository lubEntityRepository;

    @Autowired
    public UseListingServiceImpl(UseListingRepository useListingRepository,
                                 SpareEntityRepository spareEntityRepository,
                                 LubEntityRepository lubEntityRepository,
                                 SpareTypeEntityRepository spareTypeEntityRepository,
                                 LubTypeEntityRepository lubTypeEntityRepository) {
        this.useListingRepository = useListingRepository;
        this.spareEntityRepository = spareEntityRepository;
        this.lubEntityRepository = lubEntityRepository;
        this.spareTypeEntityRepository = spareTypeEntityRepository;
        this.lubTypeEntityRepository = lubTypeEntityRepository;
    }

    /**
     * 增加  使用清单DTO
     *
     * @param useListingSpareDTO 使用清单DTO
     * @return id
     */
    @Override
    public Integer create(UseListingSpareDTO useListingSpareDTO) {
        log.info(useListingSpareDTO.toString());
        HvEamUseListing useListing = useListingRepository.findByProcessInstanceIdAndTypeAndSpareId(useListingSpareDTO.getProcessInstanceId(), useListingSpareDTO.getType(), useListingSpareDTO.getSpareId());
        if (useListing == null) {
            return useListingRepository.save(DtoMapper.convert(useListingSpareDTO, HvEamUseListing.class)).getId();
        } else {
            useListing.setNumber(useListing.getNumber().add(useListingSpareDTO.getNumber()));
            return useListingRepository.save(useListing).getId();
        }
    }

    /**
     * 批量增加  使用清单DTO
     *
     * @param useListingSpareDTOS 使用清单DTO
     * @return id
     */
    @Override
    public List<Integer> createList(List<UseListingSpareDTO> useListingSpareDTOS) {
        List<Integer> ids = new ArrayList<>();
        for (UseListingSpareDTO useListingSpareDTO : useListingSpareDTOS) {
            ids.add(this.create(useListingSpareDTO));
        }
        return ids;
    }

    /**
     * 通过 processInstanceId 获取全部关联项
     * todo 添加批次號
     *
     * @param useListingQueryDTO 关联id
     * @return 分页
     */
    @Override
    @SuppressWarnings(StoreConfig.RULES)
    public Page<UseListingSpareDTO> getSpareByProcessInstanceId(UseListingQueryDTO useListingQueryDTO) {
        //关系不存在
        if (useListingQueryDTO.getProcessInstanceId() == null) {
            throw new BaseKnownException(StoreExceptionEnum.TYPE_IS_NULL_EXCEPTION);
        }
        //把DTO转换成普通类
        HvEamUseListing useListing = DtoMapper.convert(useListingQueryDTO, HvEamUseListing.class);
        //类型为备件
        useListing.setType(TYPE_SPARE);
        //匹配器
        Example<HvEamUseListing> example = Example.of(useListing);
        //查询
        Page<HvEamUseListing> page = useListingRepository.findAll(example, useListingQueryDTO.getRequest());
        //传递id
        Page<UseListingSpareDTO> shelveDTOS = DtoMapper.convertPage(page, UseListingSpareDTO.class);
        for (HvEamUseListing hvEamUseListing : page) {
            for (UseListingSpareDTO shelveDTO : shelveDTOS) {
                if (hvEamUseListing.getProcessInstanceId().equals(shelveDTO.getProcessInstanceId()) && hvEamUseListing.getSpareId().equals(shelveDTO.getSpareId())) {
                    shelveDTO.setId(hvEamUseListing.getId());
                }
            }
        }
        for (UseListingSpareDTO useListingdtoSpare : shelveDTOS) {
            //类型为备件
            //判断备件id是否存在
            Optional<HvEamSpare> spare = spareEntityRepository.findById(useListingdtoSpare.getSpareId());
            //获取备件名称
            useListingdtoSpare.setSpareName(spare.map(t -> t.getSpareName()).orElse(""));
            useListingdtoSpare.setSpareCode(spare.map(t -> t.getSpareCode()).orElse(""));
            spare.map(t -> t.getSpareTypeId()).ifPresent(t -> {
                Optional<HvEamSpareType> type = spareTypeEntityRepository.findById(t);
                useListingdtoSpare.setSpareTypeName(type.map(l -> l.getTypeName()).orElse(""));
            });

        }
        return shelveDTOS;
    }

    /**
     * 通过 processInstanceId 获取全部关联项
     *
     * @param useListingQueryDTO 关联id
     * @return 分页
     */
    @Override
    @SuppressWarnings(StoreConfig.RULES)
    public Page<UseListingLubDTO> getLubByProcessInstanceId(UseListingQueryDTO useListingQueryDTO) {
        //关系不存在
        if (useListingQueryDTO.getProcessInstanceId() == null) {
            throw new BaseKnownException(StoreExceptionEnum.TYPE_IS_NULL_EXCEPTION);
        }
        //把DTO转换成普通类
        HvEamUseListing useListing = DtoMapper.convert(useListingQueryDTO, HvEamUseListing.class);
        //类型为油品
        useListing.setType(TYPE_LUB);
        //匹配方式       xxx 采用开始匹配的方式查询 忽略大小写
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
            .withMatcher("processInstanceId", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());

        //匹配器
        Example<HvEamUseListing> example = Example.of(useListing, exampleMatcher);
        //查询
        Page<HvEamUseListing> page = useListingRepository.findAll(example, useListingQueryDTO.getRequest());

        Page<UseListingLubDTO> shelveDTOS = DtoMapper.convertPage(page, UseListingLubDTO.class);
        for (HvEamUseListing hvEamUseListing : page) {
            for (UseListingLubDTO shelveDTO : shelveDTOS) {
                if (hvEamUseListing.getProcessInstanceId().equals(shelveDTO.getProcessInstanceId()) && hvEamUseListing.getSpareId().equals(shelveDTO.getSpareId())) {
                    shelveDTO.setId(hvEamUseListing.getId());
                }
            }
        }
        for (UseListingLubDTO useListingdtoSpare : shelveDTOS) {
            //类型为备件
            //判断备件id是否存在
            if (lubEntityRepository.existsById(useListingdtoSpare.getSpareId())) {
                HvEamLubricating lub = lubEntityRepository.getOne(useListingdtoSpare.getSpareId());
                //获取备件名称
                useListingdtoSpare.setLubName(lub.getLubName());
                useListingdtoSpare.setLubCode(lub.getLubCode());
                if (lubTypeEntityRepository.existsById(lub.getLubTypeId())) {
                    HvEamLubType type = lubTypeEntityRepository.getOne(lub.getLubTypeId());
                    useListingdtoSpare.setLubTypeName(type.getTypeName());
                } else {
                    useListingdtoSpare.setLubTypeName("");
                }
            } else {
                useListingdtoSpare.setLubName("");
                useListingdtoSpare.setLubCode("");
                useListingdtoSpare.setLubTypeName("");
            }
        }
        return shelveDTOS;
    }
}
