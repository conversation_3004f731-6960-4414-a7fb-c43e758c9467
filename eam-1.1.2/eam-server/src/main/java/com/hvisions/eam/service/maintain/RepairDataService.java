package com.hvisions.eam.service.maintain;

import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.support.ListenerExecutionFailedException;

/**
 * <p>Title: RepairDataService</p>
 * <p>Description: 设备维修流程数据处理</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/11/5</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface RepairDataService {

    /**
     * 保留流程数据
     * @param processInstanceId 流程实例id
     */
    void save(String processInstanceId);

    /**
     * 处理异常
     * @param message 消息信息
     * @param e 异常
     */
    void handleError(Message message, ListenerExecutionFailedException e);
}

    
    
    
    