package com.hvisions.eam.service.publicstore;

import com.hvisions.eam.activiti.lub.ItemsLubDTO;
import com.hvisions.eam.activiti.sapre.ItemsSpareDTO;
import com.hvisions.eam.dto.publicstore.ActualUseDTO;
import com.hvisions.eam.dto.publicstore.ApplyDTO;
import com.hvisions.eam.query.publicstore.ActualUseQueryDTO;
import com.hvisions.eam.dto.spare.SpareItemDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <p>Title:ActualUseService</p>
 * <p>Description:实际使用</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/5/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
public interface ActualUseService {

    /**
     * 增加
     *
     * @param actualUseDTO 使用清单DTO
     * @return id
     */
    Integer create(ActualUseDTO actualUseDTO);

    /**
     * 批量增加
     *
     * @param actualUseDTOS 使用清单DTO List
     * @return id
     */
    List<Integer> createActualUseList(List<ActualUseDTO> actualUseDTOS);

    /**
     * 修改
     *
     * @param actualUseDTO 使用清单DTO
     * @return id
     */
    Integer update(ActualUseDTO actualUseDTO);

    /**
     * 通过id删除
     *
     * @param id id
     */
    void deleteById(Integer id);

    /**
     * 通过Id list删除
     *
     * @param ids id
     */
    void deleteByListId(List<Integer> ids);

    /**
     * 获取备件实际使用列表
     *
     * @param actualUseQueryDTO 关联id
     * @return 分页
     */
    Page<ActualUseDTO> getActual(ActualUseQueryDTO actualUseQueryDTO);


    /**
     * 获取备件实际申请列表 以集合形式返回
     *
     * @param actualUseQueryDTO     查询条件
     * @param processDefinitionKey 流程图
     * @return 全部信息
     */
    List<ActualUseDTO> spareApplyThrough(ActualUseQueryDTO actualUseQueryDTO, String processDefinitionKey);

    /**
     * 获取油品实际申请列表 以集合形式返回
     *
     * @param actualUseQueryDTO     查询条件
     * @param processDefinitionKey 流程图
     * @return 全部信息
     */
    List<ActualUseDTO> lubApplyThrough(ActualUseQueryDTO actualUseQueryDTO, String processDefinitionKey);

    /**
     * 通过BusinessKey获取备件油品类型
     *
     * @param processInstanceBusinessKey 唯一标识符
     * @param type                       类型 1备件 2 油品
     * @return 申请单list
     */
    List<ApplyDTO> getSpareOrLubApply(String processInstanceBusinessKey, Integer type);

    /**
     * 通过BusinessKey获取全部申请通过的申请项数据
     *
     * @param processInstanceBusinessKey 唯一标识符
     * @return 申请项 list
     */
    List<ItemsSpareDTO> getSpareApplyList(String processInstanceBusinessKey);

    /**
     * 通过BusinessKey获取全部申请通过的申请项数据
     *
     * @param processInstanceBusinessKey 唯一标识符
     * @return 申请项 list
     */
    List<ItemsLubDTO> getLubApplyList(String processInstanceBusinessKey);

    /**
     * 通过processInstanceIds 获取实际使用获取油品申请通过的申请项数据
     *
     * @param processInstanceIds 流程实例Id
     * @return 申请项 list
     */
    List<ActualUseDTO> getActualUseByProcessInstanceIds(List<String> processInstanceIds);


    /**
     * 通过businessKey 获取备件申请通过并出库的 备件
     *
     * @param processInstanceId 流程实例id
     * @param type 类型
     * @return 申请通过并出库 可以使用的备件
     */
    List<SpareItemDTO> getSpareOrLubCanUse(String processInstanceId, Integer type);


}
