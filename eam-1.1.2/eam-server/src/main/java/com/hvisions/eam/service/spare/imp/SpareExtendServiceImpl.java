package com.hvisions.eam.service.spare.imp;

import cn.hutool.core.util.ObjectUtil;
import com.hvisions.common.component.HvisionsI18nInternational;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.configuration.spare.StoreConfig;
import com.hvisions.eam.consts.SpareConsts;
import com.hvisions.eam.dto.publicstore.UnitDTO;
import com.hvisions.eam.dto.spare.SpareDTO;
import com.hvisions.eam.dto.spare.SpareExtendDTO;
import com.hvisions.eam.dto.spare.SpareTypeDTO;
import com.hvisions.eam.entity.spare.HvEamSpare;
import com.hvisions.eam.entity.spare.HvEamSpareBrand;
import com.hvisions.eam.enums.StoreExceptionEnum;
import com.hvisions.eam.repository.sprare.SpareBrandRepository;
import com.hvisions.eam.repository.sprare.SpareEntityRepository;
import com.hvisions.eam.repository.sprare.SpareTypeEntityRepository;
import com.hvisions.eam.service.publicstore.SpareUnitService;
import com.hvisions.eam.service.spare.SpareExtendService;
import com.hvisions.eam.service.spare.SpareTypeService;
import com.hvisions.framework.client.BaseUserClient;
import com.hvisions.framework.dto.user.UserBaseDTO;
import com.hvisions.framework.dto.user.UserInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>Title:SpareServiceExtendImpl</p>
 * <p>Description:备件导出实现类</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/5/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Slf4j
@Service
public class SpareExtendServiceImpl implements SpareExtendService {

    /**
     * 备件jpa
     */
    private final SpareEntityRepository spareEntityRepository;

    private final SpareBrandRepository spareBrandRepository;
    /**
     * 设备类型service
     */
    private final SpareTypeService spareTypeService;

    /**
     * 备件单位service
     */
    private final SpareUnitService spareUnitService;

    /**
     * 国际化
     */
    private final HvisionsI18nInternational international;


    /**
     * 租户ID
     */
    private final HttpServletRequest request;

    private final BaseUserClient authClient;

    @Autowired
    public SpareExtendServiceImpl(SpareEntityRepository spareEntityRepository,
                                  SpareBrandRepository spareBrandRepository, SpareTypeService spareTypeService,
                                  SpareUnitService spareUnitService, HvisionsI18nInternational international, HttpServletRequest request, BaseUserClient authClient) {
        //调用父类的构造函数，传入jpa对象，实现普通的增删改查功能
        this.spareEntityRepository = spareEntityRepository;
        this.spareBrandRepository = spareBrandRepository;
        this.spareUnitService = spareUnitService;
        this.spareTypeService = spareTypeService;
        this.international = international;
        this.request = request;
        this.authClient = authClient;
    }

    /**
     * 导出所有备件信息 1
     *
     * @return 设备信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @Override
    public ResponseEntity<byte[]> exportEquipmentLink() throws IOException, IllegalAccessException {
        //获取全部备件数据
        List<SpareDTO> list = this.getAllSpare();
        //设置一些相关属性
        List<SpareExtendDTO> extendDTOS = this.attributeAddById(list);
        //返回文件    参数列表 1.全部数据 2.表格名称  3.导出模板类型.class 4.spareExtendService.getExtendColumnInfo() 扩展属性
        return ExcelUtil.generateImportFile(extendDTOS, SpareConsts.SPARE_EXPORT_FILE_NAME, SpareExtendDTO.class, null);
    }


    /**
     * 导出所有备件信息 2
     *
     * @return 设备信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @Override
    public ResultVO<ExcelExportDto> exportSparePart() throws IOException, IllegalAccessException {
        //获取全部备件数据
        List<SpareDTO> list = this.getAllSpare();
        //设置一些相关属性
        List<SpareExtendDTO> extendDTOS = this.attributeAddById(list);
        //相应本体 参数列表 1.全部数据 2.表格名称  3.导出模板类型.class 4.spareExtendService.getExtendColumnInfo() 扩展属性
        ResponseEntity<byte[]> result = ExcelUtil.generateImportFile(extendDTOS, SpareConsts.SPARE_EXPORT_FILE_NAME, SpareExtendDTO.class, null);
        //excel导出DTO
        ExcelExportDto excelExportDto = new ExcelExportDto();
        //设置文件名称
        excelExportDto.setFileName(SpareConsts.SPARE_EXPORT_FILE_NAME);
        //设置文件内容
        excelExportDto.setBody(result.getBody());
        return ResultVO.success(excelExportDto);
    }

    /**
     * 导入所有设备信息
     *
     * @param file 设备信息excel
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @Override
    public ImportResult importEquipment(MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        ImportResult importResult = ExcelUtil.importEntity(file, SpareExtendDTO.class, this);
        //替换报错信息
        Map<Integer, String> errorLines = importResult.getErrorLines();
        if (errorLines.size() > 0) {
            errorLines.keySet().forEach(key -> errorLines.put(key, international.getMessage(request, errorLines.get(key))));
        }
        importResult.setErrorLines(errorLines);
        return importResult;
    }

    /**
     * 设置DTO中一些相关属性
     */
    private List<SpareExtendDTO> attributeAddById(List<SpareDTO> list) {
        return DtoMapper.convertList(list, SpareExtendDTO.class);
    }

    /**
     * 获取所有的备件类型
     *
     * @return list集合
     */
    private List<SpareDTO> getAllSpare() {
        //获取所有的备件类型
        List<SpareDTO> list = DtoMapper.convertList(spareEntityRepository.findAll(), SpareDTO.class);
        List<Integer> purchaseUserIdsList = new ArrayList<>();
        List<Integer> skillUserIdsList = new ArrayList<>();
        for (SpareDTO spare : list) {
            SpareTypeDTO typeDTO = spareTypeService.findById(spare.getSpareTypeId());
            spare.setTypeName(typeDTO.getTypeName());
            UnitDTO unitDTO = spareUnitService.findById(spare.getUnitId());
            spare.setUnitName(unitDTO.getUnitName());
            if (ObjectUtil.isNotNull(spare.getPurchaseUserId())) {
                purchaseUserIdsList.add(spare.getPurchaseUserId());
            }
            if (ObjectUtil.isNotNull(spare.getSkillUserId())) {
                skillUserIdsList.add(spare.getSkillUserId());
            }
        }
        //填充采购工程师,技术工程师名称
        List<UserInfoDto> purchaseUsers = authClient.getUserInfoByIds(purchaseUserIdsList).getData();
        List<UserInfoDto> skillUsers = authClient.getUserInfoByIds(skillUserIdsList).getData();
        if (CollectionUtils.isNotEmpty(purchaseUsers)) {
            list.forEach(l -> {
                String purchaseName = purchaseUsers.stream()
                    .filter(u -> u.getId().equals(l.getPurchaseUserId()))
                    .findFirst()
                    .map(UserInfoDto::getUserName)
                    .orElse("");
                l.setPurchaseUserName(purchaseName);
            });
        }
        if (CollectionUtils.isNotEmpty(skillUsers)) {
            list.forEach(l -> {
                String skillUserName = skillUsers.stream()
                    .filter(u -> u.getId().equals(l.getSkillUserId()))
                    .findFirst()
                    .map(UserInfoDto::getUserName)
                    .orElse("");
                l.setSkillUserName(skillUserName);
            });
        }
        return list;
    }

    /**
     * 向数据库添加数据
     *
     * @param spareDTO 数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(SpareExtendDTO spareDTO) {
        //通过编码cod获取备件
        HvEamSpare spare = spareEntityRepository.findAllBySpareCode(spareDTO.getSpareCode());
        if (spare == null) {
            spare = DtoMapper.convert(spareDTO, HvEamSpare.class);
            spare.setCreateTime(new Date());
            spare.setSpareCode(spareDTO.getSpareCode());
        }
        UnitDTO unitDTO = spareUnitService.findByUnitName(spareDTO.getUnitName());
        //单位不存在 不会添加数据
        if (unitDTO == null) {
            log.error("导入数据：" + spareDTO + " - 单位不存在");
            //没有这个单位名称的单位
            throw new BaseKnownException(StoreExceptionEnum.A_UNIT_WITHOUT_THIS_UNIT_NAME);

        }
        if (StringUtils.isBlank(spareDTO.getBrand())) {
            throw new BaseKnownException(10000, "备件品牌不能为空");
        }
        //查询备件品牌是否存在
        HvEamSpareBrand spareBrand = spareBrandRepository.findBySpareBrandName(spareDTO.getBrand());
        if (ObjectUtil.isNull(spareBrand)) {
            throw new BaseKnownException(10000, "备件品牌不存在");
        }
        if (StringUtils.isBlank(spareDTO.getSpareName())) {
            throw new BaseKnownException(10000, "备件名称不能为空");
        }
        //验证如果未填则 赋值默认值为0
        spareDTO.validation();
        //如果价格错误 不会添加数据
        if (spareDTO.getUnitPrice().compareTo(new BigDecimal(StoreConfig.ZERO)) < -1) {
            log.error("导入数据：" + spareDTO + " - 价格错误");
            //价格是错误的
            throw new BaseKnownException(StoreExceptionEnum.PRICE_IS_WRONG);
        }
        //如果库存大小错误 不会添加数据
        if (spareDTO.validation() == -1) {
            log.error("导入数据：" + spareDTO + " - 库存错误");
            //库存数量错误
            throw new BaseKnownException(StoreExceptionEnum.INVENTORY_QUANTITY_ERROR);
        }
        //如果类型不存在 不会添加数据
        Integer typeId = spareTypeService.findByTypeName(spareDTO.getTypeName());
        if (typeId == -1) {
            log.error("导入数据：" + spareDTO + " - 类型不存在");
            //没有具有此类型名称的类型
            throw new BaseKnownException(StoreExceptionEnum.THERE_IS_NO_TYPE_WITH_THIS_TYPE_NAME);
        }
        if (spareDTO.getSkillUserName() != null) {
            ResultVO<List<UserBaseDTO>> userListByUserName = authClient.getUserListByUserName(spareDTO.getSkillUserName());
            if (!userListByUserName.isSuccess()) {
                throw new BaseKnownException(userListByUserName);
            }
            if (userListByUserName.getData() != null && userListByUserName.getData().size() > 0) {
                Integer skillUserId = userListByUserName.getData().stream()
                    .filter(u -> u.getUserName().equals(spareDTO.getSkillUserName()))
                    .findFirst().map(UserBaseDTO::getId)
                    .orElse(null);
                spare.setSkillUserId(skillUserId);
            }
        }
        if (spareDTO.getPurchaseUserName() != null) {
            ResultVO<List<UserBaseDTO>> userListByUserName1 = authClient.getUserListByUserName(spareDTO.getPurchaseUserName());
            if (!userListByUserName1.isSuccess()) {
                throw new BaseKnownException(userListByUserName1);
            }

            if (userListByUserName1.getData() != null && userListByUserName1.getData().size() > 0) {
                Integer purchaseUserId = userListByUserName1.getData().stream()
                    .filter(u -> u.getUserName().equals(spareDTO.getPurchaseUserName()))
                    .findFirst().map(UserBaseDTO::getId)
                    .orElse(null);
                spare.setPurchaseUserId(purchaseUserId);
            }
        }
        //单位
        spare.setUnitId(unitDTO.getId());
        //更改名称
        spare.setSpareName(spareDTO.getSpareName());
        //添加最小值
        spare.setCargoMin(spareDTO.getCargoMin());
        //添加最大值
        spare.setCargoMax(spareDTO.getCargoMax());
        //备注
        spare.setRemarks(spareDTO.getRemarks());
        //类型id
        spare.setSpareTypeId(typeId);
        //价格
        spare.setUnitPrice(spareDTO.getUnitPrice());
        //供应商
        spare.setSupplier(spareDTO.getSupplier());
        //修改时间
        spare.setUpdateTime(new Date());
        //品牌
        spare.setBrand(spareBrand.getSpareBrandName());
        //规格
        spare.setSpecifications(spareDTO.getSpecifications());
        spare.setMetype(spareDTO.getMetype());
        //修改
        spareEntityRepository.save(spare);

    }
}
