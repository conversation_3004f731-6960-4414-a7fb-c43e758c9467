package com.hvisions.eam.service.maintain.imp;

import com.google.common.collect.Maps;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.utils.SerialUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dao.MaintainItemMapper;
import com.hvisions.eam.dto.SysBaseDTO;
import com.hvisions.eam.dto.lub.LubricatingDTO;
import com.hvisions.eam.dto.maintain.*;
import com.hvisions.eam.dto.spare.SpareDTO;
import com.hvisions.eam.entity.SysBase;
import com.hvisions.eam.entity.maintain.*;
import com.hvisions.eam.entity.spare.HvEamSpare;
import com.hvisions.eam.enums.MaintainErrorExceptionEnum;
import com.hvisions.eam.excel.MaintainItemImportDTO;
import com.hvisions.eam.repository.maintain.*;
import com.hvisions.eam.repository.sprare.SpareEntityRepository;
import com.hvisions.eam.repository.sprare.SpareTypeEntityRepository;
import com.hvisions.eam.service.maintain.MaintainItemService;
import com.hvisions.hiperbase.client.EquipmentFeignClient;
import com.hvisions.hiperbase.client.EquipmentTypeClient;
import com.hvisions.hiperbase.equipment.EquipmentDTO;
import com.hvisions.hiperbase.equipment.EquipmentTypeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title: MaintainItemServiceImpl</p >
 * <p>Description: 保养项目实现</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/18</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class MaintainItemServiceImpl implements MaintainItemService {
    private final MaintainItemRepository maintainItemRepository;
    private final ContentItemRepository contentItemRepository;
    private final ItemSparePartRepository itemSparePartRepository;
    private final ItemLubRepository itemLubRepository;
    private final MaintainItemFileRepository maintainItemFileRepository;
    private final SerialUtil serialUtil;
    private final MaintainItemMapper maintainItemMapper;
    private final MaintainItemEquipmentRepository maintainItemEquipmentRepository;
    private final MaintainItemEquipmentTypeRepository maintainItemEquipmentTypeRepository;
    private final EquipmentTypeClient equipmentTypeClient;
    private final EquipmentFeignClient equipmentFeignClient;
    private final MaintainServiceHandler serviceHandler;
    private final SpareTypeEntityRepository spareTypeEntityRepository;

    private final SpareEntityRepository spareEntityRepository;
    @Autowired
    public MaintainItemServiceImpl(MaintainItemRepository hvMaintainItemRepository,
                                   ContentItemRepository contentItemRepository,
                                   ItemSparePartRepository itemSparePartLubRepository,
                                   ItemLubRepository itemLubRepository,
                                   MaintainItemFileRepository maintainItemFileRepository,
                                   SerialUtil serialUtil,
                                   MaintainItemMapper maintainItemMapper,
                                   MaintainItemEquipmentRepository maintainItemEquipmentRepository,
                                   MaintainItemEquipmentTypeRepository maintainItemEquipmentTypeRepository,
                                   EquipmentTypeClient equipmentTypeClient,
                                   EquipmentFeignClient equipmentFeignClient,
                                   MaintainServiceHandler serviceHandler,
                                   SpareTypeEntityRepository spareTypeEntityRepository,
                                   SpareEntityRepository spareEntityRepository) {
        this.maintainItemRepository = hvMaintainItemRepository;
        this.contentItemRepository = contentItemRepository;
        this.itemSparePartRepository = itemSparePartLubRepository;
        this.itemLubRepository = itemLubRepository;
        this.maintainItemFileRepository = maintainItemFileRepository;
        this.serialUtil = serialUtil;
        this.maintainItemMapper = maintainItemMapper;
        this.maintainItemEquipmentRepository = maintainItemEquipmentRepository;
        this.maintainItemEquipmentTypeRepository = maintainItemEquipmentTypeRepository;
        this.equipmentTypeClient = equipmentTypeClient;
        this.equipmentFeignClient = equipmentFeignClient;
        this.serviceHandler = serviceHandler;
        this.spareTypeEntityRepository = spareTypeEntityRepository;
        this.spareEntityRepository = spareEntityRepository;
    }


    /**
     * 删除单个保养项目
     *
     * @param id 要删除的项目id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMaintainItemById(Integer id) {
        //判断保养项目和保养内容是否有绑定
        if (contentItemRepository.existsByItemId(id)) {
            throw new BaseKnownException(MaintainErrorExceptionEnum.DELETE_ITEM_ERROR);
        }
        //删除关联关系
        itemSparePartRepository.deleteAllByMaintainItemId(id);
        itemLubRepository.deleteAllByMaintainItemId(id);
        maintainItemFileRepository.deleteAllByMaintainItemId(id);
        maintainItemEquipmentTypeRepository.deleteAllByItemId(id);
        maintainItemEquipmentRepository.deleteAllByItemId(id);
        maintainItemRepository.deleteById(id);
    }

    /**
     * 批量删除
     *
     * @param idList 要删除的项目id集合
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMaintainItemByIdList(List<Integer> idList) {
        for (Integer id : idList) {
            deleteMaintainItemById(id);
        }
    }

    /**
     * 根据设备型号，保养部位查询保养项目信息,以及关联的备件，油品信息
     *
     * @param itemQueryDTO 查询条件
     * @return 保养项目信息
     */
    @Override
    public Page<MaintainItemDTO> getMaintainByQuery(MaintainItemQueryDTO itemQueryDTO) {
        //根据传入的设备，查找他的类型，设置类型查询条件
        itemQueryDTO.setEquipmentTypeIds(new ArrayList<>());
        if (itemQueryDTO.getEquipmentId() != null) {
            ResultVO<EquipmentDTO> equipment = equipmentFeignClient.getEquipmentById(itemQueryDTO.getEquipmentId());
            if (!equipment.isSuccess()) {
                log.warn("查询点检项目异常，设备id找不到对应的设备,id:{}", itemQueryDTO.getEquipmentId());
            } else {
                EquipmentDTO equipmentData = equipment.getData();
                if (equipmentData.getEquipmentTypeId() != null) {
                    ResultVO<List<EquipmentTypeDTO>> typeResult =
                        equipmentTypeClient.findAllparentTypeByChildId(equipmentData.getEquipmentTypeId());
                    if (!typeResult.isSuccess()) {
                        log.warn("查询点检项目异常，设备类型id查询子id异常,类型id:{}", equipmentData.getEquipmentTypeId());
                    } else {
                        List<EquipmentTypeDTO> types = typeResult.getData();
                        if (types != null) {
                            itemQueryDTO.setEquipmentTypeIds(types.stream()
                                .map(t -> t.getId()).collect(Collectors.toList()));
                        }
                    }
                }
            }
        }
        Page<MaintainItemDTO> page = PageHelperUtil.getPage(maintainItemMapper::getMaintainItemByQuery, itemQueryDTO);
        List<Integer> itemIds = page.getContent().stream()
            .map(SysBaseDTO::getId)
            .collect(Collectors.toList());
        List<HvEamMaintainItemFile> files = maintainItemFileRepository.findAllByMaintainItemIdIn(itemIds);
        List<HvEamItemSparePart> spareParts = itemSparePartRepository.getAllByMaintainItemIdIn(itemIds);
        List<HvEamItemLub> lubs = itemLubRepository.getAllByMaintainItemIdIn(itemIds);
        List<HvEamMaintainItemEquipment> equipments = maintainItemEquipmentRepository.getAllByItemIdIn(itemIds);
        List<HvEamMaintainItemEquipmentType> equipmentTypes = maintainItemEquipmentTypeRepository.findAllByItemIdIn(itemIds);
        for (MaintainItemDTO item : page) {
            //文件
            item.setFileIds(files
                .stream()
                .filter(t -> t.getMaintainItemId().equals(item.getId()))
                .map(HvEamMaintainItemFile::getFileId)
                .collect(Collectors.toList()));
            //备件
            item.setItemSparePartDTOList(spareParts
                .stream()
                .filter(t -> t.getMaintainItemId().equals(item.getId()))
                .map(t -> DtoMapper.convert(t, SparesInfo.class))
                .collect(Collectors.toList())
            );
            //油品
            item.setItemLubDTOList(lubs
                .stream()
                .filter(t -> t.getMaintainItemId().equals(item.getId()))
                .map(t -> DtoMapper.convert(t, LubDTO.class))
                .collect(Collectors.toList())
            );
            //设备
            item.setEquipmentInfos(equipments
                .stream()
                .filter(t -> t.getItemId().equals(item.getId()))
                .map(t -> DtoMapper.convert(t, EquipmentInfo.class))
                .collect(Collectors.toList())
            );
            //设备类型
            item.setEquipmentTypeInfos(equipmentTypes
                .stream()
                .filter(t -> t.getItemId().equals(item.getId()))
                .map(t -> DtoMapper.convert(t, EquipmentTypeInfo.class))
                .collect(Collectors.toList())
            );
        }
        return page;
    }


    /**
     * 保存保养项目
     *
     * @param dto 保养项目信息
     * @return 项目id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(MaintainItemDTO dto) {
        if (dto.getFileIds() == null) {
            dto.setFileIds(Collections.emptyList());
        }
        if (dto.getItemSparePartDTOList() == null) {
            dto.setItemSparePartDTOList(Collections.emptyList());
        }
        if (dto.getItemLubDTOList() == null) {
            dto.setItemLubDTOList(Collections.emptyList());
        }
        if (dto.getEquipmentInfos() == null) {
            dto.setEquipmentInfos(Collections.emptyList());
        }
        if (dto.getEquipmentTypeInfos() == null) {
            dto.setEquipmentTypeInfos(Collections.emptyList());
        }
        if (dto.getStartUsing() == null) {
            dto.setStartUsing(false);
        }
        if (dto.getShutDown() == null) {
            dto.setShutDown(false);
        }
        Assert.isTrue(dto.getManHour() >= 0, "工时不能小于0");
        //文件，备件，油品，设备，设备类型都不应该重复
        Assert.isTrue(dto.getFileIds().stream()
            .distinct().count() == dto.getFileIds().size(), "文件不能重复");
        Assert.isTrue(dto.getItemSparePartDTOList().stream()
            .map(SparesInfo::getSparePartId).distinct().count() == dto.getItemSparePartDTOList().size(), "备件不能重复");
        Assert.isTrue(dto.getItemLubDTOList().stream()
            .map(LubDTO::getLubId).distinct().count() == dto.getItemLubDTOList().size(), "油品不能重复");
        Assert.isTrue(dto.getEquipmentInfos().stream()
            .map(EquipmentInfo::getEquipmentId).distinct().count() == dto.getEquipmentInfos().size(), "设备不能重复");
        Assert.isTrue(dto.getEquipmentTypeInfos().stream()
            .map(EquipmentTypeInfo::getEquipmentTypeId).distinct().count() == dto.getEquipmentTypeInfos().size(), "设备类型不能重复");
        HvEamMaintainItem item = DtoMapper.convert(dto, HvEamMaintainItem.class);
        if (Strings.isBlank(item.getMaintainItemCode())) {
            item.setMaintainItemCode(serialUtil.getSerialNumber("maintainItem"));
        }
        maintainItemRepository.saveAndFlush(item);
        setFiles(dto.getFileIds(), item);
        setEquipments(dto.getEquipmentInfos(), item);
        setEquipmentTypes(dto.getEquipmentTypeInfos(), item);
        setSpareParts(dto.getItemSparePartDTOList(), item);
        setLubs(dto.getItemLubDTOList(), item);
        return item.getId();
    }

    /**
     * 保存保养项目文件信息
     *
     * @param fileIds 文件id列表
     * @param item    保养项目
     */
    private void setFiles(List<Integer> fileIds, HvEamMaintainItem item) {
        if (fileIds == null) {
            return;
        }
        maintainItemFileRepository.deleteAllByMaintainItemId(item.getId());
        maintainItemFileRepository.flush();
        List<HvEamMaintainItemFile> files = new ArrayList<>();
        for (Integer fileId : fileIds) {
            HvEamMaintainItemFile file = new HvEamMaintainItemFile();
            file.setFileId(fileId);
            file.setMaintainItemId(item.getId());
            files.add(file);
        }
        maintainItemFileRepository.saveAll(files);
    }


    /**
     * 启用停止保养项目
     *
     * @param id 被操作的保养项目id
     * @return 被操作的保养项目id
     */
    @Override
    public Boolean toggle(Integer id) {
        HvEamMaintainItem item = maintainItemRepository.getOne(id);
        item.setStartUsing(!item.getStartUsing());
        maintainItemRepository.save(item);
        return item.getStartUsing();
    }


    /**
     * 获取所有周期
     *
     * @return 周期集合
     */
    @Override
    public List<String> getAllCycles() {
        return maintainItemRepository.getAllCycles();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<MaintainItemDTO> getList(List<Integer> ids) {
        List<HvEamMaintainItem> itemsList = maintainItemRepository.findAllById(ids);
        return addInfo(itemsList);
    }

    /**
     * 塞数据进去
     *
     * @param itemsList 项目列表
     * @return 项目信息
     */
    private List<MaintainItemDTO> addInfo(List<HvEamMaintainItem> itemsList) {
        List<Integer> ids = itemsList.stream()
            .map(SysBase::getId)
            .collect(Collectors.toList());
        List<HvEamMaintainItemFile> files = maintainItemFileRepository.findAllByMaintainItemIdIn(ids);
        List<HvEamItemSparePart> spareParts = itemSparePartRepository.getAllByMaintainItemIdIn(ids);
        List<HvEamItemLub> lubs = itemLubRepository.getAllByMaintainItemIdIn(ids);
        List<HvEamMaintainItemEquipment> equipments = maintainItemEquipmentRepository.getAllByItemIdIn(ids);
        List<HvEamMaintainItemEquipmentType> types = maintainItemEquipmentTypeRepository.findAllByItemIdIn(ids);
        List<MaintainItemDTO> result = DtoMapper.convertList(itemsList, MaintainItemDTO.class);
        for (MaintainItemDTO maintainItemDTO : result) {
            //获得备件
            maintainItemDTO.setItemSparePartDTOList(spareParts
                .stream()
                .filter(t -> t.getMaintainItemId().equals(maintainItemDTO.getId()))
                .map(t -> {
                    SparesInfo sparesInfo = DtoMapper.convert(t, SparesInfo.class);
                    if (!ObjectUtils.isEmpty(sparesInfo.getSparePartId())) {
                        Optional<HvEamSpare> eamSpare = spareEntityRepository.findById(sparesInfo.getSparePartId());
                        //填充备件类型信息
                        eamSpare.map(HvEamSpare::getSpareTypeId)
                                .ifPresent(typeId -> spareTypeEntityRepository.findById(typeId)
                                        .ifPresent(s -> sparesInfo.setSparePartTypeName(s.getTypeName())));
                        //填充备件品牌信息
                        eamSpare.map(HvEamSpare::getBrand)
                                .ifPresent(sparesInfo::setSparePartBrand);
                        //填充specifications
                        eamSpare.map(HvEamSpare::getSpecifications)
                                .ifPresent(sparesInfo::setSparePartSpecifications);
                    }
                    return sparesInfo;
                })
                .collect(Collectors.toList()));
            //获得油品
            maintainItemDTO.setItemLubDTOList(lubs.stream()
                .filter(t -> t.getMaintainItemId().equals(maintainItemDTO.getId()))
                .map(t -> DtoMapper.convert(t, LubDTO.class))
                .collect(Collectors.toList()));
            //设置文件信息
            maintainItemDTO.setFileIds(files.stream()
                .filter(t -> t.getMaintainItemId().equals(maintainItemDTO.getId()))
                .map(HvEamMaintainItemFile::getFileId)
                .collect(Collectors.toList()));
            //设置设备信息
            maintainItemDTO.setEquipmentInfos(equipments.stream()
                .filter(t -> t.getItemId().equals(maintainItemDTO.getId()))
                .map(t -> DtoMapper.convert(t, EquipmentInfo.class))
                .collect(Collectors.toList()));
            //设置设备类型信息
            maintainItemDTO.setEquipmentTypeInfos(types.stream()
                .filter(t -> t.getItemId().equals(maintainItemDTO.getId()))
                .map(t -> DtoMapper.convert(t, EquipmentTypeInfo.class))
                .collect(Collectors.toList()));
        }
        return result;
    }

    /**
     * excel导出模板下载
     *
     * @return 导出对象
     */
    @Override
    public ExcelExportDto exportTemplate() {
        //这里可以传入数据列表就是导出功能。
        List<MaintainItemImportDTO> excels = new ArrayList<>();
        MaintainItemImportDTO data = new MaintainItemImportDTO();
        data.setCycle("每周");
        data.setMaintainItemCode("202205010001");
        data.setMaintainItemName("项目名称");
        data.setManHour(10f);
        data.setParts("零部件");
        data.setStartUsing(true);
        data.setShutDown(false);
        data.setMaintainWork("1：清理，2擦拭");
        data.setSpares("spare001:2");
        data.setLubs("lub001:2");
        data.setEquipmentTypes("equipmentType001,equipmentType002");
        data.setEquipments("equipment001,equiopment002");
        excels.add(data);
        return EasyExcelUtil.getExcel(excels, MaintainItemImportDTO.class, "保养项目导入模板.xlsx");

    }

    /**
     * 导入保养项目
     *
     * @param file 文件对象
     * @return 导入结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult importData(MultipartFile file) {
        List<MaintainItemImportDTO> importDTOS = EasyExcelUtil.getImport(file, MaintainItemImportDTO.class);
        int count = 0;
        //获取备件，油品，设备，设备类型数据
        Map<String, BigDecimal> sparePartsCode = new HashMap<>();
        Map<String, BigDecimal> lubsCode = new HashMap<>();
        List<String> equipmentsCode = new ArrayList<>();
        List<String> equipmentTypesCode = new ArrayList<>();
        for (MaintainItemImportDTO importDTO : importDTOS) {
            getInfo(importDTO.getSpares(), sparePartsCode);
            getInfo(importDTO.getLubs(), lubsCode);
            if (Strings.isNotBlank(importDTO.getEquipments())) {
                equipmentsCode.addAll(Arrays.asList(importDTO.getEquipments().split(",")));
            }
            if (Strings.isNotBlank(importDTO.getEquipmentTypes())) {
                equipmentTypesCode.addAll(Arrays.asList(importDTO.getEquipmentTypes().split(",")));
            }
        }
        List<SpareDTO> spares = serviceHandler.getAllSpareByCodeList(new ArrayList<>(sparePartsCode.keySet()));
        List<LubricatingDTO> lubs = serviceHandler.getAllLubByCodeList(new ArrayList<>(lubsCode.keySet()));
        List<EquipmentDTO> equipments = serviceHandler.getAllEquipmentByCodeList(equipmentsCode);
        List<EquipmentTypeDTO> equipmentTypes = serviceHandler.getAllEquipmentTypeByCodeList(equipmentTypesCode);

        Map<Integer, String> error = Maps.newHashMap();
        List<Integer> success = new ArrayList<>();
        for (int i = 0; i < importDTOS.size(); i++) {
            MaintainItemImportDTO importDTO = importDTOS.get(i);
            HvEamMaintainItem exists = maintainItemRepository.findByMaintainItemCode(importDTO.getMaintainItemCode());
            MaintainItemDTO item = DtoMapper.convert(importDTO, MaintainItemDTO.class);
            if (exists != null) {
                item.setId(exists.getId());
            }
            //设置导入的备件，油品，设备，设备类型。
            if (Strings.isNotBlank(importDTO.getSpares())) {
                Map<String, BigDecimal> info = getInfo(importDTO.getSpares(), new HashMap<>());
                Set<String> codes = info.keySet();
                List<SparesInfo> sparesInfos = new ArrayList<>();
                for (String code : codes) {
                    spares.stream()
                        .filter(t -> t.getSpareCode().equals(code))
                        .findFirst()
                        .ifPresent(s -> {
                            SparesInfo sparesInfo = new SparesInfo();
                            sparesInfo.setSparePartNum(info.get(code));
                            sparesInfo.setSparePartCode(code);
                            sparesInfo.setSparePartId(s.getId());
                            sparesInfo.setSparePartName(s.getSpareName());
                            sparesInfo.setSparePartUnit(s.getUnitName());
                            sparesInfo.setSparePartSupplier(s.getSupplier());
                            sparesInfos.add(sparesInfo);
                        });

                }

                item.setItemSparePartDTOList(sparesInfos);
            } else {
                item.setItemSparePartDTOList(Collections.emptyList());
            }
            if (Strings.isNotBlank(importDTO.getLubs())) {
                Map<String, BigDecimal> info = getInfo(importDTO.getLubs(), new HashMap<>());
                Set<String> codes = info.keySet();
                List<LubDTO> lubDTOS = new ArrayList<>();
                for (String code : codes) {
                    lubs.stream()
                        .filter(t -> t.getLubCode().equals(code))
                        .findFirst()
                        .ifPresent(s -> {
                            LubDTO lubDTO = new LubDTO();
                            lubDTO.setLubNum(info.get(code));
                            lubDTO.setLubCode(s.getLubCode());
                            lubDTO.setLubId(s.getId());
                            lubDTO.setLubName(s.getLubName());
                            lubDTO.setLubUnit(s.getUnitName());
                            lubDTO.setLubSupplier(s.getSupplier());
                            lubDTOS.add(lubDTO);
                        });
                }
                item.setItemLubDTOList(lubDTOS);
            } else {
                item.setItemLubDTOList(Collections.emptyList());
            }
            if (Strings.isNotBlank(importDTO.getEquipments())) {
                List<EquipmentInfo> equipmentInfos = equipments.stream()
                    .filter(t -> Arrays.stream(importDTO.getEquipments().split(","))
                        .anyMatch(e -> e.equals(t.getEquipmentCode())))
                    .map(e -> {
                        EquipmentInfo equipmentInfo = new EquipmentInfo();
                        equipmentInfo.setEquipmentCode(e.getEquipmentCode());
                        equipmentInfo.setEquipmentId(e.getId());
                        equipmentInfo.setEquipmentName(e.getEquipmentName());
                        return equipmentInfo;
                    }).collect(Collectors.toList());
                item.setEquipmentInfos(equipmentInfos);
            } else {
                item.setEquipmentInfos(Collections.emptyList());
            }
            if (Strings.isNotBlank(importDTO.getEquipmentTypes())) {
                List<EquipmentTypeInfo> types = equipmentTypes.stream()
                    .filter(t -> Arrays.stream(importDTO.getEquipmentTypes().split(","))
                        .anyMatch(e -> e.equals(t.getEquipmentTypeCode())))
                    .map(e -> {
                        EquipmentTypeInfo equipmentInfo = new EquipmentTypeInfo();
                        equipmentInfo.setEquipmentTypeCode(e.getEquipmentTypeCode());
                        equipmentInfo.setEquipmentTypeId(e.getId());
                        equipmentInfo.setEquipmentTypeName(e.getEquipmentTypeName());
                        return equipmentInfo;
                    }).collect(Collectors.toList());
                item.setEquipmentTypeInfos(types);
            } else {
                item.setEquipmentTypeInfos(Collections.emptyList());
            }
            if (item.getStartUsing() == null) {
                item.setStartUsing(false);
            }
            if (item.getShutDown() == null) {
                item.setShutDown(false);
            }
            save(item);
            log.info("成功导入:{},{}", item.getMaintainItemName(), item);
            success.add(i);
            count++;
        }
        ImportResult result = new ImportResult();
        result.setSuccessLines(success);
        result.setErrorLines(error);
        result.setTotalCount(count);
        return result;
    }

    /**
     * 获取数据
     * 格式：xxxx:num,xxxx2:num2
     *
     * @param string 字符串
     * @return 编码：数量
     */
    private Map<String, BigDecimal> getInfo(String string, Map<String, BigDecimal> result) {
        if (Strings.isBlank(string)) {
            return result;
        }
        String[] split = string.split(",");
        for (String s : split) {
            String[] codeAndNum = s.split(":");
            if (codeAndNum.length == 0) {
                continue;
            }
            if (codeAndNum.length == 1) {
                if (Strings.isNotBlank(codeAndNum[0])) {
                    //如果只有一个，写入数据
                    result.put(codeAndNum[0], new BigDecimal(1));
                }
            }
            if (codeAndNum.length >= 2) {
                if (Strings.isNotBlank(codeAndNum[0])) {
                    if (Strings.isNotBlank(codeAndNum[1])) {
                        try {
                            BigDecimal num = BigDecimal.valueOf(Float.parseFloat(codeAndNum[1]));
                            result.put(codeAndNum[0], num);
                        } catch (Exception ex) {
                            log.warn("导入数据异常，数据格式不对:{},数量录入为1", s);
                            result.put(codeAndNum[0], new BigDecimal(1));
                        }
                    }
                }
            }
        }
        return result;
    }


    /**
     * 导出保养项目
     *
     * @param ids 保养项目ID
     * @return 导出结果对象
     */
    @Override
    public ExcelExportDto export(List<Integer> ids) {
        List<MaintainItemDTO> dtoList = CollectionUtils.isEmpty(ids) ? findAll() : this.getList(ids);
        List<MaintainItemImportDTO> importDto = DtoMapper.convertList(dtoList, MaintainItemImportDTO.class);
        for (MaintainItemImportDTO dto : importDto) {
            dto.setSpares(dtoList.stream()
                .filter(t -> t.getMaintainItemCode().equals(dto.getMaintainItemCode()))
                //把备件信息拼接起来
                .map(t -> String.join(",", t.getItemSparePartDTOList()
                    .stream()
                    .map(s -> String.format("%s:%s", s.getSparePartCode(), s.getSparePartNum()))
                    .collect(Collectors.toList()))
                ).findFirst().orElse(""));
            dto.setLubs(dtoList.stream()
                .filter(t -> t.getMaintainItemCode().equals(dto.getMaintainItemCode()))
                //把油品信息拼接起来
                .map(t -> String.join(",", t.getItemLubDTOList()
                    .stream()
                    .map(s -> String.format("%s:%s", s.getLubCode(), s.getLubNum()))
                    .collect(Collectors.toList()))
                ).findFirst().orElse(""));
            dto.setEquipments(dtoList.stream()
                .filter(t -> t.getMaintainItemCode().equals(dto.getMaintainItemCode()))
                //把设备信息拼接起来
                .map(t -> String.join(",", t.getEquipmentInfos()
                    .stream()
                    .map(EquipmentInfo::getEquipmentCode)
                    .collect(Collectors.toList()))
                ).findFirst().orElse(""));
            dto.setEquipmentTypes(dtoList.stream()
                .filter(t -> t.getMaintainItemCode().equals(dto.getMaintainItemCode()))
                //把设备类型信息拼接起来
                .map(t -> String.join(",", t.getEquipmentTypeInfos()
                    .stream()
                    .map(EquipmentTypeInfo::getEquipmentTypeCode)
                    .collect(Collectors.toList()))
                ).findFirst().orElse(""));
        }

        return EasyExcelUtil.getExcel(importDto, MaintainItemImportDTO.class, "保养项目信息.xlsx");
    }

    /**
     * 获取保养项目详情
     *
     * @param id 保养项目id
     * @return 保养项目信息
     */
    @Override
    public MaintainItemDTO getById(Integer id) {
        return maintainItemRepository.findById(id)
            .map(t -> {
                MaintainItemDTO result = DtoMapper.convert(t, MaintainItemDTO.class);
                result.setFileIds(maintainItemFileRepository.findAllByMaintainItemId(result.getId())
                    .stream()
                    .map(HvEamMaintainItemFile::getFileId)
                    .collect(Collectors.toList()));
                result.setEquipmentTypeInfos(maintainItemEquipmentTypeRepository.findAllByItemId(result.getId())
                    .stream()
                    .map(type -> DtoMapper.convert(type, EquipmentTypeInfo.class))
                    .collect(Collectors.toList()));
                result.setEquipmentInfos(maintainItemEquipmentRepository.findAllByItemId(result.getId())
                    .stream()
                    .map(equipment -> DtoMapper.convert(equipment, EquipmentInfo.class))
                    .collect(Collectors.toList()));
                result.setItemSparePartDTOList(itemSparePartRepository.getAllByMaintainItemId(result.getId())
                    .stream()
                    .map(spare -> DtoMapper.convert(spare, SparesInfo.class))
                    .collect(Collectors.toList()));
                result.setItemLubDTOList(itemLubRepository.getAllByMaintainItemId(result.getId())
                    .stream()
                    .map(lub -> DtoMapper.convert(lub, LubDTO.class))
                    .collect(Collectors.toList()));
                return result;
            })
            .orElse(null);

    }

    /**
     * 获取保养项目详情
     *
     * @param itemCode 保养项目id
     * @return 保养项目信息
     */
    @Override
    public MaintainItemDTO getByCode(String itemCode) {
        HvEamMaintainItem item = maintainItemRepository.findByMaintainItemCode(itemCode);
        if (item == null) {
            return null;
        }
        return getById(item.getId());
    }


    /**
     * 查询所有项目
     *
     * @return 点巡检项目信息
     */
    private List<MaintainItemDTO> findAll() {
        List<HvEamMaintainItem> itemsList = maintainItemRepository.findAll();
        return addInfo(itemsList);
    }

    /**
     * 保存保养项目文件信息
     *
     * @param itemSparePartDTOList 文件id列表
     * @param item                 保养项目
     */
    private void setSpareParts(List<SparesInfo> itemSparePartDTOList, HvEamMaintainItem item) {
        if (itemSparePartDTOList == null) {
            return;
        }
        itemSparePartRepository.deleteAllByMaintainItemId(item.getId());
        itemSparePartRepository.flush();
        List<HvEamItemSparePart> spareParts = DtoMapper.convertList(itemSparePartDTOList, HvEamItemSparePart.class);
        for (HvEamItemSparePart equipment : spareParts) {
            equipment.setMaintainItemId(item.getId());
        }
        itemSparePartRepository.saveAll(spareParts);
    }

    /**
     * 保存保养项目文件信息
     *
     * @param equipmentInfos 文件id列表
     * @param item           保养项目
     */
    private void setEquipments(List<EquipmentInfo> equipmentInfos, HvEamMaintainItem item) {
        if (equipmentInfos == null) {
            return;
        }
        maintainItemEquipmentRepository.deleteAllByItemId(item.getId());
        maintainItemEquipmentRepository.flush();
        List<HvEamMaintainItemEquipment> equipments = DtoMapper.convertList(equipmentInfos, HvEamMaintainItemEquipment.class);
        for (HvEamMaintainItemEquipment equipment : equipments) {
            equipment.setItemId(item.getId());
        }
        maintainItemEquipmentRepository.saveAll(equipments);
    }

    /**
     * 保存保养项目文件信息
     *
     * @param equipmentTypeInfos 文件id列表
     * @param item               保养项目
     */
    private void setEquipmentTypes(List<EquipmentTypeInfo> equipmentTypeInfos, HvEamMaintainItem item) {
        if (equipmentTypeInfos == null) {
            return;
        }
        maintainItemEquipmentTypeRepository.deleteAllByItemId(item.getId());
        maintainItemEquipmentTypeRepository.flush();
        List<HvEamMaintainItemEquipmentType> equipmentTypes = DtoMapper.convertList(equipmentTypeInfos, HvEamMaintainItemEquipmentType.class);
        for (HvEamMaintainItemEquipmentType equipment : equipmentTypes) {
            equipment.setItemId(item.getId());
        }
        maintainItemEquipmentTypeRepository.saveAll(equipmentTypes);
    }


    /**
     * 保存保养项目文件信息
     *
     * @param lubDTOList 文件id列表
     * @param item       保养项目
     */
    private void setLubs(List<LubDTO> lubDTOList, HvEamMaintainItem item) {
        if (lubDTOList == null) {
            return;
        }
        itemLubRepository.deleteAllByMaintainItemId(item.getId());
        itemLubRepository.flush();
        List<HvEamItemLub> lubs = DtoMapper.convertList(lubDTOList, HvEamItemLub.class);
        for (HvEamItemLub lub : lubs) {
            lub.setMaintainItemId(item.getId());
        }
        itemLubRepository.saveAll(lubs);
    }
}