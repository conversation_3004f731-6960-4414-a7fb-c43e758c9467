package com.hvisions.eam.service.inspect;

import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.support.ListenerExecutionFailedException;

/**
 * <p>Title: InspectDataService</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/1/12</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface InspectDataService {
    /**
     * 持久化数据
     * @param processInstanceId 流程实例
     */
    void save(String processInstanceId);

    /**
     * 处理异常
     * @param message 异常信息
     * @param e 异常
     */
    void handleError(Message message, ListenerExecutionFailedException e);
}

    
    
    
    