package com.hvisions.eam.service.autonomy;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.eam.dto.autonomy.ProjectGroupCreateOrUpdateDTO;
import com.hvisions.eam.dto.autonomy.ProjectGroupDTO;
import com.hvisions.eam.entity.autonomy.HvAmProjectGroup;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
public interface ProjectGroupService extends IService<HvAmProjectGroup> {

    int add(ProjectGroupCreateOrUpdateDTO dto);

    void deleteById(Integer id);

    int update(ProjectGroupCreateOrUpdateDTO dto);

    List<ProjectGroupDTO> queryList();

    List<ProjectGroupDTO> queryById (List<ProjectGroupDTO> list);
}
