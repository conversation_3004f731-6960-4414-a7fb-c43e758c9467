package com.hvisions.eam.service.autonomy;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.eam.dto.autonomy.*;
import com.hvisions.eam.entity.autonomy.HvAmAutonomyMaintenanceProcessItem;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

public interface StatisticalService extends IService<HvAmAutonomyMaintenanceProcessItem> {

    Page<MaintenanceProcessDataDTO> queryProcessDataList(MaintenanceProcessDataQueryDTO dto);

    Map<String,Object> queryProcessItemList(MaintenanceProcessItemQueryDTO dto);

    List<CheckAbnormalInfoDTO> checkAbnormalInfo(CheckAbnormalInfoQueryDTO dto);

    List<HvAmAutonomyMaintenanceProcessItem> fuzzyQueryItem(String keyword);
}
