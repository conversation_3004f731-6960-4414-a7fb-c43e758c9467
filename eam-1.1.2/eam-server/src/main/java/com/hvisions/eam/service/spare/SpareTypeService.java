package com.hvisions.eam.service.spare;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.EntitySaver;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.spare.ImportSpareTypeDTO;
import com.hvisions.eam.dto.spare.SpareTypeDTO;
import com.hvisions.eam.query.spare.SpareTypeQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <p>Title: SpareTypeService</p>
 * <p>Description: 类型</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface SpareTypeService extends EntitySaver<ImportSpareTypeDTO> {

    /**
     * 增加类型  改 通过 类型DTO
     *
     * @param spareTypeDTO 类型DTO
     * @return 新增数据
     */
    SpareTypeDTO save(SpareTypeDTO spareTypeDTO);

    /**
     * 删除类型 通过 ID
     *
     * @param id 类型ID
     */
    void deleteById(Integer id);

    /**
     * 查询类型 通过类型ID
     *
     * @param id 类型ID
     * @return 类型DTO
     */
    SpareTypeDTO findById(Integer id);

    /**
     * 分页查询
     * 通过 类型名称  联合查询 查询所有的父级
     *
     * @param spareTypeQueryDTO 类型DTO
     * @return 分页信息
     */
    Page<SpareTypeDTO> findAllByTypeName(SpareTypeQueryDTO spareTypeQueryDTO);

    /**
     * 通过父级ID查询 子级全部信息
     *
     * @param parentId 父级ID
     * @return 子级全部信息
     */
    List<SpareTypeDTO> getChildNode(Integer parentId);

    /**
     * 查询全部信息
     *
     * @return 全部信息
     */
    List<SpareTypeDTO> getAll();

    /**
     * 通过名称查询
     *
     * @param typeName 类型名称
     * @return id
     */
    Integer findByTypeName(String typeName);

    /**
     * 通过备件类型code获取备件类型
     *
     * @param spareTypeCode 备件类型code
     * @return 备件类型
     */
    SpareTypeDTO getSpareTypeBySpareTypeCode(String spareTypeCode);

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    ResultVO<ExcelExportDto> getTypeImportTemplate() throws IOException, IllegalAccessException;

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    ResponseEntity<byte[]> getTypeImportTemplateLink() throws IOException, IllegalAccessException;

    /**
     * 导入
     *
     * @param file 信息excel
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    ImportResult importSpareType(MultipartFile file) throws IllegalAccessException, ParseException, IOException;

    /**
     * 导出
     *
     * @return 信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    ResponseEntity<byte[]> exportSpareTypeLink() throws IOException, IllegalAccessException;

    /**
     * 导出信息
     *
     * @return 信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    ResultVO<ExcelExportDto> exportSpareType() throws IOException, IllegalAccessException;

}
