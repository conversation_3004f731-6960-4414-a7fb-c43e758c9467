package com.hvisions.eam.service.autonomy.imp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.eam.common.ParamUtils;
import com.hvisions.eam.dao.InspectionProjectMapper;
import com.hvisions.eam.dto.autonomy.InspectionProjectCreateOrUpdateDTO;
import com.hvisions.eam.dto.autonomy.InspectionProjectDTO;
import com.hvisions.eam.dto.autonomy.InspectionProjectQueryDTO;
import com.hvisions.eam.dto.autonomy.ProjectGroupDTO;
import com.hvisions.eam.entity.autonomy.HvAmInspectionProject;
import com.hvisions.eam.entity.autonomy.HvAmProjectAndPlanRelation;
import com.hvisions.eam.entity.autonomy.HvAmProjectGroup;
import com.hvisions.eam.enums.HExceptionEnum;
import com.hvisions.eam.excel.InspectionProjectExcel;
import com.hvisions.eam.repository.autonomy.InspectionProjectRepository;
import com.hvisions.eam.repository.autonomy.ProjectAndPlanRelationRepository;
import com.hvisions.eam.service.autonomy.InspectionProjectService;
import com.hvisions.eam.service.autonomy.ProjectGroupService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-11
 */
@Service
public class InspectionProjectServiceImpl extends ServiceImpl<InspectionProjectMapper, HvAmInspectionProject> implements InspectionProjectService {

    private final InspectionProjectMapper inspectionProjectMapper;
    /**
     * jpa仓储层
     */
    private final InspectionProjectRepository inspectionProjectRepository;
    private final ProjectAndPlanRelationRepository projectAndPlanRelationRepository;
    private final ProjectGroupService projectGroupService;

    @Autowired
    public InspectionProjectServiceImpl(InspectionProjectMapper inspectionProjectMapper, InspectionProjectRepository inspectionProjectRepository, ProjectAndPlanRelationRepository projectAndPlanRelationRepository, ProjectGroupService projectGroupService) {
        this.inspectionProjectMapper = inspectionProjectMapper;
        this.inspectionProjectRepository = inspectionProjectRepository;
        this.projectAndPlanRelationRepository = projectAndPlanRelationRepository;
        this.projectGroupService = projectGroupService;
    }

    //启用标志;0-启用,1-未启用
    private final String enableFlag_open = "0";
    private final String enableFlag_close = "1";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int add(InspectionProjectCreateOrUpdateDTO dto) {
        //入参去空格
        try {
            ParamUtils.beanAttributeValueTrim(dto);
        } catch (Exception e) {
            throw new BaseKnownException(HExceptionEnum.SERVER_ERROR);
        }
        Boolean b = StringUtils.isBlank(dto.getNumber()) || StringUtils.isBlank(dto.getName()) || StringUtils.isBlank(dto.getTestContent()) ||
                StringUtils.isBlank(dto.getTestCycle()) || StringUtils.isBlank(dto.getTestMethod()) || StringUtils.isBlank(dto.getEnableFlag()) ||
                dto.getAbnormalTimes() == null || dto.getGroupId() == null;
        if (b) {
            throw new BaseKnownException(HExceptionEnum.INCOMPLETE_DATA);
        }
        Boolean c = "1".equals(dto.getEnableFlag()) || "0".equals(dto.getEnableFlag());
        if (!c) {
            throw new BaseKnownException(HExceptionEnum.THE_VALUE_RANGE_OF_ENABLE_FLAG_IS_INCORRECT);
        }
        Boolean d = dto.getAbnormalTimes() < 1;
        if (d) {
            throw new BaseKnownException(HExceptionEnum.ABNORMAL_TIMES_INCORRECT_VALUE_RANGE);
        }

        //验证计划编码是否存在
        if (inspectionProjectRepository.existsByNumber(dto.getNumber())) {
            throw new BaseKnownException(HExceptionEnum.INSERT_CODE_ERROR);
        }

        HvAmProjectGroup temp = projectGroupService.getById(dto.getGroupId());
        if (temp == null) {
            throw new BaseKnownException(HExceptionEnum.DATA_IN_WRONG_FORMAT);
        }
        //插入项目
        return inspectionProjectRepository.save(DtoMapper.convert(dto, HvAmInspectionProject.class)).getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Integer id) {

        //判断项目的启用状态(启用标志;0-启用,1-未启用)
        HvAmInspectionProject temp = inspectionProjectRepository.getOne(id);
        if (enableFlag_open.equals(temp.getEnableFlag())) {
            throw new BaseKnownException(HExceptionEnum.DELETE_DATA_ERROR);
        }
        //判断是否有计划引用了该项目
        List<HvAmProjectAndPlanRelation> tempList = projectAndPlanRelationRepository.findAllByProjectId(id);
        if (tempList.size() > 0) {
            throw new BaseKnownException(HExceptionEnum.DELETE_ITEM_ERROR);
        }
        //删除项目
        inspectionProjectRepository.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteList(List<Integer> idList) {

        for (Integer id : idList) {
            //判断项目的启用状态(启用标志;0-启用,1-未启用)
            HvAmInspectionProject temp = inspectionProjectRepository.getOne(id);
            if (enableFlag_open.equals(temp.getEnableFlag())) {
                throw new BaseKnownException(HExceptionEnum.DELETE_DATA_ERROR);
            }
            //判断是否有计划引用了该项目
            List<HvAmProjectAndPlanRelation> tempList = projectAndPlanRelationRepository.findAllByProjectId(id);
            if (tempList.size() > 0) {
                throw new BaseKnownException(HExceptionEnum.DELETE_ITEM_ERROR);
            }
            //删除项目
            inspectionProjectRepository.deleteById(id);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(InspectionProjectCreateOrUpdateDTO dto) {

        InspectionProjectDTO temp = this.getById(dto.getId());
        HvAmInspectionProject hvAmInspectionProject = DtoMapper.convert(dto, HvAmInspectionProject.class);
        hvAmInspectionProject.setNumber(temp.getNumber());
        hvAmInspectionProject.setGroupId(temp.getGroupId());

        return inspectionProjectRepository.save(hvAmInspectionProject).getId();
    }

    /**
     * 树状list转普通list
     *
     * @param list
     * @return
     */
    private List<ProjectGroupDTO> treeTolist(List<ProjectGroupDTO> list) {
        List<ProjectGroupDTO> result = new ArrayList<>();
        for (ProjectGroupDTO test : list) {
            List<ProjectGroupDTO> c = test.getChildNode();
            result.add(test);
            if (!org.springframework.util.CollectionUtils.isEmpty(c)) {
                result.addAll(treeTolist(c));
                test.setChildNode(null);
            }
        }
        return result;
    }

    @Override
    public Page<InspectionProjectDTO> queryList(InspectionProjectQueryDTO dto) {

        List<String> groupIdList = new ArrayList<>();
        if (dto.getGroupId() != null && dto.getGroupId() != 0) {
            //通过该groupId查询出所有下级的ID
            HvAmProjectGroup temp = projectGroupService.getById(dto.getGroupId());
            if (temp != null) {
                ProjectGroupDTO projectGroupDTO = DtoMapper.convert(temp, ProjectGroupDTO.class);
                List<ProjectGroupDTO> tempList = new ArrayList<>();
                tempList.add(projectGroupDTO);
                List<ProjectGroupDTO> list = treeTolist(projectGroupService.queryById(tempList));
                for (ProjectGroupDTO tempDto : list) {
                    groupIdList.add(tempDto.getId() + "");
                }
            }
        }
        dto.setGroupIds(groupIdList);
        Page<InspectionProjectDTO> maintainItemDTOS = PageHelperUtil.getPage(inspectionProjectMapper::getInspectionProjectS, dto);

        return maintainItemDTOS;

    }

    @Override
    public InspectionProjectDTO getById(Integer id) {

        //查询项目信息
        HvAmInspectionProject hvAmInspectionProject = inspectionProjectRepository.getOne(id);
        InspectionProjectDTO inspectionProjectDTO = DtoMapper.convert(hvAmInspectionProject, InspectionProjectDTO.class);

        return inspectionProjectDTO;
    }

    @Override
    public ResponseEntity<byte[]> getImportTemplate() {

        List<InspectionProjectExcel> excels = new ArrayList<>();

        InspectionProjectExcel data = new InspectionProjectExcel();
        data.setNumber("001");
        data.setName("示例项目");
        data.setTestContent("检查外观是否完好");
        data.setTestCycle("每天");
        data.setTestMethod("目测");
        data.setEnableFlag("0");
        data.setAbnormalTimes(3);
        data.setGroupName("分组1");

        excels.add(data);
        return EasyExcelUtil.getExcelResponse(excels, InspectionProjectExcel.class, "InspectionProjectTemplate.xlsx");
    }

    @Override
    public ExcelExportDto getImportTemplateForFront() {

        List<InspectionProjectExcel> excels = new ArrayList<>();

        InspectionProjectExcel data = new InspectionProjectExcel();
        data.setNumber("001");
        data.setName("示例项目");
        data.setTestContent("检查外观是否完好");
        data.setTestCycle("每天");
        data.setTestMethod("目测");
        data.setEnableFlag("0");
        data.setAbnormalTimes(3);
        data.setGroupName("分组1");

        excels.add(data);
        return EasyExcelUtil.getExcel(excels, InspectionProjectExcel.class, "InspectionProjectTemplate.xlsx");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult importExcel(MultipartFile file) {
        ImportResult importResult = new ImportResult();
        List<Integer> objects = new ArrayList<>();
        Map<Integer, String> objectObjectHashMap = new HashMap<>();
        List<InspectionProjectExcel> data = EasyExcelUtil.getImport(file, InspectionProjectExcel.class);
        List<InspectionProjectCreateOrUpdateDTO> dto = DtoMapper.convertList(data, InspectionProjectCreateOrUpdateDTO.class);
        //更具groupName 查询id
        for (InspectionProjectCreateOrUpdateDTO temp : dto) {
            try {
                LambdaQueryWrapper<HvAmProjectGroup> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(HvAmProjectGroup::getName, temp.getGroupName());
                HvAmProjectGroup one = projectGroupService.getOne(lambdaQueryWrapper);
                if (one == null) {
                    throw new BaseKnownException(HExceptionEnum.THE_GROUP_DOES_NOT_EXIST);
                }
                temp.setGroupId(one.getId());
                if (inspectionProjectRepository.existsByNumber(temp.getNumber())) {
                    HvAmInspectionProject allByNumber = inspectionProjectRepository.getAllByNumber(temp.getNumber());
                    temp.setId(allByNumber.getId());
                    update(temp);
                } else {
                    add(temp);
                }
                objects.add(dto.indexOf(temp));
                importResult.setSuccessLines(objects);
            } catch (Exception e) {
                objectObjectHashMap.put(dto.indexOf(temp), e.toString());
                importResult.setErrorLines(objectObjectHashMap);
            }
        }
        return importResult;
    }

    @Override
    public ResponseEntity<byte[]> exportExcel(InspectionProjectQueryDTO dto) {

        Page<InspectionProjectDTO> inspectionProjectDTOS = queryList(dto);
        List<InspectionProjectDTO> records = inspectionProjectDTOS.getContent();
        for (InspectionProjectDTO temp : records) {
            temp.setGroupName(projectGroupService.getById(temp.getGroupId()).getName());
        }
        List<InspectionProjectExcel> excels = DtoMapper.convertList(records, InspectionProjectExcel.class);

        return EasyExcelUtil.getExcelResponse(excels, InspectionProjectExcel.class, "InspectionProject.xlsx");
    }

    @Override
    public ExcelExportDto exportExcelForFront(InspectionProjectQueryDTO dto) {

        Page<InspectionProjectDTO> inspectionProjectDTOS = queryList(dto);
        List<InspectionProjectDTO> records = inspectionProjectDTOS.getContent();
        for (InspectionProjectDTO temp : records) {
            temp.setGroupName(projectGroupService.getById(temp.getGroupId()).getName());
        }
        List<InspectionProjectExcel> excels = DtoMapper.convertList(records, InspectionProjectExcel.class);

        return EasyExcelUtil.getExcel(excels, InspectionProjectExcel.class, "InspectionProject.xlsx");
    }

    @Override
    public List<String> getTestCycle() {

        List<String> list = this.baseMapper.getTestCycle();
        return list;

    }
}
