package com.hvisions.eam.service.inspect.impl;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.EasyExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.utils.SerialUtil;
import com.hvisions.eam.dao.inspect.InspectItemMapper;
import com.hvisions.hiperbase.SysBaseDTO;
import com.hvisions.eam.dto.inspect.item.EquipmentInfo;
import com.hvisions.eam.dto.inspect.item.EquipmentTypeInfo;
import com.hvisions.eam.dto.inspect.item.InspectItemDTO;
import com.hvisions.eam.dto.inspect.item.InspectItemQueryDTO;
import com.hvisions.eam.entity.SysBase;
import com.hvisions.eam.entity.inspect.HvEamInspectItem;
import com.hvisions.eam.entity.inspect.HvEamInspectItemEquipment;
import com.hvisions.eam.entity.inspect.HvEamInspectItemEquipmentType;
import com.hvisions.eam.entity.inspect.HvEamInspectItemFile;
import com.hvisions.eam.excel.InspectItemImportDTO;
import com.hvisions.eam.repository.inspect.*;
import com.hvisions.eam.service.inspect.InspectItemService;
import com.hvisions.hiperbase.equipment.EquipmentDTO;
import com.hvisions.hiperbase.equipment.EquipmentTypeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title: inspectItemServiceImpl</p >
 * <p>Description:服务实现层 </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/27</p >
 *
 * <AUTHOR>
 * @version :1..0
 */
@Service
@Slf4j
public class InspectItemServiceImpl implements InspectItemService {
    private final InspectItemRepository inspectItemRepository;
    private final InspectItemFileRepository inspectItemFileRepository;
    private final SerialUtil serialUtil;
    private final InspectItemMapper inspectItemMapper;
    private final InspectPlanItemRepository inspectPlanItemRepository;
    private final InspectItemEquipmentRepository inspectItemEquipmentRepository;
    private final InspectItemEquipmentTypeRepository inspectItemEquipmentTypeRepository;
    private final InspectServiceHandler serviceHandler;

    @Autowired
    public InspectItemServiceImpl(InspectItemRepository inspectItemRepository,
                                  InspectItemFileRepository inspectItemFileRepository,
                                  SerialUtil serialUtil,
                                  InspectItemMapper inspectItemMapper,
                                  InspectPlanItemRepository inspectPlanItemRepository,
                                  InspectItemEquipmentRepository inspectItemEquipmentRepository,
                                  InspectItemEquipmentTypeRepository inspectItemEquipmentTypeRepository,
                                  InspectServiceHandler serviceHandler) {
        this.inspectItemRepository = inspectItemRepository;
        this.inspectItemFileRepository = inspectItemFileRepository;
        this.serialUtil = serialUtil;
        this.inspectItemMapper = inspectItemMapper;
        this.inspectPlanItemRepository = inspectPlanItemRepository;
        this.inspectItemEquipmentRepository = inspectItemEquipmentRepository;
        this.inspectItemEquipmentTypeRepository = inspectItemEquipmentTypeRepository;
        this.serviceHandler = serviceHandler;
    }

    /**
     * 新增点检项目
     *
     * @param dto 点检项目DTO
     * @return 新增点检项目id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(InspectItemDTO dto) {
        HvEamInspectItem item = DtoMapper.convert(dto, HvEamInspectItem.class);
        if (dto.getManHour() < 0) {
            throw new BaseKnownException("项目工时不能小于0");
        }
        if (Strings.isBlank(dto.getInspectItemCode())) {
            item.setInspectItemCode(serialUtil.getSerialNumber("inspectItem"));
        }
        inspectItemRepository.saveAndFlush(item);
        setFiles(Optional.ofNullable(dto.getFileIds()).orElse(Collections.emptyList()), item);
        setEquipments(Optional.ofNullable(dto.getEquipmentInfos()).orElse(Collections.emptyList()), item);
        setEquipmentTypes(Optional.ofNullable(dto.getEquipmentTypeInfos()).orElse(Collections.emptyList()), item);
        return item.getId();
    }

    /**
     * 保存保养项目文件信息
     *
     * @param fileIds 文件id列表
     * @param item    保养项目
     */
    private void setFiles(List<Integer> fileIds, HvEamInspectItem item) {
        Assert.notNull(fileIds, "文件信息不能传递空");
        Assert.isTrue(fileIds.stream()
            .distinct().count() == fileIds.size(), "文件id不能重复");
        inspectItemFileRepository.deleteAllByInspectItemId(item.getId());
        inspectItemFileRepository.flush();
        List<HvEamInspectItemFile> files = new ArrayList<>();
        for (Integer fileId : fileIds) {
            HvEamInspectItemFile file = new HvEamInspectItemFile();
            file.setFileId(fileId);
            file.setInspectItemId(item.getId());
            files.add(file);
        }
        inspectItemFileRepository.saveAll(files);
    }


    /**
     * 保存保养项目文件信息
     *
     * @param equipmentInfos 文件id列表
     * @param item           保养项目
     */
    private void setEquipments(List<EquipmentInfo> equipmentInfos, HvEamInspectItem item) {
        Assert.notNull(equipmentInfos, "设备不能为空");
        inspectItemEquipmentRepository.deleteAllByItemId(item.getId());
        inspectItemEquipmentRepository.flush();
        List<HvEamInspectItemEquipment> equipments = DtoMapper.convertList(equipmentInfos, HvEamInspectItemEquipment.class);
        for (HvEamInspectItemEquipment equipment : equipments) {
            equipment.setItemId(item.getId());
        }
        inspectItemEquipmentRepository.saveAll(equipments);
    }

    /**
     * 保存保养项目文件信息
     *
     * @param equipmentTypeInfos 文件id列表
     * @param item               保养项目
     */
    private void setEquipmentTypes(List<EquipmentTypeInfo> equipmentTypeInfos, HvEamInspectItem item) {
        Assert.notNull(equipmentTypeInfos, "设备类型不能为空");
        inspectItemEquipmentTypeRepository.deleteAllByItemId(item.getId());
        inspectItemEquipmentTypeRepository.flush();
        List<HvEamInspectItemEquipmentType> equipmentTypes = DtoMapper.convertList(equipmentTypeInfos, HvEamInspectItemEquipmentType.class);
        for (HvEamInspectItemEquipmentType equipment : equipmentTypes) {
            equipment.setItemId(item.getId());
        }
        inspectItemEquipmentTypeRepository.saveAll(equipmentTypes);
    }

    /**
     * 删除单个
     *
     * @param id id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Integer id) {
        if (inspectPlanItemRepository.existsByInspectItemId(id)) {
            throw new BaseKnownException("已经被计划使用，无法删除");
        }
        //删除与文件关系
        inspectItemFileRepository.deleteAllByInspectItemId(id);
        //删除设备关系
        inspectItemEquipmentRepository.deleteAllByItemId(id);
        inspectItemEquipmentTypeRepository.deleteAllByItemId(id);
        //删除本体数据
        inspectItemRepository.deleteById(id);
    }

    /**
     * 批量删除
     *
     * @param idList id集合
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIdList(List<Integer> idList) {
        for (Integer id : idList) {
            deleteById(id);
        }
    }


    /**
     * 条件分页查询点巡检
     *
     * @param itemQueryDTO 查询条件
     * @return 点检项目分页数据
     */
    @Override
    public Page<InspectItemDTO> getInspectByQuery(InspectItemQueryDTO itemQueryDTO) {
        //根据传入的设备，查找他的类型，设置类型查询条件
        itemQueryDTO.setEquipmentTypeIds(new ArrayList<>());
        if (itemQueryDTO.getEquipmentId() != null) {
            EquipmentDTO equipment = serviceHandler.findEquipment(itemQueryDTO.getEquipmentId());
            if (equipment != null) {
                if (equipment.getEquipmentTypeId() != null) {
                    List<EquipmentTypeDTO> typs = serviceHandler.findAllParentTypeByChildId(equipment.getEquipmentTypeId());
                    itemQueryDTO.setEquipmentTypeIds(typs.stream()
                        .map(SysBaseDTO::getId).collect(Collectors.toList()));
                }
            }
        }
        Page<InspectItemDTO> result = PageHelperUtil.getPage(inspectItemMapper::getInspectItemByQuery, itemQueryDTO);
        List<Integer> ids = result.stream()
            .map(com.hvisions.eam.dto.SysBaseDTO::getId)
            .collect(Collectors.toList());
        //一次找到对应的关联信息
        List<HvEamInspectItemFile> fileEntitys = inspectItemFileRepository.findAllByInspectItemIdIn(ids);
        List<HvEamInspectItemEquipment> equipments = inspectItemEquipmentRepository.getAllByItemIdIn(ids);
        List<HvEamInspectItemEquipmentType> equipmentTypes = inspectItemEquipmentTypeRepository.findAllByItemIdIn(ids);
        //拼接对象
        for (InspectItemDTO item : result) {
            //文件
            item.setFileIds(fileEntitys
                .stream()
                .filter(t -> t.getInspectItemId().equals(item.getId()))
                .map(HvEamInspectItemFile::getFileId)
                .collect(Collectors.toList()));
            //设备
            item.setEquipmentInfos(equipments
                .stream()
                .filter(t -> t.getItemId().equals(item.getId()))
                .map(t -> DtoMapper.convert(t, EquipmentInfo.class))
                .collect(Collectors.toList())
            );
            //设备类型
            item.setEquipmentTypeInfos(equipmentTypes
                .stream()
                .filter(t -> t.getItemId().equals(item.getId()))
                .map(t -> DtoMapper.convert(t, EquipmentTypeInfo.class))
                .collect(Collectors.toList())
            );
        }
        return result;
    }


    /**
     * 是否启用项目
     *
     * @param id 被操作的项目id
     * @return 被操作的项目id
     */
    @Override
    public Boolean toggle(Integer id) {
        HvEamInspectItem item = inspectItemRepository.getOne(id);
        item.setStartUsing(!item.getStartUsing());
        inspectItemRepository.save(item);
        return item.getStartUsing();
    }


    /**
     * excel导出模板下载
     *
     * @return 导出对象
     */
    @Override
    public ExcelExportDto exportTemplate() {
        //这里可以传入数据列表就是导出功能。
        List<InspectItemImportDTO> excels = new ArrayList<>();
        InspectItemImportDTO data = new InspectItemImportDTO();
        data.setCycle("每周");
        data.setInspectItemCode("202205010001");
        data.setInspectItemName("项目名称");
        data.setManHour(10f);
        data.setParts("零部件");
        data.setStartUsing(true);
        data.setShutDown(false);
        data.setEquipmentTypes("equipmentType001,equipmentType002");
        data.setEquipments("equipment001,equiopment002");
        excels.add(data);
        return EasyExcelUtil.getExcel(excels, InspectItemImportDTO.class, "点巡检项目导入模板.xlsx");
    }

    /**
     * 导入保养项目
     *
     * @param file 文件对象
     * @return 导入结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult importData(MultipartFile file) {
        List<InspectItemImportDTO> importDTOS = EasyExcelUtil.getImport(file, InspectItemImportDTO.class);
        int count = 0;
        List<String> equipmentsCode = new ArrayList<>();
        List<String> equipmentTypesCode = new ArrayList<>();
        for (InspectItemImportDTO importDTO : importDTOS) {
            if (Strings.isNotBlank(importDTO.getEquipments())) {
                equipmentsCode.addAll(Arrays.asList(importDTO.getEquipments().split(",")));
            }
            if (Strings.isNotBlank(importDTO.getEquipmentTypes())) {
                equipmentTypesCode.addAll(Arrays.asList(importDTO.getEquipmentTypes().split(",")));
            }
        }
        List<EquipmentDTO> equipments = serviceHandler.getAllEquipmentByCodeList(equipmentsCode);
        List<EquipmentTypeDTO> equipmentTypes = serviceHandler.getAllEquipmentTypeByCodeList(equipmentTypesCode);
        Map<Integer, String> error = new HashMap<>();
        List<Integer> success = new ArrayList<>();
        for (int i = 0; i < importDTOS.size(); i++) {
            InspectItemImportDTO importDTO = importDTOS.get(i);
            InspectItemDTO item = DtoMapper.convert(importDTO, InspectItemDTO.class);
            HvEamInspectItem exists = inspectItemRepository.findByInspectItemCode(item.getInspectItemCode());
            if (exists != null) {
                item.setId(exists.getId());
            }
            if (Strings.isNotBlank(importDTO.getEquipments())) {
                List<EquipmentInfo> equipmentInfos = equipments.stream()
                    .filter(t -> Arrays.stream(importDTO.getEquipments().split(","))
                        .anyMatch(e -> e.equals(t.getEquipmentCode())))
                    .map(e -> {
                        EquipmentInfo equipmentInfo = new EquipmentInfo();
                        equipmentInfo.setEquipmentCode(e.getEquipmentCode());
                        equipmentInfo.setEquipmentId(e.getId());
                        equipmentInfo.setEquipmentName(e.getEquipmentName());
                        return equipmentInfo;
                    }).collect(Collectors.toList());
                item.setEquipmentInfos(equipmentInfos);
            } else {
                item.setEquipmentInfos(Collections.emptyList());
            }
            if (Strings.isNotBlank(importDTO.getEquipmentTypes())) {
                List<EquipmentTypeInfo> types = equipmentTypes.stream()
                    .filter(t -> Arrays.stream(importDTO.getEquipmentTypes().split(","))
                        .anyMatch(e -> e.equals(t.getEquipmentTypeCode())))
                    .map(e -> {
                        EquipmentTypeInfo equipmentInfo = new EquipmentTypeInfo();
                        equipmentInfo.setEquipmentTypeCode(e.getEquipmentTypeCode());
                        equipmentInfo.setEquipmentTypeId(e.getId());
                        equipmentInfo.setEquipmentTypeName(e.getEquipmentTypeName());
                        return equipmentInfo;
                    }).collect(Collectors.toList());
                item.setEquipmentTypeInfos(types);
            } else {
                item.setEquipmentTypeInfos(Collections.emptyList());
            }
            save(item);
            success.add(i);
            count++;
        }
        ImportResult result = new ImportResult();
        result.setSuccessLines(success);
        result.setErrorLines(error);
        result.setTotalCount(count);
        return result;
    }

    /**
     * 导出保养项目
     *
     * @param ids 保养项目ID
     * @return 导出结果对象
     */
    @Override
    public ExcelExportDto exportData(List<Integer> ids) {
        List<InspectItemDTO> dtoList = CollectionUtils.isEmpty(ids) ? findAll() : this.getList(ids);
        List<InspectItemImportDTO> importDto = DtoMapper.convertList(dtoList, InspectItemImportDTO.class);
        for (InspectItemImportDTO dto : importDto) {
            dto.setEquipments(dtoList.stream()
                .filter(t -> t.getInspectItemCode().equals(dto.getInspectItemCode()))
                //把设备信息拼接起来
                .map(t -> String.join(",", t.getEquipmentInfos()
                    .stream()
                    .map(EquipmentInfo::getEquipmentCode)
                    .collect(Collectors.toList()))
                ).findFirst().orElse(""));
            dto.setEquipmentTypes(dtoList.stream()
                .filter(t -> t.getInspectItemCode().equals(dto.getInspectItemCode()))
                //把设备类型信息拼接起来
                .map(t -> String.join(",", t.getEquipmentTypeInfos()
                    .stream()
                    .map(EquipmentTypeInfo::getEquipmentTypeCode)
                    .collect(Collectors.toList()))
                ).findFirst().orElse(""));
        }
        return EasyExcelUtil.getExcel(importDto, InspectItemImportDTO.class, "点巡检项目信息.xlsx");
    }

    /**
     * 查询所有项目
     *
     * @return 点巡检项目信息
     */
    private List<InspectItemDTO> findAll() {
        List<HvEamInspectItem> itemsList = inspectItemRepository.findAll();
        return addInfo(itemsList);
    }

    /**
     * {@inheritDoc}
     */
    private List<InspectItemDTO> getList(List<Integer> ids) {
        List<HvEamInspectItem> itemsList = inspectItemRepository.findAllById(ids);
        return addInfo(itemsList);
    }

    /**
     * 塞数据进去
     *
     * @param itemsList 项目列表
     * @return 项目信息
     */
    private List<InspectItemDTO> addInfo(List<HvEamInspectItem> itemsList) {
        List<Integer> ids = itemsList.stream()
            .map(SysBase::getId)
            .collect(Collectors.toList());
        List<HvEamInspectItemFile> files = inspectItemFileRepository.findAllByInspectItemIdIn(ids);
        List<HvEamInspectItemEquipment> equipments = inspectItemEquipmentRepository.getAllByItemIdIn(ids);
        List<HvEamInspectItemEquipmentType> types = inspectItemEquipmentTypeRepository.findAllByItemIdIn(ids);
        List<InspectItemDTO> result = DtoMapper.convertList(itemsList, InspectItemDTO.class);
        for (InspectItemDTO maintainItemDTO : result) {
            //设置文件信息
            maintainItemDTO.setFileIds(files.stream()
                .filter(t -> t.getInspectItemId().equals(maintainItemDTO.getId()))
                .map(HvEamInspectItemFile::getFileId)
                .collect(Collectors.toList()));
            //设置设备信息
            maintainItemDTO.setEquipmentInfos(equipments.stream()
                .filter(t -> t.getItemId().equals(maintainItemDTO.getId()))
                .map(t -> DtoMapper.convert(t, EquipmentInfo.class))
                .collect(Collectors.toList()));
            //设置设备类型信息
            maintainItemDTO.setEquipmentTypeInfos(types.stream()
                .filter(t -> t.getItemId().equals(maintainItemDTO.getId()))
                .map(t -> DtoMapper.convert(t, EquipmentTypeInfo.class))
                .collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    public List<String> getAllCycle() {
        return inspectItemRepository.getAllCycle()
            .stream()
            .filter(Strings::isNotBlank)
            .collect(Collectors.toList());
    }

    /**
     * 根据id获取保养项目详情
     *
     * @param id 保养项目id
     * @return 保养项目信息
     */
    @Override
    public InspectItemDTO getById(Integer id) {
        HvEamInspectItem entity = inspectItemRepository.findById(id).orElse(null);
        if (entity == null) {
            return null;
        }
        InspectItemDTO result = DtoMapper.convert(entity, InspectItemDTO.class);
        //找需要的数据
        List<HvEamInspectItemEquipment> equipment = inspectItemEquipmentRepository.findAllByItemId(id);
        List<HvEamInspectItemEquipmentType> types = inspectItemEquipmentTypeRepository.findAllByItemId(id);
        result.setFileIds(inspectItemFileRepository.findAllByInspectItemId(result.getId())
            .stream()
            .map(HvEamInspectItemFile::getFileId)
            .collect(Collectors.toList()));
        result.setEquipmentInfos(
            equipment.stream()
                .map(t -> DtoMapper.convert(t, EquipmentInfo.class))
                .collect(Collectors.toList()));
        result.setEquipmentTypeInfos(
            types.stream()
                .map(t -> DtoMapper.convert(t, EquipmentTypeInfo.class))
                .collect(Collectors.toList()));
        return result;
    }


    /**
     * 根据点检项目编码查询
     *
     * @param inspectItemCode 点检项目编码
     * @return 点检项目DTO
     */
    @Override
    public InspectItemDTO getByCode(String inspectItemCode) {
        if (!inspectItemRepository.existsByInspectItemCode(inspectItemCode)) {
            return null;
        }
        HvEamInspectItem hvEamInspectItem = inspectItemRepository.findByInspectItemCode(inspectItemCode);
        return getById(hvEamInspectItem.getId());
    }
}