package com.hvisions.eam.service.inspect;

import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.eam.dto.inspect.plan.InspectPlanDTO;
import com.hvisions.eam.dto.inspect.plan.InspectPlanQueryDTO;
import com.hvisions.eam.dto.inspect.plan.InspectPlanRejectDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <p>Title: InspectPlanService</p >
 * <p>Description: 点检计划interface接口</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/4/13</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface InspectPlanService {
    /**
     * 查询点检计划
     *
     * @param inspectPlanQueryDTO 点检计划查询条件
     * @return 点检计划
     */
    Page<InspectPlanDTO> getPage(InspectPlanQueryDTO inspectPlanQueryDTO);


    /**
     * 新增点检计划(不带内容)
     *
     * @param inspectPlanCreateOrUpdateDTO 点检计划DTO
     * @return 新增计划id
     */
    Integer createInspectPlan(InspectPlanDTO inspectPlanCreateOrUpdateDTO);

    /**
     * 编辑点检计划(不带内容)
     *
     * @param inspectPlanCreateOrUpdateDTO 点检计划DTO
     * @return 被编辑的id
     */
    Integer updateInspectPlan(InspectPlanDTO inspectPlanCreateOrUpdateDTO);


    /**
     * 批量删除计划
     *
     * @param idList id集合
     */
    void deleteInspectPlanByIdList(List<Integer> idList);

    /**
     * 审批通过计划
     *
     * @param taskIds  任务id
     * @param userInfo 用户信息
     * @param token    token
     */
    void startPlanById(List<String> taskIds, UserInfoDTO userInfo, String token);

    /**
     * 驳回计划
     *
     * @param inspectPlanRejectDTO 驳回DTO
     * @param userInfo             用户信息
     * @param token                token
     */
    void refusePlanById(InspectPlanRejectDTO inspectPlanRejectDTO, UserInfoDTO userInfo, String token);

    /**
     * 强制停用计划
     *
     * @param ids id列表
     */
    void forcedStopInspectPlan(List<Integer> ids);

    /**
     * 根据周期id查看有多少计划绑定
     *
     * @param timerId timerId
     * @return 绑定个数
     */
    Integer checkBindingByTimerId(Integer timerId);

    /**
     * 根据点巡检计划编码查询
     *
     * @param inspectPlanNum 点巡检计划编码
     * @return 保养计划
     */
    InspectPlanDTO findByInspectPlanNum(String inspectPlanNum);


    /**
     * 通过计划id手动触发计划
     *
     * @param planId 任务id
     * @return 流程实例id
     */
    String triggerPlanById(Integer planId);

    /**
     * 根据计划id查询计划详情
     *
     * @param planId 计划id
     * @return 点巡检计划详细信息
     */
    InspectPlanDTO getDetail(Integer planId);


    /**
     * 发起临时点巡检任务
     *
     * @param dto 点巡检配置项
     * @return 流程实例id
     */
    String createTemporaryTask(InspectPlanDTO dto);
}
