package com.hvisions.eam.service.autonomy;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.eam.dto.autonomy.InspectionProjectCreateOrUpdateDTO;
import com.hvisions.eam.dto.autonomy.InspectionProjectDTO;
import com.hvisions.eam.dto.autonomy.InspectionProjectQueryDTO;
import com.hvisions.eam.entity.autonomy.HvAmInspectionProject;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-11
 */
public interface InspectionProjectService extends IService<HvAmInspectionProject> {

    /**
     * 新增
     * <AUTHOR>
     * @param dto 入参
     * @return int
     */
    int add(InspectionProjectCreateOrUpdateDTO dto);

    /**
     * 删除单个
     * <AUTHOR>
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 批量删除
     * <AUTHOR>
     * @param idList 主键list
     */
    void deleteList(List<Integer> idList);

    /**
     * 修改
     * <AUTHOR>
     * @param dto 修改信息
     * @return int 主键
     */
    int update(InspectionProjectCreateOrUpdateDTO dto);

    /**
     * 分页查询
     * <AUTHOR>
     * @param dto 查询入参
     * @return 分页对象
     */
    Page<InspectionProjectDTO> queryList(InspectionProjectQueryDTO dto);

    /**
     * 获取计划详情
     * <AUTHOR>
     * @param id 主键
     * @return InspectionProjectDTO 项目
     */
    InspectionProjectDTO getById(Integer id);

    /**
     * 下载模板
     * <AUTHOR>

     * @return 模板
     */
    ResponseEntity<byte[]> getImportTemplate();

    /**
     * 下载模板(给前端使用）
     *
     * @return 模板数据
     */
    ExcelExportDto getImportTemplateForFront();

    /**
     * 导入
     * <AUTHOR>
     * @param file 文件
     *
     * @return 导入结果
     */
    ImportResult importExcel(MultipartFile file);

    /**
     * 导出
     * <AUTHOR>
     * @param dto 入参
     * @return 数据文件
     */
    ResponseEntity<byte[]> exportExcel(InspectionProjectQueryDTO dto);

    /**
     * 导出(给前端使用）
     * @param dto 入参dto
     * @return 数据文件
     */
    ExcelExportDto exportExcelForFront(InspectionProjectQueryDTO dto);

    /**
     * 获取检测周期
     * <AUTHOR>
     * @return 检测周期list
     */
    List<String> getTestCycle();
}
