package com.hvisions.eam.service.maintain.imp;

import com.hvisions.eam.client.spare.ActivitiHistoryClient;
import com.hvisions.eam.dao.DataMapper;
import com.hvisions.eam.dto.maintain.process.*;
import com.hvisions.eam.entity.maintain.HvEamMaintainProcessData;
import com.hvisions.eam.entity.maintain.HvEamMaintainProcessDataLub;
import com.hvisions.eam.entity.maintain.HvEamMaintainProcessDataSparepart;
import com.hvisions.eam.entity.maintain.HvEamMaintainProcessItem;
import com.hvisions.eam.repository.maintain.MaintainProcessDataLubRepository;
import com.hvisions.eam.repository.maintain.MaintainProcessDataRepository;
import com.hvisions.eam.repository.maintain.MaintainProcessDataSparepartRepository;
import com.hvisions.eam.repository.maintain.MaintainProcessItemRepository;
import com.hvisions.eam.service.maintain.MaintainDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.support.ListenerExecutionFailedException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title: MaintainDataServiceImpl</p>
 * <p>Description: 业务流程数据解析保存服务 </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/9/28</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class MaintainDataServiceImpl implements MaintainDataService {
    private final ActivitiHistoryClient client;
    private final MaintainProcessDataRepository repository;
    private final MaintainProcessDataLubRepository lubRepository;
    private final DataMapper dataMapper;
    private final MaintainProcessDataSparepartRepository sparepartRepository;
    private final MaintainProcessItemRepository itemRepository;

    @Autowired
    public MaintainDataServiceImpl(ActivitiHistoryClient client,
                                   MaintainProcessDataRepository repository,
                                   MaintainProcessDataLubRepository lubRepository,
                                   DataMapper dataMapper,
                                   MaintainProcessDataSparepartRepository sparepartRepository,
                                   MaintainProcessItemRepository itemRepository) {
        this.client = client;
        this.repository = repository;
        this.lubRepository = lubRepository;
        this.dataMapper = dataMapper;
        this.sparepartRepository = sparepartRepository;
        this.itemRepository = itemRepository;
    }

    /**
     * 处理流程实例信息
     *
     * @param processInstanceId 流程实例id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(String processInstanceId) {
        log.info("对保养流程实例进行保存：,ID:" + processInstanceId);
        if (repository.existsByProcessInstanceId(processInstanceId)) {
            log.info("已经存在保养流程实例,ID:" + processInstanceId);
            return;
        }
        JsonRootBean processInstance = client.getProcessInstance(processInstanceId);
        if (processInstance.getCode() == 200) {
            Data data = processInstance.getData();
            ProcessInstanceVariables variables = data.getProcessInstanceVariables();
            if (variables == null) {
                return;
            }
            //根据设备id获取产线列表
            List<Integer> lineIds = dataMapper.getLineIdsByEquipmentId(variables.getEquipmentId());
            if (lineIds == null || lineIds.size() == 0) {
                lineIds = new ArrayList<>();
                lineIds.add(0);
            }
            for (Integer lineId : lineIds) {
                HvEamMaintainProcessData maintainInstance = new HvEamMaintainProcessData();
                BeanUtils.copyProperties(data, maintainInstance, "id");
                BeanUtils.copyProperties(variables, maintainInstance, "id");
                maintainInstance.setLineId(lineId);
                repository.save(maintainInstance);
                MaintainContentDTO maintainContentDTO = variables.getMaintainContentDTO();
                if (maintainContentDTO == null) {
                    return;
                }
                List<MaintainItemDTOList> maintainItems = maintainContentDTO.getMaintainItemDTOList();
                if (maintainItems == null) {
                    return;
                }
                for (MaintainItemDTOList maintainItem : maintainItems) {
                    HvEamMaintainProcessItem item = new HvEamMaintainProcessItem();
                    BeanUtils.copyProperties(maintainItem, item);
                    item.setProcessDataId(maintainInstance.getId());
                    item.setMaintainItemId(maintainItem.getId());
                    itemRepository.save(item);
                    List<ItemLubDTOList> lubDtos = maintainItem.getItemLubDTOList();
                    if (lubDtos != null) {
                        //保存油品信息
                        for (ItemLubDTOList lubDto : lubDtos) {
                            HvEamMaintainProcessDataLub lubEntity = new HvEamMaintainProcessDataLub();
                            BeanUtils.copyProperties(lubDto, lubEntity);
                            lubEntity.setItemId(item.getId());
                            lubRepository.save(lubEntity);
                        }
                    }
                    //保存备件数据
                    List<ItemSparePartDTOList> spareDtos = maintainItem.getItemSparePartDTOList();
                    if (spareDtos != null) {
                        for (ItemSparePartDTOList lubDto : spareDtos) {
                            HvEamMaintainProcessDataSparepart sparepart = new HvEamMaintainProcessDataSparepart();
                            BeanUtils.copyProperties(lubDto, sparepart);
                            sparepart.setItemId(item.getId());
                            sparepartRepository.save(sparepart);
                        }
                    }
                }
            }
            log.info("成功保存流程实例,ID:" + processInstanceId);
        } else {
            log.info("流程信息获取失败,ID:" + processInstanceId);
        }

    }

    /**
     * 处理业务异常
     *
     * @param message 消息
     * @param e       异常
     */
    @Override
    public void handleError(Message message, ListenerExecutionFailedException e) {
        log.info("保存流程实例失败,ID:" + message.toString());
        log.error(message.toString(), e);
    }
}









