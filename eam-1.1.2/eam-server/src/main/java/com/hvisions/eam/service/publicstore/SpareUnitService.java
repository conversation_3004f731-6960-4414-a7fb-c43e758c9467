package com.hvisions.eam.service.publicstore;


import com.hvisions.eam.dto.publicstore.UnitDTO;
import com.hvisions.eam.query.spare.SpareUnitQueryDTO;
import org.springframework.data.domain.Page;

/**
 * <p>Title: SpareUnitService</p>
 * <p>Description: 备件单位</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface SpareUnitService {

    /**
     * 增加备件单位 改 通过 备件单位DTO
     *
     * @param unitDTO 备件单位DTO
     * @return 新增数据的ID
     */
    Integer save(UnitDTO unitDTO);

    /**
     * 删除备件单位 通过 ID
     *
     * @param id 库位ID
     */
    void deleteById(Integer id);

    /**
     * 查询备件单位 通过库位ID
     *
     * @param id 备件单位ID
     * @return 备件单位DTO
     */
    UnitDTO findById(Integer id);

    /**
     * 分页查询
     * 通过 单位名称  联合查询
     *
     * @param spareUnitQueryDTO 库位DTO
     * @return 分页信息
     */
    Page<UnitDTO> findAllByUnitName(SpareUnitQueryDTO spareUnitQueryDTO);

    /**
     * 通过单位名称获取单位id
     *
     * @param unitName 单位名称
     * @return 单位
     */
    UnitDTO findByUnitName(String unitName);

}
