package com.hvisions.eam.service.maintain.imp;

import com.google.common.collect.Lists;
import com.hvisions.activiti.client.TaskClient;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.EquipmentFeignClient;
import com.hvisions.eam.dao.CalendarMapper;
import com.hvisions.eam.dto.maintain.HiTaskDTO;
import com.hvisions.eam.dto.maintain.RepairMaintainCalendarCard;
import com.hvisions.eam.dto.maintain.RepairMaintainTaskDetail;
import com.hvisions.eam.dto.maintain.process.PlanEquipmentDTO;
import com.hvisions.eam.entity.maintain.HvEamMaintainPlan;
import com.hvisions.eam.entity.maintain.HvEamMaintainPlanEquipment;
import com.hvisions.eam.entity.SysBase;
import com.hvisions.eam.enums.RepairMaintainEnum;
import com.hvisions.eam.enums.TaskTypeEnum;
import com.hvisions.eam.repository.maintain.MaintainPlanEquipmentRepository;
import com.hvisions.eam.repository.maintain.MaintainPlanRepository;
import com.hvisions.eam.service.maintain.MaintainPlanService;
import com.hvisions.eam.service.maintain.RepairMaintainCalendarService;
import com.hvisions.hipertools.client.TimerClient;
import com.hvisions.hipertools.dto.PredictionTimerDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * <p>Title: RepairMaintainCalendarServiceImpl</p >
 * <p>Description: 维修保养日历service实现类</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2022/2/7</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Service
public class RepairMaintainCalendarServiceImpl implements RepairMaintainCalendarService {

    @Autowired
    TaskClient taskClient;

    @Autowired
    CalendarMapper calendarMapper;

    @Autowired
    MaintainPlanService maintainPlanService;

    @Autowired
    TimerClient timerClient;

    @Autowired
    MaintainPlanRepository maintainPlanRepository;

    @Autowired
    MaintainPlanEquipmentRepository planItemRepository;

    @Autowired
    EquipmentFeignClient equipmentFeignClient;

    /**
     * @return 维修保养任务详情数据
     */
    @Override
    public List<RepairMaintainCalendarCard> getRepairMaintainCalendar(LocalDate queryDate) throws ParseException {
        if (queryDate == null) {
            queryDate = LocalDate.now();
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //当月第一天 0点
        LocalDateTime startOfDay = queryDate.with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay();
        //当月最后一天 23.59
        LocalDateTime lastDay = LocalDateTime.of(queryDate.with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX);
        ZoneId zoneId = ZoneId.systemDefault();
        //当月的历史任务
        //保养
        List<HiTaskDTO> hiTask = calendarMapper.getHiTask(localDateToDate(startOfDay), localDateToDate(lastDay), "equipment-maintain");
        //维修
        List<HiTaskDTO> hiMainTask = calendarMapper.getHiTask(localDateToDate(startOfDay), localDateToDate(lastDay),
            "equipment-maintenance");
        //当月未开始的任务
        //保养
        List<HiTaskDTO> task = calendarMapper.getTask(localDateToDate(startOfDay), localDateToDate(lastDay), "equipment-maintain");
        //维修
        List<HiTaskDTO> mainTask = calendarMapper.getTask(localDateToDate(startOfDay), localDateToDate(lastDay),
            "equipment-maintenance");
        //合并任务信息
        hiTask.addAll(mainTask);
        hiTask.addAll(hiMainTask);
        hiTask.addAll(task);
        List<HiTaskDTO> sorted = hiTask.stream().sorted(Comparator.comparing(HiTaskDTO::getEndTime,
            Comparator.nullsFirst(String::compareTo)).reversed()).collect(Collectors.toList());
        List<HiTaskDTO> hiTaskDTOArrayList = sorted.stream().collect(
            collectingAndThen(
                toCollection(() -> new TreeSet<>(Comparator.comparing(HiTaskDTO::getTaskId
                ))),
                ArrayList::new));
        List<RepairMaintainTaskDetail> repairMaintainTaskDetails = new ArrayList<>();
        for (HiTaskDTO hiTaskDTO : hiTaskDTOArrayList) {
            RepairMaintainTaskDetail repairMaintainTaskDetail = new RepairMaintainTaskDetail();
            repairMaintainTaskDetail.setTaskId(hiTaskDTO.getTaskId());
            repairMaintainTaskDetail.setTaskDesc(hiTaskDTO.getTaskDesc());
            if (hiTaskDTO.getDefKey().equals("equipment-maintain")) {
                repairMaintainTaskDetail.setTaskType(TaskTypeEnum.MAINTAIN_TASK.getCode());
            } else if (hiTaskDTO.getDefKey().equals("equipment-maintenance")) {
                repairMaintainTaskDetail.setTaskType(TaskTypeEnum.REPAIR_TASK.getCode());
            }
            repairMaintainTaskDetail.setTaskStartTime(LocalDateTime.ofInstant(hiTaskDTO.getTaskStartTime().toInstant(), zoneId));
            // 有结束时间的任务为历史任务 已完成的
            if (hiTaskDTO.getTaskEndTime() != null) {
                repairMaintainTaskDetail.setTaskEndTime(LocalDateTime.ofInstant(hiTaskDTO.getTaskEndTime().toInstant(), zoneId));
                repairMaintainTaskDetail.setTaskStatus(RepairMaintainEnum.FINISHED.getCode());
            } else if (hiTaskDTO.getState().equals("0")) {
                repairMaintainTaskDetail.setTaskStatus(RepairMaintainEnum.PENDING.getCode());
            } else if (hiTaskDTO.getState().equals("1")) {
                repairMaintainTaskDetail.setTaskStatus(RepairMaintainEnum.PROCESSING.getCode());
            }
            //判断任务结束时间于当前时间比较大小  当前时间大则逾期
            if (hiTaskDTO.getEndTime() != null) {
                Date endTime = sdf.parse(hiTaskDTO.getEndTime());
                Date date = new Date();
                int i = endTime.compareTo(date);
                if (i < 0) {
                    repairMaintainTaskDetail.setTaskStatus(RepairMaintainEnum.OVERDUE.getCode());
                }
            }
            repairMaintainTaskDetails.add(repairMaintainTaskDetail);
        }
        //排列从月初到月底的日历
        List<RepairMaintainCalendarCard> calendarCards = new ArrayList<>();
        for (int i = 0; i < lastDay.getDayOfMonth(); i++) {
            RepairMaintainCalendarCard calendarCard = new RepairMaintainCalendarCard();
            // 当月第一天开始到结束 再加上循环的日期
            LocalDateTime dayStart = queryDate.with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay().plusDays(i);
            LocalDateTime dayEnd = LocalDateTime.of(queryDate.with(TemporalAdjusters.firstDayOfMonth()),
                LocalTime.MAX).plusDays(i);
            //时间转化long 用于比较大小
            long start = dayStart.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            long end = dayEnd.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            //排列日期
            calendarCard.setTaskDetails(new ArrayList<>());
            calendarCard.setCardDate(dayStart.toLocalDate());
            //过滤出每天的历史任务 添加到日历
            for (RepairMaintainTaskDetail repairMaintainTaskDetail : repairMaintainTaskDetails) {
                long l = repairMaintainTaskDetail.getTaskStartTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                if (l >= start && l <= end) {
                    calendarCard.getTaskDetails().add(repairMaintainTaskDetail);
                }
            }
            calendarCards.add(calendarCard);
        }

        //判断查询时间是否大于等于当前月份，如果是，需要根据计划信息估计未来的保养任务信息
        if (startOfDay.getMonthValue() >= (LocalDateTime.now().getMonthValue())) {
            LocalDateTime dayStart = LocalDate.now().plusDays(1).atStartOfDay();
            LocalDateTime dayEnd = LocalDateTime.of(LocalDate.now().plusDays(1), LocalTime.MAX);
            if (startOfDay.getMonthValue() == LocalDateTime.now().getMonthValue()) {
                //如果当前时间月等于查询时间月 只查今天以后的
                dayStart = LocalDateTime.now();
                dayEnd = lastDay;
            } else if (startOfDay.getMonthValue() > LocalDateTime.now().getMonthValue()) {
                //如果当前时间月小与查询时间月 查整月的
                dayStart = startOfDay;
                dayEnd = lastDay;
            }
            //查保养计划的所有激活的timer
            List<Integer> timeIdByPlan = calendarMapper.getTimeIdByPlan();
            //获取有运行轨迹的time,记录下timer的运行轨迹信息
            PredictionTimerDTO predictionTimerDTO = new PredictionTimerDTO();
            predictionTimerDTO.setExecutionTimeAfter(localDateToDate(dayEnd));
            predictionTimerDTO.setExecutionTimeBefore(localDateToDate(dayStart));
            List<Integer> timerIdList = new ArrayList<>();
            Map<Integer, List<String>> timerTriggerInfo = new HashMap<>();
            for (Integer timerId : timeIdByPlan) {
                predictionTimerDTO.setTimerId(timerId);
                ResultVO<List<String>> predictionTimerByTimerId = timerClient.getPredictionTimerByTimerId(predictionTimerDTO);
                if (predictionTimerByTimerId.isSuccess()) {
                    if (predictionTimerByTimerId.getData() != null) {
                        if (predictionTimerByTimerId.getData() != null && predictionTimerByTimerId.getData().size() > 0) {
                            timerIdList.add(timerId);
                            timerTriggerInfo.put(timerId, predictionTimerByTimerId.getData());
                        }
                    }
                }
            }
            //会生成任务的保养计划,2通过状态，4无需审批的状态
            List<HvEamMaintainPlan> plans =
                maintainPlanRepository.findAllByTimerIdInAndMaintainPlanConditionIdIn(timerIdList, Lists.newArrayList(2, 4));
            if (plans.size() == 0) {
                return calendarCards;
            }
            //查询关联的保养计划
            //生成未来的虚拟任务
            List<PlanEquipmentDTO> planEquipment = calendarMapper
                .getPlanEquipment(plans
                    .stream()
                    .map(SysBase::getId)
                    .collect(Collectors.toList()));
            for (HvEamMaintainPlan plan : plans) {
                List<HvEamMaintainPlanEquipment> items =
                    planItemRepository.getAllByMaintainPlanId(plan.getId());
                List<Integer> equipmentIdList = items
                    .stream()
                    .map(t -> t.getEquipmentId())
                    .distinct()
                    .collect(Collectors.toList());
                //先找下个时间点的执行时间
                predictionTimerDTO.setTimerId(plan.getTimerId());
                List<String> predictionTimer = timerTriggerInfo.get(plan.getTimerId());
                List<LocalDateTime> dates = new ArrayList<>();
                //根据循环处理运行轨迹时间
                for (String datum : predictionTimer) {
                    Date date = sdf.parse(datum);
                    LocalDateTime localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                    dates.add(localDate);
                }
                for (RepairMaintainCalendarCard calendarCard : calendarCards) {
                    for (LocalDateTime date : dates) {
                        int month = date.getMonthValue();
                        int day = date.getDayOfMonth();
                        int dayOfMonth = calendarCard.getCardDate().getDayOfMonth();
                        int monthValue = calendarCard.getCardDate().getMonthValue();
                        if (monthValue == month && dayOfMonth == day) {
                            //创建虚拟任务
                            //有几台设备创建几个虚拟任务
                            for (Integer integer : equipmentIdList) {
                                String equipmentName = "";
                                Optional<PlanEquipmentDTO> first =
                                        planEquipment.stream().filter(t -> t.getEquipmentId().equals(integer)).findFirst();
                                if (first.isPresent()) {
                                    equipmentName = first.get().getEquipmentName();
                                }
                                RepairMaintainTaskDetail repairMaintainTaskDetail = new RepairMaintainTaskDetail();
                                repairMaintainTaskDetail.setTaskStatus(RepairMaintainEnum.DENY.getCode());
                                repairMaintainTaskDetail.setTaskDesc(plan.getMaintainPlanName() + equipmentName);
                                repairMaintainTaskDetail.setTaskType(TaskTypeEnum.MAINTAIN_TASK.getCode());
                                repairMaintainTaskDetail.setTaskStartTime(date);
                                calendarCard.getTaskDetails().add(repairMaintainTaskDetail);
                            }

                        }
                    }
                }

            }
        }

        return calendarCards;
    }

    /**
     * LocalDate转换成Date
     *
     * @param localDateTime
     * @return
     */
    public static Date localDateToDate(LocalDateTime localDateTime) {
        if (null == localDateTime) {
            return null;
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

}
