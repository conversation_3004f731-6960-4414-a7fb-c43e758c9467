package com.hvisions.eam.service.autonomy.imp;

import com.hvisions.activiti.dto.process.ProcessDefinationQuery;
import com.hvisions.activiti.dto.process.ProcessDefinitionDTO;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.client.autonomy.MaintainsActivitiClient;
import com.hvisions.eam.service.autonomy.CheckActivitiBpmnService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <p>Title: CheckActivitiBpmnServiceImpl</p >
 * <p>Description: 检查bpmn文件是否存在</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/10</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class CheckActivitiBpmnServiceImpl implements CheckActivitiBpmnService {
    private final MaintainsActivitiClient maintainsActivitiClient;
    /**
     * 流程文件发布配置
     */
    @Value("${h-visions.bpmn-update}")
    private Boolean bpmnUpdate;

    @Autowired
    public CheckActivitiBpmnServiceImpl(@Qualifier("autonomyMaintainsActivitiClient") MaintainsActivitiClient inspectActivitiClient) {
        this.maintainsActivitiClient = inspectActivitiClient;
    }

    /**
     * 检查流程定义
     */
    @Override
    public void checkActivitiBpmn() {

        //自主维护任务
        String autonomymaintenanceKey = "autonomymaintenance";
        String autonomymaintenanceResourceName = "autonomymaintenance.bpmn20.xml";

        if (bpmnUpdate) {
            //自主维护任务流程文件强制发布
            deployProcess(autonomymaintenanceResourceName);
        } else {
            //自主维护任务流程查询，若无则发布
            checkBpmnAndDeploy(autonomymaintenanceKey, autonomymaintenanceResourceName);
        }
    }

    /**
     * 检查流程定义是否存在，不存在则发布
     *
     * @param key          key
     * @param resourceName 文件名
     */
    private void checkBpmnAndDeploy(String key, String resourceName) {
        ProcessDefinationQuery processDefinationQuery = new ProcessDefinationQuery();
        processDefinationQuery.setProcessDefinitionKey(key);
        ResultVO<List<ProcessDefinitionDTO>> process = maintainsActivitiClient.getAllProcess(processDefinationQuery);
        if (!process.isSuccess()) {
            log.error("流程定义查询失败{}", process);
            return;
        }
        List<ProcessDefinitionDTO> data = process.getData();
        if (data.size() <= 0) {
            //流程不存在，发布流程
            deployProcess(resourceName);
            return;
        }
        //取第一个进行判断
        ProcessDefinitionDTO processDefinitionDTO = data.get(0);
        if (processDefinitionDTO.getKey().equals(key)) {
            log.info(key + "定义文件存在");
            return;
        }
        //发布流程
        deployProcess(resourceName);
    }

    /**
     * 发布流程
     *
     * @param resourceName resourceName
     */
    private void deployProcess(String resourceName) {
        InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("process/" + resourceName);
        String configContent = null;
        try {
            configContent = readFile(inputStream);
        } catch (IOException e) {
            e.printStackTrace();
        }
        ResultVO resultVO = maintainsActivitiClient.deployByText(resourceName, configContent);
        if (resultVO.isSuccess()) {
            log.info(resourceName + "流程定义发布成功");
        } else {
            log.info(resourceName + "流程定义发布失败{}", resultVO);
        }
    }

    /**
     * 读取文件
     *
     * @param inputStream 输入流
     * @return 文件内容
     * @throws IOException io
     */
    private String readFile(InputStream inputStream) throws IOException {
        StringBuilder builder = new StringBuilder();
        try {
            InputStreamReader reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
            BufferedReader bfReader = new BufferedReader(reader);
            String tmpContent;
            while ((tmpContent = bfReader.readLine()) != null) {
                builder.append(tmpContent);
            }
            bfReader.close();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return this.filter(builder.toString());
    }

    /**
     * 过滤输入字符串, 剔除多行注释以及替换掉反斜杠
     *
     * @param input 内容
     * @return 替换后的内容
     */
    private String filter(String input) {
        return input.replaceAll("/\\*[\\s\\S]*?\\*/", "");
    }
}