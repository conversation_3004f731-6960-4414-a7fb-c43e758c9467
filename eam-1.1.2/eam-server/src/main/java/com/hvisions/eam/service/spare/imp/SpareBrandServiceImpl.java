package com.hvisions.eam.service.spare.imp;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.enums.StoreExceptionEnum;
import com.hvisions.eam.consts.SpareConsts;
import com.hvisions.eam.dao.SpareBrandMapper;
import com.hvisions.eam.dto.spare.SpareBrandDTO;
import com.hvisions.eam.dto.spare.SpareBrandQuery;
import com.hvisions.eam.entity.spare.HvEamSpareBrand;
import com.hvisions.eam.repository.sprare.SpareBrandRepository;
import com.hvisions.eam.repository.sprare.SpareEntityRepository;
import com.hvisions.eam.service.spare.SpareBrandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.Optional;

/**
 * <p>Title: SpareBrandServiceImpl</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/5/11</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Service
public class SpareBrandServiceImpl implements SpareBrandService {

    @Autowired
    SpareBrandMapper spareBrandMapper;

    @Autowired
    SpareBrandRepository spareBrandRepository;

    @Autowired
    SpareEntityRepository spareEntityRepository;

    /**
     * 新增备件品牌
     *
     * @param spareBrandDTO 备件品牌数据
     * @return 备件品牌信息
     */
    @Override
    public SpareBrandDTO createBrand(SpareBrandDTO spareBrandDTO) {
        HvEamSpareBrand save = spareBrandRepository.save(DtoMapper.convert(spareBrandDTO, HvEamSpareBrand.class));
        return DtoMapper.convert(save, SpareBrandDTO.class);
    }

    /**
     * 更新备件品牌
     *
     * @param spareBrandDTO 备件品牌数据
     * @return 备件品牌信息
     */
    @Override
    public SpareBrandDTO updateBrand(SpareBrandDTO spareBrandDTO) {
        HvEamSpareBrand save = spareBrandRepository.save(DtoMapper.convert(spareBrandDTO, HvEamSpareBrand.class));
        return DtoMapper.convert(save, SpareBrandDTO.class);
    }

    /**
     * 删除备件品牌信息
     *
     * @param id 备件品牌信息id
     */
    @Override
    public void deleteSpareBrand(Integer id) {
        Optional<HvEamSpareBrand> byId = spareBrandRepository.findById(id);
        if (byId.isPresent()) {
            //查询备件品牌是否被使用 如果已经使用禁止删除
            Boolean aBoolean = spareEntityRepository.existsByBrand(byId.get().getSpareBrandCode());
            if (aBoolean) {
                throw new BaseKnownException(StoreExceptionEnum.BRAND_USED);
            }
            spareBrandRepository.deleteById(id);
        }
    }

    /**
     * 分页查询
     *
     * @param spareBrandQuery 品牌查询条件
     * @return 分页信息
     */
    @Override
    public Page<SpareBrandDTO> getBrandByQuery(SpareBrandQuery spareBrandQuery) {
        return PageHelperUtil.getPage(spareBrandMapper::getBrandByQuery, spareBrandQuery);
    }

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @Override
    public ResultVO<ExcelExportDto> getBrandImportTemplate() throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> result =
                ExcelUtil.generateImportFile(SpareBrandDTO.class,
                        SpareConsts.SPARE_BRAND_FILE_NAME);
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName(SpareConsts.SPARE_BRAND_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @Override
    public ResponseEntity<byte[]> getBrandImportTemplateLink() throws IOException, IllegalAccessException {
        return ExcelUtil.generateImportFile(SpareBrandDTO.class,
                SpareConsts.SPARE_BRAND_FILE_NAME
        );
    }

    /**
     * 导入所有备件品牌信息信息
     *
     * @param file 设备信息excel
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @Override
    public ImportResult importSpareBrand(MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        return ExcelUtil.importEntity(file,
                SpareBrandDTO.class,
                this);
    }

    /**
     * 导出品牌信息
     *
     * @return 品牌信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    @Override
    public ResponseEntity<byte[]> exportSpareBrandLink() throws IOException, IllegalAccessException {
        return ExcelUtil.generateImportFile(DtoMapper.convertList(spareBrandRepository.findAll(), SpareBrandDTO.class),
                SpareConsts.SPARE_BRAND_FILE_NAME,
                SpareBrandDTO.class);

    }

    /**
     * 导出品牌信息
     *
     * @return 品牌信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    @Override
    public ResultVO<ExcelExportDto> exportSpareBrand() throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> responseEntity = exportSpareBrandLink();
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(responseEntity.getBody());
        excelExportDto.setFileName(SpareConsts.SPARE_BRAND_FILE_NAME);
        return ResultVO.success(excelExportDto);
    }


    @Override
    public void saveOrUpdate(SpareBrandDTO spareBrandDTO) {
        if (spareBrandDTO.getSpareBrandCode() != null) {
            HvEamSpareBrand allBySpareBrandCode = spareBrandRepository.getAllBySpareBrandCode(spareBrandDTO.getSpareBrandCode());
            if (allBySpareBrandCode != null) {
                spareBrandDTO.setId(allBySpareBrandCode.getId());
                updateBrand(spareBrandDTO);
            } else {
                createBrand(spareBrandDTO);
            }
        }

    }
}
