package com.hvisions.eam.service.publicstore.imp;


import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.eam.activiti.lub.VariablesLubDTO;
import com.hvisions.eam.activiti.sapre.VariablesSpareDTO;
import com.hvisions.eam.client.task.HistoryVariables;
import com.hvisions.eam.client.task.Items;
import com.hvisions.eam.dto.SysBaseDTO;
import com.hvisions.eam.dto.publicstore.HeaderDTO;
import com.hvisions.eam.dto.publicstore.LineDTO;
import com.hvisions.eam.dto.publicstore.RejectDTO;
import com.hvisions.eam.dto.spare.SpareToShelveDTO;
import com.hvisions.eam.entity.lub.HvEamLubToShelve;
import com.hvisions.eam.entity.lub.HvEamLubricating;
import com.hvisions.eam.entity.publicstore.HvEamStoreHeader;
import com.hvisions.eam.entity.spare.HvEamSpare;
import com.hvisions.eam.entity.spare.HvEamSpareToShelve;
import com.hvisions.eam.enums.StoreExceptionEnum;
import com.hvisions.eam.query.publicstore.HeaderQueryDTO;
import com.hvisions.eam.repository.lub.LubEntityRepository;
import com.hvisions.eam.repository.lub.LubToShelveEntityRepository;
import com.hvisions.eam.repository.publicstore.StoreHeaderRepository;
import com.hvisions.eam.repository.sprare.SpareEntityRepository;
import com.hvisions.eam.repository.sprare.SpareToShelveEntityRepository;
import com.hvisions.eam.service.publicstore.StoreHeaderService;
import com.hvisions.eam.service.publicstore.StoreLineService;
import com.hvisions.eam.service.spare.SpareToShelveService;
import com.hvisions.framework.client.BaseUserClient;
import com.hvisions.framework.dto.user.UserInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title:StoreHeaderServiceImpl</p>
 * <p>Description:头表</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/31</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Slf4j
@Service
public class StoreHeaderServiceImpl implements StoreHeaderService {

    private final String NULL = "null";

    private final StoreHeaderRepository storeHeaderRepository;
    private final StoreLineService storeLineService;
    private final SpareEntityRepository spareEntityRepository;
    private final LubEntityRepository lubEntityRepository;
    private final SpareToShelveService spareToShelveService;
    private final SpareToShelveEntityRepository spareToShelveEntityRepository;
    private final LubToShelveEntityRepository lubToShelveEntityRepository;
    private final BaseUserClient authClient;

    @Autowired
    public StoreHeaderServiceImpl(StoreHeaderRepository storeHeaderRepository,
                                  StoreLineService storeLineService,
                                  SpareEntityRepository spareEntityRepository,
                                  LubEntityRepository lubEntityRepository,
                                  SpareToShelveService spareToShelveService,
                                  SpareToShelveEntityRepository spareToShelveEntityRepository,
                                  LubToShelveEntityRepository lubToShelveEntityRepository,
                                  BaseUserClient authClient) {

        this.storeHeaderRepository = storeHeaderRepository;
        this.storeLineService = storeLineService;
        this.spareEntityRepository = spareEntityRepository;
        this.lubEntityRepository = lubEntityRepository;
        this.spareToShelveService = spareToShelveService;
        this.spareToShelveEntityRepository = spareToShelveEntityRepository;
        this.lubToShelveEntityRepository = lubToShelveEntityRepository;
        this.authClient = authClient;
    }

    /**
     * 批量出库
     *
     * @param headerIds 头表列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deliveryOfCargoFromStorages(List<Integer> headerIds) {
        if (headerIds == null) {
            throw new BaseKnownException(StoreExceptionEnum.SELECT_OPERATION_DATA);
        }
        if (headerIds.size() <= 0) {
            throw new BaseKnownException(StoreExceptionEnum.SELECT_OPERATION_DATA);
        }
        for (Integer headerId : headerIds) {
            deliveryOfCargoFromStorage(headerId);
        }
    }


    /**
     * 通过头表进行出库
     *
     * @param headerId 头表id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deliveryOfCargoFromStorage(Integer headerId) {
        //通过头表id获取全部数据
        HeaderDTO header = getHeaderById(headerId);
        List<LineDTO> lineDTOS = header.getLineDTOS();
        if (lineDTOS == null || header.getComplete()) {
            throw new BaseKnownException(StoreExceptionEnum.CURRENT_OUTBOUND_ORDER_IS_INVALID);
        }
        //信息为空报错
        if (lineDTOS.size() == 0) {
            throw new BaseKnownException(StoreExceptionEnum.EMPTY_LINE);
        }
        //验证数据的有效性
        for (LineDTO lineDTO : lineDTOS) {
            if (Strings.isBlank(lineDTO.getBatchNumber())) {
                throw new BaseKnownException(StoreExceptionEnum.BATCH_NUMBER_INFORMATION_EXCEPTION);
            }
            if (lineDTO.getShelveId() == null) {
                throw new BaseKnownException(StoreExceptionEnum.SHELVE_INFORMATION_EXCEPTION);
            }
            if (lineDTO.getNumber() == null) {
                throw new BaseKnownException(StoreExceptionEnum.NUMBER_EXCEPTION);
            }
            if (lineDTO.getNumber().compareTo(BigDecimal.ZERO) <= 0) {
                throw new BaseKnownException(StoreExceptionEnum.NUMBER_EXCEPTION);
            }
            if (lineDTO.getSpareId() == null) {
                throw new BaseKnownException(StoreExceptionEnum.SPARE_PART_ERROR);
            }
        }
        //进行数据验证
        // 备件
        if (header.getTypeClass() == 1) {
            //入库
            if (header.getInOut() == 1) {
                for (LineDTO lineDTO : lineDTOS) {
                    HvEamSpareToShelve stocks =
                        spareToShelveEntityRepository
                            .findAllBySpareIdAndShelveIdAndBatchNumber(
                                lineDTO.getSpareId(),
                                lineDTO.getShelveId(),
                                lineDTO.getBatchNumber());
                    if (stocks == null) {
                        //统一管理
                        //库存中无此数据
                        HvEamSpareToShelve newStock = new HvEamSpareToShelve(lineDTO.getSpareId(), lineDTO.getShelveId(), lineDTO.getBatchNumber());
                        newStock.setNumber(lineDTO.getNumber().intValue());
                        //调用专门的增加接口
                        spareToShelveService.save(DtoMapper.convert(newStock, SpareToShelveDTO.class));
                    } else {
                        //有此数据
                        stocks.setNumber(lineDTO.getNumber().add(new BigDecimal(stocks.getNumber())).intValue());
                        spareToShelveService.save(DtoMapper.convert(stocks, SpareToShelveDTO.class));
                    }
                }
                //出库单作废
                storeLineService.deliveryOfCargoFromStorage(lineDTOS);
                this.validation(headerId);
            }
            //出库
            else {
                for (LineDTO lineDTO : lineDTOS) {
                    HvEamSpareToShelve spareToShelve = spareToShelveEntityRepository.findAllBySpareIdAndShelveIdAndBatchNumber(lineDTO.getSpareId(), lineDTO.getShelveId(), lineDTO.getBatchNumber());
                    String spareName = "备件";
                    //备件库存数量不足
                    if (spareToShelve == null) {
                        //库房批次号未查询到当前备件
                        throw new BaseKnownException(StoreExceptionEnum.SHELVE_AND_BATCH_NUMBER_NOT_SELECT_XXX, spareName);
                    }
                    //如果库存数量不足
                    if ((lineDTO.getNumber().compareTo(new BigDecimal(spareToShelve.getNumber())) > 0)) {
                        //备件库存数量不足
                        Optional<HvEamSpare> spare = spareEntityRepository.findById(lineDTO.getSpareId());
                        if (spare.isPresent()) {
                            spareName = spare.get().getSpareName();
                        }
                        throw new BaseKnownException(StoreExceptionEnum.XXX_SHELVE_NUMBER_EXCEPTION, spareName);
                    }
                }
                log.info("出库成功");
                // ---= 开始出库 =----
                for (LineDTO lineDTO : lineDTOS) {
                    HvEamSpareToShelve spareToShelve = spareToShelveEntityRepository.findAllBySpareIdAndShelveIdAndBatchNumber(lineDTO.getSpareId(), lineDTO.getShelveId(), lineDTO.getBatchNumber());
                    spareToShelve.setNumber(new BigDecimal(spareToShelve.getNumber()).subtract(lineDTO.getNumber()).intValue());
                    spareToShelveEntityRepository.save(spareToShelve);
                }
                // ---= 更改头表全部信息 =---
                storeLineService.deliveryOfCargoFromStorage(lineDTOS);
                //进行验证如果内容项全部完成则头表也变为完成状态
                this.validation(headerId);
            }
        }
        //油品
        else {
            //入库
            if (header.getInOut() == 1) {
                for (LineDTO lineDTO : lineDTOS) {
                    HvEamLubToShelve lubToShelve = lubToShelveEntityRepository.findAllByLubIdAndShelveIdAndBatchNumber(lineDTO.getSpareId(), lineDTO.getShelveId(), lineDTO.getBatchNumber());
                    if (lubToShelve == null) {
                        HvEamLubToShelve lubToShelve1 = new HvEamLubToShelve(lineDTO.getSpareId(), lineDTO.getShelveId(), lineDTO.getNumber(), lineDTO.getBatchNumber());
                        lubToShelveEntityRepository.save(lubToShelve1);
                    } else {
                        //有此数据
                        lubToShelve.setNumber(lineDTO.getNumber().add(lubToShelve.getNumber()));
                        SpareToShelveDTO convert = DtoMapper.convert(lubToShelve, SpareToShelveDTO.class);
                        convert.setSpareId(lubToShelve.getLubId());
                        spareToShelveService.save(convert);
                    }
                }
                // ---= 更改头表全部信息 =---
                storeLineService.deliveryOfCargoFromStorage(lineDTOS);
                //进行验证如果内容项全部完成则头表也变为完成状态
                this.validation(headerId);
            }
            //出库
            else {
                for (LineDTO lineDTO : lineDTOS) {
                    HvEamLubToShelve lubToShelve = lubToShelveEntityRepository.findAllByLubIdAndShelveIdAndBatchNumber(lineDTO.getSpareId(), lineDTO.getShelveId(), lineDTO.getBatchNumber());
                    String lubName = "油品";
                    //油品库存数量不足
                    if (lubToShelve == null) {
                        //库房批次号未查询到当前备件
                        throw new BaseKnownException(StoreExceptionEnum.SHELVE_AND_BATCH_NUMBER_NOT_SELECT_XXX, lubName);
                    }
                    //如果库存数量不足
                    if ((lineDTO.getNumber().compareTo(lubToShelve.getNumber()) > 0)) {
                        //备件库存数量不足
                        Optional<HvEamLubricating> lub = lubEntityRepository.findById(lineDTO.getSpareId());
                        if (lub.isPresent()) {
                            lubName = lub.get().getLubName();
                        }
                        throw new BaseKnownException(StoreExceptionEnum.XXX_SHELVE_NUMBER_EXCEPTION, lubName);
                    }
                }
                // ---= 开始出库 =----
                for (LineDTO lineDTO : lineDTOS) {
                    HvEamLubToShelve lubToShelve = lubToShelveEntityRepository.findAllByLubIdAndShelveIdAndBatchNumber(lineDTO.getSpareId(), lineDTO.getShelveId(), lineDTO.getBatchNumber());

                    lubToShelve.setNumber(lubToShelve.getNumber().subtract(lineDTO.getNumber()));
                    lubToShelveEntityRepository.save(lubToShelve);
                }
                // ---= 更改头表全部信息 =---
                storeLineService.deliveryOfCargoFromStorage(lineDTOS);
                //进行验证如果内容项全部完成则头表也变为完成状态
                this.validation(headerId);
                log.info("出库成功");
            }
        }
    }


    /**
     * 增加头表 同时增加行表
     *
     * @param variables 头表数据
     * @return 展示信息
     */
    @Override
    public VariablesSpareDTO addHeader(VariablesSpareDTO variables) {
        //创建行表
        HvEamStoreHeader hvEamStoreHeader = new HvEamStoreHeader();
        //添加单号 标题 出入库 来源
        hvEamStoreHeader.setReceiptNumber(variables.getReceiptNumber());
        hvEamStoreHeader.setTitle(variables.getTitle());
        hvEamStoreHeader.setInOut(Integer.parseInt(variables.getInOut()));
        hvEamStoreHeader.setSource(variables.getSource());
        hvEamStoreHeader.setUpdateTime(new Date());
        hvEamStoreHeader.setCreateTime(new Date());
        if (variables.getProcessInstanceId() != null) {
            hvEamStoreHeader.setProcessInstanceId(variables.getProcessInstanceId());
        }
        //类型为 1备件
        hvEamStoreHeader.setTypeClass(1);
        //添加行表
        HvEamStoreHeader header = storeHeaderRepository.save(hvEamStoreHeader);
        variables.setHeaderId(header.getId());
        storeLineService.addSpareLine(header.getId(), variables.getItems());
        return variables;
    }

    /**
     * 增加头表 同时增加行表
     *
     * @param variables 头表数据
     * @return 展示信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public VariablesLubDTO addHeader(VariablesLubDTO variables) {
        //创建行表
        HvEamStoreHeader hvEamStoreHeader = new HvEamStoreHeader();
        //添加单号 标题 出入库 来源
        hvEamStoreHeader.setReceiptNumber(variables.getReceiptNumber());
        hvEamStoreHeader.setInOut(Integer.parseInt(variables.getInOut()));
        hvEamStoreHeader.setTitle(variables.getTitle());
        hvEamStoreHeader.setSource(variables.getSource());
        //类型为 2油品
        hvEamStoreHeader.setTypeClass(2);
        //添加行表
        HvEamStoreHeader header = storeHeaderRepository.save(hvEamStoreHeader);
        storeLineService.addLubLine(header.getId(), variables.getItems());
        return variables;
    }

    /**
     * 增加头表 同时增加行表
     *
     * @param processInstanceId 流程id
     * @param map               信息
     */
    @Override
    public void addHeader(String processInstanceId, HistoryVariables map, int typeClass) {
        int inOut = 0;
        try {
            inOut = Integer.parseInt(map.getInOut());
        } catch (Exception e) {
            log.info("InOut String -> Integer error");
            throw new BaseKnownException(StoreExceptionEnum.DATE_ERROR_EXCEPTION);
        }
        //标题 批次号
        String source = map.getSource();
        String title = map.getTitle();
        String receiptNumber = map.getReceiptNumber();
        HvEamStoreHeader header = new HvEamStoreHeader(receiptNumber, title, processInstanceId, inOut, typeClass, source);
        header.setCreateTime(new Date());
        header.setUpdateTime(new Date());
        //创建头表
        HvEamStoreHeader save = storeHeaderRepository.save(header);
        List<Items> items = map.getItems();
        //创建行表
        if (typeClass == 1) {
            //备件
            storeLineService.addSpareLine(save.getId(), items, 1);
        } else {
            //油品
            storeLineService.addSpareLine(save.getId(), items, 2);
        }

    }

    /**
     * 分页查询
     *
     * @param headerQueryDTO 头表查询
     * @return 分页
     */
    @Override
    public Page<HeaderDTO> getHeader(HeaderQueryDTO headerQueryDTO) {
        //把DTO转换成普通类
        HvEamStoreHeader header = DtoMapper.convert(headerQueryDTO, HvEamStoreHeader.class);
        SimpleDateFormat sdfmat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Specification<HvEamStoreHeader> specification = (Specification<HvEamStoreHeader>) (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            if (StringUtils.isNotBlank(header.getReceiptNumber())) {
                list.add(criteriaBuilder.like((root.get("receiptNumber")).as(String.class), '%' + header.getReceiptNumber() + '%'));
            }
            if (StringUtils.isNotBlank(header.getTitle())) {
                list.add(criteriaBuilder.like((root.get("title")).as(String.class), '%' + header.getTitle() + '%'));
            }
            if (StringUtils.isNotBlank(header.getProcessInstanceId())) {
                list.add(criteriaBuilder.like((root.get("processInstanceId")).as(String.class), '%' + header.getProcessInstanceId() + '%'));
            }
            if (header.getSource() != null) {
                list.add(criteriaBuilder.like((root.get("source")).as(String.class), '%' + header.getSource() + '%'));
            }
            if (header.getInOut() != null && (header.getInOut() == 1 || header.getInOut() == 2)) {
                list.add(criteriaBuilder.equal((root.get("inOut")).as(Integer.class), header.getInOut()));
            }
            if (header.getTypeClass() != null) {
                list.add(criteriaBuilder.equal((root.get("typeClass")).as(Integer.class), header.getTypeClass()));
            }
            if (header.getComplete() != null) {
                list.add(criteriaBuilder.equal((root.get("complete")).as(Boolean.class), header.getComplete()));
            }

            if (headerQueryDTO.verification()) {

                try {
                    list.add(criteriaBuilder.between(root.get("createTime"), sdfmat.parse(sdf.format(headerQueryDTO.getStartTime())), sdfmat.parse(sdf.format(headerQueryDTO.getEndTime()))));
                } catch (ParseException e) {
                    e.printStackTrace();
                }

            }

            Predicate[] predicates = list.toArray(new Predicate[0]);
            return criteriaBuilder.and(predicates);

        };
        Page<HvEamStoreHeader> all = storeHeaderRepository.findAll(specification, headerQueryDTO.getRequest());
        Page<HeaderDTO> headerDTOS = DtoMapper.convertPage(all, HeaderDTO.class);
        List<UserInfoDto> userInfoDtos = authClient.getUserInfoByIds(headerDTOS
            .stream().map(SysBaseDTO::getCreatorId).collect(Collectors.toList())).getData();
        for (HeaderDTO headerDTO : headerDTOS) {
            List<LineDTO> itemByHeaderId = new ArrayList<>();
            try {
                itemByHeaderId = storeLineService.getItemByHeaderId(headerDTO.getId());
            } catch (Exception e) {
                log.info("获取子表异常 父表id :" + headerDTO.getId());
            }
            if (headerDTO.getSource() == null || NULL.equals(headerDTO.getSource())) {
                headerDTO.setSource("");
            }
            headerDTO.setLineDTOS(itemByHeaderId);
            headerDTO.setCreatorName(userInfoDtos.stream().filter(t -> t.getId().equals(headerDTO.getCreatorId()))
                .findFirst().map(UserInfoDto::getUserName).orElse(""));
        }
        return headerDTOS;
    }

    /**
     * 查询出库表 list
     *
     * @param headerQueryDTO 查询条件
     * @return list
     */
    @Override
    public List<HeaderDTO> getHeaderList(HeaderQueryDTO headerQueryDTO) {
        List<HeaderDTO> list = new ArrayList<>();
        HvEamStoreHeader example = new HvEamStoreHeader();
        example.setInOut(headerQueryDTO.getInOut());
        example.setProcessInstanceId(headerQueryDTO.getProcessInstanceId());
        example.setTypeClass(headerQueryDTO.getTypeClass());
        example.setComplete(null);
        for (HvEamStoreHeader header : storeHeaderRepository.findAll(Example.of(example))) {
            list.add(getHeaderById(header.getId()));
        }
        return list;
    }

    /**
     * 修改出库数据
     *
     * @param lineDTO 行表
     * @return id
     */
    @Override
    public Integer updateHeaderLine(LineDTO lineDTO) {
        return storeLineService.update(lineDTO);
    }

    /**
     * 修改出库信息数据
     *
     * @param lineDTO 行表
     * @return 行表id
     */
    @Override
    public List<Integer> updateHeaderLineList(List<LineDTO> lineDTO) {
        List<Integer> idList = new ArrayList<>();
        for (LineDTO line : lineDTO) {
            idList.add(updateHeaderLine(line));
        }
        return idList;
    }

    /**
     * 删除
     *
     * @param lineDTOId id
     */
    @Override
    public void deleteHeaderLine(Integer lineDTOId) {
        storeLineService.delete(lineDTOId);
    }

    @Override
    public List<String> getAllSource() {
        //查询全部数据 取出来源 去重
        return storeHeaderRepository.findAll().stream().map(HvEamStoreHeader::getSource).distinct().collect(Collectors.toList());
    }

    /**
     * 驳回出库单
     *
     * @param rejectDTO 驳回信息
     */
    @Override
    public void reject(RejectDTO rejectDTO) {
        storeHeaderRepository.findById(rejectDTO.getHeaderId())
            .ifPresent(t -> {
                if (2 != t.getInOut()) {
                    throw new BaseKnownException(10000, "只能操作出库单");
                }
                if (t.getComplete()) {
                    throw new BaseKnownException(10000, "出库单已经关闭");
                }
                t.setComplete(true);
                t.setReject(1);
                t.setRejectReason(rejectDTO.getReason());
                storeHeaderRepository.save(t);
            });
    }

    /**
     * 通过头表id获取全部数据
     *
     * @param headerId 头表id
     * @return 全部数据
     */
    private HeaderDTO getHeaderById(Integer headerId) {
        Optional<HvEamStoreHeader> optional = storeHeaderRepository.findById(headerId);
        //如果不存在
        if (!optional.isPresent()) {
            throw new BaseKnownException(StoreExceptionEnum.DATE_ERROR_EXCEPTION);
        }
        HeaderDTO headerDTO = DtoMapper.convert(optional.get(), HeaderDTO.class);
        headerDTO.setLineDTOS(storeLineService.getItemByHeaderId(headerDTO.getId()));
        return headerDTO;
    }

    /**
     * 验证行表是否已经完全出库
     *
     * @param headerId 头表id
     */
    private void validation(Integer headerId) {
        HeaderDTO header = getHeaderById(headerId);
        boolean next = true;
        for (LineDTO lineDTO : header.getLineDTOS()) {
            if (!lineDTO.getComplete()) {
                next = false;
            }
        }
        if (header.getLineDTOS().size() == 0) {
            next = true;
        }
        if (next) {
            header.setComplete(next);
            HvEamStoreHeader save = storeHeaderRepository.save(DtoMapper.convert(header, HvEamStoreHeader.class));
            log.info(" ---= 表id=" + save.getId() + " 已经全部出库 =--- ");
        }
    }

}
