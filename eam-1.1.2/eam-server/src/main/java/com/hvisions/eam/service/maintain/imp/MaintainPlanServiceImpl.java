package com.hvisions.eam.service.maintain.imp;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Maps;
import com.hvisions.activiti.client.ActivitiClient;
import com.hvisions.activiti.dto.instance.ProcessInstanceStartDTO;
import com.hvisions.activiti.dto.process.ProcessInstanceDTO;
import com.hvisions.activiti.dto.task.TaskHandleDTO;
import com.hvisions.activiti.dto.task.TaskWithVariableDTO;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.utils.SerialUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.activiti.lub.ItemsLubDTO;
import com.hvisions.eam.activiti.lub.RootLubDTO;
import com.hvisions.eam.activiti.lub.VariablesLubDTO;
import com.hvisions.eam.activiti.sapre.ItemsSpareDTO;
import com.hvisions.eam.activiti.sapre.RootSpareDTO;
import com.hvisions.eam.activiti.sapre.VariablesSpareDTO;
import com.hvisions.eam.configuration.maintain.ServiceClient;
import com.hvisions.eam.consts.MaintainConsts;
import com.hvisions.eam.dao.MaintainPlanMapper;
import com.hvisions.eam.dto.SysBaseDTO;
import com.hvisions.eam.dto.maintain.*;
import com.hvisions.eam.dto.publicstore.UseListingSpareDTO;
import com.hvisions.eam.dto.spare.SpareDTO;
import com.hvisions.eam.entity.maintain.*;
import com.hvisions.eam.enums.MaintainErrorExceptionEnum;
import com.hvisions.eam.enums.PlanConditionEnum;
import com.hvisions.eam.repository.maintain.*;
import com.hvisions.eam.repository.sprare.SpareTypeEntityRepository;
import com.hvisions.eam.service.maintain.MaintainItemService;
import com.hvisions.eam.service.maintain.MaintainPlanService;
import com.hvisions.eam.service.publicstore.ApplyService;
import com.hvisions.eam.service.publicstore.UseListingService;
import com.hvisions.eam.service.spare.SpareService;
import com.hvisions.framework.client.AuthClient;
import com.hvisions.framework.dto.user.UserInfoDto;
import com.hvisions.hiperbase.equipment.EquipmentDTO;
import com.hvisions.hipertools.client.TimerClient;
import com.hvisions.hipertools.dto.TimerDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title: HvMaintainPlanServiceImpl</p >
 * <p>Description: 保养计划实现</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/19</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class MaintainPlanServiceImpl implements MaintainPlanService {
    private final MaintainPlanRepository maintainPlanRepository;
    private final AuthClient authClient;
    private final TimerClient timerClient;
    private final ActivitiClient activitiClient;
    private final MaintainPlanEquipmentRepository maintainPlanEquipmentRepository;
    private final SerialUtil serialUtil;
    private final ApplyService applyService;
    private final ServiceClient serviceClient;
    private final MaintainItemService maintainItemService;
    private final MaintainItemFileRepository maintainItemFileRepository;
    private final SpareTypeEntityRepository spareTypeEntityRepository;

    private final UseListingService useListingService;
    private final MaintainPlanMapper maintainPlanMapper;
    private final MaintainPlanSparePartRepository maintainPlanSparePartRepository;
    private final MaintainPlanFileRepository maintainPlanFileRepository;
    private final MaintainPlanLubRepository maintainPlanLubRepository;
    private final SpareService spareService;

    /**
     * 计划是否需要审批
     */
    @Value("${h-visions.approve}")
    private Boolean approve;

    @Autowired
    public MaintainPlanServiceImpl(AuthClient authClient,
                                   ActivitiClient activitiClient,
                                   MaintainPlanRepository maintainPlanRepository,
                                   TimerClient timerClient,
                                   MaintainPlanEquipmentRepository maintainPlanEquipmentRepository,
                                   SerialUtil serialUtil,
                                   ApplyService applyService,
                                   ServiceClient serviceClient, MaintainItemService maintainItemService,
                                   MaintainItemFileRepository maintainItemFileRepository,
                                   SpareTypeEntityRepository spareTypeEntityRepository, UseListingService useListingService, MaintainPlanMapper maintainPlanMapper,
                                   MaintainPlanSparePartRepository maintainPlanSparePartRepository,
                                   MaintainPlanFileRepository maintainPlanFileRepository,
                                   MaintainPlanLubRepository maintainPlanLubRepository, SpareService spareService) {
        this.maintainPlanRepository = maintainPlanRepository;
        this.authClient = authClient;
        this.timerClient = timerClient;
        this.activitiClient = activitiClient;
        this.maintainPlanEquipmentRepository = maintainPlanEquipmentRepository;
        this.serialUtil = serialUtil;
        this.applyService = applyService;
        this.serviceClient = serviceClient;
        this.maintainItemService = maintainItemService;
        this.maintainItemFileRepository = maintainItemFileRepository;
        this.spareTypeEntityRepository = spareTypeEntityRepository;
        this.useListingService = useListingService;
        this.maintainPlanMapper = maintainPlanMapper;
        this.maintainPlanSparePartRepository = maintainPlanSparePartRepository;
        this.maintainPlanFileRepository = maintainPlanFileRepository;
        this.maintainPlanLubRepository = maintainPlanLubRepository;
        this.spareService = spareService;
    }

    /**
     * 新增无内容计划
     *
     * @param maintainPlanDTO 计划DTO
     * @return 新增计划id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createMaintainPlan(MaintainPlanDTO maintainPlanDTO) {
        Assert.isNull(maintainPlanDTO.getId(), "新增时不能传递id");
        Assert.notNull(maintainPlanDTO.getPlanItems(), "保养项目不能为空");
        validPlan(maintainPlanDTO);
        HvEamMaintainPlan planEntity = DtoMapper.convert(maintainPlanDTO, HvEamMaintainPlan.class);
        planEntity.setCandidateGroups(maintainPlanDTO.getCandidateGroupsString());
        planEntity.setCandidateUsers(maintainPlanDTO.getCandidateUsersString());
        //保养计划编码
        String maintainPlanNum = serialUtil.getSerialNumber("maintainPlan");
        //创建计划
        Integer id;
        planEntity.setMaintainPlanNum(maintainPlanNum);
        //计划需要审批
        if (approve) {
            //计划启用
            if (planEntity.getStartUsing()) {
                //保养计划审批状态初始为待审批
                log.info("新增保养计划，启用，计划需要审批，状态为待审批,");
                planEntity.setMaintainPlanConditionId(PlanConditionEnum.TO_BE_APPROVED.getCode());
                id = maintainPlanRepository.save(planEntity).getId();
                maintainPlanDTO.setId(id);
                //启动计划审批流程
                startValidProcess(maintainPlanDTO);
            } else {
                //计划未启用
                log.info("新增保养计划，未启用，状态为未启用，不发起审批");
                planEntity.setMaintainPlanConditionId(PlanConditionEnum.NOT_ENABLED.getCode());
                id = maintainPlanRepository.save(planEntity).getId();
            }
        } else {
            //计划不需要审批
            //计划启用
            if (planEntity.getStartUsing()) {
                //保养计划审批状态为无需审批
                log.info("新增保养计划，启用，计划无需审批，状态为无需审批");
                planEntity.setMaintainPlanConditionId(PlanConditionEnum.NO_APPROVE.getCode());
            } else {
                //计划未启用
                log.info("新增保养计划，未启用，计划无需审批，状态为未启用");
                planEntity.setMaintainPlanConditionId(PlanConditionEnum.NOT_ENABLED.getCode());
            }
            id = maintainPlanRepository.save(planEntity).getId();
        }
        List<HvEamMaintainPlanEquipment> equipmentItem = new ArrayList<>();
        for (MaintainPlanItemDTO itemDTO : maintainPlanDTO.getPlanItems()) {
            List<Integer> itemIds = itemDTO.getMaintainItems()
                .stream()
                .map(SysBaseDTO::getId)
                .collect(Collectors.toList());
            for (Integer maintainItemId : itemIds) {
                HvEamMaintainPlanEquipment equipment = new HvEamMaintainPlanEquipment();
                equipment.setMaintainItemId(maintainItemId);
                equipment.setMaintainPlanId(id);
                equipment.setEquipmentId(itemDTO.getEquipmentId());
                equipmentItem.add(equipment);
            }
        }
        maintainPlanEquipmentRepository.saveAll(equipmentItem);
        List<HvEamMaintainPlanSparePart> allSpares = new ArrayList<>();
        List<HvEamMaintainPlanLub> allLubs = new ArrayList<>();
        for (MaintainPlanItemDTO planItem : maintainPlanDTO.getPlanItems()) {
            //备件
            if (planItem.getSparesInfos() != null) {
                List<HvEamMaintainPlanSparePart> spares = DtoMapper.convertList(planItem.getSparesInfos(), HvEamMaintainPlanSparePart.class);
                for (HvEamMaintainPlanSparePart spare : spares) {
                    spare.setMaintainPlanId(id);
                    spare.setEquipmentId(planItem.getEquipmentId());
                }
                allSpares.addAll(spares);
            }
            //油品
            if (planItem.getSparesInfos() != null) {
                List<HvEamMaintainPlanLub> lubs = DtoMapper.convertList(planItem.getLubDTOList(), HvEamMaintainPlanLub.class);
                for (HvEamMaintainPlanLub lub : lubs) {
                    lub.setEquipmentId(planItem.getEquipmentId());
                    lub.setMaintainPlanId(id);
                }
                allLubs.addAll(lubs);
            }
        }
        maintainPlanSparePartRepository.saveAll(allSpares);
        maintainPlanLubRepository.saveAll(allLubs);
        if (maintainPlanDTO.getFiles() != null) {
            List<HvEamMaintainPlanFile> files = DtoMapper.convertList(maintainPlanDTO.getFiles(), HvEamMaintainPlanFile.class);
            for (HvEamMaintainPlanFile file : files) {
                file.setMaintainPlanId(id);
            }
            maintainPlanFileRepository.saveAll(files);
        }
        return id;
    }

    private void validPlan(MaintainPlanDTO maintainPlanDTO) {
        if (maintainPlanDTO.getTransactorId() == null) {
            if (maintainPlanDTO.getCandidateGroups() == null || maintainPlanDTO.getCandidateGroups().size() == 0) {
                if (maintainPlanDTO.getCandidateUsers() == null || maintainPlanDTO.getCandidateUsers().size() == 0) {
                    throw new BaseKnownException(MaintainErrorExceptionEnum.NEED_CANDIDATE);
                }
            }
        }
        if (approve && maintainPlanDTO.getCheckerId() == null) {
            throw new BaseKnownException(MaintainErrorExceptionEnum.CHECKER_IS_NULL);
        }
        if (maintainPlanDTO.getPlanItems().size() <= 0) {
            throw new BaseKnownException(MaintainErrorExceptionEnum.ITEM_IS_NULL);
        }
        Assert.isTrue(maintainPlanDTO.getPlanItems().stream()
            .map(MaintainPlanItemDTO::getEquipmentId)
            .count() == maintainPlanDTO.getPlanItems().size(), "计划不能选择重复的设备信息");
        for (MaintainPlanItemDTO planItem : maintainPlanDTO.getPlanItems()) {
            if (planItem.getSparesInfos() != null) {
                Assert.isTrue(planItem.getSparesInfos()
                    .stream()
                    .map(SparesInfo::getSparePartId)
                    .count() == planItem.getSparesInfos().size(), "计划不能选择重复的备件");
            }
            if (planItem.getLubDTOList() != null) {
                Assert.isTrue(planItem.getLubDTOList()
                    .stream()
                    .map(LubDTO::getLubId)
                    .count() == planItem.getLubDTOList().size(), "计划不能选择重复的备件");
            }
            if (CollectionUtil.isEmpty(planItem.getMaintainItems())) {
                throw new BaseKnownException("计划中的设备保养项目不能为空");
            }
            Assert.isTrue(planItem.getMaintainItems().stream()
                    .map(MaintainItemDTO::getMaintainItemCode)
                    .distinct().count() == planItem.getMaintainItems().size(),
                "设备的保养项目不能重复,设备名:" + planItem.getEquipmentName());
        }
    }

    /**
     * 编辑保养计划（无保养内容）
     *
     * @param maintainPlanDTO 保养计划DTO
     * @return 编辑的计划id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateMaintainPlan(MaintainPlanDTO maintainPlanDTO) {
        Assert.notNull(maintainPlanDTO.getId(), "更新需要传递计划id");
        Assert.notNull(maintainPlanDTO.getPlanItems(), "保养项目不能为空");
        validPlan(maintainPlanDTO);
        //保养计划审批状态id
        Integer maintainConditionId = maintainPlanRepository.getOne(maintainPlanDTO.getId()).getMaintainPlanConditionId();
        //待审批，审批通过状态的计划不可以编辑
        if (maintainConditionId.equals(PlanConditionEnum.TO_BE_APPROVED.getCode()) || maintainConditionId.equals(PlanConditionEnum.PASSED.getCode())) {
            throw new BaseKnownException(MaintainErrorExceptionEnum.UPDATE_PLAN_ERROR);
        }
        //修改计划
        HvEamMaintainPlan hvEamMaintainPlan = DtoMapper.convert(maintainPlanDTO, HvEamMaintainPlan.class);
        hvEamMaintainPlan.setCandidateGroups(maintainPlanDTO.getCandidateGroupsString());
        hvEamMaintainPlan.setCandidateUsers(maintainPlanDTO.getCandidateUsersString());
        Integer id;
        if (approve) {
            //启用
            if (hvEamMaintainPlan.getStartUsing()) {
                hvEamMaintainPlan.setMaintainPlanConditionId(PlanConditionEnum.TO_BE_APPROVED.getCode());
                id = maintainPlanRepository.save(hvEamMaintainPlan).getId();
                maintainPlanDTO.setId(id);
                //启动计划审批流程
                log.info("编辑保养计划，需审批，准备启动审批流程");
                startValidProcess(maintainPlanDTO);
            } else {
                //未启用,不发起审批
                hvEamMaintainPlan.setMaintainPlanConditionId(PlanConditionEnum.NOT_ENABLED.getCode());
                id = maintainPlanRepository.save(hvEamMaintainPlan).getId();
            }
        } else {
            //计划不需要审批功能时，则不需要启动流程
            //启用
            if (hvEamMaintainPlan.getStartUsing()) {
                hvEamMaintainPlan.setMaintainPlanConditionId(PlanConditionEnum.NO_APPROVE.getCode());
            } else {
                //未启用
                hvEamMaintainPlan.setMaintainPlanConditionId(PlanConditionEnum.NOT_ENABLED.getCode());
            }
            id = maintainPlanRepository.save(hvEamMaintainPlan).getId();
        }
        //更新计划和保养项目关系
        maintainPlanEquipmentRepository.deleteAllByMaintainPlanId(maintainPlanDTO.getId());
        maintainPlanEquipmentRepository.flush();
        List<HvEamMaintainPlanEquipment> equipmentItem = new ArrayList<>();
        for (MaintainPlanItemDTO itemDTO : maintainPlanDTO.getPlanItems()) {
            List<Integer> itemIds = itemDTO.getMaintainItems()
                .stream()
                .map(t -> t.getId())
                .collect(Collectors.toList());
            for (Integer maintainItemId : itemIds) {
                HvEamMaintainPlanEquipment equipment = new HvEamMaintainPlanEquipment();
                equipment.setMaintainItemId(maintainItemId);
                equipment.setMaintainPlanId(id);
                equipment.setEquipmentId(itemDTO.getEquipmentId());
                equipmentItem.add(equipment);
            }
        }
        maintainPlanEquipmentRepository.saveAll(equipmentItem);
        //更新备件信息
        maintainPlanSparePartRepository.deleteAllByMaintainPlanId(id);
        maintainPlanSparePartRepository.flush();
        List<HvEamMaintainPlanSparePart> allSpares = new ArrayList<>();
        for (MaintainPlanItemDTO planItem : maintainPlanDTO.getPlanItems()) {
            if (planItem.getSparesInfos() != null && planItem.getSparesInfos().size() > 0) {
                List<HvEamMaintainPlanSparePart> spares = DtoMapper.convertList(planItem.getSparesInfos(), HvEamMaintainPlanSparePart.class);
                for (HvEamMaintainPlanSparePart spare : spares) {
                    spare.setEquipmentId(planItem.getEquipmentId());
                    spare.setMaintainPlanId(id);
                }
                allSpares.addAll(spares);
            }
        }
        //更新油品
        maintainPlanSparePartRepository.saveAll(allSpares);
        maintainPlanLubRepository.deleteAllByMaintainPlanId(id);
        maintainPlanLubRepository.flush();
        List<HvEamMaintainPlanLub> allLubs = new ArrayList<>();
        for (MaintainPlanItemDTO planItem : maintainPlanDTO.getPlanItems()) {
            if (planItem.getSparesInfos() != null) {
                List<HvEamMaintainPlanLub> lubs = DtoMapper.convertList(planItem.getLubDTOList(), HvEamMaintainPlanLub.class);
                for (HvEamMaintainPlanLub lub : lubs) {
                    lub.setEquipmentId(planItem.getEquipmentId());
                    lub.setMaintainPlanId(id);
                }
                allLubs.addAll(lubs);
            }
        }
        maintainPlanLubRepository.saveAll(allLubs);
        //更新文件信息
        maintainPlanFileRepository.deleteAllByMaintainPlanId(id);
        maintainPlanFileRepository.flush();
        if (maintainPlanDTO.getFiles() != null && maintainPlanDTO.getFiles().size() > 0) {
            List<HvEamMaintainPlanFile> files = DtoMapper.convertList(maintainPlanDTO.getFiles(), HvEamMaintainPlanFile.class);
            for (HvEamMaintainPlanFile file : files) {
                file.setMaintainPlanId(id);
            }
            maintainPlanFileRepository.saveAll(files);
        }
        return id;
    }

    /**
     * 查询保养计划
     *
     * @param maintainPlanQuery 查询条件
     * @return 保养计划分页dto
     */
    @Override
    public Page<MaintainPlanDTO> getAll(MaintainPlanQuery maintainPlanQuery) {
        Page<MaintainPlanDTO> result = PageHelperUtil.getPage(maintainPlanMapper::getPage, maintainPlanQuery);
        //根据timerIdList获取timer信息
        List<Integer> timerIdList = result.stream().map(MaintainPlanDTO::getTimerId).collect(Collectors.toList());
        ResultVO<List<TimerDTO>> timerInfo = timerClient.getByTimerIds(timerIdList);
        if (timerInfo.isSuccess()) {
            List<TimerDTO> timerData = timerInfo.getData();
            if (timerData != null) {
                for (MaintainPlanDTO planDTO : result) {
                    planDTO.setTimerDescription(timerData.stream()
                        .filter(timer -> timer.getId().equals(planDTO.getTimerId()))
                        .map(TimerDTO::getDescription)
                        .findFirst()
                        .orElse(null));
                }
            }
        }
        //审核人idList获取人员信息
        List<Integer> checkerIds = result.stream().map(MaintainPlanDTO::getCheckerId).collect(Collectors.toList());
        List<Integer> transactorIds = result.stream().map(MaintainPlanDTO::getTransactorId).collect(Collectors.toList());
        checkerIds.addAll(transactorIds);
        //dList获取人员信息
        ResultVO<List<UserInfoDto>> userInfoResult = authClient.getUserInfoByIds(checkerIds);
        for (MaintainPlanDTO one : result) {
            //获取审核人姓名
            one.setCheckerName(getUserName(userInfoResult, one.getCheckerId()));
            //获取执行人姓名
            one.setTransactorName(getUserName(userInfoResult, one.getTransactorId()));
            //获取保养计划审批状态名称
            Integer maintainPlanConditionId = one.getMaintainPlanConditionId();
            String name = PlanConditionEnum.valueOf(maintainPlanConditionId).getName();
            one.setMaintainPlanConditionName(name);
        }
        //设置保养项目数量
        List<HvEamMaintainPlanEquipment> items = maintainPlanEquipmentRepository.getAllByMaintainPlanIdIn(result.stream()
            .map(SysBaseDTO::getId).collect(Collectors.toList()));
        for (MaintainPlanDTO maintainPlanDTO : result) {
            maintainPlanDTO.setItemCount(items.stream()
                .filter(t -> t.getMaintainPlanId().equals(maintainPlanDTO.getId()))
                .count());
        }
        //设置计划里面的设备名称信息
        List<EquipmentDTO> list = serviceClient.getList(items.stream()
            .map(HvEamMaintainPlanEquipment::getEquipmentId)
            .collect(Collectors.toList()));
        for (MaintainPlanDTO dto : result) {
            List<HvEamMaintainPlanEquipment> planEquipments = items.stream()
                .filter(t -> t.getMaintainPlanId().equals(dto.getId()))
                .collect(Collectors.toList());
            List<String> collect = list.stream()
                .filter(t -> planEquipments.stream()
                    .anyMatch(e -> e.getEquipmentId().equals(t.getId())))
                .map(t -> t.getEquipmentName() + "(" + t.getEquipmentCode() + ")")
                .collect(Collectors.toList());
            dto.setEquipmentNames(String.join(",", collect));
        }
        return result;
    }

    /**
     * 查询计划的详细信息
     *
     * @param planId 计划id
     * @return 保养计划
     */
    @Override
    public MaintainPlanDTO getById(Integer planId) {
        Optional<HvEamMaintainPlan> maintainPlanResult = maintainPlanRepository.findById(planId);
        HvEamMaintainPlan maintainPlan;
        if (!maintainPlanResult.isPresent()) {
            return null;
        }
        maintainPlan = maintainPlanResult.get();
        MaintainPlanDTO maintainPlanDTO = DtoMapper.convert(maintainPlan, MaintainPlanDTO.class);
        if (maintainPlan.getCandidateUsers() != null) {
            maintainPlanDTO.setCandidateUsers(Arrays.asList(StringUtils.split(maintainPlan.getCandidateUsers(), "#")));
        }
        if (maintainPlan.getCandidateGroups() != null) {
            maintainPlanDTO.setCandidateGroups(Arrays.asList(StringUtils.split(maintainPlan.getCandidateGroups(), "#")));
        }
        //如果timer中不存在本计划绑定的timer则设置timerId为null
        Integer timerId = maintainPlanDTO.getTimerId();
        if (timerId != null) {
            ResultVO<TimerDTO> timerInfo = timerClient.getByTimerId(timerId);
            if (timerInfo.isSuccess()) {
                maintainPlanDTO.setTimerDescription(Optional.ofNullable(timerInfo.getData())
                    .map(TimerDTO::getDescription).orElse(null));
            }
        }
        List<Integer> userIdList = new ArrayList<>();
        userIdList.add(maintainPlanDTO.getCheckerId());
        userIdList.add(maintainPlanDTO.getTransactorId());
        ResultVO<List<UserInfoDto>> userInfo = authClient.getUserInfoByIds(userIdList);
        //获取审核人员姓名
        maintainPlanDTO.setCheckerName(getUserName(userInfo, maintainPlanDTO.getCheckerId()));
        //获取执行人信息
        maintainPlanDTO.setTransactorName(getUserName(userInfo, maintainPlanDTO.getTransactorId()));
        //获取保养计划审批状态名称
        maintainPlanDTO.setMaintainPlanConditionName(
            PlanConditionEnum.valueOf(maintainPlanDTO.getMaintainPlanConditionId()).getName());
        //设置保养项目信息
        List<HvEamMaintainPlanEquipment> items = maintainPlanEquipmentRepository.getAllByMaintainPlanId(planId);
        //查询所有保养项目信息
        List<MaintainItemDTO> maintainItems = maintainItemService.getList(items.stream()
            .map(HvEamMaintainPlanEquipment::getMaintainItemId).collect(Collectors.toList()));
        List<MaintainPlanItemDTO> maintainPlanItemDTOS = new ArrayList<>();
        //获取相关设备信息
        List<EquipmentDTO> equipmentList = serviceClient.getList(items.stream()
            .map(HvEamMaintainPlanEquipment::getEquipmentId)
            .collect(Collectors.toList()));
        for (HvEamMaintainPlanEquipment item : items) {
            Optional<MaintainPlanItemDTO> dto = maintainPlanItemDTOS.stream()
                .filter(t -> t.getEquipmentId().equals(item.getEquipmentId()))
                .findFirst();
            MaintainPlanItemDTO finalDto;
            if (!dto.isPresent()) {
                //设置设备信息
                Optional<EquipmentDTO> equipment = equipmentList
                    .stream()
                    .filter(t -> t.getId().equals(item.getEquipmentId()))
                    .findFirst();
                finalDto = new MaintainPlanItemDTO();
                finalDto.setEquipmentId(item.getEquipmentId());
                finalDto.setEquipmentName(equipment.map(EquipmentDTO::getEquipmentName)
                    .orElse(MaintainConsts.NA));
                finalDto.setEquipmentCode(equipment.map(EquipmentDTO::getEquipmentCode)
                    .orElse(MaintainConsts.NA));
                finalDto.setEquipmentTypeId(equipment.map(EquipmentDTO::getEquipmentTypeId)
                    .orElse(0));
                finalDto.setMaintainItems(new ArrayList<>());
                maintainPlanItemDTOS.add(finalDto);
            } else {
                finalDto = dto.get();
            }
            //添加到保养项目信息中。
            maintainItems.stream()
                .filter(t -> t.getId().equals(item.getMaintainItemId()))
                .findFirst()
                .ifPresent(t -> finalDto.getMaintainItems().add(t));
        }
        maintainPlanDTO.setPlanItems(maintainPlanItemDTOS);
        //设置计划的备件数据
        List<HvEamMaintainPlanSparePart> spares = maintainPlanSparePartRepository.findAllByMaintainPlanId(planId);
        //添加字段
        List<SpareDTO> spareDTOS = spareService.findAllById(spares.stream()
            .map(HvEamMaintainPlanSparePart::getSparePartId)
            .collect(Collectors.toList()));
        List<HvEamMaintainPlanLub> lubs = maintainPlanLubRepository.findAllByMaintainPlanId(planId);
        for (MaintainPlanItemDTO planItem : maintainPlanDTO.getPlanItems()) {
            planItem.setSparesInfos(spares.stream()
                .filter(t -> t.getEquipmentId().equals(planItem.getEquipmentId()))
                .map(t -> DtoMapper.convert(t, SparesInfo.class))
                .collect(Collectors.toList()));
            for (SparesInfo sparesInfo : planItem.getSparesInfos()) {
                spareDTOS.stream()
                    .filter(t->t.getId().equals(sparesInfo.getSparePartId()))
                    .findFirst()
                    .ifPresent(t->{
                        sparesInfo.setSparePartBrand(t.getBrand());
                        sparesInfo.setSparePartSpecifications(t.getSpecifications());
                        sparesInfo.setSparePartTypeName(t.getTypeName());
                    });
            }
            planItem.setLubDTOList(lubs.stream()
                .filter(t -> t.getEquipmentId().equals(planItem.getEquipmentId()))
                .map(t -> DtoMapper.convert(t, LubDTO.class))
                .collect(Collectors.toList()));
        }
        //设置文件信息
        List<HvEamMaintainPlanFile> fileInfo = maintainPlanFileRepository.getAllByMaintainPlanId(planId);
        maintainPlanDTO.setFiles(DtoMapper.convertList(fileInfo, FileInfo.class));
        return maintainPlanDTO;
    }

    /**
     * 删除单个
     *
     * @param id 保养计划id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMaintainPlanById(Integer id) {
        HvEamMaintainPlan plan = maintainPlanRepository.getOne(id);
        //计划状态
        Integer planConditionId = plan.getMaintainPlanConditionId();
        //如果是通过，或者待审批状态不可以删除
        if (planConditionId.equals(PlanConditionEnum.PASSED.getCode()) || planConditionId.equals(PlanConditionEnum.TO_BE_APPROVED.getCode())) {
            throw new BaseKnownException(MaintainErrorExceptionEnum.DELETE_PLAN_ERROR);
        }
        maintainPlanRepository.deleteById(id);
        //若项目中无保养内容功能，删除计划时将内容一并删除
        List<HvEamMaintainPlanEquipment> items = maintainPlanEquipmentRepository.getAllByMaintainPlanId(id);
        maintainPlanEquipmentRepository.deleteAll(items);
        //关联的备件信息也删除
        maintainPlanSparePartRepository.deleteAllByMaintainPlanId(id);
    }

    /**
     * 批量删除
     *
     * @param idList id集合
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMaintainPlanByIdList(List<Integer> idList) {
        for (Integer id : idList) {
            deleteMaintainPlanById(id);
        }
    }


    /**
     * 审批通过计划
     *
     * @param taskIds  任务ids
     * @param userInfo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startPlanById(List<String> taskIds, UserInfoDTO userInfo, String token) {
        //遍历任务id
        for (String taskId : taskIds) {
            //根据任务id获取任务信息
            ResultVO<TaskWithVariableDTO> taskInfo = activitiClient.getTaskByTaskId(taskId);
            if (taskInfo.isSuccess()) {
                TaskWithVariableDTO taskData = taskInfo.getData();
                Map<String, Object> variableMap = taskData.getVariableList();
                //在任务参数中获取对应的计划id
                Integer planId = (Integer) variableMap.get("planId");
                if (planId != null) {
                    maintainPlanRepository.findById(planId).ifPresent(plan -> {
                        Integer planConditionId = plan.getMaintainPlanConditionId();
                        //判断计划状态，是待审批计划
                        if (!planConditionId.equals(PlanConditionEnum.TO_BE_APPROVED.getCode())) {
                            throw new BaseKnownException(MaintainErrorExceptionEnum.START_PLAN_ERROR);
                        }
                        //修改对应计划的审批状态
                        plan.setMaintainPlanConditionId(PlanConditionEnum.PASSED.getCode());
                        plan.setRejectReason(null);
                        Integer id = maintainPlanRepository.save(plan).getId();
                        log.info("计划审批通过---计划id为：" + id);
                    });
                } else {
                    log.warn("计划为空，请检查");
                }
                TaskHandleDTO taskHandleDTO = new TaskHandleDTO();
                Map<String, Object> variables = taskHandleDTO.getVariables();
                variables.put("approvalResult", "审批通过");
                variables.put("approvalUserId", Optional.ofNullable(userInfo).map(UserInfoDTO::getId).orElse(0));
                variables.put("approvalUserName", Optional.ofNullable(userInfo).map(UserInfoDTO::getUserName).orElse(""));
                taskHandleDTO.setTaskId(taskId);
                taskHandleDTO.setForce(true);
                //完成任务
                ResultVO result = activitiClient.completeTask(taskHandleDTO, token);
                if (result.isSuccess()) {
                    log.info("计划审批通过，审批任务完成");
                } else {
                    log.info("任务结束失败:{}", result.getMessage());
                }
            }
        }
    }

    /**
     * 驳回计划
     *
     * @param maintainPlanRejectDTO 驳回DTO
     * @param userInfo              用户信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refusePlanById(MaintainPlanRejectDTO maintainPlanRejectDTO, UserInfoDTO userInfo, String token) {
        ResultVO<TaskWithVariableDTO> taskInfo = activitiClient.getTaskByTaskId(maintainPlanRejectDTO.getTaskId());
        if (taskInfo.isSuccess()) {
            TaskWithVariableDTO taskData = taskInfo.getData();
            Map<String, Object> variableMap = taskData.getVariableList();
            //在任务参数中获取对应的计划id
            Integer planId = (Integer) variableMap.get("planId");
            if (planId != null) {
                maintainPlanRepository.findById(planId)
                    .ifPresent(plan -> {
                        Integer planConditionId = plan.getMaintainPlanConditionId();
                        //判断计划状态，是待审批计划
                        if (!planConditionId.equals(PlanConditionEnum.TO_BE_APPROVED.getCode())) {
                            throw new BaseKnownException(MaintainErrorExceptionEnum.REFUSE_PLAN_ERROR);
                        }
                        plan.setMaintainPlanConditionId(PlanConditionEnum.REJECT.getCode());
                        plan.setRejectReason(maintainPlanRejectDTO.getRejectReason());
                        Integer id = maintainPlanRepository.save(plan).getId();
                        log.info("计划审批驳回---计划id为：" + id);
                    });
            } else {
                log.warn("计划id不存在，需要检查计划信息");
            }
            TaskHandleDTO taskHandleDTO = new TaskHandleDTO();
            Map<String, Object> variables = taskHandleDTO.getVariables();
            variables.put("approvalResult", "驳回");
            variables.put("rejectReason", maintainPlanRejectDTO.getRejectReason());
            variables.put("approvalUserId", Optional.ofNullable(userInfo).map(UserInfoDTO::getId).orElse(0));
            variables.put("approvalUserName", Optional.ofNullable(userInfo).map(UserInfoDTO::getUserName).orElse(""));
            taskHandleDTO.setTaskId(maintainPlanRejectDTO.getTaskId());
            taskHandleDTO.setForce(true);
            //完成任务
            ResultVO result = activitiClient.completeTask(taskHandleDTO, token);
            if (result.isSuccess()) {
                log.info("计划审批通过，审批任务完成");
            } else {
                log.info("任务结束失败:{}", result.getMessage());
            }
        }

    }


    /**
     * 强制停用计划
     *
     * @param ids id列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void forcedStopMaintainPlan(List<Integer> ids) {
        for (Integer id : ids) {
            HvEamMaintainPlan plan = maintainPlanRepository.getOne(id);
            //审批通过，无需审批，可以强制停用
            if (plan.getMaintainPlanConditionId().equals(PlanConditionEnum.PASSED.getCode())
                || plan.getMaintainPlanConditionId().equals(PlanConditionEnum.NO_APPROVE.getCode())) {
                plan.setStartUsing(false);
                plan.setMaintainPlanConditionId(PlanConditionEnum.FORCED_STOP.getCode());
                maintainPlanRepository.save(plan);
            } else {
                throw new BaseKnownException(MaintainErrorExceptionEnum.STOP_ERROR);
            }

        }
    }

    /**
     * 根据timerId查看绑定次数
     *
     * @param timerId timerId
     * @return 次数
     */
    @Override
    public Integer checkBindingByTimerId(Integer timerId) {
        List<HvEamMaintainPlan> all = maintainPlanRepository.findAllByTimerId(timerId);
        return all.size();
    }

    /**
     * 通过计划id手动触发计划
     *
     * @param planId 任务id
     */
    @Override
    public void triggerPlanById(Integer planId) {
        MaintainPlanDTO plan = getById(planId);
        if (plan != null) {
            triggerPlan(plan);
        }
    }

    /**
     * {@inheritDoc}
     */
    private void triggerPlan(MaintainPlanDTO plan) {
        //根据每个计划下的设备id集合生成多条任务
        for (MaintainPlanItemDTO itemsDTO : plan.getPlanItems()) {
            List<MaintainItemDTO> maintainItems = itemsDTO.getMaintainItems();
            //项目关联的 文件 备件 油品
            for (MaintainItemDTO maintainItemDTO : maintainItems) {
                //关联的文件
                List<HvEamMaintainItemFile> itemFileList = maintainItemFileRepository
                    .findAllByMaintainItemId(maintainItemDTO.getId());
                if (itemFileList.size() > 0) {
                    List<Integer> fileIds = itemFileList
                        .stream()
                        .map(HvEamMaintainItemFile::getFileId)
                        .collect(Collectors.toList());
                    maintainItemDTO.setFileIds(fileIds);
                }
            }
            ResultVO<ProcessInstanceDTO> result = getProcessInstanceStartDTO(plan, itemsDTO);
            if (!result.isSuccess()) {
                log.error("保养任务创建异常,没能创建成功.异常信息:{}", result.getMessage());
                throw new BaseKnownException("保养任务创建异常");
            }
            Assert.notNull(result.getData(), "保养任务创建失败，接口调用异常，没有返回流程示例id");
            log.info("保养任务创建成功,流程实例ID:{}", result.getData().getProcessInstanceId());
            String processInstanceId = result.getData().getProcessInstanceId();
            //自动申请备件
            if (plan.getAutoSpare()) {
                log.info("计划开启自动申请备件功能，开始申请备件流程");
                autoApplySpare(processInstanceId, maintainItems);
            }
            //自动申请油品
            if (plan.getAutoLub()) {
                log.info("计划开启自动申请油品功能，开始申请油品流程");
                autoApplyLub(processInstanceId, maintainItems);
            }
            //生成备件油品清单
            createSpareAndLubListing(processInstanceId, maintainItems);
        }
    }

    /**
     * 开启保养流程
     *
     * @param plan     计划
     * @param itemsDTO 项目
     * @return 流程信息开启
     */
    private ResultVO<ProcessInstanceDTO> getProcessInstanceStartDTO(MaintainPlanDTO plan, MaintainPlanItemDTO itemsDTO) {
        ProcessInstanceStartDTO processInstanceStartDTO = new ProcessInstanceStartDTO();
        Map<String, Object> map = Maps.newHashMap();
        MaintainContentDTO maintainContent = new MaintainContentDTO();
        maintainContent.setMaintainItemDTOList(itemsDTO.getMaintainItems());
        String businessKey = plan.getId() == null ? "" : plan.getId().toString();
        processInstanceStartDTO.setBusinessKey(businessKey);
        processInstanceStartDTO.setProcessDefinitionKey("equipment-maintain");
        map.put("spares", plan.getAllItemSpareInfo(itemsDTO.getEquipmentId()));
        map.put("lubs", plan.getAllItemLubs(itemsDTO.getEquipmentId()));
        map.put("taskNum", plan.getMaintainPlanNum());
        //设置任务的各种参数
        map.put("checkerId", plan.getCheckerId());
        map.put("checkerName", plan.getCheckerName());
        map.put("executor_id", plan.getTransactorId());
        map.put("equipmentName", itemsDTO.getEquipmentName());
        map.put("equipmentId", itemsDTO.getEquipmentId());
        map.put("equipmentCode", itemsDTO.getEquipmentCode());
        map.put("taskName", plan.getMaintainPlanName());
        map.put("manHour", maintainContent.getManHourString());
        map.put("maintainContentDTO", maintainContent);
        map.put("remark", plan.getRemark());
        map.put("shutDown", maintainContent.getShutDown());
        //候选人信息
        map.put("candidateUsers", plan.getCandidateUsers());
        map.put("candidateGroups", plan.getCandidateGroups());
        //任务完成时间为：生成任务时间加上任务期限
        Calendar ca = Calendar.getInstance();
        Integer num = plan.getDeadline();
        ca.add(Calendar.DATE, num);
        Date d = ca.getTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String taskEndTime = format.format(d);
        map.put("taskEndTime", taskEndTime);
        processInstanceStartDTO.setVariables(map);
        return activitiClient.startProcessInstance(processInstanceStartDTO);
    }

    @Override
    public void triggerPlanByTimerId(Integer id) {
        log.info("根据timerId获取已经启用的并且是审批通过或者无需审批的保养计划，准备生成任务");
        List<Integer> conditionIds = new ArrayList<>();
        conditionIds.add(PlanConditionEnum.PASSED.getCode());
        conditionIds.add(PlanConditionEnum.NO_APPROVE.getCode());
        List<HvEamMaintainPlan> all =
            maintainPlanRepository.findAllByTimerIdAndStartUsingIsTrueAndMaintainPlanConditionIdIn(id, conditionIds);
        all.forEach(t -> triggerPlanById(t.getId()));
    }

    /**
     * 创建临时任务
     *
     * @param dto 保养计划信息
     */
    @Override
    public void createTemporaryTask(MaintainPlanDTO dto) {
        Assert.isTrue(dto.getPlanItems() != null && dto.getPlanItems().size() > 0, "保养信息不能为空");
        for (MaintainPlanItemDTO planItem : dto.getPlanItems()) {
            Assert.isTrue(planItem.getMaintainItems() != null && planItem.getMaintainItems().size() > 0, "保养项目不能为空");
        }
        //设置dto里面的item详细信息（前端只传递了id）
        for (MaintainPlanItemDTO planItem : dto.getPlanItems()) {
            planItem.setMaintainItems(
                maintainItemService.getList(planItem.getMaintainItems()
                    .stream()
                    .map(SysBaseDTO::getId)
                    .collect(Collectors.toList())));
        }
        triggerPlan(dto);
    }


    /**
     * 获取审核人员信息
     *
     * @param userInfo 审核人信息
     * @param userId   审核人信息DATA
     */
    private String getUserName(ResultVO<List<UserInfoDto>> userInfo, Integer userId) {
        if (!userInfo.isSuccess()) {
            return MaintainConsts.NA;
        }
        List<UserInfoDto> userInfos = userInfo.getData();
        if (userInfos == null) {
            return MaintainConsts.NA;
        }
        return userInfos.stream()
            .filter(t -> t.getId().equals(userId))
            .findFirst()
            .map(UserInfoDto::getUserName)
            .orElse(MaintainConsts.NA);
    }


    /**
     * 启动计划审批流程
     *
     * @param maintainPlanDTO 保养计划DTO
     */
    private void startValidProcess(MaintainPlanDTO maintainPlanDTO) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("manHour", maintainPlanDTO.getManHourString());
        map.put("checker_id", maintainPlanDTO.getCheckerId());
        map.put("planName", maintainPlanDTO.getMaintainPlanName());
        map.put("planId", maintainPlanDTO.getId());
        map.put("remark", maintainPlanDTO.getRemark());
        //计划详细信息
        //查询计划相关的设备保养信息，并进行添加
        List<ValidProcessItem> items = new ArrayList<>();
        for (MaintainPlanItemDTO planItem : maintainPlanDTO.getPlanItems()) {
            ValidProcessItem item = new ValidProcessItem();
            items.add(item);
            item.setEquipmentId(planItem.getEquipmentId());
            Optional<EquipmentDTO> equipmentDTO = serviceClient.get(planItem.getEquipmentId());
            item.setEquipmentTypeName(equipmentDTO.map(EquipmentDTO::getEquipmentTypeCode).orElse(""));
            item.setEquipmentCode(equipmentDTO.map(EquipmentDTO::getEquipmentCode).orElse(""));
            item.setEquipmentName(equipmentDTO.map(EquipmentDTO::getEquipmentName).orElse(""));
            item.setItems(maintainItemService.getList(planItem.getMaintainItems()
                .stream().map(SysBaseDTO::getId).collect(Collectors.toList())));
        }
        map.put("planItems", items);
        //任务结束时间
        Calendar ca = Calendar.getInstance();
        Integer num = maintainPlanDTO.getDeadline();
        ca.add(Calendar.DATE, num);
        Date d = ca.getTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String taskEndTime = format.format(d);
        map.put("taskEndTime", taskEndTime);
        //任务描述增加timer描述
        Integer timerId = maintainPlanDTO.getTimerId();
        if (timerId != null) {
            ResultVO<TimerDTO> timerInfo = timerClient.getByTimerId(timerId);
            if (timerInfo.isSuccess()) {
                TimerDTO timerDTO = timerInfo.getData();
                map.put("timerDesc", timerDTO.getDescription());
            }
        } else {
            map.put("timerDesc", "未配置生成任务周期");
        }
        ProcessInstanceStartDTO processInstanceStartDTO = new ProcessInstanceStartDTO();
        processInstanceStartDTO.setProcessDefinitionKey("maintain-approve");
        String businessKey = "maintain-approve-" + maintainPlanDTO.getMaintainPlanNum();
        processInstanceStartDTO.setBusinessKey(businessKey);
        processInstanceStartDTO.setVariables(map);
        //启动审批流程
        ResultVO<ProcessInstanceDTO> result = activitiClient.startProcessInstance(processInstanceStartDTO);
        if (result.isSuccess()) {
            String processInstanceId = Optional.ofNullable(result.getData()).map(t -> t.getProcessInstanceId()).orElse("0");
            log.info("保养审批流程启动,流程id为--" + processInstanceId);
        } else {
            log.warn("启动保养审批流程失败,异常信息:{}", result.getMessage());
        }
    }


    /**
     * 自动申请油品
     *
     * @param processInstanceId processInstanceId
     * @param itemDTOS          油品List
     */
    private void autoApplyLub(String processInstanceId, List<MaintainItemDTO> itemDTOS) {
        RootLubDTO rootLubDTO = new RootLubDTO();
        rootLubDTO.setBusinessKey(processInstanceId);
        //设置备件自动申请的自定义参数
        VariablesLubDTO variablesLubDTO = new VariablesLubDTO();
        variablesLubDTO.setTitle("保养油品出库自动申请");
        variablesLubDTO.setInOut("2");
        variablesLubDTO.setApplyUserName("系统自动");
        variablesLubDTO.setIsPass("1");
        variablesLubDTO.setSource("保养");
        //要申请的油品集合
        List<ItemsLubDTO> items = new ArrayList<>();
        for (MaintainItemDTO itemDTO : itemDTOS) {
            List<LubDTO> lubDTOList = itemDTO.getItemLubDTOList();
            if (lubDTOList.size() > 0) {
                for (LubDTO lubDTO : lubDTOList) {
                    ItemsLubDTO itemsLubDTO = new ItemsLubDTO();
                    itemsLubDTO.setLubId(lubDTO.getLubId());
                    itemsLubDTO.setLubName(lubDTO.getLubName());
                    itemsLubDTO.setNumber(lubDTO.getLubNum());
                    items.add(itemsLubDTO);
                }
            }
        }
        variablesLubDTO.setItems(items);
        rootLubDTO.setVariables(variablesLubDTO);
        RootLubDTO rootLubDTOResultVO = applyService.applyLub(rootLubDTO);
        if (rootLubDTOResultVO != null) {
            log.info("油品自动申请流程发送成功");
        }
    }

    /**
     * 自动申请备件
     *
     * @param processInstanceId processInstanceId
     * @param itemDTOS          备件LIST
     */
    private void autoApplySpare(String processInstanceId, List<MaintainItemDTO> itemDTOS) {
        RootSpareDTO rootSpareDTO = new RootSpareDTO();
        rootSpareDTO.setBusinessKey(processInstanceId);
        //设置备件自动申请的自定义参数
        VariablesSpareDTO variablesSpareDTO = new VariablesSpareDTO();
        variablesSpareDTO.setTitle("保养备件出库自动申请");
        variablesSpareDTO.setInOut("2");
        variablesSpareDTO.setApplyUserName("系统自动");
        variablesSpareDTO.setIsPass("1");
        variablesSpareDTO.setSource("保养");
        //备件LIST
        List<ItemsSpareDTO> items = new ArrayList<>();
        for (MaintainItemDTO itemDTO : itemDTOS) {
            List<SparesInfo> itemSparePartDTOList = itemDTO.getItemSparePartDTOList();
            if (itemSparePartDTOList.size() > 0) {
                for (SparesInfo itemSparePartDTO : itemSparePartDTOList) {
                    ItemsSpareDTO itemsSpareDTO = new ItemsSpareDTO();
                    itemsSpareDTO.setSpareId(itemSparePartDTO.getSparePartId());
                    itemsSpareDTO.setSpareName(itemSparePartDTO.getSparePartName());
                    itemsSpareDTO.setNumber(itemSparePartDTO.getSparePartNum());
                    items.add(itemsSpareDTO);
                }
            }
        }
        variablesSpareDTO.setItems(items);
        rootSpareDTO.setVariables(variablesSpareDTO);
        RootSpareDTO rootSpareDTOResultVO = applyService.applySpare(rootSpareDTO);
        if (rootSpareDTOResultVO != null) {
            log.info("备件自动申请流程发送成功");
        }
    }


    /**
     * 生成备件油品清单
     *
     * @param processInstanceId 流程示例
     * @param maintainItemDTOS  保养项目DTO
     */
    private void createSpareAndLubListing(String processInstanceId, List<MaintainItemDTO> maintainItemDTOS) {
        List<UseListingSpareDTO> useListingDTOList = new ArrayList<>();
        for (MaintainItemDTO itemDTO : maintainItemDTOS) {
            if (itemDTO.getStartUsing()) {
                //备件清单信息
                List<SparesInfo> itemSparePartDTOList = itemDTO.getItemSparePartDTOList();
                if (itemSparePartDTOList.size() > 0) {
                    for (SparesInfo spare : itemSparePartDTOList) {
                        UseListingSpareDTO useListingDTO = new UseListingSpareDTO();
                        useListingDTO.setType(1);
                        useListingDTO.setSpareId(spare.getSparePartId());
                        useListingDTO.setProcessInstanceId(processInstanceId);
                        useListingDTO.setNumber(spare.getSparePartNum());
                        useListingDTOList.add(useListingDTO);
                    }
                }
                //油品清单信息
                List<LubDTO> lubDTOList = itemDTO.getItemLubDTOList();
                if (lubDTOList.size() > 0) {
                    for (LubDTO lub : lubDTOList) {
                        UseListingSpareDTO useListingDTO = new UseListingSpareDTO();
                        useListingDTO.setType(2);
                        useListingDTO.setSpareId(lub.getLubId());
                        useListingDTO.setProcessInstanceId(processInstanceId);
                        useListingDTO.setNumber(lub.getLubNum());
                        useListingDTOList.add(useListingDTO);
                    }
                }
            }
        }
        List<Integer> list = useListingService.createList(useListingDTOList);
        if (CollectionUtil.isNotEmpty(list)) {
            log.info("保养备件油品清单创建完成");
        }
    }


}