package com.hvisions.eam.service.inspect.impl;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.client.inspect.ActivitiHistoryClient;
import com.hvisions.eam.dto.inspect.process.Data;
import com.hvisions.eam.dto.inspect.process.InspectPlanContentDTO;
import com.hvisions.eam.dto.inspect.process.ItemDTO;
import com.hvisions.eam.entity.inspect.HvEamInspectProcessContentItem;
import com.hvisions.eam.entity.inspect.HvEamInspectProcessData;
import com.hvisions.eam.entity.inspect.HvEamInspectProcessPlanContent;
import com.hvisions.eam.repository.inspect.InspectDataRepository;
import com.hvisions.eam.repository.inspect.InspectionProcessContentItemRepository;
import com.hvisions.eam.repository.inspect.InspectionProcessContentRepository;
import com.hvisions.eam.service.inspect.InspectDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.support.ListenerExecutionFailedException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;

/**
 * <p>Title: InspectDataServiceImpl</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/1/12</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class InspectDataServiceImpl implements InspectDataService {
    private final ActivitiHistoryClient client;
    private final InspectDataRepository dataRepository;
    private final InspectionProcessContentRepository contentRepository;
    private final InspectionProcessContentItemRepository itemRepository;


    @Autowired
    public InspectDataServiceImpl(ActivitiHistoryClient client,
                                  InspectDataRepository dataRepository,
                                  InspectionProcessContentRepository contentRepository,
                                  InspectionProcessContentItemRepository itemRepository) {
        this.client = client;
        this.dataRepository = dataRepository;
        this.contentRepository = contentRepository;
        this.itemRepository = itemRepository;
    }


    /**
     * 持久化数据
     *
     * @param processInstanceId 流程实例
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(String processInstanceId) {
        ResultVO<Data> processInstance = client.getProcessInstance(processInstanceId);
        if (!processInstance.isSuccess()) {
            log.info("获取设备点巡检流程实例信息错误,流程实例Id:{}", processInstanceId);
            return;
        }
        Data data = processInstance.getData();
        String s = new SimpleDateFormat("yyyy-MM").format(data.getStartTime());
        convertProcessInstanceData(data, s, processInstanceId);
    }

    /**
     * 处理异常
     *
     * @param message 异常信息
     * @param e       异常
     */
    @Override
    public void handleError(Message message, ListenerExecutionFailedException e) {
        log.info("设备点巡检信息获取异常,消息队列数据处理异常:{},异常信息为：{}", message.toString(), e.getMessage());
    }

    @Caching(evict = {@CacheEvict(value = "countForQualifiedItem", key = "#dataString"),
            @CacheEvict(value = "countForUnqualifiedItem", key = "#dataString"),
            @CacheEvict(value = "countForAllItem", key = "#dataString")})
    public void convertProcessInstanceData(Data data, String dataString, String processInstanceId) {
        HvEamInspectProcessData dataEntity = DtoMapper.convert(data, HvEamInspectProcessData.class, "id");
        if (data.getProcessInstanceVariables() == null) {
            log.info("获取设备点巡检流程实例信息错误,信息为空,流程实例Id:{}", processInstanceId);
            return;
        }
        BeanUtils.copyProperties(data.getProcessInstanceVariables(), dataEntity);
        dataRepository.save(dataEntity);
        for (InspectPlanContentDTO inspectPlanContentDTO : data.getProcessInstanceVariables().getInspectPlanContentDTOList()) {
            HvEamInspectProcessPlanContent contentEntity = DtoMapper.convert(inspectPlanContentDTO, HvEamInspectProcessPlanContent.class, "id");
            contentEntity.setProcessInstanceId(processInstanceId);
            contentEntity.setContentId(inspectPlanContentDTO.getId());
            contentRepository.save(contentEntity);
            if (inspectPlanContentDTO.getItemDTOList() == null) {
                continue;
            }
            for (ItemDTO itemDTO : inspectPlanContentDTO.getItemDTOList()) {
                HvEamInspectProcessContentItem itemEntity = DtoMapper.convert(itemDTO, HvEamInspectProcessContentItem.class, "id");
                itemEntity.setItemId(itemDTO.getId());
                itemEntity.setContentId(contentEntity.getId());
                itemRepository.save(itemEntity);
            }
        }
        log.info("获取设备点巡检流程实例信息成功，流程实例id：{}", processInstanceId);
    }
}









