package com.hvisions.eam.service.maintain.imp;

import com.google.common.collect.Maps;
import com.hvisions.activiti.client.ActivitiClient;
import com.hvisions.activiti.dto.history.HistoricProcessQuery;
import com.hvisions.activiti.dto.history.HistoryProcessInstanceDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.client.maintain.NewTaskClient;
import com.hvisions.eam.dto.maintain.*;
import com.hvisions.eam.dto.publicstore.ActualUseDTO;
import com.hvisions.eam.enums.MaintainErrorExceptionEnum;
import com.hvisions.eam.service.maintain.MaintainStatisticalService;
import com.hvisions.eam.service.publicstore.ActualUseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title: MaintainStatisticalServiceImpl</p >
 * <p>Description: 保养统计实现类</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/4</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Slf4j
@Service
public class MaintainStatisticalServiceImpl implements MaintainStatisticalService {
    private final ActivitiClient activitiClient;
    private final NewTaskClient newTaskClient;
    /**
     * 实际使用jpa
     */
    private final ActualUseService actualUseService;

    @Autowired
    public MaintainStatisticalServiceImpl(ActivitiClient activitiClient,
                                          NewTaskClient newTaskClient, ActualUseService actualUseService) {
        this.activitiClient = activitiClient;
        this.newTaskClient = newTaskClient;
        this.actualUseService = actualUseService;
    }

    /**
     * 获取保养润滑看板 获取当前正在进行中的任务
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 看板
     */
    @Override
    public MaintainDrawingBoardDTO getMaintainDrawingBoardDTO(Date startTime, Date endTime) {
        Integer lubNum = 0, MaiNum = 0;
        MaintainDrawingBoardDTO boardDTO = new MaintainDrawingBoardDTO();
        //时间验证
        if (endTime.compareTo(startTime) < 0) {
            throw new BaseKnownException(MaintainErrorExceptionEnum.START_END_TIME_EXCEPTION);
        }

        ResultVO<Map<String, Object>> vo = newTaskClient.getTaskCount("");
        if (vo.isSuccess()) {
            Map<String, Object> data = vo.getData();
            try {
                MaiNum = Integer.parseInt(data.get("设备保养") + "");
                lubNum = Integer.parseInt(data.get("设备润滑") + "");
            } catch (Exception e) {
                log.info("Integer.parseInt error 保养：" + data.get("设备保养") + "  润滑：" + data.get("设备润滑"));
            }
        }

        boardDTO.setMaintainNum(MaiNum);
        boardDTO.setLubricateNum(lubNum);
        return boardDTO;
    }

    /**
     * 查询正在运行中的数量
     *
     * @param definitionKey definitionKey
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @return 数量
     */
    private Integer getActivitiRunTimeNum(String definitionKey, Date startTime, Date endTime) {
        Date date = new Date();
        SimpleDateFormat after = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        SimpleDateFormat before = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
        String a = after.format(startTime);
        String b = before.format(endTime);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date finishedAfter = sdf.parse(a);
            Date finishedBefore = sdf.parse(b);
            HistoricProcessQuery historicProcessQuery = new HistoricProcessQuery();
            historicProcessQuery.setProcessDefinitionKey(definitionKey);
            historicProcessQuery.setFinishedAfter(finishedAfter);
            historicProcessQuery.setFinishedBefore(finishedBefore);
            ResultVO<List<HistoryProcessInstanceDTO>> historicProcessInstanceInfo = activitiClient.getHistoricProcessInstanceList(historicProcessQuery);
            if (historicProcessInstanceInfo.isSuccess()) {
                List<HistoryProcessInstanceDTO> historicProcessInstanceInfoData = historicProcessInstanceInfo.getData();
                return historicProcessInstanceInfoData.size();
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 保养统计
     *
     * @param maintainStatisticalQueryDTO 查询条件
     * @return 统计
     */
    @Override
    public List<MaintainStatisticalDTO> getMaintainStatistical(MaintainStatisticalQueryDTO maintainStatisticalQueryDTO) {
        HistoricProcessQuery historicProcessQuery = new HistoricProcessQuery();
        historicProcessQuery.setProcessDefinitionKey("equipment-maintain");
        //设备名称
        String equipmentName = maintainStatisticalQueryDTO.getEquipmentName();
        //完成时间之前
        Date finishedAfter = maintainStatisticalQueryDTO.getFinishedAfter();
        //完成时间之后
        Date finishedBefore = maintainStatisticalQueryDTO.getFinishedBefore();
        Calendar ca = Calendar.getInstance();
        if (finishedAfter == null) {
            finishedAfter = ca.getTime();
        }
        if (finishedBefore == null) {
            ca.add(Calendar.DATE, -30);
            finishedBefore = ca.getTime();
        }
        Map<String, String> map = Maps.newHashMap();
        map.put("equipmentName", "%" + equipmentName + "%");
        historicProcessQuery.setVariableValueLikeIgnoreCase(map);
        historicProcessQuery.setFinished(true);
        historicProcessQuery.setFinishedAfter(finishedBefore);
        historicProcessQuery.setFinishedBefore(finishedAfter);
        ResultVO<List<HistoryProcessInstanceDTO>> historicProcessInstanceList = activitiClient.getHistoricProcessInstanceList(historicProcessQuery);
        if (historicProcessInstanceList.isSuccess()) {
            List<MaintainStatisticalDTO> maintainStatisticalDTOList = new ArrayList<>();
            List<HistoryProcessInstanceDTO> historicProcessInstanceListData = historicProcessInstanceList.getData();
            if (historicProcessInstanceListData.isEmpty()) {
                return null;
            } else {
                //根据设备编码过滤历史数据
                ArrayList<String> equipmentCodes = new ArrayList<>();
                List<HistoryProcessInstanceDTO> onlyHistory = new ArrayList<>();
                for (HistoryProcessInstanceDTO historicProcessInstanceListDatum : historicProcessInstanceListData) {
                    Map<String, Object> processInstanceVariables = historicProcessInstanceListDatum.getProcessInstanceVariables();
                    //设备编码
                    String equipmentCode = (String) processInstanceVariables.get("equipmentCode");
                    if (!equipmentCodes.contains(equipmentCode)) {
                        equipmentCodes.add(equipmentCode);
                        onlyHistory.add(historicProcessInstanceListDatum);
                    }
                }
                //循环遍历过滤后的数据
                for (HistoryProcessInstanceDTO historicProcessInstanceListDatum : onlyHistory) {
                    Map<String, Object> processInstanceVariables = historicProcessInstanceListDatum.getProcessInstanceVariables();
                    MaintainStatisticalDTO maintainStatisticalDTO = new MaintainStatisticalDTO();
                    //设备名称
                    String equipmentNameA = (String) processInstanceVariables.get("equipmentName");
                    maintainStatisticalDTO.setEquipmentName(equipmentNameA);
                    //设备编码
                    String equipmentCode = (String) processInstanceVariables.get("equipmentCode");
                    maintainStatisticalDTO.setEquipmentCode(equipmentCode);
                    //设备的总保养次数
                    Integer maintainCount = getMaintainCount(historicProcessQuery, equipmentCode);
                    maintainStatisticalDTO.setMaintainCount(maintainCount);
                    //计划备件总数量init
                    BigDecimal sparePlanCount = new BigDecimal("0");
                    //计划油品总数量init
                    BigDecimal lubPlanCount = new BigDecimal("0");
                    //得到设备相关的所有保养项目
                    Map<String, Object> newMap = Maps.newHashMap();
                    newMap.put("equipmentCode", equipmentCode);
                    historicProcessQuery.setVariableValueLikeIgnoreCase(null);
                    historicProcessQuery.setVariableValueEquals(newMap);
                    ResultVO<List<HistoryProcessInstanceDTO>> historicProcessInstanceList1 = activitiClient.getHistoricProcessInstanceList(historicProcessQuery);
                    if (historicProcessInstanceList1.isSuccess()) {
                        List<HistoryProcessInstanceDTO> historicProcessInstanceList1Data = historicProcessInstanceList1.getData();
                        //关联的所有流程实例id
                        List<String> processInstanceIds = historicProcessInstanceList1Data.stream().map(HistoryProcessInstanceDTO::getProcessInstanceId).collect(Collectors.toList());
                        //根据processInstanceIds获取实际使用备件数量以及价格
                        List<ActualUseDTO> data = actualUseService.getActualUseByProcessInstanceIds(processInstanceIds);
                        //备件实际总数
                        BigDecimal sparePartCount = new BigDecimal("0");
                        //备件实际总价
                        BigDecimal sparePrice = new BigDecimal("0");
                        //油品实际总数
                        BigDecimal lubCount = new BigDecimal("0");
                        //油品实际总价
                        BigDecimal lubPrice = new BigDecimal("0");
                        for (ActualUseDTO datum : data) {
                            if (datum.getType().equals(1)) {
                                sparePartCount = sparePartCount.add(datum.getNumber());
                                if (datum.getUnitPrice() == null) {
                                    sparePrice = new BigDecimal("0");
                                } else {
                                    sparePrice = sparePrice.add(datum.getNumber().multiply(datum.getUnitPrice()));
                                }
                            }
                            if (datum.getType().equals(2)) {
                                lubCount = lubCount.add(datum.getNumber());
                                if (datum.getUnitPrice() == null) {
                                    lubPrice = new BigDecimal("0");
                                } else {
                                    lubPrice = lubPrice.add(datum.getNumber().multiply(datum.getUnitPrice()));
                                }
                            }

                        }
                        //实际备件总数
                        maintainStatisticalDTO.setSparePartCount(sparePartCount);
                        //实际备件总价
                        maintainStatisticalDTO.setSparePrice(sparePrice);
                        //实际油品总数
                        maintainStatisticalDTO.setLubCount(lubCount);
                        //实际油品总价
                        maintainStatisticalDTO.setLubPrice(lubPrice);
                        //子项
                        List<MaintainStatisticalItemDTO> statisticalItemDTOList = new ArrayList<>();
                        for (HistoryProcessInstanceDTO historicProcessInstanceList1Datum : historicProcessInstanceList1Data) {
                            //流程开始时间
                            Date startTime = historicProcessInstanceList1Datum.getStartTime();
                            //流程结束时间
                            Date endTime = historicProcessInstanceList1Datum.getEndTime();
                            Map<String, Object> processInstanceVariables1 = historicProcessInstanceList1Datum.getProcessInstanceVariables();
                            Map mapContent = (Map) processInstanceVariables1.get("maintainContentDTO");
                            List maintainItemDTOList = (List) mapContent.get("maintainItemDTOList");
                            for (Object itemDTO : maintainItemDTOList) {
                                Map mapItemDTO = (Map) itemDTO;
                                //关联的备件信息
                                List itemSparePartDTOList = (List) mapItemDTO.get("itemSparePartDTOList");
                                for (Object spare : itemSparePartDTOList) {
                                    Map mapSpare = (Map) spare;
                                    BigDecimal sparePartNum = new BigDecimal(mapSpare.get("sparePartNum").toString());
                                    sparePlanCount = sparePlanCount.add(sparePartNum);
                                }
                                //关联的油品信息
                                List itemLubDTOList = (List) mapItemDTO.get("itemLubDTOList");
                                for (Object lub : itemLubDTOList) {
                                    Map mapLub = (Map) lub;
                                    BigDecimal lubNum = new BigDecimal(mapLub.get("lubNum").toString());
                                    lubPlanCount = lubPlanCount.add(lubNum);
                                }
                                //保养部位
                                String maintainPosition = (String) mapItemDTO.get("maintainPosition");
                                //保养工作
                                String maintainWork = (String) mapItemDTO.get("maintainWork");
                                //工时
                                Float manHour = Float.parseFloat(mapItemDTO.get("manHour").toString());
                                MaintainStatisticalItemDTO maintainStatisticalItemDTO = new MaintainStatisticalItemDTO();
                                maintainStatisticalItemDTO.setMaintainPosition(maintainPosition);
                                maintainStatisticalItemDTO.setMaintainWork(maintainWork);
                                maintainStatisticalItemDTO.setManHour(manHour);
                                maintainStatisticalItemDTO.setBeginTime(startTime);
                                maintainStatisticalItemDTO.setEndTime(endTime);
                                statisticalItemDTOList.add(maintainStatisticalItemDTO);
                            }
                            //计划备件总数量
                            maintainStatisticalDTO.setSparePartPlanCount(sparePlanCount);
                            //计划油品总数量
                            maintainStatisticalDTO.setLubPlanCount(lubPlanCount);
                            //保养统计子项
                            maintainStatisticalDTO.setStatisticalItemDTOList(statisticalItemDTOList);
                        }
                    }
                    maintainStatisticalDTOList.add(maintainStatisticalDTO);
                }
                return maintainStatisticalDTOList;
            }
        } else {
            throw new BaseKnownException(historicProcessInstanceList);
        }
    }

    /**
     * 获取保养次数
     *
     * @param historicProcessQuery 查询条件
     * @param equipmentCode        设备编码
     * @return 设备保养总次数
     */
    private Integer getMaintainCount(HistoricProcessQuery historicProcessQuery, String equipmentCode) {
        Map<String, Object> newMap = Maps.newHashMap();
        newMap.put("equipmentCode", equipmentCode);
        historicProcessQuery.setVariableValueEquals(newMap);
        ResultVO<List<HistoryProcessInstanceDTO>> historicProcessInstanceList1 = activitiClient.getHistoricProcessInstanceList(historicProcessQuery);
        Integer maintainCount = null;
        if (historicProcessInstanceList1.isSuccess()) {
            maintainCount = historicProcessInstanceList1.getData().size();
        }
        return maintainCount;
    }


    /**
     * 获取今日保养次数
     *
     * @return 次数
     */
    @Override
    public Integer getTodayMaintainCount() {
        Date date = new Date();
        String definitionKey = "equipment-maintain";
        return getActivitiRunTimeNum(definitionKey, date, date);
    }

    /**
     * 获取本周保养次数
     *
     * @return 次数
     */
    @Override
    public Integer getWeekCount() {
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int weekDay = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        if (weekDay == 0) {
            weekDay = 7;
        }
        //周一
        calendar.add(Calendar.DATE, -weekDay + 1);
        Date monday = calendar.getTime();
        //周日
        calendar.add(Calendar.DATE, 6);
        Date sunday = calendar.getTime();
        SimpleDateFormat after = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        SimpleDateFormat before = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
        String mondayStr = after.format(monday);
        String sundayStr = before.format(sunday);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            //周一00:00:00
            Date finishedAfter = sdf.parse(mondayStr);
            //周日23:59:59
            Date finishedBefore = sdf.parse(sundayStr);
            HistoricProcessQuery historicProcessQuery = new HistoricProcessQuery();
            historicProcessQuery.setProcessDefinitionKey("equipment-maintain");
            historicProcessQuery.setFinishedAfter(finishedAfter);
            historicProcessQuery.setFinishedBefore(finishedBefore);
            ResultVO<List<HistoryProcessInstanceDTO>> historicProcessInstanceInfo = activitiClient.getHistoricProcessInstanceList(historicProcessQuery);
            if (historicProcessInstanceInfo.isSuccess()) {
                List<HistoryProcessInstanceDTO> historicProcessInstanceInfoData = historicProcessInstanceInfo.getData();
                return historicProcessInstanceInfoData.size();
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 根据时间段查次数
     *
     * @param maintainStatisticalQueryDTO 查询条件
     * @return list
     */
    @Override
    public List<MaintainStatisticalTimeQuantumDTO> getCountByTimeQuantum(MaintainStatisticalQueryDTO maintainStatisticalQueryDTO) {
        HistoricProcessQuery historicProcessQuery = new HistoricProcessQuery();
        historicProcessQuery.setProcessDefinitionKey("equipment-maintain");
        //设备名称
        String equipmentName = maintainStatisticalQueryDTO.getEquipmentName();
        //完成时间之前
        Date finishedAfter = maintainStatisticalQueryDTO.getFinishedAfter();
        //完成时间之后
        Date finishedBefore = maintainStatisticalQueryDTO.getFinishedBefore();
        Calendar ca = Calendar.getInstance();
        if (finishedAfter == null) {
            finishedAfter = ca.getTime();
        }
        if (finishedBefore == null) {
            ca.add(Calendar.DATE, -30);
            finishedBefore = ca.getTime();
        }
        Map<String, String> map = Maps.newHashMap();
        map.put("equipmentName", "%" + equipmentName + "%");
        historicProcessQuery.setVariableValueLikeIgnoreCase(map);
        historicProcessQuery.setFinished(true);
        historicProcessQuery.setFinishedAfter(finishedBefore);
        historicProcessQuery.setFinishedBefore(finishedAfter);
        ResultVO<List<HistoryProcessInstanceDTO>> historicProcessInstanceList = activitiClient.getHistoricProcessInstanceList(historicProcessQuery);
        if (historicProcessInstanceList.isSuccess()) {
            List<HistoryProcessInstanceDTO> data = historicProcessInstanceList.getData();
            List<MaintainStatisticalTimeQuantumDTO> dtos = new ArrayList<>();
            for (HistoryProcessInstanceDTO datum : data) {
                MaintainStatisticalTimeQuantumDTO maintainStatisticalTimeQuantumDTO = new MaintainStatisticalTimeQuantumDTO();
                maintainStatisticalTimeQuantumDTO.setStartTime(datum.getStartTime());
                maintainStatisticalTimeQuantumDTO.setEndTime(datum.getEndTime());
                Map<String, Object> processInstanceVariables = datum.getProcessInstanceVariables();
                maintainStatisticalTimeQuantumDTO.setEquipmentName(processInstanceVariables.get("equipmentName").toString());
                maintainStatisticalTimeQuantumDTO.setEquipmentCode(processInstanceVariables.get("equipmentCode").toString());
                dtos.add(maintainStatisticalTimeQuantumDTO);
            }
            return dtos;
        }
        return null;
    }


}