package com.hvisions.eam.service.spare.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.eam.enums.StoreExceptionEnum;
import com.hvisions.eam.dto.publicstore.UnitDTO;
import com.hvisions.eam.entity.publicstore.HvEamSpareUnit;
import com.hvisions.eam.repository.publicstore.SpareUnitEntityRepository;
import com.hvisions.eam.service.publicstore.MatchingService;
import com.hvisions.eam.service.publicstore.SpareUnitService;
import com.hvisions.eam.dao.SpareMapper;
import com.hvisions.eam.dto.spare.SpareDTO;
import com.hvisions.eam.dto.spare.SpareTypeDTO;
import com.hvisions.eam.query.spare.SpareQueryDTO;
import com.hvisions.eam.entity.spare.HvEamSpare;
import com.hvisions.eam.entity.spare.HvEamSpareBrand;
import com.hvisions.eam.entity.spare.HvEamSpareType;
import com.hvisions.eam.repository.sprare.SpareBrandRepository;
import com.hvisions.eam.repository.sprare.SpareEntityRepository;
import com.hvisions.eam.repository.sprare.SpareTypeEntityRepository;
import com.hvisions.eam.service.spare.SpareFileService;
import com.hvisions.eam.service.spare.SpareService;
import com.hvisions.eam.service.spare.SpareToShelveService;
import com.hvisions.eam.service.spare.SpareTypeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>Title:SpareServiceImpl</p>
 * <p>Description:备件类</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/19</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Slf4j
@Service
public class SpareServiceImpl implements SpareService {

    @Value(value = "${spring.datasource.url}")
    private String url;
    /**
     * 备件jpa
     */
    private final SpareEntityRepository spareEntityRepository;
    private final SpareUnitEntityRepository spareUnitEntityRepository;
    private final SpareTypeEntityRepository spareTypeEntityRepository;

    /**
     * 备件库存关键service
     */
    private final SpareToShelveService spareToShelveService;

    /**
     * 设备类型service
     */
    private final SpareTypeService spareTypeService;

    /**
     * 备件单位service
     */
    private final SpareUnitService spareUnitService;

    /**
     * 匹配规则
     */
    private final MatchingService matchingService;


    /**
     * 备件文件
     */
    private final SpareFileService spareFileService;

    private final SpareMapper spareMapper;

    private final SpareBrandRepository spareBrandRepository;

    @Autowired
    public SpareServiceImpl(SpareEntityRepository spareEntityRepository,
                            SpareUnitEntityRepository spareUnitEntityRepository, SpareTypeEntityRepository spareTypeEntityRepository, SpareTypeService spareTypeService,
                            SpareToShelveService spareToShelveService,
                            SpareUnitService spareUnitService,
                            MatchingService matchingService,
                            SpareFileService spareFileService, SpareMapper spareMapper, SpareBrandRepository spareBrandRepository) {
        //调用父类的构造函数，传入jpa对象，实现普通的增删改查功能
        this.spareEntityRepository = spareEntityRepository;
        this.spareUnitEntityRepository = spareUnitEntityRepository;
        this.spareTypeEntityRepository = spareTypeEntityRepository;
        this.spareToShelveService = spareToShelveService;
        this.spareUnitService = spareUnitService;
        this.spareTypeService = spareTypeService;
        this.matchingService = matchingService;
        this.spareFileService = spareFileService;
        this.spareMapper = spareMapper;
        this.spareBrandRepository = spareBrandRepository;
    }


    /**
     * 通过备件code获取备件基础信息
     *
     * @param spareCods 备件code
     * @return 备件基础信息
     */
    @Override
    public SpareDTO getSpareBySpareCode(String spareCods) {
        HvEamSpare spare = spareEntityRepository.findAllBySpareCode(spareCods);
        if (spare != null) {
            return DtoMapper.convert(spare, SpareDTO.class);
        }
        return null;
    }

    @Override
    public List<SpareDTO> findAllById(List<Integer> spareIds) {
        List<HvEamSpare> spare = spareEntityRepository.findAllById(spareIds);
        return finishSpare(spare);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer save(SpareDTO spareDTO) {
        //备件最大库存最小库存验证
        //当两个库存数量都为空时 不进行验证
        spareDTO.verification();
        if (spareDTO.getCargoMax() < spareDTO.getCargoMin()) {
            //最小库存大于0 最大库存大于最小库存
            throw new BaseKnownException(StoreExceptionEnum.MAX_OR_MIN_SHELVE_NUMBER_EXCEPTION);
        }

        //如果typeID里有值 则
        List<Integer> typeIds = spareDTO.getTypeIds();
        if (typeIds != null) {
            spareDTO.setSpareTypeId(typeIds.get(typeIds.size() - 1));
        } else {
            spareDTO.setSpareTypeId(0);
        }
        String brandCode = spareDTO.getBrandCode();
        if (Strings.isNotBlank(brandCode)) {
            HvEamSpareBrand b = spareBrandRepository.getAllBySpareBrandCode(brandCode);
            if (b == null) {
                throw new BaseKnownException(10000, "未知的品牌信息，请先维护对应的品牌数据");

            }
        }
        Integer spareId = spareEntityRepository.save(DtoMapper.convert(spareDTO, HvEamSpare.class)).getId();
        //添加文件
        spareFileService.addFileBySpareId(spareId, spareDTO.getFiles());
        return spareId;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void deleteById(Integer id) {
        //只有当前备件的总库存数量 为零的时候 才可以删除
        if (spareToShelveService.sumSpareNumBySpareId(id) != 0) {
            //service层中直接抛出异常。不需要手动捕获,由controller增强器直接拦截并统一返回
            throw new BaseKnownException(StoreExceptionEnum.ENUM_NOT_ZERO_EXCEPTION);
        }
        //只有当关系表中无当前备件的时候才可以删除
        Set<Integer> spareIds = spareToShelveService.findAllBySpareId(id);
        if (spareIds.size() != 0) {
            //service层中直接抛出异常。不需要手动捕获,由controller增强器直接拦截并统一返回
            throw new BaseKnownException(StoreExceptionEnum.IN_USE);
        }
        //备件库存备件关系
        Set<Integer> allBySpareId = spareToShelveService.findAllBySpareId(id);
        for (Integer ids : allBySpareId) {
            //删除备件库存关系
            spareToShelveService.deleteById(ids);
        }
        //删除备件
        spareEntityRepository.deleteById(id);
        spareFileService.deleteBySpareId(id);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SpareDTO findById(Integer id) {
        //通过备件ID获取备件信息
        SpareDTO spareDTO = DtoMapper.convert(spareEntityRepository.getOne(id), SpareDTO.class);
        //通过备件获取备件单位信息
        UnitDTO unitDTO = spareUnitService.findById(spareDTO.getUnitId());
        if (unitDTO != null) {
            //添加单位名称
            spareDTO.setUnitName(unitDTO.getUnitName());
            //添加单位符号
            spareDTO.setUnitSymbol(unitDTO.getUnitSymbol());
        } else {
            spareDTO.setUnitName("");
            spareDTO.setUnitName("");
        }
        //获取备件类型信息
        SpareTypeDTO spareTypeDTO = spareTypeService.findById(spareDTO.getSpareTypeId());
        if (spareTypeDTO != null) {
            //添加备件类型名称
            spareDTO.setUnitName(spareTypeDTO.getTypeName());
            List<Integer> ids = new ArrayList<>();
            boolean next = true;
            ids.add(spareTypeDTO.getId());
            //获取父类型
            Integer parentId = spareTypeDTO.getParentId();
            while (next) {
                //如果父类型不为0
                if (!parentId.equals(0)) {
                    //通过父类型id 获取 父类型
                    try {
                        SpareTypeDTO spareTypeDTO1 = spareTypeService.findById(parentId);
                        ids.add(spareTypeDTO1.getId());
                        parentId = spareTypeDTO1.getParentId();
                    } catch (Exception e) {
                        log.info("id为" + parentId + "的备件类型 异常");
                        next = false;
                    }
                } else {
                    next = false;
                }
            }
            // 倒序排列
            Collections.reverse(ids);
            spareDTO.setTypeIds(ids);
        } else {
            spareDTO.setUnitName("");
            spareDTO.setTypeIds(new ArrayList<>());
        }
        return spareDTO;
    }


    /**
     * 分页查询 通过备件编码 备件名称 备件类型 是否关键部位 联合查询
     *
     * @param spareQueryDTO 备件分页DTO
     * @return 分页
     */
    @Override
    public Page<SpareDTO> findAllBySpareCodeAndSpareNameAndSpareTypeSub(SpareQueryDTO spareQueryDTO) {

        //把DTO转换成普通类
        HvEamSpare spare = DtoMapper.convert(spareQueryDTO, HvEamSpare.class);

        //匹配方式       xxx 采用开始匹配的方式查询 忽略大小写
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
            .withMatcher("spareCode", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
            .withMatcher("spareName", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());

        //匹配器
        Example<HvEamSpare> example = Example.of(spare, exampleMatcher);
        Page<HvEamSpare> page = spareEntityRepository.findAll(example, spareQueryDTO.getRequest());
        //查询全部信息
        Page<SpareDTO> spareDTOS = DtoMapper.convertPage(page, SpareDTO.class);
        for (SpareDTO s : spareDTOS) {
            //备件单位
            UnitDTO unitDTO = spareUnitService.findById(s.getUnitId());
            if (unitDTO == null) {
                s.setUnitName("");
                s.setUnitSymbol("");
            } else {
                //添加单位名称
                s.setUnitName(unitDTO.getUnitName());
                //添加单位符号
                s.setUnitSymbol(unitDTO.getUnitSymbol());
            }
            //备件类型
            SpareTypeDTO spareTypeDTO = spareTypeService.findById(s.getSpareTypeId());
            if (spareTypeDTO == null) {
                s.setSpareName("");
            } else {
                //添加类型名称
                s.setTypeName(spareTypeDTO.getTypeName());
            }

            boolean next = true;
            List<Integer> ids = new ArrayList<>();
            ids.add(spareTypeDTO.getId());
            Integer parentId = spareTypeDTO.getParentId();
            while (next) {
                if (!parentId.equals(0)) {
                    SpareTypeDTO spareTypeDTO1 = spareTypeService.findById(parentId);
                    ids.add(spareTypeDTO1.getId());
                    parentId = spareTypeDTO1.getParentId();
                } else {
                    next = false;
                }
            }
            // 倒序排列
            Collections.reverse(ids);
            s.setTypeIds(ids);
            //获取备件文件相关
            s.setFiles(spareFileService.getFileBySpareId(s.getId()));
        }
        return spareDTOS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<SpareDTO> getSpareListByIdList(List<Integer> ids) {
        List<SpareDTO> list = new ArrayList<>();
        for (Integer id : ids) {
            SpareDTO spare = DtoMapper.convert(spareEntityRepository.getOne(id), SpareDTO.class);
            UnitDTO unit = spareUnitService.findById(spare.getUnitId());
            SpareTypeDTO byId = spareTypeService.findById(spare.getSpareTypeId());
            spare.setTypeName(byId.getTypeName());
            //单位 名称 和 符号
            spare.setUnitName(unit.getUnitName());
            spare.setUnitSymbol(unit.getUnitSymbol());
            list.add(spare);

        }
        return list;
    }

    /**
     * 通过批次号 和 解析规则中的服务名获取 备件信息
     *
     * @param service 解析规则中的服务名获取
     * @return 备件基础信息
     */
    @Override
    public SpareDTO getSpareByBatchNumber(String service, String batchNumber) {
        String code = matchingService.getCodeByBatchNumber(batchNumber, service);
        if (code != null) {
            HvEamSpare spare = spareEntityRepository.findAllBySpareCode(code);
            if (spare != null) {
                return DtoMapper.convert(spare, SpareDTO.class);
            }
        }
        return null;
    }

    /**
     * 分页查询
     *
     * @param spareQueryDTO 查询条件
     * @return 备件信息
     */
    @Override
    public Page<SpareDTO> getSparePageByQuery(SpareQueryDTO spareQueryDTO) {
        Page<SpareDTO>
            page = PageHelperUtil.getPage(spareMapper::getSpareByQuery, spareQueryDTO);
        //备件类型
        for (SpareDTO s : page) {
            SpareTypeDTO spareTypeDTO = spareTypeService.findById(s.getSpareTypeId());
            if (spareTypeDTO == null) {
                s.setSpareName("");
            } else {
                //添加类型名称
                s.setTypeName(spareTypeDTO.getTypeName());
            }
            boolean next = true;
            List<Integer> ids = new ArrayList<>();
            ids.add(spareTypeDTO.getId());
            Integer parentId = spareTypeDTO.getParentId();
            while (next) {
                if (!parentId.equals(0)) {
                    SpareTypeDTO spareTypeDTO1 = spareTypeService.findById(parentId);
                    ids.add(spareTypeDTO1.getId());
                    parentId = spareTypeDTO1.getParentId();
                } else {
                    next = false;
                }
            }
            // 倒序排列
            Collections.reverse(ids);
            s.setTypeIds(ids);
            //获取备件文件相关
            s.setFiles(spareFileService.getFileBySpareId(s.getId()));
        }
        return page;
    }

    @Override
    public List<SpareDTO> getSpareBySpareCodeList(List<String> spareCodeList) {
        List<HvEamSpare> spare = spareEntityRepository.findAllBySpareCodeIn(spareCodeList);
        return finishSpare(spare);
    }

    /**
     * 补充备件信息
     * @param spare 备件列表
     * @return 完善的备件信息
     */
    private List<SpareDTO>  finishSpare(List<HvEamSpare> spare){
        List<Integer> unitIds = spare.stream()
            .map(HvEamSpare::getUnitId)
            .collect(Collectors.toList());
        List<Integer> typeIds = spare.stream()
            .map(HvEamSpare::getSpareTypeId)
            .collect(Collectors.toList());
        List<HvEamSpareUnit> untis = spareUnitEntityRepository.findAllById(unitIds);
        List<HvEamSpareType> types = spareTypeEntityRepository.findAllById(typeIds);
        List<SpareDTO> result = DtoMapper.convertList(spare, SpareDTO.class);
        for (SpareDTO spareDTO : result) {
            spareDTO.setUnitName(untis.stream()
                .filter(t -> t.getId().equals(spareDTO.getUnitId()))
                .map(HvEamSpareUnit::getUnitName)
                .findFirst().orElse(""));
            spareDTO.setUnitSymbol(untis.stream()
                .filter(t -> t.getId().equals(spareDTO.getUnitId()))
                .map(HvEamSpareUnit::getUnitSymbol)
                .findFirst().orElse(""));
            spareDTO.setTypeName(types.stream()
                .filter(t -> t.getId().equals(spareDTO.getSpareTypeId()))
                .map(HvEamSpareType::getTypeName).findFirst().orElse(""));
        }
        return result;
    }
}