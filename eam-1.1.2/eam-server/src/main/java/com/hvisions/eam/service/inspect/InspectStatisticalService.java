package com.hvisions.eam.service.inspect;

import com.hvisions.activiti.dto.history.HistoricTaskInstanceDTO;
import com.hvisions.activiti.dto.process.ProcessInstanceDTO;
import com.hvisions.common.dto.HvPage;
import com.hvisions.eam.dto.inspect.statistical.InspectHistoryQueryDTO;
import com.hvisions.eam.dto.inspect.statistical.InspectStatisticalDTO;
import com.hvisions.eam.dto.inspect.statistical.InspectStatisticalQueryDTO;
import com.hvisions.eam.dto.inspect.statistical.InspectStatisticalTimeQuantumDTO;

import java.util.List;

/**
 * <p>Title: InspectStatisticalService</p >
 * <p>Description: 点检统计service</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/8</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface InspectStatisticalService {
    /**
     * 完成任务并存储统计信息
     *
     * @param taskId 任务id
     * @param token  token
     */
    void completeTaskAndStatistical(String taskId,String token);

    /**
     * 统计报表查询
     *
     * @param inspectStatisticalQueryDTO 查询条件
     * @return 统计信息
     */
    List<InspectStatisticalDTO> getStatistical(InspectStatisticalQueryDTO inspectStatisticalQueryDTO);

    /**
     * 获取今日点巡检次数
     *
     * @return 次数
     */
    Integer getTodayInspectCount();

    /**
     * 本周点巡检次数
     *
     * @return 次数
     */
    Integer getWeekCount();

    /**
     * 根据时间段查询点检次数
     *
     * @param inspectStatisticalQueryDTO 查询条件
     * @return list
     */
    List<InspectStatisticalTimeQuantumDTO> getCountByTimeQuantum(InspectStatisticalQueryDTO inspectStatisticalQueryDTO);

    /**
     * 根据设备id查询历史
     *
     * @param inspectHistoryQueryDTO 查询条件
     * @return 历史任务
     */
    List<HistoricTaskInstanceDTO> getHistoryByEquipmentId(InspectHistoryQueryDTO inspectHistoryQueryDTO);

    /**
     * 根据设备id查正在进行的任务
     *
     * @param inspectHistoryQueryDTO 查询条件
     * @return 分页
     */
    HvPage<ProcessInstanceDTO> getOngoingByEquipmentId(InspectHistoryQueryDTO inspectHistoryQueryDTO);

}
