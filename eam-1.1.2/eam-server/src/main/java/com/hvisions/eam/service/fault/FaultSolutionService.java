package com.hvisions.eam.service.fault;

import com.hvisions.eam.dto.fault.FaultSolutionDTO;
import com.hvisions.eam.dto.fault.FaultSolutionQueryDTO;
import org.springframework.data.domain.Page;


/**
 * <p>Title: FaultSolutionService</p >
 * <p>Description: 故障解决方案service</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/27</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface FaultSolutionService {

    /**
     * 新增或更新故障解决方案
     *
     * @param faultSolutionDTO 解决方案DTO
     * @return 解决方案id
     */
    Integer addOrUpdateFaultSolution(FaultSolutionDTO faultSolutionDTO);

    /**
     * 删除解决方案
     *
     * @param id 解决方案id
     */
    void deleteFaultSolution(Integer id);

    /**
     * 查询解决方案
     *
     * @param faultSolutionQueryDTO 故障原因查询解决方案DTO
     * @return 解决方案集合
     */
    Page<FaultSolutionDTO> getFaultSolutionByFaultReasonId(FaultSolutionQueryDTO faultSolutionQueryDTO);

    /**
     * 根据id 查询解决方案
     *
     * @param id 解决方案id
     * @return 解决方案
     */
    FaultSolutionDTO getFaultSolutionById(Integer id);

    /**
     * 通过编码获取解决方案
     *
     * @param code 编码
     * @return 解决方案
     */
    FaultSolutionDTO getFaultSolutionBySolutionCode(String code);

    /**
     * 通过解决方案编码获取解决方案
     *
     * @param solutionCode 解决方案编码
     */
    void deleteFaultSolutionByCode(String solutionCode);
}
