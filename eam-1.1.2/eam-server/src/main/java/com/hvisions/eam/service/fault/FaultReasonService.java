package com.hvisions.eam.service.fault;

import com.hvisions.eam.dto.fault.FaultReasonDTO;
import com.hvisions.eam.dto.fault.FaultReasonQueryDTO;
import org.springframework.data.domain.Page;


/**
 * <p>Title: FaultReasonService</p >
 * <p>Description: 故障原因service</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/27</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface FaultReasonService {

    /**
     * 新增或更新故障原因
     *
     * @param faultReasonDTO 故障原因DTO
     * @return 故障原因id
     */
    Integer addOrUpdateFaultReason(FaultReasonDTO faultReasonDTO);

    /**
     * 删除故障原因
     *
     * @param id 故障原因id
     */
    void deleteFaultReason(Integer id);

    /**
     * 根据故障查询故障原因
     *
     * @param faultReasonQueryDTO 故障查询故障原因DTO
     * @return 故障原因集合
     */
    Page<FaultReasonDTO> getFaultReasonByFaultId(FaultReasonQueryDTO faultReasonQueryDTO);

    /**
     * 根据id查询故障原因
     *
     * @param id 故障原因id
     * @return 故障原因
     */
    FaultReasonDTO getFaultReasonById(Integer id);

    /**
     * 通过编码删除故障原因
     *
     * @param reasonCode 故障原因编码
     */
    void deleteFaultReasonCode(String reasonCode);

    /**
     * 通过故障原因编码查询故障原因
     *
     * @param code 故障原因编码
     * @return 故障原因
     */
    FaultReasonDTO getFaultReasonByCode(String code);
}
