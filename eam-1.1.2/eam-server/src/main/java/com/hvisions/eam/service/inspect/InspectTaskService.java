package com.hvisions.eam.service.inspect;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.eam.dto.inspect.report.InspectMapDO;

import java.util.List;

/**
 * <p>Title: MaintainTaskService</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/5/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface InspectTaskService {
    /**
     * 批量处理保养任务
     *
     * @param taskIds 任务id列表
     * @param id
     * @param token   token
     */
    void finishTask(List<String> taskIds, String id, String token);

    /**
     * 导出任务信息
     *
     * @return 任务信息excel
     */
    ExcelExportDto exportData();


    /**
     * 导出未完成任务信息
     *
     * @return 任务信息excel
     */
    ExcelExportDto exportTaskData();
    /**
     * 获取点巡检任务地图
     *
     * @param id 用户id
     * @return 任务地图信息
     */
    List<InspectMapDO> getTaskMap(Integer id);
}

    
    
    
    