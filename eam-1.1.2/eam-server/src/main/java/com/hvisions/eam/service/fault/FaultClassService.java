package com.hvisions.eam.service.fault;

import com.hvisions.eam.dto.fault.FaultClassDTO;

import java.util.List;

/**
 * <p>Title: FaultClassService</p >
 * <p>Description: 故障类service</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/27</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface FaultClassService {

    /**
     * 新增和更新 故障类
     *
     * @param faultClassDTO 故障类DTO
     * @return 故障类DTO
     */
    FaultClassDTO addOrUpdateFaultClass(FaultClassDTO faultClassDTO);

    /**
     * 删除故障类
     *
     * @param id 故障类id
     */
    void deleteFaultClass(int id);

    /**
     * 通过父类查询子类
     *
     * @param parentId 父类id
     * @return 子故障类集合
     */
    List<FaultClassDTO> getFaultClassByParentId(Integer parentId);

    /**
     * 通过设备类型查找故障类
     *
     * @param equipmentClassId 设备类型id
     * @return 故障类集合
     */
    List<FaultClassDTO> getFaultClassByEquipmentClassId(Integer equipmentClassId);


    /**
     * 根据故障类id查询故障
     *
     * @param id 故障类id
     * @return 故障类
     */
    FaultClassDTO getFaultClassById(Integer id);

    /**
     * 通过故障类名称查询故障类
     *
     * @param faultClassName 故障类名称
     * @return 故障类
     */
    List<FaultClassDTO> getFaultClassByFaultClassName(String faultClassName);

    /**
     * 通过故障类型名称删除
     *
     * @param faultClassName 故障名称
     */
    void deleteFaultClassByFaultClassName(String faultClassName);
}
