package com.hvisions.eam.service.publicstore.imp;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.eam.activiti.lub.ItemsLubDTO;
import com.hvisions.eam.activiti.sapre.ItemsSpareDTO;
import com.hvisions.eam.client.task.Items;
import com.hvisions.eam.dto.publicstore.LineDTO;
import com.hvisions.eam.entity.lub.HvEamLubType;
import com.hvisions.eam.entity.lub.HvEamLubricating;
import com.hvisions.eam.entity.publicstore.HvEamShelve;
import com.hvisions.eam.entity.publicstore.HvEamStoreHeader;
import com.hvisions.eam.entity.publicstore.HvEamStoreLine;
import com.hvisions.eam.entity.spare.HvEamSpare;
import com.hvisions.eam.entity.spare.HvEamSpareType;
import com.hvisions.eam.repository.lub.LubEntityRepository;
import com.hvisions.eam.repository.lub.LubTypeEntityRepository;
import com.hvisions.eam.repository.publicstore.ShelveEntityRepository;
import com.hvisions.eam.repository.publicstore.StoreHeaderRepository;
import com.hvisions.eam.repository.publicstore.StoreLineRepository;
import com.hvisions.eam.repository.sprare.SpareEntityRepository;
import com.hvisions.eam.repository.sprare.SpareTypeEntityRepository;
import com.hvisions.eam.service.publicstore.StoreLineService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>Title:StoreLineServiceImpl</p>
 * <p>Description:行表</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/31</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Slf4j
@Service(value = "line")
public class StoreLineServiceImpl implements StoreLineService {

    /**
     * 行表jpa
     */
    private final StoreLineRepository storeLineRepository;

    /**
     * 头表
     */
    private final StoreHeaderRepository storeHeaderRepository;
    /**
     * 备件jpa
     */
    private final SpareEntityRepository spareEntityRepository;
    /**
     * 油品jpa
     */
    private final LubEntityRepository lubEntityRepository;

    /**
     * 油品类型
     */
    private final LubTypeEntityRepository lubTypeEntityRepository;

    /**
     * 备件类型
     */
    private final SpareTypeEntityRepository spareTypeEntityRepository;

    /**
     * 库房jpa
     */
    private final ShelveEntityRepository shelveEntityRepository;

    @Autowired
    public StoreLineServiceImpl(StoreLineRepository storeLineRepository, StoreHeaderRepository storeHeaderRepository, SpareEntityRepository spareEntityRepository, LubEntityRepository lubEntityRepository, LubTypeEntityRepository lubTypeEntityRepository, SpareTypeEntityRepository spareTypeEntityRepository, ShelveEntityRepository shelveEntityRepository) {

        this.storeLineRepository = storeLineRepository;
        this.storeHeaderRepository = storeHeaderRepository;
        this.spareEntityRepository = spareEntityRepository;
        this.lubEntityRepository = lubEntityRepository;
        this.lubTypeEntityRepository = lubTypeEntityRepository;
        this.spareTypeEntityRepository = spareTypeEntityRepository;
        this.shelveEntityRepository = shelveEntityRepository;
    }

    /**
     * 添加备件
     *
     * @param headerId 头表id
     * @param items    数据
     */
    @Override
    public void addSpareLine(Integer headerId, List<ItemsSpareDTO> items) {
        List<HvEamStoreLine> lines = new ArrayList<>();
        for (ItemsSpareDTO item : items) {
            HvEamStoreLine line = new HvEamStoreLine(headerId);
            line.setSpareId(item.getSpareId());
            line.setCreateTime(new Date());
            line.setUpdateTime(new Date());
            line.setShelveId(item.getShelveId());
            line.setNumber(item.getNumber());
            line.setBatchNumber(item.getBatchNumber());
            lines.add(line);
        }
        storeLineRepository.saveAll(lines);
    }

    /**
     * 增加
     *
     * @param headerId  头表id
     * @param items     内容项
     * @param typeClass 类型
     */
    @SuppressWarnings("all")
    @Override
    public void addSpareLine(Integer headerId, List<Items> items, int typeClass) {
        if (typeClass == 1) {
            List<ItemsSpareDTO> itemsSpareDTOS = new ArrayList<>();
            for (Items item : items) {
                ItemsSpareDTO itemsSpareDTO = DtoMapper.convert(item, ItemsSpareDTO.class);
                if (item.getNumber() > 0) {
                    itemsSpareDTO.setNumber(new BigDecimal(item.getNumber()));
                } else {
                    log.info("err number:{}", item.getNumber());
                    itemsSpareDTO.setNumber(new BigDecimal("0"));
                }

                itemsSpareDTOS.add(itemsSpareDTO);
            }
            this.addSpareLine(headerId, itemsSpareDTOS);
        } else {
            List<ItemsLubDTO> itemsLubDTOS = new ArrayList<>();
            for (Items item : items) {
                ItemsLubDTO itemsLubDTO = DtoMapper.convert(item, ItemsLubDTO.class);
                if (item.getNumber() > 0) {
                    itemsLubDTO.setNumber(new BigDecimal(item.getNumber()));
                } else {
                    log.info("err number:{}", item.getNumber());
                    itemsLubDTO.setNumber(new BigDecimal("0"));
                }

                itemsLubDTOS.add(itemsLubDTO);
            }
            this.addLubLine(headerId, itemsLubDTOS);
        }
    }

    /**
     * 添加油品
     *
     * @param headerId 头表id
     * @param items    数据
     */
    @Override
    public void addLubLine(Integer headerId, List<ItemsLubDTO> items) {
        List<HvEamStoreLine> lines = new ArrayList<>();
        for (ItemsLubDTO item : items) {
            HvEamStoreLine line = new HvEamStoreLine(headerId);
            line.setSpareId(item.getLubId());
            line.setShelveId(item.getShelveId());
            line.setNumber(item.getNumber());
            line.setBatchNumber(item.getBatchNumber());
            line.setCreateTime(new Date());
            line.setUpdateTime(new Date());
            lines.add(line);
        }
        storeLineRepository.saveAll(lines);
    }

    /**
     * 通过头表获取 内容
     *
     * @param headerId 头表id
     * @return 内容
     */
    @Override
    public List<LineDTO> getItemByHeaderId(Integer headerId) {
        Optional<HvEamStoreHeader> header = storeHeaderRepository.findById(headerId);
        if (!header.isPresent()) {
            log.warn("不应该传递一个找不到的header。");
            return new ArrayList<>();
        }
        List<LineDTO> lineDTOS = DtoMapper.convertList(storeLineRepository.findByHeaderId(headerId), LineDTO.class);
        if (header.get().getTypeClass() == 1) {
            //当前是备件的单子
            for (LineDTO lineDTO : lineDTOS) {

                if (lineDTO.getSpareId() != null) {
                    Optional<HvEamSpare> spare = spareEntityRepository.findById(lineDTO.getSpareId());
                    if (spare.isPresent()) {
                        HvEamSpare hvEamSpare = spare.get();
                        lineDTO.setSpareName(hvEamSpare.getSpareName());
                        lineDTO.setBrand(hvEamSpare.getBrand());
                        if (hvEamSpare.getSpareTypeId() != null) {
                            lineDTO.setTypeName(spareTypeEntityRepository.findById(hvEamSpare.getSpareTypeId()).map(HvEamSpareType::getTypeName).orElse(""));
                        }
                        lineDTO.setSpecifications(hvEamSpare.getSpecifications());
                        lineDTO.setSupplier(hvEamSpare.getSupplier());
                    }
                }
            }
        } else {
            //当前是油品的单子
            for (LineDTO lineDTO : lineDTOS) {
                HvEamLubricating lub = lubEntityRepository.findById(lineDTO.getSpareId()).orElse(null);
                if (lub != null) {
                    lineDTO.setSpareName(lub.getLubName());
                    lineDTO.setBrand("");
                    if (lub.getLubTypeId() != null) {
                        lineDTO.setTypeName(lubTypeEntityRepository.findById(lub.getLubTypeId()).map(HvEamLubType::getTypeName).orElse(""));
                    }
                    lineDTO.setSpecifications(lub.getRemarks());
                    lineDTO.setSupplier(lub.getSupplier());
                }
            }
        }
        //设置库位信息
        for (LineDTO lineDTO : lineDTOS) {
            if (lineDTO.getShelveId() != null) {
                lineDTO.setShelveName(shelveEntityRepository.findById(lineDTO.getShelveId()).map(HvEamShelve::getShelveName).orElse(""));
            }
        }
        return lineDTOS;
    }

    /**
     * 更改状态
     *
     * @param lineDTOS 更改集合
     */
    @Override
    public void deliveryOfCargoFromStorage(List<LineDTO> lineDTOS) {
        storeLineRepository.saveAll(DtoMapper.convertList(lineDTOS, HvEamStoreLine.class).stream().peek(t -> t.setComplete(true)).collect(Collectors.toList()));
    }

    @Override
    public Integer update(LineDTO lineDTO) {
//        lineDTO.setNumber(null);
        return storeLineRepository.save(DtoMapper.convert(lineDTO, HvEamStoreLine.class)).getId();
    }

    /**
     * 删除
     *
     * @param lineDTOId id
     */
    @Override
    public void delete(Integer lineDTOId) {
        Optional<HvEamStoreLine> optional = storeLineRepository.findById(lineDTOId);
        if (optional.isPresent()) {
            storeLineRepository.deleteById(lineDTOId);
        }
    }
}