package com.hvisions.eam.dao.inspect;

import com.hvisions.eam.dto.inspect.table.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: ReportStatisticalMapper</p >
 * <p>Description: 报表统计</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/1/5</p >
 *
 * <AUTHOR> czh
 * @version :1.0.0
 */
@Mapper
@Component
public interface InspectReportStatisticalMapper {

    /**
     * 数据表格查询
     *
     * @param inspectProcessQueryDTO 查询条件
     * @return 数据表格
     */
    List<InspectProcessResultDTO> queryForInspectProcessTable(@Param("dto") InspectProcessQueryDTO inspectProcessQueryDTO);

    /**
     * 巡检任务表格查询
     *
     * @param inspectProcessQueryDTO 查询条件
     * @return 数据表格
     */
    List<InspectTaskResultDTO> queryForInspectTaskTable(@Param("query") InspectTaskQueryDTO inspectProcessQueryDTO);

    /**
     * 每月总数
     *
     * @param monthQueryDTO 查询条件
     * @return 数据表格
     */
     Integer countForAllItem(@Param("monthQueryDTO") MonthQueryDTO monthQueryDTO);

    /**
     * 每月合格品总数
     *
     * @param monthQueryDTO 查询条件
     * @return 数据表格
     */
    Integer countForQualifiedItem(@Param("monthQueryDTO") MonthQueryDTO monthQueryDTO);

    /**
     * 每月不合格品总数
     *
     * @param monthQueryDTO 查询条件
     * @return 数据表格
     */
    Integer countForUnqualifiedItem(@Param("monthQueryDTO") MonthQueryDTO monthQueryDTO);
    /**
     * 每月不合格品总数,按日分类
     *
     * @param monthQueryDTO 查询条件
     * @return 数据表格
     */
    List<ItemStatisticalDTO> countForDailyItem(@Param("monthQueryDTO") MonthQueryDTO monthQueryDTO);
    /**
     * 设备巡检项不合格数
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    List<ItemStatisticalDTO> countForUnqualifiedItemGroupByEquipment(@Param("monthQueryDTO") MonthQueryDTO monthQueryDTO);
    /**
     * 巡检项不合格数
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    List<ItemStatisticalDTO> countForUnqualifiedItemGroupByItem(@Param("monthQueryDTO") MonthQueryDTO monthQueryDTO);
    /**
     * 设备巡检项不合格数
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    List<ItemStatisticalDTO> countForUnqualifiedItemGroupByItemAndEquipment(@Param("monthQueryDTO") MonthQueryDTO monthQueryDTO);
    /**
     * 巡检任务数据
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    TaskStatisticalDTO countForTask(@Param("monthQueryDTO")MonthQueryDTO monthQueryDTO);

    /**
     * 人员平均完成时间
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    List<InspectTaskStaDTO> countDurationByPerson(@Param("monthQueryDTO")MonthQueryDTO monthQueryDTO);
    /**
     * 人员完成任务数
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    List<InspectTaskStaDTO> countTaskNumByPerson(@Param("monthQueryDTO") MonthQueryDTO monthQueryDTO);
    /**
     * 每天巡检任务数量
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    List<InspectTaskStaDTO> countTaskNumByPersonAndTaskName(@Param("monthQueryDTO") MonthQueryDTO monthQueryDTO);
    /**
     * 每天巡检任务时间
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    List<InspectTaskStaDTO> countDurationByPersonAndTaskName(MonthQueryDTO monthQueryDTO);
    /**
     * 人员任务延迟时间（平均）
     *
     * @param monthQueryDTO 年月
     * @return 历史
     */
    List<InspectTaskStaDTO> countDelayByPerson(MonthQueryDTO monthQueryDTO);
}