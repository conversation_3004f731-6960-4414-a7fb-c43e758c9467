package com.hvisions.eam.service.autonomy.imp;

import cn.hutool.core.date.DateUtil;
import com.hvisions.eam.client.autonomy.ActivitiHistoryClient;
import com.hvisions.eam.client.autonomy.AuthClient;
import com.hvisions.eam.dto.autonomy.JsonByUserId;
import com.hvisions.eam.dto.autonomy.MessageCreateDTO;
import com.hvisions.eam.dto.autonomy.process.AutonomyMaintenanceContentDTO;
import com.hvisions.eam.dto.autonomy.process.Data;
import com.hvisions.eam.dto.autonomy.process.JsonRootBean;
import com.hvisions.eam.dto.autonomy.process.ProcessInstanceVariables;
import com.hvisions.eam.entity.autonomy.HvAmAutonomyMaintenanceProcessData;
import com.hvisions.eam.entity.autonomy.HvAmAutonomyMaintenanceProcessItem;
import com.hvisions.eam.repository.autonomy.AutonomyMaintenanceProcessDataRepository;
import com.hvisions.eam.repository.autonomy.AutonomyMaintenanceProcessItemRepository;
import com.hvisions.eam.service.autonomy.AutonomyMaintenanceDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.support.ListenerExecutionFailedException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title: MaintainDataServiceImpl</p>
 * <p>Description: 业务流程数据解析保存服务 </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/9/28</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class AutonomyMaintenanceDataServiceImpl implements AutonomyMaintenanceDataService {

    private final AutonomyMaintenanceProcessDataRepository repository;
    private final ActivitiHistoryClient client;
    private final AuthClient authClient;
    private final AutonomyMaintenanceProcessItemRepository itemRepository;
    private final AutonomyMaintenanceProcessDataRepository dataRepository;

    @Autowired
    public AutonomyMaintenanceDataServiceImpl(AutonomyMaintenanceProcessDataRepository repository,
                                              ActivitiHistoryClient client,
                                              AuthClient authClient,
                                              AutonomyMaintenanceProcessItemRepository itemRepository,
                                              AutonomyMaintenanceProcessDataRepository dataRepository) {
        this.repository = repository;
        this.client = client;
        this.authClient = authClient;
        this.itemRepository = itemRepository;
        this.dataRepository = dataRepository;
    }

    /**
     * 处理流程实例信息
     * <AUTHOR>
2021/6/23
     * @param processInstanceId 流程实例id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(String processInstanceId,Boolean b) {

        log.info("对自主维护流程实例进行保存：,ID:" + processInstanceId);
        if (repository.existsByProcessInstanceId(processInstanceId)) {
            log.info("已经存在自主维护流程实例,ID:" + processInstanceId);
            return;
        }
        JsonRootBean processInstance = client.getProcessInstance(processInstanceId);

        if (processInstance.getCode() == 200) {

            Data data = processInstance.getData();

            log.info("流程信息获取成功,ID:" + processInstanceId + "数据为：" + data.toString());
            ProcessInstanceVariables variables = data.getProcessInstanceVariables();

            log.info("流程信息获取成功,ID:" + processInstanceId + "数据为：" + variables.toString());
            if (variables == null) {
                log.error("流程信息获取成功,ID:" + processInstanceId + "数据异常，流程参数为null");
                return;
            }

            List<AutonomyMaintenanceContentDTO> itemList = variables.getProjectDTOList();
            if (itemList == null || itemList.size()<1) {
                log.error("流程信息获取成功,ID:" + processInstanceId + "数据异常，检查项目list为null或size小于1");
                return;
            }

            HvAmAutonomyMaintenanceProcessData autonomyMaintenanceProcessData = new HvAmAutonomyMaintenanceProcessData();
            BeanUtils.copyProperties(data, autonomyMaintenanceProcessData, "id");
            BeanUtils.copyProperties(variables, autonomyMaintenanceProcessData, "id");
            if (StringUtils.isBlank(autonomyMaintenanceProcessData.getEndTime())) {
                autonomyMaintenanceProcessData.setEndTime(DateUtil.now());
            }
            String dataHasError = "0";// 0 - 否，1 - 是
            for (AutonomyMaintenanceContentDTO temp:itemList) {
                if ("1".equals(temp.getHasError())){
                    dataHasError = "1";
                    break;
                }
            }
            autonomyMaintenanceProcessData.setHasError(dataHasError);

            Integer processDataId = repository.save(autonomyMaintenanceProcessData).getId();
            log.info("成功保存流程实例,ID:" + processInstanceId);

            Integer n = 1;//连续异常次数
            StringBuffer content = new StringBuffer("自主维护发现异常,项目编号为：");
            Boolean c = false;
            for (AutonomyMaintenanceContentDTO temp:itemList) {
                HvAmAutonomyMaintenanceProcessItem item = new HvAmAutonomyMaintenanceProcessItem();
                BeanUtils.copyProperties(temp, item);
                item.setProcessDataId(autonomyMaintenanceProcessData.getId());
                itemRepository.save(item);

                //是否需要 发送连续异常警告
                if (b){
                    //0 - 否，1 - 是
                    if ("0".equals(temp.getHasError())){
                        continue;
                    }else {
                        n = temp.getAbnormalTimes();
                        if (n==1){
                            c = true;
                            content.append(temp.getNumber());
                        }else {
                            Boolean sendMsg = false;
                            //查询所有该项目的 检查 记录
                            List<HvAmAutonomyMaintenanceProcessItem> list = new ArrayList<>();
                            list = itemRepository.getAllByNumberOrderByIdDesc(temp.getNumber());
                            if (list.size()<n-1){
                                continue;
                            }else {
                                for (int i = 0; i < n-1; i++){
                                    if ("0".equals(list.get(i).getHasError())){
                                        sendMsg = false;
                                        break;
                                    }else {
                                        sendMsg = true;
                                    }
                                }
                                if (sendMsg){
                                    c = true;
                                    content.append(","+temp.getNumber());
                                }
                            }
                            if (sendMsg){
                                c = true;
                                content.append(","+temp.getNumber());
                            }
                        }
                    }
                }
            }

            if (c){
                //发消息
                MessageCreateDTO messageCreateDTO = new MessageCreateDTO();
                messageCreateDTO.setContent(content.toString());
                messageCreateDTO.setTitle("自主维护发现异常!");
                messageCreateDTO.setUrl("/task/inspection-statistics");

                List<Integer> userIds = new ArrayList<>();
                userIds.add(Integer.parseInt(autonomyMaintenanceProcessData.getLeader()));
                messageCreateDTO.setUserIds(userIds);
                try {
                    JsonByUserId message = authClient.createMessage(messageCreateDTO);
                    if (message.getCode()!=200){
                        throw new Exception("接口返回失败！");
                    }
                }catch (Exception e){
                    log.error("发送消息失败！");
                }

            }
        } else {
            log.error("流程信息获取失败,ID:" + processInstanceId + "错误信息为：" + processInstance.getMessage());
        }

    }

    /**
     * 处理业务异常
     * <AUTHOR>
2021/6/23
     * @param message   消息
     * @param e         异常
     */
    @Override
    public void handleError(Message message, ListenerExecutionFailedException e) {
        log.info("保存流程实例失败,ID:" + message.toString());
        log.error(message.toString(), e);
    }
}
