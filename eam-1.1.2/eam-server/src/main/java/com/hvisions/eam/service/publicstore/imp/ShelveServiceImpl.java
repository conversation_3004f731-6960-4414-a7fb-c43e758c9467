package com.hvisions.eam.service.publicstore.imp;

import com.hvisions.framework.client.AuthClient;
import com.hvisions.framework.dto.user.UserInfoDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.configuration.spare.StoreConfig;
import com.hvisions.eam.enums.StoreExceptionEnum;
import com.hvisions.eam.service.lub.LubToShelveService;
import com.hvisions.eam.dto.publicstore.ShelveDTO;
import com.hvisions.eam.entity.publicstore.HvEamShelve;
import com.hvisions.eam.repository.publicstore.ShelveEntityRepository;
import com.hvisions.eam.service.publicstore.ShelveService;
import com.hvisions.eam.query.spare.ShelveQueryDTO;
import com.hvisions.eam.service.spare.SpareToShelveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <p>Title:ShelveServiceImpl</p>
 * <p>Description:库房</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Service
public class ShelveServiceImpl implements ShelveService {

    /**
     * 库房jpa
     */
    private final ShelveEntityRepository shelveEntityRepository;

    /**
     * 备件库房关系 service
     */
    private final SpareToShelveService spareToShelveService;

    /**
     * 油品库房关系 service
     */
    private final LubToShelveService lubToShelveService;

    /**
     * 人员client
     */
    private final AuthClient authClient;

    @SuppressWarnings(StoreConfig.RULES)
    @Autowired
    public ShelveServiceImpl(ShelveEntityRepository shelveEntityRepository,
                             SpareToShelveService spareToShelveService,
                             AuthClient authClient,
                             LubToShelveService lubToShelveService) {
        this.shelveEntityRepository = shelveEntityRepository;
        this.spareToShelveService = spareToShelveService;
        this.authClient = authClient;
        this.lubToShelveService = lubToShelveService;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer save(ShelveDTO shelveDTO) {
        HvEamShelve shelve = DtoMapper.convert(shelveDTO, HvEamShelve.class);
        return shelveEntityRepository.save(shelve).getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void deleteById(Integer id) {
        //只有当前备件的总库存数量 为零的时候 才可以删除
        if (spareToShelveService.findAllByShelveIds(id) != 0) {
            //service层中直接抛出异常。不需要手动捕获,由controller增强器直接拦截并统一返回
            //抛出 被使用异常
            throw new BaseKnownException(StoreExceptionEnum.IN_USE);
        }
        //只有当前油品的总库存数量 为零的时候 才可以删除\
        if (lubToShelveService.findAllByShelveIds(id).compareTo(new BigDecimal("0")) != 0) {
            //service层中直接抛出异常。不需要手动捕获,由controller增强器直接拦截并统一返回
            //抛出 被使用异常
            throw new BaseKnownException(StoreExceptionEnum.SHELVE_NUMBER_IS_NOT_ZERO_EXCEPTION);
        }
        //只有当前备件的总库存数量 为零的时候 才可以删除
        if (spareToShelveService.findAllByShelveIds(id) != 0) {
            throw new BaseKnownException(StoreExceptionEnum.SHELVE_NUMBER_IS_NOT_ZERO_EXCEPTION);
        }
        //备件库存备件关系
        Set<Integer> allBySpareId = spareToShelveService.findAllByShelveId(id);
        if (allBySpareId.size() > 0) {
            throw new BaseKnownException(StoreExceptionEnum.IN_USE);
        }
        //油品库存备件关系
        Set<Integer> allByLubId = lubToShelveService.findAllByShelveId(id);
        if (allByLubId.size() > 0) {
            throw new BaseKnownException(StoreExceptionEnum.IN_USE);
        }
        //删除库位
        shelveEntityRepository.deleteById(id);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ShelveDTO findById(Integer id) {
        ShelveDTO shelveDTO = DtoMapper.convert(shelveEntityRepository.getOne(id), ShelveDTO.class);
        //添加 库房人员姓名
        shelveDTO.setShelveUserName(getUserName(shelveDTO));
        return shelveDTO;
    }

    /**
     * 通过人员ID获取 人员姓名
     *
     * @param shelveDTO 库房DTO
     * @return 人员姓名
     */
    private String getUserName(ShelveDTO shelveDTO) {
        //当前人员姓名
        String userName = "";
        //创建一个人员ID集合
        List<Integer> userId = new ArrayList<>();
        //向集合中添加人员ID
        userId.add(shelveDTO.getShelveUserId());
        //调用人员Client接口
        ResultVO<List<UserInfoDto>> userInfoByIds = authClient.getUserInfoByIds(userId);
        //判断返回值是否正确
        if (!userInfoByIds.isSuccess()) {
            throw new BaseKnownException(userInfoByIds);
        }
        //取出数据进行遍历
        List<UserInfoDto> data = userInfoByIds.getData();
        for (UserInfoDto user : data) {
            userName = user.getUserName();
        }
        //返回姓名
        return userName;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Page<ShelveDTO> findAllByShelveCodeAndShelveNameAndShelvePace(ShelveQueryDTO shelveQueryDTO) {
        //把DTO转换成普通类
        HvEamShelve shelve = DtoMapper.convert(shelveQueryDTO, HvEamShelve.class);

        //匹配方式       xxx 采用开始匹配的方式查询 忽略大小写
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
                .withMatcher("shelveCode", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("shelveName", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("shelvePlace", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());

        //匹配器
        Example<HvEamShelve> example = Example.of(shelve, exampleMatcher);
        //查询
        Page<HvEamShelve> page = shelveEntityRepository.findAll(example, shelveQueryDTO.getRequest());
        Page<ShelveDTO> shelveDTOS = DtoMapper.convertPage(page, ShelveDTO.class);
        for (ShelveDTO h : shelveDTOS) {
            h.setShelveUserName(getUserName(h));
        }
        return shelveDTOS;
    }
}