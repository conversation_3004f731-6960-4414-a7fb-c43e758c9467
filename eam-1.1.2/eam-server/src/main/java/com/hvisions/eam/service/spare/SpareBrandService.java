package com.hvisions.eam.service.spare;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.EntitySaver;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.spare.SpareBrandDTO;
import com.hvisions.eam.dto.spare.SpareBrandQuery;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;

/**
 * <p>Title: SpareBrandService</p >
 * <p>Description: 备件品牌服务层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/5/11</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public interface SpareBrandService extends EntitySaver<SpareBrandDTO> {

    /**
     * 新增备件品牌
     *
     * @param spareBrandDTO 备件品牌数据
     * @return 备件品牌信息
     */
    SpareBrandDTO createBrand(SpareBrandDTO spareBrandDTO);

    /**
     * 更新备件品牌
     *
     * @param spareBrandDTO 备件品牌数据
     * @return 备件品牌信息
     */
    SpareBrandDTO updateBrand(SpareBrandDTO spareBrandDTO);

    /**
     * 删除备件品牌信息
     *
     * @param id 备件品牌信息id
     */
    void deleteSpareBrand(Integer id);

    /**
     * 分页查询
     *
     * @param spareBrandQuery 品牌查询条件
     * @return 分页信息
     */
    Page<SpareBrandDTO> getBrandByQuery(SpareBrandQuery spareBrandQuery);

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    ResultVO<ExcelExportDto> getBrandImportTemplate() throws IOException, IllegalAccessException;

    /**
     * 获取导入模板
     *
     * @return 导入模板
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    ResponseEntity<byte[]> getBrandImportTemplateLink() throws IOException, IllegalAccessException;

    /**
     * 导入所有备件品牌信息信息
     *
     * @param file 信息excel
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    ImportResult importSpareBrand(MultipartFile file) throws IllegalAccessException, ParseException, IOException;

    /**
     * 导出品牌信息
     *
     * @return 品牌信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    ResponseEntity<byte[]> exportSpareBrandLink() throws IOException, IllegalAccessException;

    /**
     * 导出品牌信息
     *
     * @return 品牌信息excel表
     * @throws IOException            io异常
     * @throws IllegalAccessException 访问异常
     */
    ResultVO<ExcelExportDto> exportSpareBrand() throws IOException, IllegalAccessException;

}
