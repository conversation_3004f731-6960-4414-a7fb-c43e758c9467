package com.hvisions.eam.service.inspect.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.google.common.collect.Maps;
import com.hvisions.activiti.client.ActivitiClient;
import com.hvisions.activiti.dto.instance.ProcessInstanceStartDTO;
import com.hvisions.activiti.dto.process.ProcessInstanceDTO;
import com.hvisions.activiti.dto.task.TaskHandleDTO;
import com.hvisions.activiti.dto.task.TaskWithVariableDTO;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.utils.SerialUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dao.inspect.InspectPlanMapper;
import com.hvisions.eam.dto.SysBaseDTO;
import com.hvisions.eam.dto.inspect.item.InspectItemDTO;
import com.hvisions.eam.dto.inspect.plan.*;
import com.hvisions.eam.entity.SysBase;
import com.hvisions.eam.entity.inspect.*;
import com.hvisions.eam.enums.InspectErrorExceptionEnum;
import com.hvisions.eam.enums.PlanConditionEnum;
import com.hvisions.eam.repository.inspect.*;
import com.hvisions.eam.service.inspect.InspectPlanService;
import com.hvisions.hiperbase.equipment.EquipmentDTO;
import com.hvisions.framework.client.AuthClient;
import com.hvisions.framework.dto.user.UserInfoDto;
import com.hvisions.hipertools.client.TimerClient;
import com.hvisions.hipertools.dto.TimerDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title: InspectPlanServiceImpl</p >
 * <p>Description: 点检计划实现类</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/4/13</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class InspectPlanServiceImpl implements InspectPlanService {
    private final InspectPlanRepository inspectPlanRepository;
    private final InspectServiceHandler serviceHandler;
    private final AuthClient authClient;
    private final TimerClient timerClient;
    private final ActivitiClient activitiClient;
    private final InspectItemFileRepository inspectItemFileRepository;
    private final InspectItemRepository inspectItemRepository;
    private final SerialUtil serialUtil;
    private final InspectStatisticalRepository inspectStatisticalRepository;
    private final InspectPlanItemRepository inspectPlanItemRepository;
    private final InspectPlanMapper planMapper;
    private final InspectPlanFileRepository inspectPlanFileRepository;
    /**
     * 计划是否需要审批
     */
    @Value("${h-visions.approve}")
    private Boolean approve;

    @Autowired
    public InspectPlanServiceImpl(InspectPlanRepository inspectPlanRepository,
                                  InspectServiceHandler serviceHandler, AuthClient authClient, TimerClient timerClient,
                                  ActivitiClient activitiClient,
                                  InspectItemFileRepository inspectItemFileRepository,
                                  InspectItemRepository inspectItemRepository,
                                  SerialUtil serialUtil,
                                  InspectStatisticalRepository inspectStatisticalRepository,
                                  InspectPlanItemRepository inspectPlanItemRepository,
                                  InspectPlanMapper planMapper, InspectPlanFileRepository inspectPlanFileRepository) {
        this.inspectPlanRepository = inspectPlanRepository;
        this.serviceHandler = serviceHandler;
        this.authClient = authClient;
        this.timerClient = timerClient;
        this.activitiClient = activitiClient;
        this.inspectItemFileRepository = inspectItemFileRepository;
        this.inspectItemRepository = inspectItemRepository;
        this.serialUtil = serialUtil;
        this.inspectStatisticalRepository = inspectStatisticalRepository;
        this.inspectPlanItemRepository = inspectPlanItemRepository;
        this.planMapper = planMapper;
        this.inspectPlanFileRepository = inspectPlanFileRepository;
    }

    /**
     * 新增点检计划(不带内容)
     *
     * @param dto 点检计划DTO
     * @return 新增计划id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createInspectPlan(InspectPlanDTO dto) {
        Assert.isNull(dto.getId(), "新增的时候不能传递id");
        valid(dto);
        //计划编码
        String inspectPlanNum = serialUtil.getSerialNumber("inspectPlan");
        HvEamInspectPlan hvEamInspectPlan = DtoMapper.convert(dto, HvEamInspectPlan.class);
        hvEamInspectPlan.setInspectPlanNum(inspectPlanNum);
        hvEamInspectPlan.setCandidateGroups(StringUtils.join(dto.getCandidateGroups(), "#"));
        hvEamInspectPlan.setCandidateUsers(StringUtils.join(dto.getCandidateUsers(), "#"));
        if (dto.getDeadline() != null) {
            hvEamInspectPlan.setDeadline(dto.getDeadline());
        }
        Integer inspectPlanId;
        inspectPlanId = inspectPlanRepository.save(hvEamInspectPlan).getId();
        //建立关系
        List<HvEamInspectPlanItem> itemEntityList = new ArrayList<>();
        int index = 1;
        for (InspectPlanContentDTO inspectPlanItemDTO : dto.getInspectPlanContentDTOList()) {
            for (InspectItemDTO inspectItemId : inspectPlanItemDTO.getItemDTOList()) {
                HvEamInspectPlanItem itemEntity = new HvEamInspectPlanItem();
                itemEntity.setEquipmentId(inspectPlanItemDTO.getEquipmentId());
                itemEntity.setInspectPlanId(inspectPlanId);
                itemEntity.setPriority(index++);
                itemEntity.setInspectItemId(inspectItemId.getId());
                itemEntityList.add(itemEntity);
            }
        }
        inspectPlanItemRepository.saveAll(itemEntityList);
        //需要审批
        if (approve) {
            //启用
            if (dto.getStartUsing()) {
                log.info("新增点巡检计划，计划配置需要审批，启用，状态设置为待审批，发起审批");
                hvEamInspectPlan.setInspectPlanConditionId(PlanConditionEnum.TO_BE_APPROVED.getCode());

                log.info("创建审批流程" + inspectPlanId);
                //启动审批流程
                startProcess(dto, inspectPlanId);
            } else {
                //未启用
                log.info("新增点巡检计划，计划配置需要审批，未启用，状态设置为未启用，不发起审批流程");
                hvEamInspectPlan.setInspectPlanConditionId(PlanConditionEnum.NOT_ENABLED.getCode());
                inspectPlanId = inspectPlanRepository.save(hvEamInspectPlan).getId();
            }
        } else {
            //计划配置无需审批
            //启用
            if (dto.getStartUsing()) {
                log.info("新增点巡检计划，计划配置为不需要审批，启用，状态设置为无需审批");
                //点检计划审批状态初始为待审批
                hvEamInspectPlan.setInspectPlanConditionId(PlanConditionEnum.NO_APPROVE.getCode());
            } else {
                //未启用
                log.info("新增点巡检计划，计划配置为不需要审批，未启用，状态设置为未启用");
                //点检计划审批状态初始为待审批
                hvEamInspectPlan.setInspectPlanConditionId(PlanConditionEnum.NOT_ENABLED.getCode());
            }
            inspectPlanId = inspectPlanRepository.save(hvEamInspectPlan).getId();
        }
        if (dto.getFiles() != null) {
            List<HvEamInspectPlanFile> files = DtoMapper.convertList(dto.getFiles(), HvEamInspectPlanFile.class);
            for (HvEamInspectPlanFile file : files) {
                file.setInspectPlanId(inspectPlanId);
            }
            inspectPlanFileRepository.saveAll(files);
        }
        return inspectPlanId;
    }


    /**
     * 编辑点检计划(不带内容)
     *
     * @param dto 点检计划DTO
     * @return 被编辑的id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateInspectPlan(InspectPlanDTO dto) {
        if (dto.getId() == null) {
            throw new BaseKnownException(10000, "修改时候需要传递计划id");
        }
        valid(dto);
        Integer planId = dto.getId();

        //点检计划审批状态id
        Integer inspectContentId = inspectPlanRepository.getOne(planId).getInspectPlanConditionId();
        //删除旧的数据
        inspectPlanItemRepository.deleteAllByInspectPlanId(planId);
        inspectItemRepository.flush();
        //建立关系
        int index = 1;
        List<HvEamInspectPlanItem> itemEntityList = new ArrayList<>();
        for (InspectPlanContentDTO inspectPlanItemDTO : dto.getInspectPlanContentDTOList()) {
            for (InspectItemDTO inspectItem : inspectPlanItemDTO.getItemDTOList()) {
                HvEamInspectPlanItem itemEntity = new HvEamInspectPlanItem();
                itemEntity.setEquipmentId(inspectPlanItemDTO.getEquipmentId());
                itemEntity.setInspectPlanId(dto.getId());
                itemEntity.setPriority(index++);
                itemEntity.setInspectItemId(inspectItem.getId());
                itemEntityList.add(itemEntity);
            }
        }
        inspectPlanItemRepository.saveAll(itemEntityList);

        Integer id;
        //待审批，审批通过的不允许修改
        if (inspectContentId.equals(PlanConditionEnum.TO_BE_APPROVED.getCode()) || inspectContentId.equals(PlanConditionEnum.PASSED.getCode())) {
            throw new BaseKnownException(InspectErrorExceptionEnum.UPDATE_PLAN_ERROR);
        }
        HvEamInspectPlan hvEamInspectPlan = DtoMapper.convert(dto, HvEamInspectPlan.class);
        if (dto.getDeadline() != null) {
            hvEamInspectPlan.setDeadline(dto.getDeadline());
        }
        hvEamInspectPlan.setCandidateGroups(StringUtils.join(dto.getCandidateGroups(), "#"));
        hvEamInspectPlan.setCandidateUsers(StringUtils.join(dto.getCandidateUsers(), "#"));
        if (approve) {
            //启用
            if (dto.getStartUsing()) {

                hvEamInspectPlan.setInspectPlanConditionId(PlanConditionEnum.TO_BE_APPROVED.getCode());
                id = inspectPlanRepository.save(hvEamInspectPlan).getId();
                //启动审批流程
                log.info("编辑点巡检计划，需审批，准备启动审批流程");
                startProcess(dto, id);
            } else {
                //未启用
                hvEamInspectPlan.setInspectPlanConditionId(PlanConditionEnum.NOT_ENABLED.getCode());
                id = inspectPlanRepository.save(hvEamInspectPlan).getId();
            }
        } else {
            //驳回，无需审批，未启用状态的计划不需要审批功能时，不需要启动审批流程
            //启用，状态设置为无需审批
            if (dto.getStartUsing()) {
                hvEamInspectPlan.setInspectPlanConditionId(PlanConditionEnum.NO_APPROVE.getCode());
            } else {
                //未启用，状态设置为未启用
                hvEamInspectPlan.setInspectPlanConditionId(PlanConditionEnum.NOT_ENABLED.getCode());
            }
            id = inspectPlanRepository.save(hvEamInspectPlan).getId();
        }
        inspectPlanFileRepository.deleteAllByInspectPlanId(id);
        inspectPlanFileRepository.flush();
        if (dto.getFiles() != null) {
            List<HvEamInspectPlanFile> files = DtoMapper.convertList(dto.getFiles(), HvEamInspectPlanFile.class);
            for (HvEamInspectPlanFile file : files) {
                file.setInspectPlanId(id);
            }
            inspectPlanFileRepository.saveAll(files);
        }
        return id;
    }

    /**
     * 对象进行审批
     *
     * @param dto 点巡检计划
     */
    private void valid(InspectPlanDTO dto) {
        if (approve) {
            if (dto.getCheckerId() == null) {
                throw new BaseKnownException(InspectErrorExceptionEnum.CHECKER_IS_NULL);
            }
        }
        if ((dto.getCandidateGroups() == null || dto.getCandidateGroups().size() == 0) &&
            (dto.getCandidateUsers() == null || dto.getCandidateUsers().size() == 0) &&
            dto.getTransactorId() == null) {
            throw new BaseKnownException(InspectErrorExceptionEnum.ASSIGNEE_IS_NULL);
        }
        if (CollectionUtil.isEmpty(dto.getInspectPlanContentDTOList())) {
            throw new BaseKnownException("点巡检项目信息不能为空");
        }
        for (InspectPlanContentDTO contentDTO : dto.getInspectPlanContentDTOList()) {
            if (CollectionUtil.isEmpty(contentDTO.getItemDTOList())) {
                throw new BaseKnownException("设备:" + contentDTO.getEquipmentName() + "项目信息不能为空");
            }
        }
    }

    /**
     * 批量删除计划
     *
     * @param idList id集合
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteInspectPlanByIdList(List<Integer> idList) {
        for (Integer id : idList) {
            HvEamInspectPlan plan = inspectPlanRepository.getOne(id);
            Integer planConditionId = plan.getInspectPlanConditionId();
            //审批通过，待审批状态的不可删除
            if (planConditionId.equals(PlanConditionEnum.PASSED.getCode()) || planConditionId.equals(PlanConditionEnum.TO_BE_APPROVED.getCode())) {
                throw new BaseKnownException(InspectErrorExceptionEnum.DELETE_PLAN_ERROR);
            }
            inspectPlanFileRepository.deleteAllByInspectPlanId(id);
            inspectPlanItemRepository.deleteAllByInspectPlanId(id);
            inspectPlanRepository.deleteById(id);
        }
    }


    /**
     * 查询点检计划
     *
     * @param inspectPlanQueryDTO 点检计划查询条件
     * @return 点检计划分页
     */
    @Override
    public Page<InspectPlanDTO> getPage(InspectPlanQueryDTO inspectPlanQueryDTO) {
        //匹配查询
        Page<InspectPlanDTO> result = PageHelperUtil.getPage(planMapper::getPage, inspectPlanQueryDTO);
        //根据timerIdList获取timer信息
        List<Integer> timerIdList = result.stream().map(InspectPlanDTO::getTimerId).collect(Collectors.toList());
        ResultVO<List<TimerDTO>> timerInfo = timerClient.getByTimerIds(timerIdList);
        if (timerInfo.isSuccess()) {
            List<TimerDTO> timerData = timerInfo.getData();
            for (InspectPlanDTO inspectPlanDTO : result) {
                inspectPlanDTO.setTimerDescription(timerData
                    .stream()
                    .filter(timer -> timer.getId().equals(inspectPlanDTO.getTimerId()))
                    .findFirst()
                    .map(TimerDTO::getDescription)
                    .orElse(null));
            }
        }
        //查询相关的idList获取人员信息
        List<Integer> checkerIds = result.stream().map(InspectPlanDTO::getCheckerId).collect(Collectors.toList());
        List<Integer> transactorIds = result.stream().map(InspectPlanDTO::getTransactorId).collect(Collectors.toList());
        checkerIds.addAll(transactorIds);
        ResultVO<List<UserInfoDto>> checkerInfo = authClient.getUserInfoByIds(checkerIds);
        for (InspectPlanDTO one : result) {
            updateUserInfo(one, checkerInfo);
            //获取点检计划审批状态名称
            Integer inspectPlanConditionId = one.getInspectPlanConditionId();
            String name = PlanConditionEnum.valueOf(inspectPlanConditionId).getName();
            one.setInspectPlanConditionName(name);
        }
        //设置点巡检项目数量
        List<HvEamInspectPlanItem> items = inspectPlanItemRepository.findAllByInspectPlanIdIn(result.stream()
            .map(SysBaseDTO::getId).collect(Collectors.toList()));
        for (InspectPlanDTO maintainPlanDTO : result) {
            maintainPlanDTO.setItemCount(items.stream()
                .filter(t -> t.getInspectPlanId().equals(maintainPlanDTO.getId()))
                .count());
        }
        //设置计划里面的设备名称信息
        List<EquipmentDTO> list = serviceHandler.getList(items.stream()
            .map(HvEamInspectPlanItem::getEquipmentId)
            .collect(Collectors.toList()));
        for (InspectPlanDTO dto : result) {
            List<HvEamInspectPlanItem> planEquipments = items.stream()
                .filter(t -> t.getInspectPlanId().equals(dto.getId()))
                .collect(Collectors.toList());
            List<String> collect = list.stream()
                .filter(t -> planEquipments.stream()
                    .anyMatch(e -> e.getEquipmentId().equals(t.getId())))
                .map(t -> t.getEquipmentName() + "(" + t.getEquipmentCode() + ")")
                .collect(Collectors.toList());
            dto.setEquipmentNames(String.join(",", collect));
        }
        return result;
    }

    /**
     * 根据计划id查询计划详情
     *
     * @param planId 计划id
     * @return 点巡检计划详细信息
     */
    @Override
    public InspectPlanDTO getDetail(Integer planId) {
        //获取相关数据
        HvEamInspectPlan inspectPlan = inspectPlanRepository.findById(planId).orElse(null);
        if (inspectPlan == null) {
            return null;
        }
        //计划和项目关系
        List<HvEamInspectPlanItem> planItems = inspectPlanItemRepository.findAllByInspectPlanId(planId);
        //根据计划id查询关联关系
        List<HvEamInspectItem> items = inspectItemRepository.findAllById(planItems.
            stream().map(HvEamInspectPlanItem::getInspectItemId).collect(Collectors.toList()));
        //获取所有相关的文件id
        List<HvEamInspectItemFile> filesEntity = inspectItemFileRepository.findAllByInspectItemIdIn(items.stream()
            .map(SysBase::getId)
            .collect(Collectors.toList()));
        InspectPlanDTO result = DtoMapper.convert(inspectPlan, InspectPlanDTO.class);
        if (inspectPlan.getCandidateGroups() != null) {
            result.setCandidateGroups(Arrays.asList(StringUtils.split(inspectPlan.getCandidateGroups(), "#")));
        }
        if (inspectPlan.getCandidateUsers() != null) {
            result.setCandidateUsers(Arrays.asList(StringUtils.split(inspectPlan.getCandidateUsers(), "#")));
        }
        //如果timer中不存在本计划绑定的timer则设置timerId为null
        Integer timerId = result.getTimerId();
        List<Integer> timerIdList = new ArrayList<>();
        timerIdList.add(timerId);
        ResultVO<List<TimerDTO>> timerInfo = timerClient.getByTimerIds(timerIdList);
        List<TimerDTO> timerData = timerInfo.getData();
        if (timerInfo.isSuccess()) {
            List<Integer> timerIds = timerData.stream().map(TimerDTO::getId).collect(Collectors.toList());
            if (!timerIds.contains(result.getTimerId())) {
                result.setTimerId(null);
            }
        }
        //设置文件信息
        List<HvEamInspectPlanFile> fileInfo = inspectPlanFileRepository.findAllByInspectPlanId(planId);
        result.setFiles(DtoMapper.convertList(fileInfo, FileInfo.class));
        //关联的设备id
        List<Integer> equipmentIds = planItems.stream().map(HvEamInspectPlanItem::getEquipmentId).collect(Collectors.toList());
        List<EquipmentDTO> equipmentInfoData = serviceHandler.findEquipmentDTO(equipmentIds);
        //每个设备一个InspectPlanContentDTO
        List<InspectPlanContentDTO> inspectPlanContentDTOS = new ArrayList<>();
        if (equipmentInfoData != null && equipmentInfoData.size() > 0) {
            for (EquipmentDTO equipment : equipmentInfoData) {
                InspectPlanContentDTO contentDTO = new InspectPlanContentDTO();
                contentDTO.setEquipmentId(equipment.getId());
                contentDTO.setEquipmentCode(equipment.getEquipmentCode());
                for (HvEamInspectPlanItem planItem : planItems) {
                    if (planItem.getInspectPlanId().equals(planId)) {
                        if (contentDTO.getEquipmentId().equals(planItem.getEquipmentId())) {
                            contentDTO.setPriority(planItem.getPriority());
                        }
                    }
                }
                contentDTO.setEquipmentName(equipment.getEquipmentName());
                //设置下面的项目信息
                //获取对应的项目id列表
                List<Integer> itemIds = planItems.stream()
                    .filter(t -> t.getEquipmentId().equals(equipment.getId()))
                    .map(HvEamInspectPlanItem::getInspectItemId)
                    .collect(Collectors.toList());
                List<InspectItemDTO> itemDTOS = DtoMapper.convertList(items
                    .stream()
                    .filter(t -> itemIds.contains(t.getId()))
                    .collect(Collectors.toList()), InspectItemDTO.class);
                //找对应的文件信息，塞进去
                for (InspectItemDTO itemDTO : itemDTOS) {
                    itemDTO.setFileIds(filesEntity.stream().filter(t -> t.getInspectItemId().equals(itemDTO.getId()))
                        .map(HvEamInspectItemFile::getFileId)
                        .collect(Collectors.toList()));
                }
                contentDTO.setItemDTOList(itemDTOS);
                inspectPlanContentDTOS.add(contentDTO);
            }
        }
        //设置顺序号优先级
        inspectPlanContentDTOS.sort(Comparator.comparing(InspectPlanContentDTO::getPriority));
        result.setInspectPlanContentDTOList(inspectPlanContentDTOS);
        //审核人idList获取人员信息
        List<Integer> checkerIdList = new ArrayList<>();
        Integer checkerId = result.getCheckerId();
        checkerIdList.add(checkerId);
        ResultVO<List<UserInfoDto>> checkerInfo = authClient.getUserInfoByIds(checkerIdList);
        List<UserInfoDto> checkerData = checkerInfo.getData();
        //获取审核人姓名
        getCheckerName(result, checkerInfo, checkerData);
        //执行人idList获取人员信息
        List<Integer> transactorIdList = new ArrayList<>();
        Integer transactorId = result.getTransactorId();
        transactorIdList.add(transactorId);
        ResultVO<List<UserInfoDto>> transactorInfo = authClient.getUserInfoByIds(transactorIdList);
        List<UserInfoDto> transactorData = transactorInfo.getData();
        //获取执行人姓名
        getTransactorName(result, transactorInfo, transactorData);
        //获取点检计划审批状态名称
        Integer inspectPlanConditionId = result.getInspectPlanConditionId();
        String name = PlanConditionEnum.valueOf(inspectPlanConditionId).getName();
        result.setInspectPlanConditionName(name);
        return result;
    }

    @Override
    public String createTemporaryTask(InspectPlanDTO dto) {
        if ((dto.getCandidateGroups() == null || dto.getCandidateGroups().size() == 0) &&
            (dto.getCandidateUsers() == null || dto.getCandidateUsers().size() == 0) &&
            dto.getTransactorId() == null) {
            throw new BaseKnownException(InspectErrorExceptionEnum.ASSIGNEE_IS_NULL);
        }
        if (CollectionUtil.isEmpty(dto.getInspectPlanContentDTOList())) {
            throw new BaseKnownException("点巡检项目信息不能为空");
        }
        for (InspectPlanContentDTO contentDTO : dto.getInspectPlanContentDTOList()) {
            if (CollectionUtil.isEmpty(contentDTO.getItemDTOList())) {
                throw new BaseKnownException("设备:" + contentDTO.getEquipmentName() + "项目信息不能为空");
            }
        }
        return startWorkProcess(dto);
    }

    /**
     * 审批通过点巡检计划
     *
     * @param taskIds  任务id
     * @param userInfo 用户信息
     * @param token    token
     */
    @Override
    public void startPlanById(List<String> taskIds, UserInfoDTO userInfo, String token) {
        //遍历任务id
        for (String taskId : taskIds) {
            //根据任务id获取任务信息
            ResultVO<TaskWithVariableDTO> taskInfo = activitiClient.getTaskByTaskId(taskId);
            if (!taskInfo.isSuccess()) {
                continue;
            }
            TaskWithVariableDTO taskData = taskInfo.getData();
            Map<String, Object> variableMap = taskData.getVariableList();
            //在任务参数中获取对应的计划id
            Integer planId = (Integer) variableMap.get("planId");
            HvEamInspectPlan plan = inspectPlanRepository.getOne(planId);
            Integer planConditionId = plan.getInspectPlanConditionId();
            //判断计划状态，是待审批计划
            if (!planConditionId.equals(PlanConditionEnum.TO_BE_APPROVED.getCode())) {
                log.error("根据任务id获取任务信息失败（ activitiClient.getTaskByTaskId(taskId)）");
                throw new BaseKnownException(InspectErrorExceptionEnum.START_PLAN_ERROR);
            }
            //修改对应计划的审批状态
            plan.setInspectPlanConditionId(PlanConditionEnum.PASSED.getCode());
            plan.setRejectReason(null);
            Integer id = inspectPlanRepository.save(plan).getId();
            log.info("点巡检计划审批通过---计划id为：" + id);
            TaskHandleDTO taskHandleDTO = new TaskHandleDTO();
            Map<String, Object> variables = taskHandleDTO.getVariables();
            variables.put("approvalResult", "审批通过");
            variables.put("approvalUserId", Optional.ofNullable(userInfo).map(UserInfoDTO::getId).orElse(0));
            variables.put("approvalUserName", Optional.ofNullable(userInfo).map(UserInfoDTO::getUserName).orElse(""));
            taskHandleDTO.setForce(true);
            taskHandleDTO.setTaskId(taskId);
            //完成任务
            activitiClient.completeTask(taskHandleDTO, token);
            log.info("计划审批通过，审批任务完成");
        }
    }

    /**
     * 驳回计划
     *
     * @param inspectPlanRejectDTO 驳回dto
     * @param userInfo             用户信息
     * @param token                token
     */
    @Override
    public void refusePlanById(InspectPlanRejectDTO inspectPlanRejectDTO, UserInfoDTO userInfo, String token) {
        ResultVO<TaskWithVariableDTO> taskInfo = activitiClient.getTaskByTaskId(inspectPlanRejectDTO.getTaskId());
        if (!taskInfo.isSuccess()) {
            return;
        }
        TaskWithVariableDTO taskData = taskInfo.getData();
        Map<String, Object> variableMap = taskData.getVariableList();
        //在任务参数中获取对应的计划id
        Integer planId = (Integer) variableMap.get("planId");
        HvEamInspectPlan plan = inspectPlanRepository.getOne(planId);
        Integer planConditionId = plan.getInspectPlanConditionId();
        //判断计划状态，是待审批计划
        if (!planConditionId.equals(PlanConditionEnum.TO_BE_APPROVED.getCode())) {
            log.error("根据任务id获取任务信息失败:{}", planConditionId);
            throw new BaseKnownException(InspectErrorExceptionEnum.REFUSE_PLAN_ERROR);
        }
        plan.setInspectPlanConditionId(PlanConditionEnum.REJECT.getCode());
        plan.setRejectReason(inspectPlanRejectDTO.getRejectReason());
        Integer id = inspectPlanRepository.save(plan).getId();
        log.info("点巡检计划审批驳回---计划id为：" + id);
        TaskHandleDTO taskHandleDTO = new TaskHandleDTO();
        Map<String, Object> variables = taskHandleDTO.getVariables();
        variables.put("approvalResult", "驳回");
        variables.put("rejectReason", inspectPlanRejectDTO.getRejectReason());
        variables.put("approvalUserId", Optional.ofNullable(userInfo).map(UserInfoDTO::getId).orElse(0));
        variables.put("approvalUserName", Optional.ofNullable(userInfo).map(UserInfoDTO::getUserName).orElse(""));
        taskHandleDTO.setTaskId(inspectPlanRejectDTO.getTaskId());
        taskHandleDTO.setForce(true);
        //完成任务
        activitiClient.completeTask(taskHandleDTO, token);
        log.info("计划驳回，审批任务完成");
    }

    /**
     * 强制停用计划
     *
     * @param ids id列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void forcedStopInspectPlan(List<Integer> ids) {
        for (Integer id : ids) {
            HvEamInspectPlan plan = inspectPlanRepository.getOne(id);
            //审批通过，无需审批，可以强制停用
            if (!plan.getInspectPlanConditionId().equals(PlanConditionEnum.PASSED.getCode()) && !plan.getInspectPlanConditionId().equals(PlanConditionEnum.NO_APPROVE.getCode())) {
                throw new BaseKnownException(InspectErrorExceptionEnum.STOP_ERROR);
            }
            plan.setStartUsing(false);
            plan.setInspectPlanConditionId(PlanConditionEnum.FORCED_STOP.getCode());
            inspectPlanRepository.save(plan);

        }
    }

    /**
     * 根据timerId查看绑定次数
     *
     * @param timerId timerId
     * @return 次数
     */
    @Override
    public Integer checkBindingByTimerId(Integer timerId) {
        List<HvEamInspectPlan> all = inspectPlanRepository.findAllByTimerId(timerId);
        return all.size();
    }

    /**
     * 根据点巡检计划编码查询
     *
     * @param inspectPlanNum 点巡检计划编码
     * @return 保养计划
     */
    @Override
    public InspectPlanDTO findByInspectPlanNum(String inspectPlanNum) {
        HvEamInspectPlan plan = inspectPlanRepository.findByInspectPlanNum(inspectPlanNum);
        if (plan == null) {
            return null;
        }
        return getDetail(plan.getId());
    }

    /**
     * 通过计划id手动触发计划
     *
     * @param planId 任务id
     */
    @Override
    public String triggerPlanById(Integer planId) {
        Optional<HvEamInspectPlan> result = inspectPlanRepository.findById(planId);
        if (!result.isPresent()) {
            return "";
        }
        return startWorkProcess(getDetail(result.get().getId()));
    }


    /**
     * 启动流程
     *
     * @param plan 计划详情
     * @return 流程实例id
     */
    private String startWorkProcess(InspectPlanDTO plan) {
        ProcessInstanceStartDTO processInstanceStartDTO = new ProcessInstanceStartDTO();
        Map<String, Object> map = Maps.newHashMap();
        processInstanceStartDTO.setBusinessKey(plan.getId() == null ? "" : plan.getId().toString());
        processInstanceStartDTO.setProcessDefinitionKey("equipment-inspect");
        for (int i = 0; i < plan.getInspectPlanContentDTOList().size(); i++) {
            plan.getInspectPlanContentDTOList().get(i).setId(i + 1);
        }
        map.put("inspectPlanContentDTOList", plan.getInspectPlanContentDTOList());
        //任务工时
        map.put("manHourString", plan.getManHourString());
        //审核人id
        map.put("checkerId", plan.getCheckerId());
        map.put("category", "equipment-inspect");
        map.put("checkerName", plan.getCheckerName());
        //执行人id
        map.put("executor_id", plan.getTransactorId());
        map.put("executorName", plan.getTransactorName());
        map.put("taskNum", plan.getInspectPlanNum());
        //任务名称为计划名称
        map.put("taskName", plan.getInspectPlanName());
        //获取全部的设备名称并 去重
        map.put("equipmentName", plan.getInspectPlanContentDTOList().stream()
            .map(InspectPlanContentDTO::getEquipmentName)
            .collect(Collectors.toList()).toString());
        //设置候选人，候选人组
        map.put("candidateUsers", plan.getCandidateUsers());
        map.put("candidateGroups", plan.getCandidateGroups());
        //任务完成时间为：生成任务时间加上任务期限
        Calendar ca = Calendar.getInstance();
        ca.add(Calendar.DATE, plan.getDeadline());
        Date d = ca.getTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String taskEndTime = format.format(d);
        map.put("taskEndTime", taskEndTime);
        processInstanceStartDTO.setVariables(map);
        log.info("开始创建任务");
        ResultVO<ProcessInstanceDTO> processInstanceDTOResultVO = activitiClient.startProcessInstance(processInstanceStartDTO);
        if (!processInstanceDTOResultVO.isSuccess()) {
            throw new BaseKnownException(processInstanceDTOResultVO);
        }
        ProcessInstanceDTO data = processInstanceDTOResultVO.getData();
        String processInstanceId = data.getProcessInstanceId();
        log.info("从消息队列接受消息并 创建流程记录");
        log.info(plan.toString() + "    " + processInstanceId);
        //创建流程记录
        createProcessRecord(plan, processInstanceId);
        return processInstanceId;
    }


    /**
     * 创建流程记录
     *
     * @param plan              计划
     * @param processInstanceId 流程id
     */
    private void createProcessRecord(InspectPlanDTO plan, String processInstanceId) {
        for (InspectPlanContentDTO planContentDTO : plan.getInspectPlanContentDTOList()) {
            HvEamInspectStatistical hvEamInspectStatistical = new HvEamInspectStatistical();
            //流程id
            hvEamInspectStatistical.setProcessInstanceId(processInstanceId);
            //设备id
            hvEamInspectStatistical.setEquipmentId(planContentDTO.getEquipmentId());
            //设备名称
            hvEamInspectStatistical.setEquipmentName(planContentDTO.getEquipmentName());
            //设备Code
            hvEamInspectStatistical.setEquipmentCode(planContentDTO.getEquipmentCode());
            Integer id = inspectStatisticalRepository.save(hvEamInspectStatistical).getId();
            log.info("创建流程记录，记录id为：{}", id);
        }
    }

    /**
     * 获取执行人信息
     *
     * @param inspectPlanDTO 点巡检计划DTO
     * @param transactorInfo 执行人信息
     * @param transactorData 执行人信息DATA
     */
    private void getTransactorName(InspectPlanDTO inspectPlanDTO, ResultVO<List<UserInfoDto>> transactorInfo, List<UserInfoDto> transactorData) {
        if (!transactorInfo.isSuccess()) {
            inspectPlanDTO.setTransactorName("---");
        }
        for (UserInfoDto user : transactorData) {
            if (user.getId().equals(inspectPlanDTO.getTransactorId())) {
                inspectPlanDTO.setTransactorName(user.getUserName());
                break;
            } else {
                inspectPlanDTO.setTransactorName("---");
            }
        }
    }

    /**
     * 获取审核人信息
     *
     * @param inspectPlanDTO 点巡检计划DTO
     * @param checkerInfo    审核人信息
     * @param checkerData    审核人信息DATA
     */
    private void getCheckerName(InspectPlanDTO inspectPlanDTO, ResultVO<List<UserInfoDto>> checkerInfo, List<UserInfoDto> checkerData) {
        if (!checkerInfo.isSuccess()) {
            inspectPlanDTO.setCheckerName("---");
        } else {
            inspectPlanDTO.setCheckerName(checkerData.stream()
                .filter(t -> t.getId().equals(inspectPlanDTO.getCheckerId()))
                .findAny()
                .map(UserInfoDto::getUserName)
                .orElse("---"));
        }
    }


    /**
     * 启动审批流程
     *
     * @param planDTO       点巡检计划DTO
     * @param inspectPlanId 计划id
     */
    private void startProcess(InspectPlanDTO planDTO, Integer inspectPlanId) {
        log.info("创建审批流程");
        //设置参数
        Map<String, Object> map = Maps.newHashMap();
        map.put("checker_id", planDTO.getCheckerId());
        map.put("planName", planDTO.getInspectPlanName());
        map.put("planId", inspectPlanId);
        map.put("remark", planDTO.getRemark());
        map.put("rejectReason", planDTO.getRejectReason());
        //任务结束时间
        Calendar ca = Calendar.getInstance();
        Integer num = planDTO.getDeadline();
        ca.add(Calendar.DATE, num);
        Date d = ca.getTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String taskEndTime = format.format(d);
        map.put("taskEndTime", taskEndTime);
        log.info("任务结束时间设置完成");
        //任务描述增加timer描述
        Integer timerId = planDTO.getTimerId();
        if (timerId != null) {
            ResultVO<TimerDTO> timerInfo = timerClient.getByTimerId(timerId);
            if (timerInfo.isSuccess()) {
                TimerDTO timerDTO = timerInfo.getData();
                map.put("timerDesc", timerDTO.getDescription());
                log.info("timer描述获取完成");
            }
        } else {
            map.put("timerDesc", "未配置生成任务周期");
        }
        //计划详细信息
        map.put("inspectPlanDTO", planDTO);
        map.put("manHour", planDTO.getManHourString());
        ProcessInstanceStartDTO processInstanceStartDTO = new ProcessInstanceStartDTO();
        processInstanceStartDTO.setProcessDefinitionKey("inspect-approve");
        processInstanceStartDTO.setBusinessKey(inspectPlanId.toString());
        processInstanceStartDTO.setVariables(map);
        ResultVO<ProcessInstanceDTO> processInstanceDTOResultVO = activitiClient.startProcessInstance(processInstanceStartDTO);
        if (processInstanceDTOResultVO.isSuccess()) {
            String processInstanceId = processInstanceDTOResultVO.getData().getProcessInstanceId();
            log.info("点巡检审批流程启动,流程id为--" + processInstanceId);
        } else {
            log.warn("启动点巡检审批流程失败,{}", processInstanceDTOResultVO.getMessage());
        }
    }


    private void updateUserInfo(InspectPlanDTO one, ResultVO<List<UserInfoDto>> checkerInfo) {
        if (!checkerInfo.isSuccess()) {
            one.setCheckerName("N/A");
            one.setTransactorName("N/A");
            return;
        }
        List<UserInfoDto> data = checkerInfo.getData();
        if (data == null) {
            one.setCheckerName("N/A");
            one.setTransactorName("N/A");
            return;
        }
        one.setCheckerName(data
            .stream()
            .filter(t -> t.getId().equals(one.getCheckerId()))
            .map(UserInfoDto::getUserName)
            .findFirst()
            .orElse("N/A")
        );
        one.setTransactorName(data
            .stream()
            .filter(t -> t.getId().equals(one.getTransactorId()))
            .map(UserInfoDto::getUserName)
            .findFirst()
            .orElse("N/A")
        );
    }
}