package com.hvisions.eam.service.inspect.impl;

import com.hvisions.activiti.client.ActivitiClient;
import com.hvisions.activiti.client.HistoryClient;
import com.hvisions.activiti.client.RuntimeClient;
import com.hvisions.activiti.dto.history.HistoricProcessQuery;
import com.hvisions.activiti.dto.history.HistoricTaskInstanceDTO;
import com.hvisions.activiti.dto.history.HistoryProcessInstanceDTO;
import com.hvisions.activiti.dto.history.HistoryTaskQueryDTO;
import com.hvisions.activiti.dto.instance.ProcessInstanceQueryDTO;
import com.hvisions.activiti.dto.process.ProcessInstanceDTO;
import com.hvisions.activiti.dto.task.TaskHandleDTO;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.inspect.statistical.*;
import com.hvisions.eam.entity.inspect.HvEamInspectStatistical;
import com.hvisions.eam.entity.inspect.HvEamInspectStatisticalItem;
import com.hvisions.eam.repository.inspect.InspectStatisticalItemRepository;
import com.hvisions.eam.repository.inspect.InspectStatisticalRepository;
import com.hvisions.eam.service.inspect.InspectStatisticalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title: InspectStatisticalServiceImpl</p >
 * <p>Description: 点检统计实现</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/8</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class InspectStatisticalServiceImpl implements InspectStatisticalService {

    private final ActivitiClient activitiClient;
    private final InspectStatisticalRepository inspectStatisticalRepository;
    private final InspectStatisticalItemRepository inspectStatisticalItemRepository;

    private final RuntimeClient runtimeClient;

    private final HistoryClient historyClient;

    @Autowired
    public InspectStatisticalServiceImpl(ActivitiClient activitiClient, InspectStatisticalRepository inspectStatisticalRepository, InspectStatisticalItemRepository inspectStatisticalItemRepository, HistoryClient historyClient, RuntimeClient runtimeClient, HistoryClient historyClient1) {
        this.activitiClient = activitiClient;
        this.inspectStatisticalRepository = inspectStatisticalRepository;
        this.inspectStatisticalItemRepository = inspectStatisticalItemRepository;
        this.runtimeClient = runtimeClient;
        this.historyClient = historyClient1;
    }

    /**
     * 完成任务并存储统计信息
     *
     * @param taskId 任务id
     * @param token  token
     */
    @Override
    public void completeTaskAndStatistical(String taskId, String token) {
        //获取流程实例id
        ResultVO<Map<String, Object>> variableInstanceInfo = activitiClient.getVariableInstance(taskId);
        //完成任务
        TaskHandleDTO taskHandleDTO = new TaskHandleDTO();
        taskHandleDTO.setTaskId(taskId);
        activitiClient.completeTask(taskHandleDTO, token);
        //查询本条历史任务
        if (variableInstanceInfo.isSuccess()) {
            Map task = (Map) variableInstanceInfo.getData().get("h-visions-task_start_user_name");
            String processInstanceId = (String) task.get("processInstanceId");
            //根据流程实例id查询参数并存储到统计表
            HistoricProcessQuery historicProcessQuery = new HistoricProcessQuery();
            historicProcessQuery.setProcessDefinitionKey("equipment-inspect");
            historicProcessQuery.setProcessInstanceId(processInstanceId);
            historicProcessQuery.setFinished(true);
            ResultVO<List<HistoryProcessInstanceDTO>> historicProcessInstanceListInfo = activitiClient.getHistoricProcessInstanceList(historicProcessQuery);
            if (historicProcessInstanceListInfo.isSuccess()) {
                List<HistoryProcessInstanceDTO> historicProcessInstanceListInfoData = historicProcessInstanceListInfo.getData();
                for (HistoryProcessInstanceDTO historicProcessInstanceListInfoDatum : historicProcessInstanceListInfoData) {
                    //流程开始时间
                    Date startTime = historicProcessInstanceListInfoDatum.getStartTime();
                    //流程结束时间
                    Date endTime = historicProcessInstanceListInfoDatum.getEndTime();
                    Map<String, Object> processInstanceVariables = historicProcessInstanceListInfoDatum.getProcessInstanceVariables();
                    List inspectPlanContentDTOList = (List) processInstanceVariables.get("inspectPlanContentDTOList");
                    for (Object planContent : inspectPlanContentDTOList) {
                        HvEamInspectStatistical hvEamInspectStatistical = new HvEamInspectStatistical();
                        hvEamInspectStatistical.setProcessInstanceId(processInstanceId);
                        hvEamInspectStatistical.setStartTime(startTime);
                        hvEamInspectStatistical.setEndTime(endTime);
                        Map planContentMap = (Map) planContent;
                        //设备名称
                        String equipmentName = (String) planContentMap.get("equipmentName");
                        hvEamInspectStatistical.setEquipmentName(equipmentName);
                        //设备id
                        Integer equipmentId = (Integer) planContentMap.get("equipmentId");
                        //根据流程id和设备id查流程记录,得到id
                        HvEamInspectStatistical hvEamInspectStatisticalRecord = inspectStatisticalRepository.findByEquipmentIdAndProcessInstanceId(equipmentId, processInstanceId);
                        if (hvEamInspectStatisticalRecord != null) {
                            Integer id = hvEamInspectStatisticalRecord.getId();
                            hvEamInspectStatistical.setId(id);
                        }
                        hvEamInspectStatistical.setEquipmentId(equipmentId);
                        //设备编码
                        String equipmentCode = (String) planContentMap.get("equipmentCode");
                        hvEamInspectStatistical.setEquipmentCode(equipmentCode);
                        //工厂
                        String factory = (String) planContentMap.get("factory");
                        hvEamInspectStatistical.setFactory(factory);
                        //车间
                        String workShop = (String) planContentMap.get("workShop");
                        hvEamInspectStatistical.setWorkShop(workShop);
                        //产线
                        String line = (String) planContentMap.get("line");
                        hvEamInspectStatistical.setLine(line);
                        inspectStatisticalRepository.save(hvEamInspectStatistical);
                        //存储子项
                        List itemDTOList = (List) planContentMap.get("itemDTOList");
                        for (Object itemDTO : itemDTOList) {
                            HvEamInspectStatisticalItem hvEamInspectStatisticalItem = new HvEamInspectStatisticalItem();
                            hvEamInspectStatisticalItem.setProcessInstanceId(processInstanceId);
                            hvEamInspectStatisticalItem.setEquipmentCode(equipmentCode);
                            Map itemDTOMap = (Map) itemDTO;
                            //点检部位
                            String inspectPosition = (String) itemDTOMap.get("inspectPosition");
                            hvEamInspectStatisticalItem.setInspectPosition(inspectPosition);
                            //点检工作
                            String inspectWork = (String) itemDTOMap.get("inspectWork");
                            hvEamInspectStatisticalItem.setInspectWork(inspectWork);
                            //点检理论值
                            String inspectTheoreticalValue = (String) itemDTOMap.get("inspectTheoreticalValue");
                            hvEamInspectStatisticalItem.setInspectTheoreticalValue(inspectTheoreticalValue);
                            //工时
                            Float manHour = Float.parseFloat(itemDTOMap.get("manHour").toString());
                            hvEamInspectStatisticalItem.setManHour(manHour);
                            inspectStatisticalItemRepository.save(hvEamInspectStatisticalItem);
                        }
                    }
                }
            }
        }
    }

    /**
     * 统计报表查询
     *
     * @param inspectStatisticalQueryDTO 查询条件
     */
    @Override
    public List<InspectStatisticalDTO> getStatistical(InspectStatisticalQueryDTO inspectStatisticalQueryDTO) {
        Date finishedBefore = inspectStatisticalQueryDTO.getFinishedBefore();
        Date finishedAfter = inspectStatisticalQueryDTO.getFinishedAfter();
        Calendar ca = Calendar.getInstance();
        if (finishedAfter == null) {
            finishedAfter = ca.getTime();
        }
        if (finishedBefore == null) {
            ca.add(Calendar.DATE, -30);
            finishedBefore = ca.getTime();
        }
        List<HvEamInspectStatistical> hvEamInspectStatisticalS = inspectStatisticalRepository.findAllByEquipmentNameContainsAndLineContainsAndEndTimeBetweenAndEndTimeIsNotNull(inspectStatisticalQueryDTO.getEquipmentName(),
            inspectStatisticalQueryDTO.getLine(), finishedBefore, finishedAfter);

        //去重
        List<HvEamInspectStatistical> inspectStatisticalList = new ArrayList<>();
        for (HvEamInspectStatistical hvEamInspectStatistical : hvEamInspectStatisticalS) {
            List<String> equipmentCodes = inspectStatisticalList.stream().map(HvEamInspectStatistical::getEquipmentCode).collect(Collectors.toList());
            if (!equipmentCodes.contains(hvEamInspectStatistical.getEquipmentCode())) {
                inspectStatisticalList.add(hvEamInspectStatistical);
            }
        }
        //去重后的统计数据
        List<InspectStatisticalDTO> inspectStatisticalDTOS = DtoMapper.convertList(inspectStatisticalList, InspectStatisticalDTO.class);
        for (InspectStatisticalDTO inspectStatisticalDTO : inspectStatisticalDTOS) {
            String equipmentCode = inspectStatisticalDTO.getEquipmentCode();
            List<HvEamInspectStatistical> allByEquipmentCode = inspectStatisticalRepository.findAllByEquipmentCodeAndEndTimeBetween(equipmentCode, finishedBefore, finishedAfter);
            inspectStatisticalDTO.setInspectCount(allByEquipmentCode.size());
            //设备对应的获取流程ids
            List<String> processInstanceIds = allByEquipmentCode.stream().map(HvEamInspectStatistical::getProcessInstanceId).collect(Collectors.toList());
            //获取关联的点检项目
            List<InspectStatisticalItemDTO> aaa = new ArrayList<>();
            for (String instanceId : processInstanceIds) {
                List<HvEamInspectStatisticalItem> inspectStatisticalItem = inspectStatisticalItemRepository.findAllByEquipmentCodeAndProcessInstanceId(equipmentCode, instanceId);
                List<InspectStatisticalItemDTO> inspectStatisticalItemDTOS = DtoMapper.convertList(inspectStatisticalItem, InspectStatisticalItemDTO.class);
                //获取开始结束时间
                for (InspectStatisticalItemDTO inspectStatisticalItemDTO : inspectStatisticalItemDTOS) {
                    String itemProcessInstanceId = inspectStatisticalItemDTO.getProcessInstanceId();
                    HvEamInspectStatistical hvEamInspectStatistical = inspectStatisticalRepository.findByEquipmentCodeAndProcessInstanceIdAndEndTimeIsNotNull(equipmentCode, itemProcessInstanceId);
                    Date startTime = hvEamInspectStatistical.getStartTime();
                    inspectStatisticalItemDTO.setStartTime(startTime);
                    Date endTime = hvEamInspectStatistical.getEndTime();
                    inspectStatisticalItemDTO.setEndTime(endTime);
                    aaa.add(inspectStatisticalItemDTO);
                }
            }
            inspectStatisticalDTO.setInspectItemDTOList(aaa);
        }
        return inspectStatisticalDTOS;
    }

    /**
     * 今日点巡检次数
     *
     * @return 次数
     */
    @Override
    public Integer getTodayInspectCount() {
        Date date = new Date();
        SimpleDateFormat after = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        SimpleDateFormat before = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
        String a = after.format(date);
        String b = before.format(date);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date todayBegin = sdf.parse(a);
            Date todayEnd = sdf.parse(b);
            List<HvEamInspectStatistical> allByEndTimeBetween = inspectStatisticalRepository.findAllByEndTimeBetween(todayBegin, todayEnd);
            return allByEndTimeBetween.size();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 本周点巡检次数
     *
     * @return 次数
     */
    @Override
    public Integer getWeekCount() {
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int weekDay = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        if (weekDay == 0) {
            weekDay = 7;
        }
        //周一
        calendar.add(Calendar.DATE, -weekDay + 1);
        Date monday = calendar.getTime();
        //周日
        calendar.add(Calendar.DATE, 6);
        Date sunday = calendar.getTime();
        SimpleDateFormat after = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        SimpleDateFormat before = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
        String mondayStr = after.format(monday);
        String sundayStr = before.format(sunday);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            //周一00:00:00
            Date finishedAfter = sdf.parse(mondayStr);
            //周日23:59:59
            Date finishedBefore = sdf.parse(sundayStr);
            List<HvEamInspectStatistical> allByEndTimeBetween = inspectStatisticalRepository.findAllByEndTimeBetween(finishedAfter, finishedBefore);
            return allByEndTimeBetween.size();

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 根据时间段查次数
     *
     * @param inspectStatisticalQueryDTO 查询条件
     * @return list
     */
    @Override
    public List<InspectStatisticalTimeQuantumDTO> getCountByTimeQuantum(InspectStatisticalQueryDTO inspectStatisticalQueryDTO) {
        Date finishedBefore = inspectStatisticalQueryDTO.getFinishedBefore();
        Date finishedAfter = inspectStatisticalQueryDTO.getFinishedAfter();
        Calendar ca = Calendar.getInstance();
        if (finishedAfter == null) {
            finishedAfter = ca.getTime();
        }
        if (finishedBefore == null) {
            ca.add(Calendar.DATE, -30);
            finishedBefore = ca.getTime();
        }
        ca.setTime(finishedAfter);
        long afterMillis = ca.getTimeInMillis();
        ca.setTime(finishedBefore);
        long beforeMillis = ca.getTimeInMillis();
        //两日期相差天数
        long between = (afterMillis - beforeMillis) / (1000 * 3600 * 24);

        List<InspectStatisticalTimeQuantumDTO> list = new ArrayList<>();
        InspectStatisticalTimeQuantumDTO dto = new InspectStatisticalTimeQuantumDTO();
        SimpleDateFormat before1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        SimpleDateFormat after1 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
        //时间段第一天
        String a = before1.format(finishedBefore);
        String b = after1.format(finishedBefore);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date todayBegin = sdf.parse(a);
            Date todayEnd = sdf.parse(b);
            List<HvEamInspectStatistical> allByEndTimeBetween = inspectStatisticalRepository.findAllByEquipmentNameContainsAndLineContainsAndEndTimeBetweenAndEndTimeIsNotNull(inspectStatisticalQueryDTO.getEquipmentName(), inspectStatisticalQueryDTO.getLine(), todayBegin, todayEnd);
            dto.setTime(todayBegin);
            dto.setCount(allByEndTimeBetween.size());
            list.add(dto);
            ca.setTime(finishedBefore);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        for (int i = 1; i <= between; i++) {
            InspectStatisticalTimeQuantumDTO dto1 = new InspectStatisticalTimeQuantumDTO();
            //时间段下一天
            ca.add(Calendar.DATE, 1);
            Date nextDay = ca.getTime();
            a = before1.format(nextDay);
            b = after1.format(nextDay);
            try {
                Date todayBegin = sdf.parse(a);
                Date todayEnd = sdf.parse(b);
                List<HvEamInspectStatistical> allByEndTimeBetween = inspectStatisticalRepository.findAllByEquipmentNameContainsAndLineContainsAndEndTimeBetweenAndEndTimeIsNotNull(inspectStatisticalQueryDTO.getEquipmentName(), inspectStatisticalQueryDTO.getLine(), todayBegin, todayEnd);
                dto1.setTime(todayBegin);
                dto1.setCount(allByEndTimeBetween.size());
                list.add(dto1);
                ca.setTime(nextDay);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return list;
    }

    /**
     * 根据设备id查历史任务
     *
     * @param inspectHistoryQueryDTO 查询条件
     * @return 历史
     */
    @Override
    public List<HistoricTaskInstanceDTO> getHistoryByEquipmentId(InspectHistoryQueryDTO inspectHistoryQueryDTO) {
        List<HistoricTaskInstanceDTO> list = new ArrayList<>();
        //设备id
        Integer equipmentId = inspectHistoryQueryDTO.getEquipmentId();
        List<HvEamInspectStatistical> hvEamInspectStatisticalList = inspectStatisticalRepository.findAllByEquipmentIdAndEndTimeIsNotNull(equipmentId);
        if (hvEamInspectStatisticalList.size() > 0) {
            List<String> processInstanceIds = hvEamInspectStatisticalList.stream().map(HvEamInspectStatistical::getProcessInstanceId).collect(Collectors.toList());
            for (String processInstanceId : processInstanceIds) {
                HistoryTaskQueryDTO taskQueryDTO = new HistoryTaskQueryDTO();
                taskQueryDTO.setPage(inspectHistoryQueryDTO.getPage());
                taskQueryDTO.setPageSize(inspectHistoryQueryDTO.getPageSize());
                taskQueryDTO.setFinished(true);
                taskQueryDTO.setProcessFinished(true);
                taskQueryDTO.setDueDateAfter(inspectHistoryQueryDTO.getBeginTime());
                taskQueryDTO.setDueDateBefore(inspectHistoryQueryDTO.getEndTime());
                taskQueryDTO.setProcessInstanceId(processInstanceId);
                ResultVO<HvPage<HistoricTaskInstanceDTO>> historicTaskInstance = historyClient.getHistoricTaskInstance(taskQueryDTO);
                if (historicTaskInstance.isSuccess()) {
                    List<HistoricTaskInstanceDTO> content = historicTaskInstance.getData().getContent();
                    list.addAll(content);
                } else {
                    log.error("查询activiti历史失败{}", historicTaskInstance);
                }
            }
        }
        return list;
    }

    /**
     * 根据设备id查正在进行的任务
     *
     * @param inspectHistoryQueryDTO 查询条件
     * @return 分页
     */
    @Override
    public HvPage<ProcessInstanceDTO> getOngoingByEquipmentId(InspectHistoryQueryDTO inspectHistoryQueryDTO) {
        List<HvEamInspectStatistical> hvEamInspectStatisticalList = inspectStatisticalRepository.findAllByEquipmentIdAndEndTimeIsNull(inspectHistoryQueryDTO.getEquipmentId());
        log.info(hvEamInspectStatisticalList.size() + "");
        if (hvEamInspectStatisticalList.size() > 0) {
            List<String> processInstanceIds = hvEamInspectStatisticalList.stream().map(HvEamInspectStatistical::getProcessInstanceId).collect(Collectors.toList());
            ProcessInstanceQueryDTO processInstanceQueryDTO = new ProcessInstanceQueryDTO();
            processInstanceQueryDTO.setStartedAfter(inspectHistoryQueryDTO.getBeginTime());
            processInstanceQueryDTO.setStartedBefore(inspectHistoryQueryDTO.getEndTime());
            processInstanceQueryDTO.setPage(inspectHistoryQueryDTO.getPage());
            processInstanceQueryDTO.setBusinessKey(inspectHistoryQueryDTO.getBusinessKey());
            processInstanceQueryDTO.setPageSize(inspectHistoryQueryDTO.getPageSize());
            processInstanceQueryDTO.setProcessInstanceIds(processInstanceIds);
            ResultVO<HvPage<ProcessInstanceDTO>> processInstanceInfo = runtimeClient.getProcessInstancePage(processInstanceQueryDTO);
            if (processInstanceInfo.isSuccess()) {
                return processInstanceInfo.getData();
            } else {
                log.error("查询activiti正在进行的任务失败{}", processInstanceInfo);
                return null;
            }
        }
        return null;
    }
}