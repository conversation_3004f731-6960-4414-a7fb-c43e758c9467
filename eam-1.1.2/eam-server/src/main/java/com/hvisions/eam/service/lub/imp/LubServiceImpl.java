package com.hvisions.eam.service.lub.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.eam.configuration.spare.StoreConfig;
import com.hvisions.eam.enums.StoreExceptionEnum;
import com.hvisions.eam.dto.lub.LubTypeDTO;
import com.hvisions.eam.dto.lub.LubricatingDTO;
import com.hvisions.eam.query.lub.LubricatingQueryDTO;
import com.hvisions.eam.entity.lub.HvEamLubType;
import com.hvisions.eam.entity.lub.HvEamLubricating;
import com.hvisions.eam.repository.lub.LubEntityRepository;
import com.hvisions.eam.repository.lub.LubTypeEntityRepository;
import com.hvisions.eam.service.lub.LubService;
import com.hvisions.eam.service.lub.LubToShelveService;
import com.hvisions.eam.service.lub.LubTypeService;
import com.hvisions.eam.dto.publicstore.UnitDTO;
import com.hvisions.eam.entity.publicstore.HvEamSpareUnit;
import com.hvisions.eam.repository.publicstore.SpareUnitEntityRepository;
import com.hvisions.eam.service.publicstore.MatchingService;
import com.hvisions.eam.service.publicstore.SpareUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>Title:SpareServiceImpl</p>
 * <p>Description:油品类</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/19</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Service
public class LubServiceImpl implements LubService {

    /**
     * 油品jpa
     */
    private final LubEntityRepository lubEntityRepository;

    /**
     * 油品库存关系 service
     */
    private final LubToShelveService lubToShelveService;

    /**
     * 设备类型 service
     */
    private final LubTypeService lubTypeService;
    private final LubTypeEntityRepository lubTypeEntityRepository;

    /**
     * 单位 service
     */
    private final SpareUnitService spareUnitService;

    /**
     * 编码解析规则 service
     */
    private final MatchingService matchingService;
    private final SpareUnitEntityRepository unitEntityRepository;

    @Autowired
    public LubServiceImpl(LubEntityRepository lubEntityRepository,
                          LubToShelveService lubToShelveService,
                          LubTypeService lubTypeService,
                          LubTypeEntityRepository lubTypeEntityRepository,
                          SpareUnitService spareUnitService,
                          MatchingService matchingService,
                          SpareUnitEntityRepository unitEntityRepository) {
        //调用父类的构造函数，传入jpa对象，实现普通的增删改查功能
        this.lubEntityRepository = lubEntityRepository;
        this.lubToShelveService = lubToShelveService;
        this.lubTypeService = lubTypeService;
        this.lubTypeEntityRepository = lubTypeEntityRepository;
        this.spareUnitService = spareUnitService;
        this.matchingService = matchingService;
        this.unitEntityRepository = unitEntityRepository;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer save(LubricatingDTO lubricatingDTO) {
        //油品最大库存最小库存验证
        //当两个库存数量都为空时 不进行验证
        if (!(lubricatingDTO.getCargoMin() == null && lubricatingDTO.getCargoMax() == null)) {
            lubricatingDTO.validation();
        }
        //如果typeID里有值 则
        List<Integer> typeIds = lubricatingDTO.getTypeIds();
        if (typeIds != null) {
            lubricatingDTO.setLubTypeId(typeIds.get(typeIds.size() - 1));
        }
        HvEamLubricating lub = DtoMapper.convert(lubricatingDTO, HvEamLubricating.class);
        return lubEntityRepository.save(lub).getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void deleteById(Integer id) {
        //只有当前油品的总库存数量 为零的时候 才可以删除
        if (lubToShelveService.sumLubNumByLubId(id).compareTo(new BigDecimal(StoreConfig.ZERO)) != 0) {
            //service层中直接抛出异常。不需要手动捕获,由controller增强器直接拦截并统一返回
            throw new BaseKnownException(StoreExceptionEnum.ENUM_NOT_ZERO_EXCEPTION);
        }
        //只有当关系表中无当前油品的时候才可以删除
        Set<Integer> spareIds = lubToShelveService.findAllByLubId(id);
        if (spareIds.size() != 0) {
            //service层中直接抛出异常。不需要手动捕获,由controller增强器直接拦截并统一返回
            throw new BaseKnownException(StoreExceptionEnum.IN_USE);
        }
        //油品库存油品关系
        Set<Integer> allBySpareId = lubToShelveService.findAllByLubId(id);
        for (Integer ids : allBySpareId) {
            //删除油品库存关系
            lubToShelveService.deleteById(ids);
        }
        //删除油品
        lubEntityRepository.deleteById(id);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LubricatingDTO findById(Integer id) {
        List<Integer> ids = new ArrayList<>();
        //通过油品ID获取油品信息
        LubricatingDTO lubricatingDTO = DtoMapper.convert(lubEntityRepository.getOne(id), LubricatingDTO.class);
        //通过备件获取油品单位信息
        UnitDTO unitDTO = spareUnitService.findById(lubricatingDTO.getUnitId());
        //添加单位名称
        lubricatingDTO.setUnitName(unitDTO.getUnitName());
        //添加单位符号
        lubricatingDTO.setUnitSymbol(unitDTO.getUnitSymbol());
        //获取油品类型信息
        LubTypeDTO lubTypeDTO = lubTypeService.findById(lubricatingDTO.getLubTypeId());
        //添加油品类型名称
        lubricatingDTO.setUnitName(lubTypeDTO.getTypeName());
        boolean next = true;
        ids.add(lubTypeDTO.getId());
        //获取父类id
        Integer parentId = lubTypeDTO.getParentId();
        while (next) {
            //如果父类id不为0这
            if (!parentId.equals(0)) {
                LubTypeDTO lubTypeDTO1 = lubTypeService.findById(parentId);
                ids.add(lubTypeDTO1.getId());
                parentId = lubTypeDTO1.getParentId();
            } else {
                next = false;
            }
        }
        // 倒序排列
        Collections.reverse(ids);
        lubricatingDTO.setTypeIds(ids);
        return lubricatingDTO;
    }

    /**
     * 分页查询 通过油品编码 油品名称 油品类型 是否关键部位 联合查询
     *
     * @param lubricatingQueryDTO 油品分页DTO
     * @return 分页
     */
    @Override
    public Page<LubricatingDTO> findAllByLubCodeAndLubNameAndLubType(LubricatingQueryDTO lubricatingQueryDTO) {

        //把DTO转换成普通类
        HvEamLubricating lubricating = DtoMapper.convert(lubricatingQueryDTO, HvEamLubricating.class);

        //匹配方式       xxx 采用开始匹配的方式查询 忽略大小写
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
            .withMatcher("lubCode", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
            .withMatcher("lubName", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());

        //匹配器
        Example<HvEamLubricating> example = Example.of(lubricating, exampleMatcher);
        Page<HvEamLubricating> page = lubEntityRepository.findAll(example, lubricatingQueryDTO.getRequest());
        //查询全部信息
        Page<LubricatingDTO> lubricatingDTOS = DtoMapper.convertPage(page, LubricatingDTO.class);
        for (LubricatingDTO s : lubricatingDTOS) {
            //油品类型
            LubTypeDTO lubType = lubTypeService.findById(s.getLubTypeId());
            if (lubType == null) {
                s.setTypeName("");
                s.setTypeIds(new ArrayList<>());
            } else {
                //添加类型名称
                s.setTypeName(lubType.getTypeName());
                boolean next = true;
                List<Integer> ids = new ArrayList<>();
                ids.add(lubType.getId());
                Integer parentId = lubType.getParentId();
                while (next) {
                    if (!parentId.equals(0)) {
                        LubTypeDTO lubTypeDTO = lubTypeService.findById(parentId);
                        ids.add(lubTypeDTO.getId());
                        parentId = lubTypeDTO.getParentId();
                    } else {
                        next = false;
                    }
                }
                // 倒序排列
                Collections.reverse(ids);
                s.setTypeIds(ids);
            }
            //油品单位
            UnitDTO unitDTO = spareUnitService.findById(s.getUnitId());
            if (unitDTO == null) {
                s.setUnitSymbol("");
                s.setUnitName("");
            } else {
                //添加单位符号
                s.setUnitSymbol(unitDTO.getUnitSymbol());
                //添加单位名称
                s.setUnitName(unitDTO.getUnitName());
            }

        }
        return lubricatingDTOS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<LubricatingDTO> getLubListByIdList(List<Integer> ids) {
        List<LubricatingDTO> list = DtoMapper.convertList(lubEntityRepository.findAllById(ids), LubricatingDTO.class);
        List<HvEamSpareUnit> units = unitEntityRepository.findAll();
        for (LubricatingDTO dto : list) {
            if (dto.getUnitId() != null) {
                dto.setUnitName(units.stream()
                    .filter(t -> t.getId().equals(dto.getUnitId()))
                    .map(HvEamSpareUnit::getUnitName)
                    .findFirst()
                    .orElse(null));
                dto.setUnitSymbol(units.stream()
                    .filter(t -> t.getId().equals(dto.getUnitId()))
                    .map(HvEamSpareUnit::getUnitSymbol)
                    .findFirst()
                    .orElse(null));
            }
        }
        return list;
    }

    /**
     * 通过批次号 和 解析规则中的服务名获取 油品信息
     *
     * @param service 解析规则中的服务名获取
     * @return 油品基础信息
     */
    @Override
    public LubricatingDTO getSpareDTOByBatchNumber(String service, String batchNumber) {
        String code = matchingService.getCodeByBatchNumber(batchNumber, service);
        if (code != null) {
            HvEamLubricating lubricating = lubEntityRepository.findByLubCode(code);
            if (lubricating != null) {
                return DtoMapper.convert(lubricating, LubricatingDTO.class);
            }
        }
        return null;
    }

    /**
     * 查询油品 通过编码
     *
     * @param code 油品编号
     * @return 油品数据
     */
    @Override
    public LubricatingDTO getLubByCode(String code) {
        HvEamLubricating entity = lubEntityRepository.findByLubCode(code);
        return DtoMapper.convert(entity, LubricatingDTO.class);
    }

    /**
     * 查询油品 通过编码列表
     *
     * @param codes 油品编码列表
     * @return 油品
     */
    @Override
    public List<LubricatingDTO> getLubByCodeList(List<String> codes) {
        List<HvEamLubricating> entity = lubEntityRepository.findAllByLubCodeIn(codes);
        List<Integer> unitIds = entity.stream()
            .map(t -> t.getUnitId())
            .collect(Collectors.toList());
        List<Integer> typeIds = entity.stream()
            .map(t -> t.getLubTypeId())
            .collect(Collectors.toList());
        List<HvEamSpareUnit> units = unitEntityRepository.findAllById(unitIds);
        List<HvEamLubType> types = lubTypeEntityRepository.findAllById(typeIds);

        List<LubricatingDTO> lubs = DtoMapper.convertList(entity, LubricatingDTO.class);
        for (LubricatingDTO lub : lubs) {
            lub.setUnitName(units.stream().filter(t -> t.getId().equals(lub.getUnitId())).map(t -> t.getUnitName()).findFirst().orElse(""));
            lub.setUnitSymbol(units.stream().filter(t -> t.getId().equals(lub.getUnitId())).map(t -> t.getUnitSymbol()).findFirst().orElse(""));
            lub.setTypeName(types.stream().filter(t -> t.getId().equals(lub.getLubTypeId())).map(t -> t.getTypeName()).findFirst().orElse(""));
        }
        return lubs;
    }
}