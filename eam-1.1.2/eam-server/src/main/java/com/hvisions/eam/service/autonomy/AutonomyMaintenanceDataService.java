package com.hvisions.eam.service.autonomy;

import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.support.ListenerExecutionFailedException;

/**
 * 处理自主维护的相关数据
 * <AUTHOR>
 */
public interface AutonomyMaintenanceDataService {

    /**
     * 处理流程实例信息
     *
     * @param processInstanceId 流程实例id
     * @param b  是否需要发消息
     */
    void save(String processInstanceId,Boolean b);

    /**
     * 处理业务异常
     * @param message 消息
     * @param e 异常
     */
    void handleError(Message message, ListenerExecutionFailedException e);

}









