package com.hvisions.eam.service.autonomy.imp;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.eam.dao.MaintenanceProcessDataMapper;
import com.hvisions.eam.entity.autonomy.HvAmAutonomyMaintenanceProcessData;
import com.hvisions.eam.service.autonomy.MaintenanceProcessDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-11
 */
@Service
@Slf4j
public class MaintenanceProcessDataServiceImpl extends ServiceImpl<MaintenanceProcessDataMapper, HvAmAutonomyMaintenanceProcessData> implements MaintenanceProcessDataService {


    @Override
    public List<HvAmAutonomyMaintenanceProcessData> getDataList(String startTime, String endTime) {

        QueryWrapper<HvAmAutonomyMaintenanceProcessData> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)){
            //无条件
        }else {
            queryWrapper.between("end_time",startTime,endTime);
        }
        List<HvAmAutonomyMaintenanceProcessData> list = this.baseMapper.selectList(queryWrapper);

        return list;
    }

    @Override
    public List<HvAmAutonomyMaintenanceProcessData> getByIdS(List<Integer> idList) {

        return this.baseMapper.selectBatchIds(idList);
    }
}
