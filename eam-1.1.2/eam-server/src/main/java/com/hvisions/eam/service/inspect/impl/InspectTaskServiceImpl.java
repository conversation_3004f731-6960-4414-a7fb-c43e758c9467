package com.hvisions.eam.service.inspect.impl;

import cn.hutool.core.lang.Assert;
import com.hvisions.activiti.client.HistoryClient;
import com.hvisions.activiti.client.TaskClient;
import com.hvisions.activiti.dto.history.HistoricTaskInstanceDTO;
import com.hvisions.activiti.dto.history.HistoryTaskQueryDTO;
import com.hvisions.activiti.dto.task.TaskHandleDTO;
import com.hvisions.activiti.dto.task.TaskQueryDTO;
import com.hvisions.activiti.dto.task.TaskWithVariableDTO;
import com.hvisions.activiti.dto.task.Variable;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.HvExportUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.inspect.content.TaskExportDTO;
import com.hvisions.eam.dto.inspect.report.EquipmentTaskInfo;
import com.hvisions.eam.dto.inspect.report.InspectMapDO;
import com.hvisions.eam.entity.inspect.HvEamInspectStatistical;
import com.hvisions.eam.repository.inspect.InspectStatisticalRepository;
import com.hvisions.eam.service.inspect.InspectTaskService;
import com.hvisions.hiperbase.equipment.map.EquipmentMapDetailDto;
import com.hvisions.hiperbase.equipment.map.EquipmentMapDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title: MaintainTaskServiceImpl</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/5/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Slf4j
@Service
public class InspectTaskServiceImpl implements InspectTaskService {
    private final TaskClient taskClient;
    private final HistoryClient historyClient;
    private final InspectServiceHandler serviceHandler;
    private final InspectStatisticalRepository inspectStatisticalRepository;
    @Value("${h-visions.equipmentInspect.taskTime:60}")
    private Integer taskTime;

    @Autowired
    public InspectTaskServiceImpl(TaskClient taskClient,
                                  HistoryClient historyClient,
                                  InspectServiceHandler serviceHandler,
                                  InspectStatisticalRepository inspectStatisticalRepository) {
        this.taskClient = taskClient;
        this.historyClient = historyClient;
        this.serviceHandler = serviceHandler;
        this.inspectStatisticalRepository = inspectStatisticalRepository;
    }


    /**
     * 批量处理保养任务
     *
     * @param taskIds 任务id列表
     * @param userId  用户id
     * @param token   当前用户id
     */
    @Override
    public void finishTask(List<String> taskIds, String userId, String token) {
        if (Strings.isBlank(userId)) {
            throw new BaseKnownException(10000, "获取用户数据异常，请检查是否登录");
        }
        for (String taskId : taskIds) {
            //获取任务信息
            ResultVO<TaskWithVariableDTO> result = taskClient.getTaskByTaskId(taskId);
            if (!result.isSuccess()) {
                throw new BaseKnownException(10000, "调用工作流服务接口异常," + result.getMessage());
            }
            TaskWithVariableDTO task = result.getData();
            Assert.isTrue(userId.equals(task.getAssignee()), "任务执行人验证错误，如果不是当前执行人，需要先领取任务或等待派发");
            List<String> tasks = new ArrayList<>();
            tasks.add(taskId);
            //开启任务
            ResultVO<List<Map<String, Variable>>> startResult = taskClient.startTask(tasks, token);
            if (!startResult.isSuccess()) {
                throw new BaseKnownException(10000, "调用工作流服务开启任务接口异常," + startResult.getMessage());
            }
            //设置变量
            Map<String, Object> variables = task.getVariableList();
            if (variables == null) {
                throw new BaseKnownException(10000, "任务数据异常：变量表数据为空。taskId:" + taskId);
            }
            List<Map<String, Object>> contents = (List<Map<String, Object>>) variables.get("inspectPlanContentDTOList");
            if (contents == null) {
                throw new BaseKnownException(10000, "任务数据异常：content数据为空:" + taskId);
            }
            for (Map<String, Object> content : contents) {
                List<Map<String, Object>> items = (List<Map<String, Object>>) content.get("itemDTOList");
                if (items == null) {
                    throw new BaseKnownException(10000, "任务数据异常：items数据为空:" + taskId);
                }
                for (Map<String, Object> item : items) {
                    //设置为完成
                    item.put("done", "2");
                }
            }
            TaskHandleDTO request = new TaskHandleDTO();
            request.setVariables(variables);
            request.setTaskId(taskId);
            //结束任务
            ResultVO changeVariableResult = taskClient.completeTask(request, token);
            if (!changeVariableResult.isSuccess()) {
                throw new BaseKnownException(10000, "更新任务数据失败:" + changeVariableResult.getMessage());
            }
        }
    }

    /**
     * 导出任务信息
     *
     * @return 任务信息excel
     */
    @Override
    public ExcelExportDto exportData() {
        HistoryTaskQueryDTO historyTaskQueryDTO = new HistoryTaskQueryDTO();
        historyTaskQueryDTO.setPageSize(Integer.MAX_VALUE);
        List<String> strings = new ArrayList<>();
        strings.add("equipment-maintain");
        strings.add("equipment-lubrication");
        strings.add("equipment-inspect");
        strings.add("store-spare");
        strings.add("store-lub");
        strings.add("equipment-maintenance");
        historyTaskQueryDTO.setUnAssignee(false);
        historyTaskQueryDTO.setProcessDefinitionKeyIn(strings);
        ResultVO<HvPage<HistoricTaskInstanceDTO>> historicTaskInstance = historyClient.getHistoricTaskInstance(historyTaskQueryDTO);
        List<HistoricTaskInstanceDTO> content = historicTaskInstance.getData().getContent();
        List<TaskExportDTO> taskExportDTOS = DtoMapper.convertList(content, TaskExportDTO.class);
        return HvExportUtil.exportData(taskExportDTOS, TaskExportDTO.class, "task", "任务信息.xls", null);
    }

    /**
     * 导出未完成任务信息
     *
     * @return 任务信息excel
     */
    @Override
    public ExcelExportDto exportTaskData() {
        TaskQueryDTO taskQueryDTO = new TaskQueryDTO();
        taskQueryDTO.setPageSize(Integer.MAX_VALUE);
        List<String> strings = new ArrayList<>();
        strings.add("equipment-maintain");
        strings.add("equipment-lubrication");
        strings.add("equipment-inspect");
        strings.add("store-spare");
        strings.add("store-lub");
        strings.add("equipment-maintenance");
        taskQueryDTO.setProcessDefinitionKeyList(strings);
        ResultVO<HvPage<TaskWithVariableDTO>> taskWithVariables = taskClient.getTaskWithVariables(taskQueryDTO);
        if (!taskWithVariables.isSuccess()) {
            throw new BaseKnownException(taskWithVariables);
        }
        List<TaskExportDTO> taskExportDTOS = DtoMapper.convertList(taskWithVariables.getData().getContent(),
            TaskExportDTO.class);
        return HvExportUtil.exportData(taskExportDTOS, TaskExportDTO.class, "task", "未完成任务.xls", null);
    }

    /**
     * 获取点巡检任务地图
     *
     * @param id 用户id
     * @return 任务地图信息
     */
    @Override
    public List<InspectMapDO> getTaskMap(Integer id) {
        //获取所有的地图信息
        List<EquipmentMapDto> maps = serviceHandler.getAllMap();
        if (CollectionUtils.isEmpty(maps)) {
            return Collections.emptyList();
        }
        //转成结果对象
        List<InspectMapDO> result = parse(maps);
        //根据地图上面的设备信息，找到相关的流程信息
        List<Integer> equipmentIds = new ArrayList<>();
        for (EquipmentMapDto map : maps) {
            equipmentIds.addAll(map.getEquipmentMapDetailDtos().stream()
                .map(EquipmentMapDetailDto::getEquipmentId)
                .collect(Collectors.toList()));
        }
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR, -taskTime * 24);
        List<HvEamInspectStatistical> processInfo =
            inspectStatisticalRepository.findAllByEquipmentIdInAndCreateTimeAfter(
                equipmentIds.stream()
                    .distinct()
                    .collect(Collectors.toList()),
                calendar.getTime());
        //找到这个人所有的点巡检任务
        TaskQueryDTO queryDTO = new TaskQueryDTO();
        queryDTO.setProcessDefinitionKey("equipment-inspect");
        queryDTO.setProcessInstanceIdIn(processInfo.stream()
            .map(HvEamInspectStatistical::getProcessInstanceId)
            .collect(Collectors.toList()));
        queryDTO.setCandidateOrAssignee(id.toString());
        queryDTO.setPageSize(Integer.MAX_VALUE);
        ResultVO<HvPage<TaskWithVariableDTO>> taskInstance = taskClient.getTaskWithVariables(queryDTO);
        if (!taskInstance.isSuccess()) {
            throw new BaseKnownException("工作流服务查询异常，请联系管理员");
        }
        List<TaskWithVariableDTO> tasks = Optional.of(taskInstance)
            .map(ResultVO::getData)
            .map(HvPage::getContent)
            .orElse(Collections.emptyList());
        //将任务信息和地图上的设备进行关联
        for (InspectMapDO mapDO : result) {
            List<EquipmentTaskInfo> equipments = mapDO.getEquipmentTaskInfoList();
            for (EquipmentTaskInfo equipment : equipments) {
                //找到设备相关的流程id
                List<String> processInstanceIdsForEquipment = processInfo.stream()
                    .filter(t -> t.getEquipmentId().equals(equipment.getEquipmentId()))
                    .map(HvEamInspectStatistical::getProcessInstanceId)
                    .collect(Collectors.toList());
                //过滤对应的流程任务
                List<TaskWithVariableDTO> taskInstanceDTOS = tasks.stream()
                    .filter(t -> processInstanceIdsForEquipment.stream()
                        .anyMatch(p -> p.equals(t.getProcessInstanceId())))
                    .collect(Collectors.toList());
                //设置进去
                equipment.setTaskInfo(taskInstanceDTOS);
            }
        }
        return result;
    }

    /**
     * 转换
     *
     * @param map 设备地图
     * @return 点巡检地图
     */
    private List<InspectMapDO> parse(List<EquipmentMapDto> map) {
        List<InspectMapDO> result = new ArrayList<>();
        for (EquipmentMapDto mapDto : map) {
            InspectMapDO inspectMapDO = DtoMapper.convert(mapDto, InspectMapDO.class);
            inspectMapDO.setEquipmentTaskInfoList(DtoMapper.convertList(mapDto.getEquipmentMapDetailDtos(),
                EquipmentTaskInfo.class));
            result.add(inspectMapDO);
        }
        return result;
    }
}









