package com.hvisions.eam.service.inspect.impl;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.EquipmentFeignClient;
import com.hvisions.hiperbase.client.EquipmentMapClient;
import com.hvisions.hiperbase.client.EquipmentTypeClient;
import com.hvisions.hiperbase.equipment.EquipmentDTO;
import com.hvisions.hiperbase.equipment.EquipmentTypeDTO;
import com.hvisions.hiperbase.equipment.map.EquipmentMapDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title: ServiceHandler</p>
 * <p>Description: 处理接口问题</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2022/5/18</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class InspectServiceHandler {
    private final EquipmentFeignClient equipmentFeignClient;
    private final EquipmentTypeClient equipmentTypeClient;
    private final EquipmentMapClient equipmentMapClient;

    @Autowired
    public InspectServiceHandler(EquipmentFeignClient equipmentFeignClient,
                                 EquipmentTypeClient equipmentTypeClient,
                                 EquipmentMapClient equipmentMapClient) {
        this.equipmentFeignClient = equipmentFeignClient;
        this.equipmentTypeClient = equipmentTypeClient;
        this.equipmentMapClient = equipmentMapClient;
    }

    /**
     * 获取设备数据
     *
     * @param id id
     * @return 设备信息
     */
    public EquipmentDTO findEquipment(Integer id) {
        ResultVO<EquipmentDTO> result = equipmentFeignClient.getEquipmentById(id);
        if (!result.isSuccess()) {
            log.warn("设备接口查询错误,{}", result.getMessage());
            return null;
        }
        return Optional.ofNullable(result.getData()).orElse(null);
    }

    public List<EquipmentDTO> getList(List<Integer> id) {
        ResultVO<List<EquipmentDTO>> result = equipmentFeignClient.getEquipmentById(id);
        if (!result.isSuccess()) {
            return null;
        }
        return Optional.ofNullable(result.getData()).orElse(new ArrayList<>());
    }

    /**
     * 获取设备数据
     *
     * @param ids id列表
     * @return 设备信息
     */
    public List<EquipmentDTO> findEquipmentDTO(List<Integer> ids) {
        ResultVO<List<EquipmentDTO>> result = equipmentFeignClient.getEquipmentById(ids);
        if (!result.isSuccess()) {
            log.warn("设备接口查询错误,{}", result.getMessage());
            return Collections.emptyList();
        }
        return Optional.ofNullable(result.getData()).orElse(Collections.emptyList());
    }


    /**
     * 获取设备类型
     *
     * @param ids 类型id列表
     * @return 类型信息列表
     */
    public List<EquipmentTypeDTO> findEquipmentType(List<Integer> ids) {
        ResultVO<List<EquipmentTypeDTO>> result = equipmentTypeClient.getTypeAllByIdIn(ids);
        if (!result.isSuccess()) {
            log.warn("设备接口查询错误,{}", result.getMessage());
            return Collections.emptyList();
        }
        return Optional.ofNullable(result.getData()).orElse(Collections.emptyList());
    }

    /**
     * 获取子类型
     *
     * @param parentId 父级id
     * @return 子类型
     */
    public List<EquipmentTypeDTO> findAllChildTypeByParentId(Integer parentId) {
        ResultVO<List<EquipmentTypeDTO>> result =
            equipmentTypeClient.findAllChildTypeByParentId(parentId);
        if (!result.isSuccess()) {
            log.warn("设备接口查询错误,{}", result.getMessage());
            return Collections.emptyList();
        }
        return Optional.ofNullable(result.getData()).orElse(Collections.emptyList());
    }

    /**
     * 获取设备数据
     *
     * @param codes 编码列表
     * @return 设备信息
     */
    public List<EquipmentDTO> getAllEquipmentByCodeList(List<String> codes) {
        ResultVO<List<EquipmentDTO>> result = equipmentFeignClient.getAllEquipmentsByCodeIn(codes);
        if (!result.isSuccess()) {
            log.warn("设备接口查询错误,{}", result.getMessage());
            return Collections.emptyList();
        }
        return Optional.ofNullable(result.getData()).orElse(Collections.emptyList());
    }

    /**
     * 获取设备数据
     *
     * @param codes 编码列表
     * @return 设备信息
     */
    public List<EquipmentTypeDTO> getAllEquipmentTypeByCodeList(List<String> codes) {
        ResultVO<List<EquipmentTypeDTO>> result = equipmentTypeClient.getAllByEquipmentTypeCodeIn(codes);
        if (!result.isSuccess()) {
            log.warn("设备接口查询错误,{}", result.getMessage());
            return Collections.emptyList();
        }
        return Optional.ofNullable(result.getData()).orElse(Collections.emptyList());
    }

    /**
     * 根据设备类型获取所有上级的设备类型数据
     *
     * @param equipmentTypeId 设备类型id
     * @return 设备类型信息列表
     */
    public List<EquipmentTypeDTO> findAllParentTypeByChildId(Integer equipmentTypeId) {

        ResultVO<List<EquipmentTypeDTO>> typeResult =
            equipmentTypeClient.findAllparentTypeByChildId(equipmentTypeId);
        if (!typeResult.isSuccess()) {
            log.warn("查询点检项目异常，设备类型id查询子id异常,类型id:{}", equipmentTypeId);
            return Collections.emptyList();
        }
        List<EquipmentTypeDTO> types = typeResult.getData();
        if (types == null) {
            return Collections.emptyList();
        }
        return types;
    }


    /**
     * 获取所有地图信息
     *
     * @return 获取所有设备地图信息
     */
    public List<EquipmentMapDto> getAllMap() {
        ResultVO<List<EquipmentMapDto>> result = equipmentMapClient.findAll();
        if (!result.isSuccess()) {
            log.warn("设备地图服务异常：{}", result.getMessage());
            return Collections.emptyList();
        }
        return Optional.ofNullable(result.getData()).orElse(Collections.emptyList());

    }
}









