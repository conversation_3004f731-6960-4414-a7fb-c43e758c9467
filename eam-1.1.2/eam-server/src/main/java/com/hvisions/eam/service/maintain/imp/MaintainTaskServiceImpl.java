package com.hvisions.eam.service.maintain.imp;

import cn.hutool.core.lang.Assert;
import com.hvisions.activiti.client.TaskClient;
import com.hvisions.activiti.dto.task.TaskHandleDTO;
import com.hvisions.activiti.dto.task.TaskWithVariableDTO;
import com.hvisions.activiti.dto.task.Variable;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.service.maintain.MaintainTaskService;
import com.hvisions.eam.utils.UserIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: MaintainTaskServiceImpl</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/5/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Slf4j
@Service
public class MaintainTaskServiceImpl implements MaintainTaskService {
    private final TaskClient taskClient;
    private final UserIdUtil userIdUtil;
    private final HttpServletRequest request;

    @Autowired
    public MaintainTaskServiceImpl(TaskClient taskClient, UserIdUtil userIdUtil, HttpServletRequest request) {
        this.taskClient = taskClient;
        this.userIdUtil = userIdUtil;
        this.request = request;
    }


    /**
     * 批量处理保养任务
     *
     * @param taskIds 任务id列表
     */
    @Override
    public void finishTask(List<String> taskIds) {
        String token = request.getHeader("token");
        log.info(token);
        String userId = userIdUtil.getUserId();
        if (Strings.isBlank(userId)) {
            throw new BaseKnownException(10000, "获取当前登录人员信息错误，请检查登录信息");
        }
        for (String taskId : taskIds) {
            //获取任务信息
            ResultVO<TaskWithVariableDTO> result = taskClient.getTaskByTaskId(taskId);
            if (!result.isSuccess()) {
                throw new BaseKnownException(10000, "调用工作流服务接口异常," + result.getMessage());
            }
            List<String> tasks = new ArrayList<>();
            tasks.add(taskId);
            //设置变量
            TaskWithVariableDTO task = result.getData();
            Assert.isTrue(userId.equals(task.getAssignee()), "当前用户错误，请先领取任务或者通知管理员切换执行人");
            //开启任务
            ResultVO<List<Map<String, Variable>>> startResult = taskClient.startTask(tasks,token);
            if (!startResult.isSuccess()) {
                throw new BaseKnownException(10000, "调用工作流服务开启任务接口异常," + result.getMessage());
            }
            Map<String, Object> variables = task.getVariableList();
            if (variables == null) {
                throw new BaseKnownException(10000, "任务数据异常：变量表数据为空。taskId:" + taskId);
            }
            Map<String, Object> content = (Map<String, Object>) variables.get("maintainContentDTO");
            if (content == null) {
                throw new BaseKnownException(10000, "任务数据异常：content数据为空:" + taskId);
            }
            List<Map<String, Object>> items = (List<Map<String, Object>>) content.get("maintainItemDTOList");
            if (items == null) {
                throw new BaseKnownException(10000, "任务数据异常：items数据为空:" + taskId);
            }
            for (Map<String, Object> item : items) {
                //设置为完成
                item.put("done", "2");
            }
            TaskHandleDTO request = new TaskHandleDTO();
            request.setVariables(variables);
            request.setTaskId(taskId);
            ResultVO changeVariableResult = taskClient.completeTask(request,token);
            if (!changeVariableResult.isSuccess()) {
                throw new BaseKnownException(10000, "更新任务数据失败:" + changeVariableResult.getMessage());
            }
            //结束任务
        }
    }
}









