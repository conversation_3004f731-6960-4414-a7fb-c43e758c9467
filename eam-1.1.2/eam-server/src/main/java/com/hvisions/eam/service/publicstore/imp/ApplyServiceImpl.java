package com.hvisions.eam.service.publicstore.imp;

import com.hvisions.activiti.client.ActivitiClient;
import com.hvisions.activiti.dto.history.HistoricProcessQuery;
import com.hvisions.activiti.dto.history.HistoricTaskInstanceDTO;
import com.hvisions.activiti.dto.history.HistoryProcessInstanceDTO;
import com.hvisions.activiti.dto.instance.ProcessInstanceStartDTO;
import com.hvisions.activiti.dto.process.ProcessInstanceDTO;
import com.hvisions.activiti.dto.task.TaskHandleDTO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.SerialUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.activiti.lub.ItemsLubDTO;
import com.hvisions.eam.activiti.lub.RootLubDTO;
import com.hvisions.eam.activiti.lub.VariablesLubDTO;
import com.hvisions.eam.activiti.sapre.ImportSpareDTO;
import com.hvisions.eam.activiti.sapre.ItemsSpareDTO;
import com.hvisions.eam.activiti.sapre.RootSpareDTO;
import com.hvisions.eam.activiti.sapre.VariablesSpareDTO;
import com.hvisions.eam.client.task.Data;
import com.hvisions.eam.client.task.HistoryClient;
import com.hvisions.eam.client.task.HistoryVariables;
import com.hvisions.eam.client.task.JsonRootBean;
import com.hvisions.eam.configuration.spare.StoreConfig;
import com.hvisions.eam.dto.publicstore.ActualUseDTO;
import com.hvisions.eam.entity.HvEamSerialNumber;
import com.hvisions.eam.entity.lub.HvEamLubToShelve;
import com.hvisions.eam.entity.lub.HvEamLubricating;
import com.hvisions.eam.entity.publicstore.HvEamOutboundCargo;
import com.hvisions.eam.entity.publicstore.HvEamShelve;
import com.hvisions.eam.entity.spare.HvEamSpare;
import com.hvisions.eam.entity.spare.HvEamSpareToShelve;
import com.hvisions.eam.enums.StoreExceptionEnum;
import com.hvisions.eam.repository.lub.LubEntityRepository;
import com.hvisions.eam.repository.lub.LubToShelveEntityRepository;
import com.hvisions.eam.repository.publicstore.SerialNumberRepository;
import com.hvisions.eam.repository.publicstore.ShelveEntityRepository;
import com.hvisions.eam.repository.sprare.SpareEntityRepository;
import com.hvisions.eam.repository.sprare.SpareToShelveEntityRepository;
import com.hvisions.eam.service.publicstore.ActualUseService;
import com.hvisions.eam.service.publicstore.ApplyService;
import com.hvisions.eam.service.publicstore.StoreHeaderService;
import com.hvisions.eam.utils.ExcelUtils;
import com.hvisions.framework.client.AuthClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title:HvEamApplyServiceImpl</p>
 * <p>Description:申请备件表</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/19</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Slf4j
@Service
public class ApplyServiceImpl implements ApplyService {
    /**
     * 人员client
     */
    private final AuthClient authClient;
    /**
     * 出入库类型
     */
    private final String INOUT = "inOut";
    /**
     * 出库类型 - 入库
     */
    private final String INOUT_TYPE_IN = "1";
    /**
     * 出库类型 - 出库
     */
    private final String INOUT_TYPE_OUT = "2";
    /**
     * 申请项字段
     */
    private final String ITEM = "items";
    /**
     * 备件库存关系jpa
     */
    private final SpareToShelveEntityRepository spareToShelveEntityRepository;
    /**
     * 油品库存关系jpa
     */
    private final LubToShelveEntityRepository lubToShelveEntityRepository;
    /**
     * 油品jpa
     */
    private final LubEntityRepository lubEntityRepository;
    /**
     * 备件jpa
     */
    private final SpareEntityRepository spareEntityRepository;
    /**
     * 流程接口
     */
    private final ActivitiClient activitiClient;
    /**
     * 流水号repository
     */
    private final SerialNumberRepository serialNumberRepository;
    /**
     * 库房jpa
     */
    private final ShelveEntityRepository shelveEntityRepository;
    /**
     * 出库表
     */
    private final OutboundCargoServiceImpl outboundCargoService;

    private final ActualUseService actualUseService;

    /**
     * 是否开启审批
     */
    @Value("${h-visions.approve}")
    Boolean isApproval;

    /**
     * 获取单号
     */
    @Autowired
    SerialUtil serialUtil;

    /**
     * 行表
     */
    private final StoreHeaderService storeHeaderService;

    private final HistoryClient historyClient;

    @Autowired
    public ApplyServiceImpl(SpareToShelveEntityRepository spareToShelveEntityRepository,
                            LubToShelveEntityRepository lubToShelveEntityRepository,
                            SerialNumberRepository serialNumberRepository,
                            SpareEntityRepository spareEntityRepository,
                            AuthClient authClient,
                            ActivitiClient activitiClient,
                            HistoryClient historyClient,
                            ShelveEntityRepository shelveEntityRepository,
                            LubEntityRepository lubEntityRepository,
                            OutboundCargoServiceImpl outboundCargoService,
                            ActualUseService actualUseService, StoreHeaderService storeHeaderService) {
        this.spareToShelveEntityRepository = spareToShelveEntityRepository;
        this.activitiClient = activitiClient;
        this.lubToShelveEntityRepository = lubToShelveEntityRepository;
        this.serialNumberRepository = serialNumberRepository;
        this.lubEntityRepository = lubEntityRepository;
        this.spareEntityRepository = spareEntityRepository;
        this.shelveEntityRepository = shelveEntityRepository;
        this.authClient = authClient;
        this.outboundCargoService = outboundCargoService;
        this.actualUseService = actualUseService;
        this.storeHeaderService = storeHeaderService;
        this.historyClient = historyClient;
    }

    //----------------------------------= 入库申请 =---------------------------------------

    /**
     * 发起备件出/入库申请
     *
     * @param rootSpareDTO 出库申请申请单
     * @return 出库申请单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @SuppressWarnings(StoreConfig.RULES)
    public RootSpareDTO applySpare(RootSpareDTO rootSpareDTO) {
        //获取申请项
        @Valid List<ItemsSpareDTO> items = rootSpareDTO.getVariables().getItems();
        if (items == null) {
            throw new BaseKnownException(StoreExceptionEnum.TYPE_IS_NULL_EXCEPTION);
        }
        if (items.size() <= 0) {
            throw new BaseKnownException(StoreExceptionEnum.TYPE_IS_NULL_EXCEPTION);
        }
        //获取自定义参数
        VariablesSpareDTO variables = rootSpareDTO.getVariables();
        //获取单号
        variables.setReceiptNumber(serialUtil.getSerialNumber(rootSpareDTO.getServiceName()));

        variables.setProcessInstanceId(rootSpareDTO.getBusinessKey());
        //赋值自定义参数
        rootSpareDTO.setVariables(variables);
        //是否开启审批
        if (isApproval) {
            //开启审批
            //设置流程图
            rootSpareDTO.setProcessDefinitionKey(StoreConfig.SPARE_PROCESS_DEFINITION_KEY);
            //转换对象
            ProcessInstanceStartDTO startDTO = DtoMapper.convert(rootSpareDTO, new ProcessInstanceStartDTO().getClass());
            //定义自定义参数
            Map<String, Object> map;
            try {
                //把参数变成map形式
                map = DtoMapper.convertMap(rootSpareDTO.getVariables());
                //给自定义参数赋值
                startDTO.setVariables(map);
            } catch (Exception e) {
                log.error("applySpare() -> DtoMapper.convertMap() error");
                e.printStackTrace();
            }
            //补全相关属性
            for (ItemsSpareDTO itemsSpareDTO1 : variables.getItems()) {
                //补全备件名称
                Optional<HvEamSpare> byId = spareEntityRepository.findById(itemsSpareDTO1.getSpareId());
                if (byId.isPresent()) {
                    itemsSpareDTO1.setSpareName(byId.get().getSpareName());
                } else {
                    itemsSpareDTO1.setSpareName("");
                }
                if (itemsSpareDTO1.getShelveId() != null) {
                    //补全库房名称
                    if (shelveEntityRepository.existsById(itemsSpareDTO1.getShelveId())) {
                        itemsSpareDTO1.setShelveName(shelveEntityRepository.findById(itemsSpareDTO1.getShelveId()).get().getShelveName());
                    } else {
                        itemsSpareDTO1.setShelveName("");
                    }
                }
            }
            //创建流程
            ResultVO<ProcessInstanceDTO> processInstanceDTOResultVO = activitiClient.startProcessInstance(startDTO);
            if (!processInstanceDTOResultVO.isSuccess()) {
                throw new BaseKnownException(processInstanceDTOResultVO);
            }
        } else {
            //无审批
            rootSpareDTO.setVariables(storeHeaderService.addHeader(rootSpareDTO.getVariables()));
        }
        return rootSpareDTO;
    }


    /**
     * 备件入库
     *
     * @param importSpareDTOS
     * @return
     */
    private RootSpareDTO setRootSpareDTO(List<ImportSpareDTO> importSpareDTOS, String inOut, String key, String source,
                                         String title) {
        RootSpareDTO rootSpareDTO = new RootSpareDTO();
        rootSpareDTO.setBusinessKey(key);
        VariablesSpareDTO variablesSpareDTO = new VariablesSpareDTO();
        variablesSpareDTO.setInOut(inOut);
        variablesSpareDTO.setIsPass("1");
        variablesSpareDTO.setSource(source);
        variablesSpareDTO.setTitle(title);
        List<String> collect = importSpareDTOS.stream().map(ImportSpareDTO::getShelveCode).collect(Collectors.toList());
        List<HvEamShelve> allByShelveCodeIn = shelveEntityRepository.findAllByShelveCodeIn(collect);
        for (ImportSpareDTO importSpareDTO : importSpareDTOS) {
            Assert.notNull(importSpareDTO.getSpareCode(), "备件编码不能为空");
            Assert.notNull(importSpareDTO.getShelveCode(), "库房编码不能为空");
            HvEamSpare allBySpareCode = spareEntityRepository.findAllBySpareCode(importSpareDTO.getSpareCode());
            if (allBySpareCode == null) {
                throw new BaseKnownException(10000, "为找到备件编码为" + importSpareDTO.getSpareCode() + "的数据", null);
            }
            importSpareDTO.setSpareId(allBySpareCode.getId());
            for (HvEamShelve hvEamShelve : allByShelveCodeIn) {
                if (importSpareDTO.getShelveCode().equals(hvEamShelve.getShelveCode())) {
                    importSpareDTO.setShelveName(hvEamShelve.getShelveName());
                    importSpareDTO.setShelveId(hvEamShelve.getId());
                }
            }
        }
        List<ItemsSpareDTO> itemsSpareDTOS = DtoMapper.convertList(importSpareDTOS, ItemsSpareDTO.class);
        variablesSpareDTO.setItems(itemsSpareDTOS);
        rootSpareDTO.setVariables(variablesSpareDTO);
        return rootSpareDTO;
    }

    /**
     * 导入备件申请信息信息
     *
     * @param file 备件申请信息excel
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @Override
    public ImportResult importInSpare(MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        ImportResult importResult = new ImportResult();
        List<ImportSpareDTO> fileData = ExcelUtils.getFileData(file, 0, ImportSpareDTO.class, importResult);
        Map<String, List<ImportSpareDTO>> map = new HashMap<>();
        for (ImportSpareDTO fileDatum : fileData) {
            String spareNo = fileDatum.getSpareNo();
            boolean key = map.containsKey(spareNo);
            if (spareNo != null) {
                if (key) {
                    List<ImportSpareDTO> spareDTOS = map.get(spareNo);
                    spareDTOS.add(fileDatum);
                    map.put(spareNo, spareDTOS);
                } else {
                    List<ImportSpareDTO> spareDTOS = new ArrayList<>();
                    spareDTOS.add(fileDatum);
                    map.put(spareNo, spareDTOS);
                }
            }
        }
        for (Map.Entry<String, List<ImportSpareDTO>> entry : map.entrySet()) {
            RootSpareDTO rootSpareDTO = setRootSpareDTO(entry.getValue(), "1", "备件-采购", "备件申请", "备件入库");
            try {
                RootSpareDTO spareDTO = applySpare(rootSpareDTO);
                storeHeaderService.deliveryOfCargoFromStorage(spareDTO.getVariables().getHeaderId());
            } catch (Exception e) {
                Map<Integer, String> objectObjectMap = new HashMap<>();
                for (Integer successLine : importResult.getSuccessLines()) {
                    objectObjectMap.put(successLine, e.toString());
                }
                importResult.setErrorLines(objectObjectMap);
                importResult.setSuccessLines(new ArrayList<>());
                log.error("导入失败");
            }
        }
        return importResult;
    }

    /**
     * 导入备件出库库申请信息信息
     *
     * @param file 备件出库申请信息excel
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @Override
    public ImportResult importOutSpare(MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        ImportResult importResult = new ImportResult();
        List<ImportSpareDTO> fileData = ExcelUtils.getFileData(file, 0, ImportSpareDTO.class, importResult);
        Map<String, List<ImportSpareDTO>> map = new HashMap<>();
        for (ImportSpareDTO fileDatum : fileData) {
            String spareNo = fileDatum.getSpareNo();
            boolean key = map.containsKey(spareNo);
            if (spareNo != null) {
                if (key) {
                    List<ImportSpareDTO> spareDTOS = map.get(spareNo);
                    spareDTOS.add(fileDatum);
                    map.put(spareNo, spareDTOS);
                } else {
                    List<ImportSpareDTO> spareDTOS = new ArrayList<>();
                    spareDTOS.add(fileDatum);
                    map.put(spareNo, spareDTOS);
                }
            }
        }
        for (Map.Entry<String, List<ImportSpareDTO>> entry : map.entrySet()) {
            RootSpareDTO rootSpareDTO = setRootSpareDTO(entry.getValue(), "2", "备件-出库", "备件-出库", "备件-出库");
            try {
                RootSpareDTO spareDTO = applySpare(rootSpareDTO);
                storeHeaderService.deliveryOfCargoFromStorage(spareDTO.getVariables().getHeaderId());
                for (ItemsSpareDTO item : spareDTO.getVariables().getItems()) {
                    ActualUseDTO actualUseDTO = new ActualUseDTO();
                    actualUseDTO.setSpareId(item.getSpareId());
                    actualUseDTO.setSpareName(item.getSpareName());
                    actualUseDTO.setBatchNumber(item.getBatchNumber());
                    actualUseDTO.setShelveId(item.getShelveId());
                    actualUseDTO.setShelveName(item.getShelveName());
                    actualUseDTO.setNumber(item.getNumber());
                    actualUseDTO.setType(1);
                    actualUseDTO.setHeaderId(spareDTO.getVariables().getHeaderId());
                    actualUseDTO.setProcessInstanceId("备件-出库");
                    actualUseService.create(actualUseDTO);
                }
            } catch (Exception e) {
                Map<Integer, String> objectObjectMap = new HashMap<>();
                for (Integer successLine : importResult.getSuccessLines()) {
                    objectObjectMap.put(successLine, e.toString());
                }
                importResult.setErrorLines(objectObjectMap);
                importResult.setSuccessLines(new ArrayList<>());
                log.error("导入失败");
            }
        }
        return importResult;
    }


    /**
     * 发起油品出/入库申请
     *
     * @param rootLubDTO 申请单
     * @return 申请单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RootLubDTO applyLub(RootLubDTO rootLubDTO) {
        //获取申请项
        @Valid List<ItemsLubDTO> items = rootLubDTO.getVariables().getItems();
        if (items == null) {
            throw new BaseKnownException(StoreExceptionEnum.TYPE_IS_NULL_EXCEPTION);
        }
        if (items.size() < 1) {
            throw new BaseKnownException(StoreExceptionEnum.TYPE_IS_NULL_EXCEPTION);
        }
        //获取自定义参数
        VariablesLubDTO variables = rootLubDTO.getVariables();
        //获取单号
        variables.setReceiptNumber(serialUtil.getSerialNumber(rootLubDTO.getServiceName()));

        //是否开启审批
        if (isApproval) {
            //转换对象
            ProcessInstanceStartDTO startDTO = DtoMapper.convert(rootLubDTO, ProcessInstanceStartDTO.class);
            startDTO.setProcessDefinitionKey(StoreConfig.LUB_PROCESS_DEFINITION_KEY);
            startDTO.setBusinessKey(rootLubDTO.getVariables().getReceiptNumber());
            //定义自定义参数
            Map<String, Object> map;
            try {
                map = DtoMapper.convertMap(variables);
            } catch (IllegalAccessException e) {
                log.error("转换对象异常", e);
                throw new BaseKnownException("程序错误，转换参数对象异常");
            }
            //给自定义参数赋值
            startDTO.setVariables(map);
            //补全相关属性
            for (ItemsLubDTO itemsLubDTO : variables.getItems()) {
                HvEamLubricating lub = lubEntityRepository.findById(itemsLubDTO.getLubId())
                    .orElseThrow(() -> new BaseKnownException("找不到油品信息，请检查输入的油品信息"));
                itemsLubDTO.setLubName(lub.getLubName());
                if (Objects.isNull(itemsLubDTO.getShelveId())) {
                    throw new BaseKnownException("油品库房信息不能为空");
                }
                itemsLubDTO.setShelveName(shelveEntityRepository.findById(itemsLubDTO.getShelveId())
                    .orElseThrow(() -> new BaseKnownException("找不到库位信息，请检查录入的库位信息"))
                    .getShelveName());
            }
            //创建流程
            ResultVO<ProcessInstanceDTO> processInstanceDTOResultVO = activitiClient.startProcessInstance(startDTO);
            if (!processInstanceDTOResultVO.isSuccess()) {
                throw new BaseKnownException(processInstanceDTOResultVO);
            }
            rootLubDTO.setProcessInstanceId(processInstanceDTOResultVO.getData().getProcessInstanceId());
        } else {
            //无审批
            rootLubDTO.setVariables(storeHeaderService.addHeader(rootLubDTO.getVariables()));
        }
        return rootLubDTO;
    }

    //----------------------------------= 审批 =------------------------------------------

    /**
     * 备件出/入库申请审批 当状态为通过时isPass 为2 驳回时isPass 为 3
     *
     * @param taskID 任务ID
     * @param isPass 是否通过
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void spareAndLubIsThrough(String taskID, Integer isPass, int typeClass, String token) {
        //修改参数
        Map<String, Object> taskVar = new HashMap<>(1);
        //更新审批状态
        taskVar.put("isPass", isPass);
        //完成任务
        completeTask(taskID, taskVar, token);
        //当审批状态为通过的时候，判断是否为最后一步
        if (isPass == 2) {
            //获取参数
            Data task = getTaskByQuery(taskID);
            //判断当前为最后一步
            if (StoreConfig.ACTIVITI_END.equals(task.getTaskDefinitionKey())) {
                HistoryVariables variableList = task.getHistoryVariables();
                String processInstanceId = task.getHistoryVariables().getProcessInstanceId();
                //增加头表同时增加行表
                storeHeaderService.addHeader(processInstanceId, variableList, typeClass);
            }
        }
    }

    /**
     * 通过taskID 获取相关参数
     *
     * @param taskID 任务id
     * @return 相关参数
     */
    private Data getTaskByQuery(String taskID) {
        log.info(taskID);
        JsonRootBean taskInfo = historyClient.getHistoricTaskInstanceById(taskID);
        if (taskInfo.getCode()!=200) {
            throw new BaseKnownException(taskInfo.getMessage());
        }
        return taskInfo.getData();
    }

    //------------------------------------= 出库 =--------------------------------------------------

    /**
     * 备件出库 库管
     *
     * @param processInstanceID 流程ID
     * @param isPass            是否通过
     */
    @Override
    public Integer spareAndLubOut(String processInstanceID, Boolean isPass, Integer type) {
        //数据验证
        HvEamOutboundCargo outboundCargo = outboundCargoService.findByProcessInsttnceId(processInstanceID);
        if (outboundCargo != null && outboundCargo.getType() == 1) {
            //不可重复出库 出库单无效
            throw new BaseKnownException(StoreExceptionEnum.THE_DELIERY_ORDER_IS_INVALID_EXCEPTION);
        }

        //获取 历史流程查询对象
        HistoricProcessQuery historicProcessQuery = getHistoricProcessQuery();
        //设置流程ID
        historicProcessQuery.setProcessInstanceId(processInstanceID);
        //查询历史
        ResultVO<List<HistoryProcessInstanceDTO>> historicProcessInstanceList = activitiClient.getHistoricProcessInstanceList(historicProcessQuery);
        if (!historicProcessInstanceList.isSuccess()) {
            throw new BaseKnownException(historicProcessInstanceList);
        }
        //数据错误
        if (historicProcessInstanceList.getData().size() <= 0) {
            throw new BaseKnownException(StoreExceptionEnum.DATE_ERROR_EXCEPTION);
        }
        //是否通过
        if (isPass) {
            //通过 入库
            //取出当前参数列表
            Map<String, Object> map = historicProcessInstanceList.getData().get(0).getProcessInstanceVariables();
            //备件
            if (type == 1) {
                //------------------------- 判断当前参数正确性 -----------------------------------
                if (isPass) {
                    List<HvEamSpareToShelve> value = getMapToList(map, 1);
                    //判断当前出入库情况
                    if (INOUT_TYPE_IN.equals(map.get(INOUT))) {
                        for (HvEamSpareToShelve s : value) {
                            //查询是否有数据
                            HvEamSpareToShelve spare = spareToShelveEntityRepository.findAllBySpareIdAndShelveIdAndBatchNumber(s.getSpareId(), s.getShelveId(), s.getBatchNumber());
                            if (spare == null) {
                                //添加新的数据
                                spareToShelveEntityRepository.save(s);
                            } else {
                                //修改数据
                                spare.setNumber(spare.getNumber() + s.getNumber());
                                spareToShelveEntityRepository.save(spare);
                            }
                        }
                    } else if (INOUT_TYPE_OUT.equals(map.get(INOUT))) {
                        //通过 出库
                        //判断库存是否充足
                        for (HvEamSpareToShelve s : value) {
                            //查询是否有数据
                            HvEamSpareToShelve spare = spareToShelveEntityRepository.findAllBySpareIdAndShelveIdAndBatchNumber(s.getSpareId(), s.getShelveId(), s.getBatchNumber());
                            //备件库存数量不足
                            if (spare == null) {
                                //备件库存数量不足
                                throw new BaseKnownException(StoreExceptionEnum.SHELVE_AND_BATCH_NUMBER_NOT_SELECT_XXX);
                            }
                            //如果库存数量不足
                            if (spare.getNumber() <= s.getNumber()) {
                                //备件库存数量不足
                                throw new BaseKnownException(StoreExceptionEnum.XXX_SHELVE_NUMBER_EXCEPTION, spareEntityRepository.findById(s.getSpareId()).get().getSpareName());
                            }
                        }
                        //更改数据
                        for (HvEamSpareToShelve s : value) {
                            HvEamSpareToShelve spare = spareToShelveEntityRepository.findAllBySpareIdAndShelveIdAndBatchNumber(s.getSpareId(), s.getShelveId(), s.getBatchNumber());
                            //更新数量
                            s.setNumber(spare.getNumber() - s.getNumber());
                            spareToShelveEntityRepository.save(spare);
                        }
                        //出库表记录
                        return outboundCargo(processInstanceID, 1, 1);
                    }
                } else {
                    //驳回
                    return outboundCargo(processInstanceID, 2, 1);
                }
            }
            //油品
            if (type == 2) {
                //------------------------- 判断当前参数正确性 -----------------------------------
                if (isPass) {
                    List<HvEamLubToShelve> value = getMapToList(map, 2);
                    //判断当前出入库情况
                    if (INOUT_TYPE_IN.equals(map.get(INOUT))) {
                        for (HvEamLubToShelve s : value) {
                            //查询是否有数据
                            HvEamLubToShelve lubToShelve = lubToShelveEntityRepository.findAllByLubIdAndShelveIdAndBatchNumber(s.getLubId(), s.getShelveId(), s.getBatchNumber());
                            if (lubToShelve == null) {
                                //添加新的数据
                                lubToShelveEntityRepository.save(s);
                            } else {
                                //修改数据
                                lubToShelve.setNumber(lubToShelve.getNumber().add(s.getNumber()));
                                lubToShelveEntityRepository.save(lubToShelve);
                            }
                        }
                    } else if (INOUT_TYPE_OUT.equals(map.get(INOUT))) {
                        //通过 出库
                        //判断库存是否充足
                        for (HvEamLubToShelve s : value) {
                            //查询是否有数据
                            HvEamLubToShelve lubToShelve = lubToShelveEntityRepository.findAllByLubIdAndShelveIdAndBatchNumber(s.getLubId(), s.getShelveId(), s.getBatchNumber());
                            //备件库存数量不足
                            if (lubToShelve == null) {
                                //备件库存数量不足
                                throw new BaseKnownException(StoreExceptionEnum.SHELVE_AND_BATCH_NUMBER_NOT_SELECT_XXX);
                            }
                            //如果库存数量不足
                            if (lubToShelve.getNumber().compareTo(s.getNumber()) < 0) {
                                //备件库存数量不足
                                throw new BaseKnownException(StoreExceptionEnum.XXX_SHELVE_NUMBER_EXCEPTION, lubEntityRepository.findById(s.getLubId()).get().getLubName());
                            }
                        }
                        //更改数据
                        for (HvEamLubToShelve s : value) {
                            HvEamLubToShelve lubToShelve = lubToShelveEntityRepository.findAllByLubIdAndShelveIdAndBatchNumber(s.getLubId(), s.getShelveId(), s.getBatchNumber());
                            //更新数量
                            s.setNumber(lubToShelve.getNumber().subtract(s.getNumber()));
                            lubToShelveEntityRepository.save(lubToShelve);
                        }
                        //出库表记录
                        return outboundCargo(processInstanceID, 1, 2);
                    }
                } else {
                    //驳回
                    return outboundCargo(processInstanceID, 2, 2);
                }
            }
        }
        return null;
    }

    //------------------------------------= 过期 =--------------------------------------------------

    /**
     * 生成唯一不重复流水单号
     *
     * @param service 唯一标识 （推荐服务名）
     * @return 唯一不重复流水单号
     */
    @Deprecated
    @Override
    public synchronized String getSerialNumber(String service) {
        //获取当前天数
        Calendar cal1 = Calendar.getInstance();
        //月
        String month = 1 + cal1.get(Calendar.MONTH) + "";
        //位数不足 用0补齐
        if (month.length() < 2) {
            month = "0" + month;
        }
        //日
        String newDate = cal1.get(Calendar.DATE) + "";
        //位数不足 用0补齐
        if (newDate.length() < 2) {
            newDate = "0" + newDate;
        }
        //时间拼装 年 月 日
        String date = cal1.get(Calendar.YEAR) + month + newDate;
        //通过服务获取该服务的
        HvEamSerialNumber byService = serialNumberRepository.findByService(service);

        //当查询的服务不存在的情况下 添加当前数据
        if (byService == null) {
            byService = new HvEamSerialNumber();
            byService.setNumber(1);
            byService.setId(0);
            byService.setService(service);
            byService.setDay(new Date(System.currentTimeMillis()));
            serialNumberRepository.save(byService);
            return date + "0001";
        }

        //取出数据中的天数
        Calendar cal = Calendar.getInstance();
        cal.setTime(byService.getDay());
        int date1 = cal.get(Calendar.DATE);

        //如果天数不等 表示是第二天
        if (date1 != cal1.get(Calendar.DATE)) {
            //日期更新
            byService.setDay(new Date(System.currentTimeMillis()));
            //流水号更新
            byService.setNumber(1);
            serialNumberRepository.save(byService);
            return date + "0001";
        } else {
            //获取当前流水号
            Integer number = byService.getNumber();
            //流水号自增
            byService.setNumber(++number);
            //记录数据库
            serialNumberRepository.save(byService);
            //位数补齐操作
            String a = "0000" + number;
            return date + a.substring(a.length() - 4);
        }
    }

    /**
     * 生成唯一不重复的单号 生成规则  年 月 日 时 分 秒 随机数（四位）共计18位
     *
     * @return 唯一单号
     */
    @Deprecated
    @Override
    @SuppressWarnings(StoreConfig.RULES)
    public String getGodownReceiptNumber() {
        //生成规则  年 月 日 时 分 秒 四位随机数 共计18位
        String date = "yyyyMMddHHmmss";
        return new SimpleDateFormat(date).format(new Date()) + new Random().nextInt(10) + new Random().nextInt(10) + new Random().nextInt(10) + new Random().nextInt(10);
    }

    //------------------------------------= 工具 =--------------------------------------------------

    /**
     * 通过map 转List
     *
     * @param map  需要转换的Map
     * @param type 类型 1 备件 2 油品
     * @return List
     */
    private List getMapToList(Map<String, Object> map, Integer type) {
        if (type == 1) {
            List<Map<String, Object>> items = (List<Map<String, Object>>) map.get(ITEM);
            List<HvEamSpareToShelve> hvEamSpareToShelves = new ArrayList<>();
            for (Map<String, Object> m : items) {
                HvEamSpareToShelve itemsSpareDTO = DtoMapper.convertObject(m, HvEamSpareToShelve.class);
                itemsSpareDTO.setNumber(Integer.parseInt(m.get("number") + ""));
                itemsSpareDTO.setUpdateTime(new Date());
                itemsSpareDTO.setCreateTime(new Date());
                hvEamSpareToShelves.add(itemsSpareDTO);
            }
            return hvEamSpareToShelves;
        }
        if (type == 2) {
            List<Map<String, Object>> items = (List<Map<String, Object>>) map.get(ITEM);
            List<HvEamLubToShelve> hvEamLubToShelves = new ArrayList<>();
            for (Map<String, Object> m : items) {
                HvEamLubToShelve itemsSpareDTO = DtoMapper.convertObject(m, HvEamLubToShelve.class);
                itemsSpareDTO.setNumber(new BigDecimal(m.get("number") + ""));
                itemsSpareDTO.setUpdateTime(new Date());
                itemsSpareDTO.setCreateTime(new Date());
                hvEamLubToShelves.add(itemsSpareDTO);
            }
            return hvEamLubToShelves;
        }
        return null;
    }

    /**
     * 出库表添加
     *
     * @param processInstanceId 流程ID
     * @param inOut             出入库
     * @param type              类型
     * @return id
     */
    private Integer outboundCargo(String processInstanceId, Integer inOut, Integer type) {
        HvEamOutboundCargo cargo = new HvEamOutboundCargo();
        cargo.setInOut(inOut);
        cargo.setProcessInstanceId(processInstanceId);
        cargo.setType(type);
        cargo.setCreateTime(new Date());
        cargo.setUpdateTime(new Date());
        return outboundCargoService.save(cargo).getId();
    }

    /**
     * 通过流程ID 当前参数 结束任务
     *
     * @param taskID 流程ID
     * @param map    当前参数
     */
    private void completeTask(String taskID, Map<String, Object> map, String token) {
        //接口 DTO
        TaskHandleDTO task = new TaskHandleDTO();
        //传入taskID
        task.setTaskId(taskID);
        //传入task参数
        task.setVariables(map);
        //完成任务 并传入新的参数
        ResultVO resultVO = activitiClient.completeTask(task, token);
        if (!resultVO.isSuccess()) {
            throw new BaseKnownException(resultVO);
        }

    }

    /**
     * 获取历史流程查询对象
     *
     * @return 历史流程查询对象
     */
    private HistoricProcessQuery getHistoricProcessQuery() {
        //创建历史流程查询对象
        HistoricProcessQuery historicProcessQuery = new HistoricProcessQuery();
        //设置流程图
        historicProcessQuery.setProcessDefinitionKey(StoreConfig.SPARE_PROCESS_DEFINITION_KEY);
        //查询为以结束
        historicProcessQuery.setFinished(true);
        return historicProcessQuery;
    }


}