package com.hvisions.eam.service.fault;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.activiti.fault.RootEquipmentFaultDTO;

/**
 * <p>Title:MaintenanceReportService</p>
 * <p>Description:设备报修</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/1</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
public interface MaintenanceReportService {

    /**
     * 设备报修申请
     *
     * @param rootEquipmentFaultDTO 报修申请单
     * @return 报修申请单
     */
    ResultVO applyEquipmentFault(RootEquipmentFaultDTO rootEquipmentFaultDTO);

    /**
     * 通过服务名获取单号
     *
     * @param service 服务名
     * @return 单号
     */
    String getSerialNumber(String service);
}
