package com.hvisions.eam.service.spare;

import java.util.List;

/**
 * <p>Title:SpareFileService</p>
 * <p>Description:备件文件</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/17</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
public interface SpareFileService {

    /**
     * 通过备件ID获取 相关文件列表
     *
     * @param spareId 备件ID
     * @return 文件ID列表
     */
    List<Integer> getFileBySpareId(Integer spareId);

    /**
     * 通过备件ID添加文件
     *
     * @param spareId 备件ID
     * @param files   文件列表
     * @return 管理学
     */
    List<Integer> addFileBySpareId(Integer spareId, List<Integer> files);

    /**
     * 删除关系
     *
     * @param fileId 文件id
     */
    void deleteBySpareToFileId(Integer fileId);

    /**
     * 刪除全部关联通过备件ID
     *
     * @param spareId 备件id
     */
    void deleteBySpareId(Integer spareId);

    /**
     * 删除关系
     *
     * @param spareId 备件id
     * @param fileId  文件id
     */
    void deleteBySpareIdAndFileId(Integer spareId, Integer fileId);
}
