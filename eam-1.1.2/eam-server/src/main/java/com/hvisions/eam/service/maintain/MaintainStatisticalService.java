package com.hvisions.eam.service.maintain;

import com.hvisions.eam.dto.maintain.MaintainDrawingBoardDTO;
import com.hvisions.eam.dto.maintain.MaintainStatisticalDTO;
import com.hvisions.eam.dto.maintain.MaintainStatisticalQueryDTO;
import com.hvisions.eam.dto.maintain.MaintainStatisticalTimeQuantumDTO;

import java.util.Date;
import java.util.List;

/**
 * <p>Title: MaintainStatisticalService</p >
 * <p>Description: 保养统计service</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/4</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface MaintainStatisticalService {
    /**
     * 保养统计
     *
     * @param maintainStatisticalQueryDTO 查询条件
     * @return 统计
     */
    List<MaintainStatisticalDTO> getMaintainStatistical(MaintainStatisticalQueryDTO maintainStatisticalQueryDTO);

    /**
     * 获取今日保养次数
     *
     * @return 次数
     */
    Integer getTodayMaintainCount();

    /**
     * 获取本周保养次数
     *
     * @return 次数
     */
    Integer getWeekCount();

    /**
     * 根据时间段查次数
     *
     * @param maintainStatisticalQueryDTO 查询条件
     * @return list
     */
    List<MaintainStatisticalTimeQuantumDTO> getCountByTimeQuantum(MaintainStatisticalQueryDTO maintainStatisticalQueryDTO);

    /**
     *  获取保养润滑看板
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 看板
     */
    MaintainDrawingBoardDTO getMaintainDrawingBoardDTO(Date startTime, Date endTime);
}