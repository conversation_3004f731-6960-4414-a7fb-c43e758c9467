package com.hvisions.eam.service.autonomy.imp;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.client.autonomy.AuthClient;
import com.hvisions.eam.dao.StatisticalMapper;
import com.hvisions.eam.dto.autonomy.*;
import com.hvisions.eam.entity.autonomy.HvAmAutonomyMaintenanceProcessData;
import com.hvisions.eam.entity.autonomy.HvAmAutonomyMaintenanceProcessItem;
import com.hvisions.eam.enums.HExceptionEnum;
import com.hvisions.eam.service.autonomy.MaintenanceProcessDataService;
import com.hvisions.eam.service.autonomy.StatisticalService;
import com.hvisions.hipertools.client.TimerClient;
import com.hvisions.hipertools.dto.TimerDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.*;

import static java.util.stream.Collectors.toList;

/**
 * @author: xiehao
 * @version: 1.0
 */
@Service
@Slf4j
public class StatisticalServiceImpl extends ServiceImpl<StatisticalMapper, HvAmAutonomyMaintenanceProcessItem> implements StatisticalService {

    private final StatisticalMapper statisticalMapper;
    private final MaintenanceProcessDataService maintenanceProcessDataService;
    private final TimerClient timerClient;
    private final AuthClient authClient;

    @Autowired
    public StatisticalServiceImpl(StatisticalMapper statisticalMapper, MaintenanceProcessDataService maintenanceProcessDataService, TimerClient timerClient, AuthClient authClient) {
        this.statisticalMapper = statisticalMapper;
        this.maintenanceProcessDataService = maintenanceProcessDataService;
        this.timerClient = timerClient;
        this.authClient = authClient;
    }

    @Override
    public Page<MaintenanceProcessDataDTO> queryProcessDataList(MaintenanceProcessDataQueryDTO dto) {

        Page<MaintenanceProcessDataDTO> maintenanceProcessDataDTOS = PageHelperUtil.getPage(statisticalMapper::getdataList, dto);

        for (MaintenanceProcessDataDTO one : maintenanceProcessDataDTOS) {
            try {
                ResultVO<TimerDTO> byTimerId = timerClient.getByTimerId(one.getTimerId());
                TimerDTO data = byTimerId.getData();
                one.setTimerIdDes(data.getDescription());
                one.setCompletionStatus("1");
                one.setConfirmStatus("1");
            } catch (Exception e) {
                one.setTimerIdDes(one.getTimerId() + "");
                one.setCompletionStatus("1");
                one.setConfirmStatus("1");
            }
            try {
                JsonByUserId jsonByUserId = authClient.getUserById(Integer.parseInt(one.getPersonLiable()));
                one.setExecutor(jsonByUserId.getData().getUserName());
            } catch (Exception e) {
                one.setExecutor(one.getStartUserId());
            }
            try {
                JsonByUserId jsonByUserId = authClient.getUserById(Integer.parseInt(one.getVerifier()));
                one.setVerifier(jsonByUserId.getData().getUserName());
            } catch (Exception e) {
                one.setVerifier(one.getVerifier());
            }


        }

        return maintenanceProcessDataDTOS;
    }

    @Override
    public Map<String, Object> queryProcessItemList(MaintenanceProcessItemQueryDTO dto) {

        //获取时间内的计划信息
        List<HvAmAutonomyMaintenanceProcessData> dataList = maintenanceProcessDataService.getDataList(dto.getStartTime(), dto.getEndTime());

        HashMap<String, Object> data = new HashMap<>();

        //获取计划ID——list
        List<Integer> idList = new ArrayList<>();
        for (HvAmAutonomyMaintenanceProcessData temp : dataList) {
            idList.add(temp.getId());
        }

        //没有输入 项目编号
        if (StringUtils.isBlank(dto.getNumber())) {

            dto.setIds(idList);
            //通过计划ID查询 所有的项目编号 分页查询
            QueryWrapper<HvAmAutonomyMaintenanceProcessItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.select("number").in(idList.size() > 0, "process_data_id", idList).groupBy("number");
            IPage<HvAmAutonomyMaintenanceProcessItem> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(dto.getPage() + 1, dto.getPageSize());
            page = this.page(page, queryWrapper);

            List<MaintenanceProcessItemDTO> resultList = new ArrayList<>();
            for (HvAmAutonomyMaintenanceProcessItem temp : page.getRecords()) {
                int count = 0;//总检测数
                int errCount = 0;//异常-总检测数
                int qualifiedCount = 0;//合格-总检测数

                int count_0 = 0;
                int count_1 = 0;
                int max_0 = 0;   //最大连续合格
                int max_1 = 0;   //最大连续异常
                QueryWrapper<HvAmAutonomyMaintenanceProcessItem> queryWrapperTemp = new QueryWrapper<>();
                List<HvAmAutonomyMaintenanceProcessItem> hvAmAutonomyMaintenanceProcessItems = new ArrayList<>();
                if (idList != null && idList.size() > 0) {
                    queryWrapperTemp.eq("number", temp.getNumber()).in(idList.size() > 0, "process_data_id", idList).orderByDesc(
                            "id");
                    hvAmAutonomyMaintenanceProcessItems = this.baseMapper.selectList(queryWrapperTemp);

                }
                count = hvAmAutonomyMaintenanceProcessItems.size();
                List<Integer> tempIDList = new ArrayList<>();
                for (HvAmAutonomyMaintenanceProcessItem item : hvAmAutonomyMaintenanceProcessItems) {
                    // 0 - 否，1 - 是
                    if ("0".equals(item.getHasError())) {
                        count_0++;
                        count_1 = 0;
                        qualifiedCount = qualifiedCount + 1;
                    } else if ("1".equals(item.getHasError())) {
                        count_1++;
                        count_0 = 0;
                        errCount = errCount + 1;
                    }
                    if (count_0 > max_0) {
                        max_0 = count_0;
                    }
                    if (count_1 > max_1) {
                        max_1 = count_1;
                    }
                    tempIDList.add(item.getProcessDataId());
                }
                if (hvAmAutonomyMaintenanceProcessItems.size() > 0) {
                    MaintenanceProcessItemDTO resultDto = new MaintenanceProcessItemDTO();

                    resultDto.setNumber(hvAmAutonomyMaintenanceProcessItems.get(0).getNumber());
                    resultDto.setName(hvAmAutonomyMaintenanceProcessItems.get(0).getName());
                    resultDto.setGroupId(hvAmAutonomyMaintenanceProcessItems.get(0).getGroupId());
                    resultDto.setGroupName(hvAmAutonomyMaintenanceProcessItems.get(0).getGroupName());
                    resultDto.setTestContent(hvAmAutonomyMaintenanceProcessItems.get(0).getTestContent());
                    resultDto.setTestCycle(hvAmAutonomyMaintenanceProcessItems.get(0).getTestCycle());
                    resultDto.setTestMethod(hvAmAutonomyMaintenanceProcessItems.get(0).getTestMethod());
                    resultDto.setIdList(tempIDList);
                    resultDto.setCount(count);
                    resultDto.setErrCount(errCount);
                    resultDto.setQualifiedCount(qualifiedCount);
                    resultDto.setMaxErr(max_1);
                    resultList.add(resultDto);
                }
            }

            data.put("current", page.getCurrent());//当前页
            data.put("size", page.getSize());//当前页大小
            data.put("total", page.getTotal());//总条数数
            data.put("pages", page.getPages());//总页数
            data.put("records", resultList);//查询出来的记录list

        } else {
            //用户输入了项目编号
            //获取包含该项目的计划
            QueryWrapper<HvAmAutonomyMaintenanceProcessItem> query = new QueryWrapper<>();
            query.select("process_data_id").eq(StringUtils.isNotBlank(dto.getNumber()), "number", dto.getNumber());
            List<HvAmAutonomyMaintenanceProcessItem> list = this.list(query);
            if (list.size() < 1) {
                //没有 该项目
                data.put("current", 0);//当前页
                data.put("size", dto.getPageSize());//当前页大小
                data.put("total", 0);//总条数数
                data.put("pages", 0);//总页数
                data.put("records", new ArrayList<>());//查询出来的记录list
            } else {
                //项目内的计划ID
                List<Integer> xmIdList = new ArrayList<>();
                for (HvAmAutonomyMaintenanceProcessItem temp : list) {
                    xmIdList.add(temp.getProcessDataId());
                }
                // 时间内的计划  项目内的计划 取交集
                List<Integer> intersection = xmIdList.stream().filter(item -> idList.contains(item)).collect(toList());

                int count = 0;//总检测数
                int errCount = 0;//异常-总检测数
                int qualifiedCount = 0;//合格-总检测数

                int count_0 = 0;
                int count_1 = 0;
                int max_0 = 0;   //最大连续合格
                int max_1 = 0;   //最大连续异常
                QueryWrapper<HvAmAutonomyMaintenanceProcessItem> queryWrapperTemp = new QueryWrapper<>();

                List<HvAmAutonomyMaintenanceProcessItem> hvAmAutonomyMaintenanceProcessItems = new ArrayList<>();
                if (intersection.size() > 0) {
                    queryWrapperTemp.eq("number", dto.getNumber()).in(intersection.size() > 0, "process_data_id",
                            intersection).orderByDesc("id");

                    hvAmAutonomyMaintenanceProcessItems = this.baseMapper.selectList(queryWrapperTemp);
                    count = hvAmAutonomyMaintenanceProcessItems.size();
                }
                for (HvAmAutonomyMaintenanceProcessItem item : hvAmAutonomyMaintenanceProcessItems) {
                    // 0 - 否，1 - 是
                    if ("0".equals(item.getHasError())) {
                        count_0++;
                        count_1 = 0;
                        qualifiedCount = qualifiedCount + 1;
                    } else if ("1".equals(item.getHasError())) {
                        count_1++;
                        count_0 = 0;
                        errCount = errCount + 1;
                    }
                    if (count_0 > max_0) {
                        max_0 = count_0;
                    }
                    if (count_1 > max_1) {
                        max_1 = count_1;
                    }
                }
                List<MaintenanceProcessItemDTO> resultList = new ArrayList<>();
                MaintenanceProcessItemDTO resultDto = new MaintenanceProcessItemDTO();
                if (intersection.size() > 0) {
                    resultDto.setNumber(hvAmAutonomyMaintenanceProcessItems.get(0).getNumber());
                    resultDto.setName(hvAmAutonomyMaintenanceProcessItems.get(0).getName());
                    resultDto.setGroupId(hvAmAutonomyMaintenanceProcessItems.get(0).getGroupId());
                    resultDto.setGroupName(hvAmAutonomyMaintenanceProcessItems.get(0).getGroupName());
                    resultDto.setTestContent(hvAmAutonomyMaintenanceProcessItems.get(0).getTestContent());
                    resultDto.setTestCycle(hvAmAutonomyMaintenanceProcessItems.get(0).getTestCycle());
                    resultDto.setTestMethod(hvAmAutonomyMaintenanceProcessItems.get(0).getTestMethod());
                    resultDto.setIdList(intersection);
                    resultDto.setCount(count);
                    resultDto.setErrCount(errCount);
                    resultDto.setQualifiedCount(qualifiedCount);
                    resultDto.setMaxErr(max_1);
                    resultList.add(resultDto);
                }

                data.put("current", 1);//当前页
                data.put("size", dto.getPageSize());//当前页大小
                data.put("total", 1);//总条数数
                data.put("pages", 1);//总页数
                data.put("records", resultList);//查询出来的记录list

            }


        }

        return data;
    }

    @Override
    public List<CheckAbnormalInfoDTO> checkAbnormalInfo(CheckAbnormalInfoQueryDTO dto) {

        QueryWrapper<HvAmAutonomyMaintenanceProcessItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("process_data_id").in("process_data_id", dto.getIdList()).eq("has_error", "1");
        List<HvAmAutonomyMaintenanceProcessItem> hvAmAutonomyMaintenanceProcessItems = this.baseMapper.selectList(queryWrapper);
        if (hvAmAutonomyMaintenanceProcessItems.size() < 1) {
            throw new BaseKnownException(HExceptionEnum.SERVER_ERROR);
        }

        Set<Integer> tempSet = new HashSet<>();
        for (HvAmAutonomyMaintenanceProcessItem temp : hvAmAutonomyMaintenanceProcessItems) {
            tempSet.add(temp.getProcessDataId());
        }
        List<Integer> tempList = new ArrayList<>(tempSet);
        List<HvAmAutonomyMaintenanceProcessData> byIdS = maintenanceProcessDataService.getByIdS(tempList);
        List<CheckAbnormalInfoDTO> resultList = new ArrayList<>();
        for (HvAmAutonomyMaintenanceProcessData data : byIdS) {
            CheckAbnormalInfoDTO resultDto = new CheckAbnormalInfoDTO();

            resultDto.setStartTime(data.getStartTime());
            resultDto.setEndTime(data.getEndTime());
            resultDto.setTaskName(data.getTaskName());
//            resultDto.setPersonLiable(data.getPersonLiable());//责任人
            //转name
            try {
                JsonByUserId jsonByUserId = authClient.getUserById(Integer.parseInt(data.getPersonLiable()));
                Data data_temp = jsonByUserId.getData();
                resultDto.setPersonLiable(data_temp.getUserName());
            } catch (Exception e) {
                resultDto.setPersonLiable(data.getPersonLiable());//责任人-名称 获取失败 展示id
            }
            resultDto.setResponsibilityGroup(data.getResponsibilityGroup());

            resultList.add(resultDto);
        }
        return resultList;
    }

    @Override
    public List<HvAmAutonomyMaintenanceProcessItem> fuzzyQueryItem(String keyword) {

        QueryWrapper<HvAmAutonomyMaintenanceProcessItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("name", "number").like(StringUtils.isNotBlank(keyword), "name", keyword)
                .or().like(StringUtils.isNotBlank(keyword), "number", keyword);

        List<HvAmAutonomyMaintenanceProcessItem> result = this.baseMapper.fuzzyQueryItem(queryWrapper);

        return result;
    }


}
