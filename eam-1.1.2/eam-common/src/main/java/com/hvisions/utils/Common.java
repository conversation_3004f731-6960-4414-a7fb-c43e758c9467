package com.hvisions.utils;

/**
 * <p>Title: Common</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2022/5/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public class Common {
    public static String getTimeString(Float time) {
        if (time == null) {
            return "0分钟";
        }
        if (time < 0) {
            return "0分钟";
        }
        if (time < 60) {
            return String.format("%s 分钟", Math.round(time));
        }
        if (time % 60 == 0) {
            return String.format("%s 小时 ", (int) Math.floor(time / 60));
        }
        return String.format("%s 小时 %s 分钟", (int) Math.floor(time / 60), Math.round(time % 60));
    }
}









