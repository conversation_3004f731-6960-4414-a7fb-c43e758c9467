package com.hvisions.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>Title:RegularUtils</p>
 * <p>Description:备件批次号与code进行正则比较</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/4/22</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Component
public class RegularUtils {

    @Value(value = "${h-visions.materialBatchNum:''}")
    private String materialBatchNum;

    /**
     * 扫码匹配正则返回cod信息
     *
     * @param batchNum 批次号
     * @return cod信息
     */
    public String getCodeByMatching(String batchNum) {
        //定义 解析字符串 集合
        Map<String, Object> map = new HashMap<>();

        Pattern pattern = Pattern.compile(materialBatchNum);

        Matcher matcher = pattern.matcher(batchNum);

        if (matcher.find()) {
            map.put("spare", matcher.group("spare"));
        }




        //return map;
        return batchNum;
    }

}
