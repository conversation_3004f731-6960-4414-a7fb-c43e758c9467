package com.hvisions.eam.query.spare;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>Title:UnitDTO</p>
 * <p>Description:备件单位分页查询</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/19</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "备件单位分页查询")
public class SpareUnitQueryDTO extends PageInfo {

    /**
     * 单位名称
     */
    @ApiModelProperty(value = " 单位名称 ")
    private String unitName;


}
