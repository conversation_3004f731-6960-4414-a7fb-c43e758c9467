package com.hvisions.eam.query.publicstore;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>Title:ActualUseQueryDTO</p>
 * <p>Description:实际申请的DTO</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/5/27</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = " 备件使用清单 ")
public class ActualUseQueryDTO extends PageInfo {

    /**
     * 流程实例Id
     */
    @ApiModelProperty(value = " 流程实例Id ")
    String processInstanceId;

    /**
     * 标识符 用于与部位向关联
     */
    @ApiModelProperty(value = " 标识符 用于与部位向关联 ")
    String identifierKey;

    /**
     * 类型 1备件 2油品
     */
    @JsonIgnore
    @ApiModelProperty(value = " 类型 1备件 2油品 ")
    Integer type;

    /**
     * 数量
     */
    @ApiModelProperty(value = " 数量 ")
    BigDecimal number;

}
