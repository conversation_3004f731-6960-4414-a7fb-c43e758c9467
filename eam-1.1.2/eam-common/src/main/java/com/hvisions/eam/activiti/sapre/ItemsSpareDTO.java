package com.hvisions.eam.activiti.sapre;

import com.hvisions.eam.annotation.GreaterThanNum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * <p>Title:ItemsLubDTO</p>
 * <p>Description:备件申请项</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/6/4</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Data
@ApiModel(description = "备件申请DTO")
public class ItemsSpareDTO {

    /**
     * 油品id
     */
    @Min(1)
    @ApiModelProperty(value = "备件id")
    private Integer spareId;

    /**
     * 油品名称
     */
    @ApiModelProperty(value = "备件名称")
    private String spareName;

    /**
     * 库房ID
     */
    @ApiModelProperty(value = "库房ID")
    private Integer shelveId;

    /**
     * 库房名称
     */
    @ApiModelProperty(value = "库房名称")
    private String shelveName;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchNumber;

    /**
     * 申请数量
     */
    @GreaterThanNum(message = "申请数量必须大于0")
    @ApiModelProperty(value = "申请数量")
    private BigDecimal number;


    /**
     * 类型名称
     */
    @ApiModelProperty(value = "备件类型名称")
    private String typeName;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String supplier;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌")
    private String brand;
    /**
     * 规格
     */
    @ApiModelProperty(value = "规格")
    private String specifications;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ItemsSpareDTO that = (ItemsSpareDTO) o;
        return Objects.equals(spareId, that.spareId) &&
                Objects.equals(shelveId, that.shelveId) &&
                Objects.equals(batchNumber, that.batchNumber);
    }

    @Override
    public int hashCode() {
        return Objects.hash(spareId, shelveId, batchNumber);
    }
}
