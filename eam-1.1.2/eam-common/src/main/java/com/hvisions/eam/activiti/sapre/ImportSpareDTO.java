package com.hvisions.eam.activiti.sapre;

import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import java.math.BigDecimal;

/**
 * <p>Title: ImportSpareDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/5/10</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class ImportSpareDTO {

    /**
     * 出入库申请单号
     */
    @ApiModelProperty(value = "出入库申请单号")
    private String spareNo;

    /**
     * 油品名称
     */
    @ApiModelProperty(value = " 备件编码 ")
    private String spareCode;

    /**
     * 库房ID
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = " 库房ID ")
    private Integer shelveId;

    /**
     * 库房ID
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = " 库房ID ")
    private Integer spareId;

    /**
     * 库房名称
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = " 库房名称")
    private String shelveName;
    /**
     * 库房编码
     */
    @ApiModelProperty(value = " 库房编码 ")
    private String shelveCode;

    /**
     * 批次号
     */
    @ApiModelProperty(value = " 批次号 ")
    private String batchNumber;

    /**
     * 申请数量
     */
    @Min(1)
    @ApiModelProperty(value = " 申请数量 ")
    private BigDecimal number;
}