package com.hvisions.eam.query.spare;

import com.hvisions.common.dto.PageInfo;
import com.hvisions.common.interfaces.IObjectType;
import com.hvisions.eam.enums.StoreExceptionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>Title:SpareTypeDTO</p>
 * <p>Description:备件型号</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/19</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "备件型号")
public class SpareTypeQueryDTO extends PageInfo implements IObjectType {

    /**
     * 类型编码
     */
    @ApiModelProperty(value = " 类型编码 ")
    private String typeCode;

    /**
     * 类型名称
     */
    @ApiModelProperty(value = " 类型名称 ")
    private String typeName;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchNumber;

    @Override
    public Integer getObjectType() {
        return StoreExceptionEnum.IN_USE.getCode();
    }
}
