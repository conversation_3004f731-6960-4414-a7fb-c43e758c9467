package com.hvisions.eam.activiti.lub;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * <p>Title:RootLubDTO</p>
 * <p>Description:油品申请</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/6/4</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Data
@ApiModel(description = "油品申请DTO")
public class RootLubDTO {

    public RootLubDTO() {
        this.serviceName = "lub";
    }

    /**
     * businessKey
     */
    @NotBlank(message = "businessKey不能为空")
    @ApiModelProperty(value = " businessKey ")
    private String businessKey;

    /**
     * 流程定义key
     */
    @ApiModelProperty(value = " 流程定义key ")
    private String processDefinitionKey;

    /**
     * 自定义变量
     */
    @Valid
    @ApiModelProperty(value = " 自定义变量 ")
    private VariablesLubDTO variables;

    /**
     * processInstanceId不用传 展示用
     */
    @ApiModelProperty(value = " processInstanceId不用传 展示用 ")
    private String processInstanceId;

    /**
     * 服务名称
     */
    @ApiModelProperty(value = " 服务名称 ")
    private String serviceName;

    /**
     * taskId不用传 展示用
     */
    @ApiModelProperty(value = " taskId不用传 展示用 ")
    private String taskId;

    /**
     * 来源（保养 或 润滑）
     */
    @ApiModelProperty(value = " 来源（保养 或 润滑） ")
    private String source;
}
