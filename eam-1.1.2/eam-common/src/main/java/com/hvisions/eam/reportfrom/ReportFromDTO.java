package com.hvisions.eam.reportfrom;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.eam.enums.ObjectTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Calendar;
import java.util.Date;

/**
 * <p>Title:ReportFromDTO</p>
 * <p>Description:报表DTO</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/4</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Data
@ApiModel(value = "报表DTO")
public class ReportFromDTO {


    public ReportFromDTO(){
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        // 获取上个月
        cal.add(Calendar.MONTH, -1);
        this.startTime = cal.getTime();
        this.endTime = date;
        this.equipmentName = "";
        this.failureLevel = "";
        this.faultClassApplyName="";

    }
    /**
     * 设备名称
     */
    @ApiModelProperty(value = " 设备名称 ")
    private String equipmentName;
    /**
     * 设备编码
     */
    @ApiModelProperty(value = " 设备编码 ")
    private String equipmentCode;
    /**
     * 设备ID
     */
    @ApiModelProperty(value = " 设备ID ")
    private String equipmentId;

    /**
     * 维修等级 故障等级
     */
    @ApiModelProperty(value = " 维修等级 故障等级")
    private String failureLevel;
    /**
     * 故障类型ID
     */
    @ApiModelProperty(value = " 故障类型ID  ")
    private String faultClassApplyId;

    /**
     * 故障类型名称
     */
    @ApiModelProperty(value = " 故障类型名称 ")
    private String faultClassApplyName;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = " 开始时间 ")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = " 结束时间 ")
    private Date endTime;

    /**
     * 开始结束时间验证
     */
     public void validation() {
         if (endTime.compareTo(startTime) < 0){
            throw new BaseKnownException(ObjectTypeEnum.START_END_TIME_EXCEPTION);
         }
     }

}


