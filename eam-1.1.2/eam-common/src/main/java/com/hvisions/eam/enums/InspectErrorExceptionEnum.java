package com.hvisions.eam.enums;

import com.hvisions.common.interfaces.BaseErrorCode;
import lombok.Getter;

/**
 * <p>Title: InspectErrorExceptionEnum</p >
 * <p>Description: 点检异常枚举</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/21</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Getter
public enum InspectErrorExceptionEnum implements BaseErrorCode {

    //参数用途
    START_PLAN_ERROR(13010),
    REFUSE_PLAN_ERROR(13020),
    DELETE_PLAN_ERROR(13030),
    UPDATE_PLAN_ERROR(13040),
    DELETE_CONTENT_ERROR(13060),
    DELETE_ITEM_ERROR(13060),
    RESULT_VO_TRANSFER_ERROR(13070),
    INSERT_CODE_ERROR(13080),
    CHECKER_IS_NULL(13090),
    STOP_ERROR(13100),
    ITEM_IS_NULL(13200),
    EQUIPMENT_TYPE_NOT_EXIST_ERROR(13300),
    EQUIPMENT_TYPE_SERVER_ERROR(13310),
    MAINTAIN_ITEM_NOT_EXIST(13320),
    ASSIGNEE_IS_NULL(13330),
    ;
    private Integer code;

    InspectErrorExceptionEnum(Integer code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return this.toString();
    }


}