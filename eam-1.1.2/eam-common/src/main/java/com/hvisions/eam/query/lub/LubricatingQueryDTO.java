package com.hvisions.eam.query.lub;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 油品分页查询
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "油品 DTO分页查询")
public class LubricatingQueryDTO extends PageInfo {

    /**
     * 备件编码
     */
    @ApiModelProperty(value = "油品编码")
    private String lubCode;

    /**
     * 备件名称
     */
    @ApiModelProperty(value = "油品名称")
    private String lubName;

    /**
     * 备件类型ID
     */
    @ApiModelProperty(value = " 油品类型ID")
    private Integer lubTypeId;

    //---------------------------- 油品单位字段 ------------------------
    /**
     * 单位名称
     */
    @ApiModelProperty(value = " 单位名称 ")
    private String unitName;

    /**
     * 单位符号
     */
    @ApiModelProperty(value = " 单位符号 ")
    private String unitSymbol;

}
