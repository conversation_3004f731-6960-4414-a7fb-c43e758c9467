package com.hvisions.eam.reportfrom;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title:FaultDrawingBoardDTO</p>
 * <p>Description:故障看板</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2020/4/8</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@ApiModel(value = "故障看板")
@Data
public class FaultDrawingBoardDTO {

    /**
     * 设备名称
     */
    @ApiModelProperty(value = " 设备名称 ")
    public String equipmentName;

    /**
     * 设备的数量
     */
    @ApiModelProperty(value = " 设备的数量 ")
    public Integer equipmentNum;


}
