package com.hvisions.eam.activiti.sapre;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;

/**
 * <p>Title:RootLubDTO</p>
 * <p>Description:备件申请</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/6/4</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Data
@ApiModel(description = "备件申请DTO")
public class RootSpareDTO {

    public RootSpareDTO() {
        this.serviceName = "spare";
    }

    /**
     * 业务id
     */
    @ApiModelProperty(value = "业务id")
    private String businessKey;

    /**
     * 流程定义key
     */
    @ApiModelProperty(value = "流程定义key")
    private String processDefinitionKey;

    /**
     * 自定义变量
     */
    @Valid
    @ApiModelProperty(value = "自定义变量")
    private VariablesSpareDTO variables;

    /**
     * 流程实例id
     */
    @ApiModelProperty(value = "流程实例id", readOnly = true)
    private String processInstanceId;

    /**
     * 服务名称
     */
    @ApiModelProperty(value = " 服务名称", readOnly = true)
    private String serviceName;

    /**
     * taskId不用传 展示用
     */
    @ApiModelProperty(value = "任务id", readOnly = true)
    private String taskId;


}
