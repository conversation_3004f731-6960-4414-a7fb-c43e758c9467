package com.hvisions.eam.activiti.fault;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <p>Title:VariablesFaultDTO</p>
 * <p>Description:故障</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/1</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@ApiModel(value = "故障报修")
@Data
public class VariablesFaultDTO {

    //------------------------------= 报修单信息 =-------------------------------
    /**
     * 单号
     */
    @ApiModelProperty(value = " 单号 ")
    private String receiptNumber;

    /**
     * Y
     */
    @Min(1)
    @Max(4)
    @ApiModelProperty(value = " 紧急度 1次要 2一般 3重要 4紧急 ")
    private String state;

    /**
     * 维修等级
     */
    @ApiModelProperty(value = " 维修等级 ")
    private String failureLevel;

    /**
     * 备注
     */
    @ApiModelProperty(value = " 备注 ")
    private String describeApply;

    /**
     * 上传图片ID列表
     */
    @ApiModelProperty(value = " 上传图片ID列表 ")
    private List<Integer> fileIds;

    //-------------------------------= 设备信息 =-----------------------------------------
    /**
     * 设备ID
     */
    @Min(1)
    @ApiModelProperty(value = " 设备ID ")
    private Integer equipmentId;

    /**
     * 设备名称
     */
    @NotBlank(message = "设备名称不能为空")
    @ApiModelProperty(value = " 设备名称 ")
    private String equipmentName;

    /**
     * 设备编码
     */
    @NotBlank(message = "设备编码不能为空")
    @ApiModelProperty(value = " 设备编码 ")
    private String equipmentCode;

    /**
     * 设备位置
     */
    @ApiModelProperty(value = " 设备位置 ")
    private String equipmentPlace;

    //-------------------------------= 报修人 =--------------------------------------------------
    /**
     * 报修人ID
     */
    @ApiModelProperty(value = " 报修人ID ")
    private Integer applyUserId;

    /**
     * 报修人名称
     */
    @ApiModelProperty(value = " 报修人名称 ")
    private String applyUserName;

    /**
     * 报修电话
     */
    @ApiModelProperty(value = " 报修电话 ")
    private String warrantyPhone;

    //--------------------------------= 故障类型 =---------------------------------------------
    /**
     * 故障类型ID
     */
    @ApiModelProperty(value = " 故障类型ID  ")
    private List<Integer> faultClassApplyId;

    /**
     * 故障类型名称
     */
    @ApiModelProperty(value = " 故障类型名称 ")
    private String faultClassApplyName;

    /**
     * 故障表现ID
     */
    @ApiModelProperty(value = " 故障表现ID ")
    private Integer faultApplyId;

    /**
     * 故障表现名称
     */
    @ApiModelProperty(value = " 故障表现名称 ")
    private String faultApplyName;

    /**
    *   维修负责人
    */
    @ApiModelProperty(value = "维修负责人")
    private String  executorId;
    /**
    *   维修组
    */
    @ApiModelProperty(value = "维修组")
    private List<String> candidateGroups;

}
