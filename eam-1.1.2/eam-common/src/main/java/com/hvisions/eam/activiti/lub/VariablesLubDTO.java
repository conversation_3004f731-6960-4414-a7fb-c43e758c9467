package com.hvisions.eam.activiti.lub;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <p>Title:VariablesLubDTO</p>
 * <p>Description: 流程自定义变量DTO</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/6/4</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Data
@ApiModel(description = "流程自定义变量DTO")
public class VariablesLubDTO {

    /**
     * 单号
     */
    @ApiModelProperty(value = " 单号 ")
    private String receiptNumber;

    /**
     * 标题
     */
    @NotBlank(message = "title不能为空")
    @ApiModelProperty(value = " 标题 ")
    private String title;

    /**
     * 出库存/入库 1入库 2出库
     */
    @Max(2)
    @Min(1)
    @ApiModelProperty(value = " 出库存/入库 1入库 2出库 ")
    private String inOut;

    /**
     * 申请人
     */
    @ApiModelProperty(value = " 申请人 ")
    private Integer applyUserId;

    /**
     * 申请人名称
     */
    @ApiModelProperty(value = " 申请人名称 ")
    private String applyUserName;

    /**
     * 是否审批 1审批中 2审批通过 3驳回
     */
    @Max(3)
    @Min(1)
    @ApiModelProperty(value = " 是否审批 1审批中 2审批通过 3驳回 ")
    private String isPass;

    /**
     * 申请项
     */
    @Valid
    @ApiModelProperty(value = " 申请项 ")
    private List<ItemsLubDTO> items;

    /**
     * 来源
     */
    private String source;

    /**
     * processInstanceId不用传 展示用
     */
    @ApiModelProperty(value = " processInstanceId不用传 展示用 ")
    private String processInstanceId;


}
