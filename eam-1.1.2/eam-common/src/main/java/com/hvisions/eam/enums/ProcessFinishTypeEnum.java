package com.hvisions.eam.enums;

import com.hvisions.common.interfaces.BaseErrorCode;
import lombok.Getter;

/**
 * <p>Title: InspectErrorExceptionEnum</p >
 * <p>Description: 点检异常枚举</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/21</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Getter
public enum ProcessFinishTypeEnum implements BaseErrorCode {

    /**
     * 完成
     */
    FINISH(1),
    /**
     * 延迟
     */
    DELAY(2),
    /**
     * 遗漏
     */
    MISS(3),
    /**
     * 无任务
     */
    NO_TASK(4),
    ;
    private Integer code;

    ProcessFinishTypeEnum(Integer code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return this.toString();
    }


}