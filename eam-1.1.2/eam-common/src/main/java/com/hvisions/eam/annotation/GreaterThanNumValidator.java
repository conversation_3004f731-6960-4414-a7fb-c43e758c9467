package com.hvisions.eam.annotation;

import com.hvisions.common.exception.BaseKnownException;
import org.springframework.util.ObjectUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.math.BigDecimal;

/**
 * <p>Title: MoreThanNumValidator</p >
 * <p>Description: 自定义数字大于0注解实现类</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/10/13</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
public class GreaterThanNumValidator implements ConstraintValidator<GreaterThanNum, BigDecimal> {

    private double validateNum;

    @Override
    public void initialize(GreaterThanNum constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
        validateNum = constraintAnnotation.value();
    }

    @Override
    public boolean isValid(BigDecimal input, ConstraintValidatorContext constraintValidatorContext) {
        if (ObjectUtils.isEmpty(input)) {
            throw new BaseKnownException(1000, "数量不能为空");
        }
        return input.compareTo(BigDecimal.valueOf(validateNum)) > 0;
    }
}
