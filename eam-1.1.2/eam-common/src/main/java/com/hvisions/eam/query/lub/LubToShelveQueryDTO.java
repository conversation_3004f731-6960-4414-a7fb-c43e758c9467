package com.hvisions.eam.query.lub;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>Title:SpareToShelveQueryDTO</p>
 * <p>Description:库存关系表</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/29</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "库存关系表")
public class LubToShelveQueryDTO extends PageInfo {

    public LubToShelveQueryDTO() {
        if (showNumberZero == null) {
            showNumberZero = false;
        }
    }

    /**
     * 是否展示数量为0的数据 默认不展示false;
     */
    @ApiModelProperty(value = " 是否展示数量为0的数据 默认不展示false ", example = "false")
    private Boolean showNumberZero;

    /**
     * 库存关系ID
     */
    @ApiModelProperty(value = "库存关系ID")
    private Integer id;

    /**
     * 库位ID
     */
    @ApiModelProperty(value = "库位ID")
    private Integer shelveId;

    /**
     * 备件ID
     */
    @ApiModelProperty(value = " 油品ID ")
    private Integer lubId;

    /**
     * 备件数量
     */
    @ApiModelProperty(value = " 备件数量 ")
    private BigDecimal number;

    /**
     * 批次号
     */
    @ApiModelProperty(value = " 批次号 ")
    private String batchNumber;

    /**
     * 图片
     */
    @ApiModelProperty(value = " 图片 ")
    private Integer img;

    //---------------------------- 备件字段 ------------------------
    /**
     * 备件名称
     */
    @ApiModelProperty(value = " 备件名称 ")
    private String lubName;

    /**
     * 备件编码
     */
    @ApiModelProperty(value = " 备件编码 ")
    private String lubCode;

    //---------------------------- 库房字段 ------------------------
    /**
     * 库房编码
     */
    @ApiModelProperty(value = " 库房编码 ")
    private String shelveCode;

    /**
     * 库房名称
     */
    @ApiModelProperty(value = " 库房名称 ")
    private String shelveName;
    /**
     * 类型code
     */
    @ApiModelProperty(value = " 类型code ")
    private String typeCode;

    /**
     * 类型code
     */
    @ApiModelProperty(value = " 类型名称 ")
    private String typeName;

}