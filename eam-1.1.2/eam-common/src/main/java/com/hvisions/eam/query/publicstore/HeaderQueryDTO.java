package com.hvisions.eam.query.publicstore;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>Title:HeaderQueryDTO</p>
 * <p>Description:行表查询</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/8/1</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = " 头表查询 ")
public class HeaderQueryDTO extends PageInfo {

    /**
     * 单号
     */
    @ApiModelProperty(value = " 单号 ")
    private String receiptNumber;

    /**
     * 标题
     */
    @ApiModelProperty(value = " 标题 ")
    private String title;

    /**
     * 流程id
     */
    @ApiModelProperty(value = " 流程id ")
    private String processInstanceId;

    /**
     * 出库入库
     */
    @ApiModelProperty(value = " 出入库 1入库 2出库 ")
    private Integer inOut;

    /**
     * 区别备件油品 备件1 油品2
     */
    @ApiModelProperty(value = " 区别备件油品 备件1 油品2 ")
    private Integer typeClass;

    /**
     * 是否完成出入库操作
     */
    @ApiModelProperty(value = " 是否完成出入库操作  ")
    private Boolean complete;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = " 开始时间 ")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = " 结束时间 ")
    private Date endTime;

    /**
     * 来源
     */
    @ApiModelProperty(value = " 来源 ")
    private String  source;

    /**
     * 时间验证
     *
     * @return 是否成功
     */
    public Boolean verification() {
        if (startTime != null && endTime != null) {
            if (endTime.compareTo(startTime) > 0) {
                return true;
            }
        }
        return false;
    }

}
