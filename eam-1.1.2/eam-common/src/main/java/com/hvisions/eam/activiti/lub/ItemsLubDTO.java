package com.hvisions.eam.activiti.lub;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * <p>Title:ItemsLubDTO</p>
 * <p>Description:申请项</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/6/4</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Data
@ApiModel(description = "油品申请DTO")
public class ItemsLubDTO {
    /**
     * 油品id
     */
    @Min(1)
    @ApiModelProperty(value = " 油品id ")
    private Integer lubId;

    /**
     * 油品名称
     */
    @ApiModelProperty(value = " 油品名称 ")
    private String lubName;

    /**
     * 库房ID
     */
    @ApiModelProperty(value = " 库房ID ")
    private Integer shelveId;

    /**
     * 库房名称
     */
    @ApiModelProperty(value = " 库房名称 ")
    private String shelveName;

    /**
     * 批次号
     */
    @ApiModelProperty(value = " 批次号 ")
    private String batchNumber;

    /**
     * 申请数量
     */
    @Min(0)
    @ApiModelProperty(value = " 申请数量 ")
    private BigDecimal number;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ItemsLubDTO that = (ItemsLubDTO) o;
        return Objects.equals(lubId, that.lubId) &&
                Objects.equals(shelveId, that.shelveId) &&
                Objects.equals(batchNumber, that.batchNumber);
    }

    @Override
    public int hashCode() {
        return Objects.hash(lubId, shelveId, batchNumber);
    }
}
