package com.hvisions.eam.query.spare;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 备件分页查询
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "备件 DTO分页查询")
public class SpareQueryDTO extends PageInfo {

    /**
     * 备件编码
     */
    @ApiModelProperty(value = "备件编码")
    private String spareCode;

    /**
     * 备件名称
     */
    @ApiModelProperty(value = "备件名称")
    private String spareName;


    /**
     * 备件类型编码
     */
    @ApiModelProperty(value = "备件类型编码")
    private String spareTypeCode;

    /**
     * 备件类型名称
     */
    @ApiModelProperty(value = "备件类型名称")
    private String spareTypeName;


    /**
     * 采购工程师
     */
    @ApiModelProperty(value = "采购工程师")
    private Integer purchaseUserId;

    /**
     * 技术工程师
     */
    @ApiModelProperty(value = "技术工程师")
    private Integer skillUserId;


    /**
     * 创建时间开始
     */
    @ApiModelProperty(value = "创建时间开始")
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    @ApiModelProperty(value = "创建时间结束")
    private Date createTimeEnd;

    /**
     * 订货号
     */
    @ApiModelProperty(value = "订货号")
    private String orderNum;

    //---------------------------- 备件单位字段 ------------------------
    /**
     * 单位名称
     */
    @ApiModelProperty(value = " 单位名称 ")
    private String unitName;

    /**
     * 单位符号
     */
    @ApiModelProperty(value = " 单位符号 ")
    private String unitSymbol;


    /**
     * 备注
     */
    @ApiModelProperty(value = " 备注 ")
    private String remarks;

    /**
     * 供应商
     */
    @ApiModelProperty(value = " 供应商 ")
    private String supplier;
    /**
     * 预期使用数量
     */
    @ApiModelProperty(value = " 预期使用数量 ")
    private Integer expectedNumber;

    /**
     * 报警临界剩余数量
     */
    @ApiModelProperty(value = " 报警临界剩余数量 ")
    private Integer alarmRemainingNumber;

    /**
     * 品牌
     */
    @ApiModelProperty(value = " 品牌 ")
    private String brand;

    /**
     * 规格
     */
    @ApiModelProperty(value = " 规格 ")
    private String specifications;

    @ApiModelProperty(value = "机电类别")
    private String metype;

}
