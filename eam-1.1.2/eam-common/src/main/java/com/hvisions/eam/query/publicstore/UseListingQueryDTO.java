package com.hvisions.eam.query.publicstore;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title:UseListingSpareDTO</p>
 * <p>Description:备件使用清单</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/5/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Data
@ApiModel(description = " 备件使用清单 ")
public class UseListingQueryDTO extends PageInfo {

    /**
     * 关联 流程实例ID
     */
    @ApiModelProperty(value = " 关联 流程实例ID ")
    String processInstanceId;

}
