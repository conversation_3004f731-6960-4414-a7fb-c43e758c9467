package com.hvisions.eam.reportfrom;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Title:ReportGroupDTO</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/10</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Data
@ApiModel(value = "报表DTO")
public class ReportGroupDTO {

    /**
     * 设备名称
     */
    @ApiModelProperty(value = " 设备名称 ")
    private String equipmentName;

    /**
     * 设备编码
     */
    @ApiModelProperty(value = " 设备编码 ")
    private String equipmentCode;

    /**
     * 设备ID
     */
    @ApiModelProperty(value = " 设备ID ")
    private String equipmentId;

    /**
     * 维修次数
     */
    @ApiModelProperty(value = " 维修次数 ")
    private Integer faultNumber;


    /**
     * 具体数据
     */
    @ApiModelProperty(value = " 具体数据 ")
    private List<ReportFromDTO> reportFromDTOS;



}
