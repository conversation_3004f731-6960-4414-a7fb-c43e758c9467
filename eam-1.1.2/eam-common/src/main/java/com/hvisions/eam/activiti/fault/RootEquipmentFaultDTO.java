package com.hvisions.eam.activiti.fault;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;

/**
 * <p>Title:RootEquipmentFaultDTO</p>
 * <p>Description:维修</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/1</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@ApiModel(description = "故障报修")
@Data
public class RootEquipmentFaultDTO {


    /**
     * 自定义变量
     */
    @Valid
    @ApiModelProperty(value = " 自定义变量 ")
    private VariablesFaultDTO variables;


}


