package com.hvisions.eam.enums;

import com.hvisions.common.interfaces.BaseErrorCode;
import lombok.Getter;

/**
 * <p>Title: MaintainErrorExceptionEnum</p >
 * <p>Description: 异常枚举</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/21</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Getter
public enum MaintainErrorExceptionEnum implements BaseErrorCode {
    /**
     * 异常枚举
     */
    //参数用途
    START_PLAN_ERROR(15010),
    REFUSE_PLAN_ERROR(15020),
    DELETE_PLAN_ERROR(15030),
    UPDATE_PLAN_ERROR(15040),
    DELETE_CONTENT_ERROR(15060),
    DELETE_ITEM_ERROR(15060),
    RESULT_VO_TRANSFER_ERROR(15070),
    INSERT_CODE_ERROR(15080),
    ITEM_IS_NULL(15090),
    CHECKER_IS_NULL(15100),
    STOP_ERROR(15200),
    //开始结束时间错误
    START_END_TIME_EXCEPTION(17006),
    EQUIPMENT_TYPE_SERVER_ERROR(15210),
    EQUIPMENT_TYPE_NOT_EXIST_ERROR(15220),
    MAINTAIN_ITEM_NOT_EXIST(15230),
    NEED_CANDIDATE(15230),
    ;
    private Integer code;

    MaintainErrorExceptionEnum(int code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return this.toString();
    }


}