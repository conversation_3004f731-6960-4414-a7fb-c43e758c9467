package com.hvisions.eam.query.spare;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>Title:SpareToShelveQueryDTO</p>
 * <p>Description:库存关系表</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/29</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "库存关系表")
public class SpareToShelveQueryDTO extends PageInfo {

    public SpareToShelveQueryDTO() {
        if (showNumberZero == null) {
            showNumberZero = false;
        }
    }

    /**
     * 展示数据为0 的数据
     */
    @ApiModelProperty(value = "展示数量为零的数据")
    private Boolean showNumberZero;

    /**
     * 库存关系ID
     */
    @ApiModelProperty(value = "库存关系ID")
    private Integer id;

    /**
     * 库位ID
     */
    @ApiModelProperty(value = "库位ID")
    private Integer shelveId;

    /**
     * 备件ID
     */
    @ApiModelProperty(value = " 备件ID ")
    private Integer spareId;

    /**
     * 备件数量
     */
    @ApiModelProperty(value = " 备件数量 ")
    private Integer number;

    /**
     * 批次号
     */
    @ApiModelProperty(value = " 批次号 ")
    private String batchNumber;

    //---------------------------- 备件字段 ------------------------

    /**
     * 备件名称
     */
    @ApiModelProperty(value = " 备件名称 ")
    private String spareName;

    /**
     * 备件编码
     */
    @ApiModelProperty(value = " 备件编码 ")
    private String spareCode;

    /**
     * 图片
     */
    @ApiModelProperty(value = " 图片 ")
    private Integer img;

    //---------------------------- 库房字段 ------------------------
    /**
     * 库房编码
     */
    @ApiModelProperty(value = " 库房编码 ")
    private String shelveCode;

    /**
     * 库房名称
     */
    @ApiModelProperty(value = " 库房名称 ")
    private String shelveName;

    //----------------------------= 备件类型 =-----------------------------
    /**
     * 备件类型编码
     */
    @ApiModelProperty(value = " 备件类型编码 ")
    private String typeCode;

    /**
     * 备件类型名称
     */
    @ApiModelProperty(value = " 备件类型名称 ")
    private String typeName;

    @ApiModelProperty(value = " 备件组 ")
    private String brand;

    @ApiModelProperty(value = "规格")
    private String specifications;

    @ApiModelProperty(value = "关键字查询")
    private String keyword;
}
