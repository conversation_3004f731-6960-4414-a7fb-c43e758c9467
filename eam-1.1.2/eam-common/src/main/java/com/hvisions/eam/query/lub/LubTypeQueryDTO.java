package com.hvisions.eam.query.lub;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>Title:SpareTypeDTO</p>
 * <p>Description:型号</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/19</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "备件型号")
public class LubTypeQueryDTO extends PageInfo {

    /**
     * 类型编码
     */
    @ApiModelProperty(value = " 类型编码 ")
    private String typeCode;

    /**
     * 类型名称
     */
    @ApiModelProperty(value = " 类型名称 ")
    private String typeName;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchNumber;


}