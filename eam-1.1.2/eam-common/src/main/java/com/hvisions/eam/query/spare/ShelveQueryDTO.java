package com.hvisions.eam.query.spare;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>Title:ShelveDTO</p>
 * <p>Description:库位DTO分页查询</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/19</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = " 库位DTO 分页查询")
public class ShelveQueryDTO extends PageInfo {

    /**
     * 库位编码
     */
    @ApiModelProperty(value = " 库位编码 ")
    private String shelveCode;

    /**
     * 库位名称
     */
    @ApiModelProperty(value = " 库位名称 ")
    private String shelveName;

    /**
     * 库位地点
     */
    @ApiModelProperty(value = " 库位地点 ")
    private String shelvePlace;


}
