package com.hvisions.qms.service;

import com.hvisions.qms.dto.repair.*;
import org.springframework.data.domain.Page;

/**
 * <p>Title: TestReworkService</p >
 * <p>Description: 返修service</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/6/05</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface InspectionReworkService {


    /**
     * 根据ID删除在线检测实体
     *
     * @param id 在线检测实体id
     */
    void deleteById(Integer id);


    /**
     * 分页返修列表
     *
     * @param repairOrderPageDTO 分页查询
     * @return 分页结果集
     */
    Page<RepairOrderDTO> findAllRepairPage(RepairOrderPageDTO repairOrderPageDTO);


    /**
     * 查询返修单信息
     *
     * @param repairId 返修单ID
     * @return 返修查询结果
     */
    ResultRepairOrderDTO findRepairDefectsById(Integer repairId);


    /**
     * 创建返修单，根据质检单ID
     *
     * @param inspectionOrderId 质检单ID
     * @return 生成返修单
     */
    int addRepairOrder(Integer inspectionOrderId);

    /**
     * 修改返修单
     *
     * @param testReworkDTO 返修单更新DTO
     */
    void updateRepairOrder(RepairOrderDTO testReworkDTO);

    int createRepairDefects(RepairDefectsDTO repairStandardsDefectsDTO);
}