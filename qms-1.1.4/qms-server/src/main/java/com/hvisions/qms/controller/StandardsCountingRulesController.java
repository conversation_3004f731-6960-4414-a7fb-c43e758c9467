package com.hvisions.qms.controller;

import com.hvisions.common.utils.EnumUtil;
import com.hvisions.qms.dto.rule.CountRulesDTO;
import com.hvisions.qms.dto.rule.ResultCountRulesDTO;
import com.hvisions.qms.enums.CountRuleEnum;
import com.hvisions.qms.service.CountingRulesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: CountingRulesController</p >
 * <p>Description: 计算规则控制器controller</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/13</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Api(description = "计算规则控制器")
@RestController
@RequestMapping("/standardsRules")
public class StandardsCountingRulesController {
    private final CountingRulesService countingRulesService;

    @Autowired
    public StandardsCountingRulesController(CountingRulesService countingRulesService) {
        this.countingRulesService = countingRulesService;
    }

    /**
     * 创建计算规则
     *
     * @param countRulesDTO 计算规则
     * @return 主键
     */
    @PostMapping("/createCountingRulesController")
    @ApiOperation(value = "创建计算规则")
    public int createCountingRules(@RequestBody CountRulesDTO countRulesDTO) {
        return countingRulesService.createCountingRules(countRulesDTO);
    }


    /**
     * 删除计算规则
     *
     * @param countId 计算规则id
     */
    @ApiOperation(value = "删除计算规则")
    @DeleteMapping("/deleteRulesById/{countId}")
    public void deleteRulesById(@PathVariable Integer countId) {
        countingRulesService.deleteRulesById(countId);
    }

    /**
     * 更新计算规则
     *
     * @param countRulesDTO 计算规则
     */
    @PutMapping("/updateCountingRulesController")
    @ApiOperation(value = "更新计算规则")
    public void updateCountingRulesController(@RequestBody CountRulesDTO countRulesDTO) {
        countingRulesService.updateRule(countRulesDTO);
    }

    /**
     * 根据质检标准Id查询计算规则
     *
     * @param standardId 质检标准id
     * @return 计算规则
     */
    @GetMapping("/getCountingRulesById/{standardId}")
    @ApiOperation(value = "根据质检标准Id查询计算规则")
    public List<ResultCountRulesDTO> getAllCountingRules(@PathVariable Integer standardId) {
        return countingRulesService.findCountingRulesByStandardId(standardId);
    }

    /**
     * 获取所有计算规则类型信息
     *
     * @return 所有计算规则类型信息
     */
    @GetMapping("/getCountingTypeEnum")
    @ApiOperation(value = "获取所有计算规则类型信息")
    public Map<Integer, String> getCountingTypeEnum() {
        return EnumUtil.enumToMap(CountRuleEnum.class);
    }
}