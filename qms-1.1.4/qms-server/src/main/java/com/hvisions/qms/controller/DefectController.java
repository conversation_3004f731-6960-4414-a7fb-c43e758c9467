package com.hvisions.qms.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.*;
import com.hvisions.qms.service.DefectRouteStepService;
import com.hvisions.qms.service.DefectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Set;

/**
 * 缺陷接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/defect")
@Slf4j
@Api(description = "缺陷接口")
public class DefectController {

    @Autowired
    private DefectService defectService;
    @Autowired
    private DefectRouteStepService defectRouteStepService;

    @Resource(name = "defect_extend")
    BaseExtendService baseExtendService;

    /**
     * 新增缺陷信息
     *
     * @param defectDTO 缺陷信息
     * @return 缺陷信息id
     */
    @ApiOperation(value = "新增缺陷信息")
    @RequestMapping(value = "/addDefect", method = RequestMethod.POST)
    public int add(@RequestBody DefectDTO defectDTO) {
        return defectService.addDefect(defectDTO);
    }

    /**
     * 修改缺陷信息
     *
     * @param defectDTO 缺陷信息
     * @return 缺陷信息id
     */
    @ApiOperation(value = "修改缺陷信息")
    @RequestMapping(value = "/updateDefect", method = RequestMethod.PUT)
    public int update(@RequestBody DefectDTO defectDTO) {

        return defectService.updateDefect(defectDTO);
    }

    /**
     * 根据id删除缺陷信息
     *
     * @param id 缺陷信息id
     */
    @ApiOperation(value = "删除缺陷信息")
    @RequestMapping(value = "/deleteDefectById/{id}", method = RequestMethod.DELETE)
    public void delete(@PathVariable Integer id) {
        defectService.deleteDefectById(id);
    }

    /**
     * 分页查询
     *
     * @param defectQueryDTO 缺陷信息分页查询条件
     * @return 分页信息
     */
    @PostMapping(value = "/getDefectPageQuery")
    @ApiOperation(value = "缺陷分页查询")
    public Page<DefectDTO> getDefectPageQuery(@RequestBody DefectQueryDTO defectQueryDTO) {
        return defectService.getDefectPageQuery(defectQueryDTO);
    }

    /**
     * @param defectQueryDTO 缺陷信息分页查询条件
     * @return 分页信息
     */
    @PostMapping(value = "/getDefectByConQuery")
    @ApiOperation(value = "根据工艺路线和工艺步骤查询")
    public List<HvQmQualityDefectRouteOperationDTO> getDefectByConQuery(@RequestBody DefectRouteDTO defectQueryDTO) {
        return defectService.getDefectByConQuery(defectQueryDTO);
    }


    /**
     * @param defectQueryDTO 缺陷信息分页查询条件
     * @return 分页信息
     */
    @PostMapping(value = "/getDefectByConMoreQuery")
    @ApiOperation(value = "根据工艺路线和自定义工艺步骤查询")
    public Set<DefectDTO> getDefectByConMoreQuery(@RequestBody DefectRouteMoreDTO defectQueryDTO) {
        return defectService.getDefectByConMoreQuery(defectQueryDTO);
    }

    /**
     * 获取所有缺陷列表
     *
     * @return 所有缺陷列表
     */
    @ApiOperation(value = "获取所有缺陷")
    @RequestMapping(value = "/getAllDefect", method = RequestMethod.GET)
    public List<DefectDTO> getAllDefect() {
        return defectService.getAllDefect();
    }

    /**
     * 根据id获取缺陷信
     *
     * @param id 主键id
     * @return 缺陷信息
     */
    @ApiOperation(value = "根据id获取缺陷信息")
    @RequestMapping(value = "/getDefectById/{id}", method = RequestMethod.GET)
    public DefectDTO getDefectById(@PathVariable Integer id) {
        return defectService.getDefectById(id);
    }

    /**
     * 根据缺陷id获取工艺操作id列表
     *
     * @param defectId 缺陷id
     * @return 缺陷id对应的工艺操作id列表
     */
    @ApiOperation(value = "根据缺陷id获取工艺操作id列表")
    @RequestMapping(value = "/getRouteOperationIdsByDefectId/{defectId}", method = RequestMethod.GET)
    public List<Integer> getRouteStepIdsByDefectId(@PathVariable Integer defectId) {
        return defectRouteStepService.getRouteOperationIdsByDefectId(defectId);
    }

    /**
     * 根据工艺操作id获取缺陷信息列表
     *
     * @param routeStepId 工艺操作id
     * @return 工艺操作id对应的缺陷信息列表
     */
    @ApiOperation(value = "根据工艺操作id获取缺陷信息列表")
    @RequestMapping(value = "/getDefectsByRouteStepId/{routeStepId}", method = RequestMethod.GET)
    public List<DefectDTO> getDefectsByRouteStepId(@PathVariable Integer routeStepId) {

        return defectService.getDefectsByRouteStepId(routeStepId);

    }


    /**
     * 保存或更新缺陷-工艺操作绑定数据
     *
     * @param defectDTO 缺陷dto对象
     */
    @ApiOperation(value = "保存或更新缺陷-工艺操作绑定数据")
    @RequestMapping(value = "/saveOrUpdateRouteBind", method = RequestMethod.PUT)
    public void saveOrUpdateRouteBind(@RequestBody DefectDTO defectDTO) {

    }

    /**
     * 缺陷批量绑定缺陷
     *
     * @param defectDTO 缺陷dto对象
     */
    @ApiOperation(value = "缺陷批量绑定工艺操作")
    @RequestMapping(value = "/defectBindOperationMore", method = RequestMethod.PUT)
    public void defectBindOperationMore(@RequestBody DefectBindOperationDTO defectDTO) {
        defectRouteStepService.bindOperation(defectDTO);
    }


    /**
     * 导出所有缺陷信息
     *
     * @return 缺陷信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportDefect")
    @ApiOperation(value = "导出 数据导出")
    public ResultVO<ExcelExportDto> exportDefect() throws IOException, IllegalAccessException {
        return defectRouteStepService.exportDefect();
    }

    /**
     * 导出所有缺陷信息
     *
     * @return 缺陷信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportDefectLink")
    @ApiOperation(value = "导出 支持超链接")
    public ResponseEntity<byte[]> export() throws IOException, IllegalAccessException {
        return defectRouteStepService.export();
    }

    /**
     * 导入缺陷信息
     *
     * @param file 缺陷文档
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @PostMapping(value = "/importDefectLink")
    @ApiOperation(value = "导入缺陷信息(code存在则更新，不存在则新增)")
    public ImportResult importParameter(@RequestParam("file") MultipartFile file) throws IllegalAccessException, ParseException, IOException {
        return defectService.importDefect(file);
    }

    /**
     * 获取所有的质量缺陷信息，根据工艺操作进行分组
     *
     * @return 获取所有的质量缺陷信息
     */
    @ApiOperation(value = "获取所有的质量缺陷信息，根据工艺操作进行分组")
    @GetMapping(value = "/getAllDefectGroupByOperation")
    public List<DefectGroup> getAllDefectGroupByOperation() {
        return defectService.getAllDefectGroupByOperation();
    }


    /**
     * 添加缺陷扩展属性
     *
     * @param extendColumnInfo 扩展属性信息
     */
    @PostMapping("/createDefectExtend")
    @ApiOperation(value = "添加缺陷属性")
    public void createDefectExtend(@RequestBody ExtendColumnInfo extendColumnInfo) {
        baseExtendService.addExtend(extendColumnInfo);
    }

    /**
     * 删除缺陷扩展属性
     *
     * @param columnName 扩展属性名称
     */
    @DeleteMapping("/deleteDefectExtend/{columnName}")
    @ApiOperation(value = "删除扩展属性")
    @Transactional(rollbackFor = Exception.class)
    public void deleteDefectExtend(@PathVariable String columnName) {
        baseExtendService.dropExtend(columnName);
    }

    /**
     * 获取所有缺陷扩展字段信息
     *
     * @return 料扩展字段信息
     */
    @GetMapping(value = "/getAllDefectExtend")
    @ApiOperation(value = "获取所有缺陷扩展字段信息")
    public List<ExtendColumnInfo> getAllDefectExtend() {
        return baseExtendService.getExtendColumnInfo();
    }


}

























