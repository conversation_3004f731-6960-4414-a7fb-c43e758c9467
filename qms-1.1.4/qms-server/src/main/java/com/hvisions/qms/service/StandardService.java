package com.hvisions.qms.service;

import com.hvisions.qms.dto.standard.*;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <p>Title: TemplateService</p >
 * <p>Description: 质量模板服务层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/16</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface StandardService {


    /**
     * 质检标准生效
     *
     * @param standardId 质检标准id
     */
    void updateInspectionStandardState(Integer standardId);

    /**
     * 新增质检标准
     *
     * @param inspectionStandardDTO 质检标准
     * @return 主键
     */
    int addInspectionStandard(InspectionStandardDTO inspectionStandardDTO);

    /**
     * 克隆质检标准
     *
     * @param cloneDTO 克隆信息
     * @return 新的标准主键
     */
    int cloneInspectionStandard(CloneInspectionStandardDTO cloneDTO);

    /**
     * 更新质检标准
     *
     * @param inspectionStandardDTO 质检标准
     */
    void updateInspectionStandard(InspectionStandardDTO inspectionStandardDTO);

    /**
     * 删除质检标准
     *
     * @param id 质检标准id
     */
    void deleteStandardById(Integer id);

    /**
     * 根据多条件分页查询，获取质检标准列表"
     *
     * @param templateDTO 查询条件
     * @return 质检标准分页信息
     */
    Page<QueryStandardDTO> findInspectionStandardPageByConditions(InspectionStandardQueryDTO templateDTO);

    /**
     * 根据质检标准id、获取质检标准结果集"
     *
     * @param id 质检标准id
     * @return 质检标准结果集"
     */
    QueryStandardDTO findInspectionStandardById(Integer id);

    /**
     * 根据id列表查询质检标准
     *
     * @param ids 质检标准id列表
     * @return 质检标准列表
     */
    List<InspectionStandardDTO> findInspectionStandardByIds(List<Integer> ids);


    /**
     * 根据检测工位，获取质检标准
     *
     * @param operationId 工位id
     * @param materialId  物料id
     * @param active      版本状态
     * @return 质检标准列表
     */
    List<InspectionStandardDTO> findStandardsByOperationIdAndMaterialId(Integer operationId, Integer materialId, Integer active);

    /**
     * 批量删除质检标准
     *
     * @param ids 质检标准id列表
     */
    void deleteInspectionStandardByIds(List<Integer> ids);

    /**
     * 根据标准编码和版本号获取质检标准
     *
     * @param code    标准编码
     * @param version 版本号获
     * @return 质检标准
     */
    QueryStandardDTO getInspectionStandardByCodeAndVersion(String code, String version);


    void importInspectionKnowledgeById(Integer standardId, Integer knowledgeBaseId);

    Page<InspectionStandardMaterialDTO> getInspectionStandardMaterial(InspectionStandardQueryDTO pageInfo);

    void updateInspectionStandardArchiveState(Integer standardId);
}
