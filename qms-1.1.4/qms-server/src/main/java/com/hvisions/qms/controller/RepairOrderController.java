package com.hvisions.qms.controller;

import com.hvisions.qms.dto.repair.RepairDefectsDTO;
import com.hvisions.qms.dto.repair.RepairOrderDTO;
import com.hvisions.qms.dto.repair.RepairOrderPageDTO;
import com.hvisions.qms.dto.repair.ResultRepairOrderDTO;
import com.hvisions.qms.service.InspectionReworkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

/**
 * <p>Title: RepairOrderController</p >
 * <p>Description: 质检返修单检测控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/15</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Api(description = "质检返修单检测控制器")
@RequestMapping("/rework")
@RestController
public class RepairOrderController {
    private final InspectionReworkService reworkService;

    @Autowired
    public RepairOrderController(InspectionReworkService reworkService) {
        this.reworkService = reworkService;
    }

    /**
     * 创建返修单
     *
     * @param inspectionOrderId 在线检查DTO
     * @return 新增质检单ID
     */
    @GetMapping("/createRepairOrder/{inspectionOrderId}")
    @ApiOperation(value = "新增返修单、根据质检单ID")
    public int createRepairOrder(@PathVariable Integer inspectionOrderId) {
        return reworkService.addRepairOrder(inspectionOrderId);
    }

    /**
     * 获取缺陷
     *
     * @param repairStandardsDefects 返修缺陷录入
     * @return 获取缺陷列表
     */
    @PostMapping("/createRepairDefects")
    @ApiOperation(value = "返修缺陷录入")
    public int createRepairDefects(@RequestBody RepairDefectsDTO repairStandardsDefects) {
        return reworkService.createRepairDefects(repairStandardsDefects);
    }

    /**
     * 更新
     *
     * @param testReworkDTO 在线检查DTO
     */
    @PutMapping("/updateTestRework")
    @ApiOperation(value = "更新返修单")
    public void updateTestRework(@RequestBody RepairOrderDTO testReworkDTO) {
        reworkService.updateRepairOrder(testReworkDTO);
    }

    /**
     * 删除
     *
     * @param id 在线检查id
     */
    @DeleteMapping("/deleteTestReworkById/{id}")
    @ApiOperation(value = "删除返修单")
    public void deleteTestReworkById(@PathVariable Integer id) {
        reworkService.deleteById(id);
    }

    /**
     * 查询所有返修单
     *
     * @param repairOrderPageDTO 返修单查询DTO
     * @return 分页结果
     */
    @PostMapping("/findAllRepairPage")
    @ApiOperation(value = "获取返修单列表、多条件分页查询")
    public Page<RepairOrderDTO> findAllRepairPage(@RequestBody RepairOrderPageDTO repairOrderPageDTO) {
        return reworkService.findAllRepairPage(repairOrderPageDTO);
    }

    /**
     * 获取缺陷
     *
     * @param repairId 修单Id
     * @return 获取缺陷列表
     */
    @GetMapping("/getRepairDefectsById/{repairId}")
    @ApiOperation(value = "获取返修缺陷列表、根据返修单Id查询")
    public ResultRepairOrderDTO getRepairDefectsById(@PathVariable Integer repairId) {
        return reworkService.findRepairDefectsById(repairId);
    }
}