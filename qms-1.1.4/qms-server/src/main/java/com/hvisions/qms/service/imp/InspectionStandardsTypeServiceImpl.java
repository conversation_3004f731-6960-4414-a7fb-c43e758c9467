package com.hvisions.qms.service.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.qms.dto.knowledge.InspectionKnowledgeBaseDTO;
import com.hvisions.qms.dto.knowledge.InspectionKnowledgeBaseLayeredDTO;
import com.hvisions.qms.dto.knowledge.QueryKnowledgeBaseDTO;
import com.hvisions.qms.dto.knowledge.QueryResultKnowledgeBaseDTO;
import com.hvisions.qms.dto.knowledge.info.InspectionKnowledgeDefectsDTO;
import com.hvisions.qms.dto.knowledge.info.InspectionKnowledgeParametersDTO;
import com.hvisions.qms.entity.HvQmInspectionKnowledgeBase;
import com.hvisions.qms.enums.InspectionKnowledgeBaseExceptionEnum;
import com.hvisions.qms.repository.InspectionKnowledgeBaseRepository;
import com.hvisions.qms.repository.InspectionStandardRepository;
import com.hvisions.qms.service.InspectionKnowledgeBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.lang.reflect.Field;
import java.util.*;

/**
 * <p>Title: InspectionStandardsTypeServiceImpl</p >
 * <p>Description: 质量标准知识库实现类</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/10/10</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Slf4j
@Service
public class InspectionStandardsTypeServiceImpl implements InspectionKnowledgeBaseService {

    private final InspectionKnowledgeBaseRepository knowledgeBaseRepository;
    private final InspectionStandardRepository standardRepository;

    @Autowired
    public InspectionStandardsTypeServiceImpl(InspectionKnowledgeBaseRepository knowledgeBaseRepository, InspectionStandardRepository standardRepository) {
        this.knowledgeBaseRepository = knowledgeBaseRepository;
        this.standardRepository = standardRepository;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public int addKnowledgeBase(InspectionKnowledgeBaseDTO inspectionStandardsTypeDTO) {
        return knowledgeBaseRepository.save(DtoMapper.convert(inspectionStandardsTypeDTO, HvQmInspectionKnowledgeBase.class)).getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int updateKnowledgeBase(InspectionKnowledgeBaseDTO inspectionStandardsTypeDTO) {
        return addKnowledgeBase(inspectionStandardsTypeDTO);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void removeKnowledgeBaseById(Integer id) {

        Optional<HvQmInspectionKnowledgeBase> knowledgeBase = knowledgeBaseRepository.findById(id);
        if (knowledgeBase.isPresent()) {
            List<HvQmInspectionKnowledgeBase> knowledgeBaseChildren = knowledgeBaseRepository.findByParentId(knowledgeBase.get().getId());
            if (!knowledgeBaseChildren.isEmpty()) {
                throw new BaseKnownException(InspectionKnowledgeBaseExceptionEnum.SUB_LEVEL_EXISTS_DELETE_FAILED);

            } else {
                knowledgeBaseRepository.deleteById(id);

            }

        }


    }


    /**
     * {@inheritDoc}
     */
    @Override
    public void removeBatchKnowledgeBaseByIds(List<Integer> ids) {
        for (Integer id : ids) {
            removeKnowledgeBaseById(id);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public QueryResultKnowledgeBaseDTO findKnowledgeBaseById(Integer id) {
        Optional<HvQmInspectionKnowledgeBase> hvQmInspectionStandardsType = knowledgeBaseRepository.findById(id);
        if (!hvQmInspectionStandardsType.isPresent()) {
            return null;
        }
        QueryResultKnowledgeBaseDTO knowledgeBaseDTO = DtoMapper.convert(hvQmInspectionStandardsType.get(), QueryResultKnowledgeBaseDTO.class);
        if (!hvQmInspectionStandardsType.get().getHvQmInspectionKnowledgeDefects().isEmpty()) {
            knowledgeBaseDTO.setDefects(DtoMapper.convertList(hvQmInspectionStandardsType.get().getHvQmInspectionKnowledgeDefects(), InspectionKnowledgeDefectsDTO.class));

        }
        if (!hvQmInspectionStandardsType.get().getHvQmInspectionKnowledgeParameters().isEmpty()) {
            knowledgeBaseDTO.setParameters(DtoMapper.convertList(hvQmInspectionStandardsType.get().getHvQmInspectionKnowledgeParameters(), InspectionKnowledgeParametersDTO.class));

        }
        return knowledgeBaseDTO;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<InspectionKnowledgeBaseDTO> findKnowledgeBaseByIds(List<Integer> ids) {
        List<InspectionKnowledgeBaseDTO> standardsTypes = new ArrayList<>();
        for (Integer id : ids) {
            standardsTypes.add(findKnowledgeBaseById(id));
        }
        return standardsTypes;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public Page<InspectionKnowledgeBaseDTO> getKnowledgeBase(QueryKnowledgeBaseDTO typeDTO) {
        String standardsTypeCode = typeDTO.getKnowledgeCode();
        String standardsTypeName = typeDTO.getKnowledgeName();

        Specification<HvQmInspectionKnowledgeBase> specification = (Specification<HvQmInspectionKnowledgeBase>) (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            if (StringUtils.isNotBlank(standardsTypeCode)) {
                list.add(criteriaBuilder.like(root.get("knowledgeCode").as(String.class), "%" + standardsTypeCode + "%"));
            }
            if (StringUtils.isNotBlank(standardsTypeName)) {
                list.add(criteriaBuilder.like(root.get("knowledgeName").as(String.class), "%" + standardsTypeName + "%"));
            }
            list.add(criteriaBuilder.equal(root.get("parentId").as(Integer.class), 0));
            Predicate[] p = list.toArray(new Predicate[0]);
            return criteriaBuilder.and(p);
        };

        return DtoMapper.convertPage(knowledgeBaseRepository.findAll(specification, typeDTO.getRequest()), InspectionKnowledgeBaseDTO.class);

    }

    @Override
    public List<InspectionKnowledgeBaseDTO> findAllByParentId(Integer id) {
        return DtoMapper.convertList(knowledgeBaseRepository.findByParentId(id), InspectionKnowledgeBaseDTO.class);
    }

    @Override
    public List<InspectionKnowledgeBaseLayeredDTO> findAllAndLayered() {
        List<InspectionKnowledgeBaseLayeredDTO> inspectionKnowledgeBaseDTOS = new ArrayList<>();
        List<HvQmInspectionKnowledgeBase> knowledgeBases = knowledgeBaseRepository.findAll();
        List<InspectionKnowledgeBaseLayeredDTO> baseLayeredDTOS = DtoMapper.convertList(knowledgeBases, InspectionKnowledgeBaseLayeredDTO.class);
        List dataList = new ArrayList();
        for (InspectionKnowledgeBaseLayeredDTO knowledgeBase : baseLayeredDTOS) {
            try {
                dataList.add(objectToMap(knowledgeBase));
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        HashMap nodeList = new HashMap();
        // 根节点
        InspectionKnowledgeBaseLayeredDTO inspectionKnowledgeBaseLayeredDTO = null;
        // 根据结果集构造节点列表（存入散列表）
        for (Iterator it = dataList.iterator(); it.hasNext(); ) {
            Map dataRecord = (Map) it.next();
            InspectionKnowledgeBaseLayeredDTO layeredDTO = new InspectionKnowledgeBaseLayeredDTO();
            layeredDTO.setId((Integer) dataRecord.get("id"));
            layeredDTO.setKnowledgeCode((String) dataRecord.get("knowledgeCode"));
            layeredDTO.setKnowledgeName((String) dataRecord.get("knowledgeName"));
            layeredDTO.setParentId((Integer) dataRecord.get("parentId"));
            layeredDTO.setKnowledgeDescription((String) dataRecord.get("knowledgeDescription"));
            nodeList.put(layeredDTO.getId(), layeredDTO);
        }

        // 构造无序的多叉树
        Set entrySet = nodeList.entrySet();
        for (Iterator it = entrySet.iterator(); it.hasNext(); ) {
            InspectionKnowledgeBaseLayeredDTO inspectionKnowledgeBase = (InspectionKnowledgeBaseLayeredDTO) ((Map.Entry) it.next()).getValue();
            log.info("---"+inspectionKnowledgeBase.getId());
            if (inspectionKnowledgeBase.getParentId() == 0) {
                inspectionKnowledgeBaseLayeredDTO = inspectionKnowledgeBase;
                // 对多叉树进行横向排序
                inspectionKnowledgeBaseLayeredDTO.sortChildren();
                inspectionKnowledgeBaseDTOS.add(inspectionKnowledgeBaseLayeredDTO);
            } else {
                log.info("map"+nodeList);
                log.info("value"+nodeList.get(6));
                log.info("pId"+inspectionKnowledgeBase.getParentId());
                Object o = nodeList.get(inspectionKnowledgeBase.getParentId());
                if (null!=o){
                    ((InspectionKnowledgeBaseLayeredDTO) o).addChild(inspectionKnowledgeBase);
                }

            }
        }
        return inspectionKnowledgeBaseDTOS;
    }

    public Map<String, Object> objectToMap(Object obj) throws IllegalAccessException {

        Map<String, Object> map = new HashMap<String, Object>();
        Class<?> clazz = obj.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            String fieldName = field.getName();
            Object value = field.get(obj);
            map.put(fieldName, value);
        }
        return map;
    }
}
