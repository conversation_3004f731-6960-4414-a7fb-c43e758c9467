package com.hvisions.qms.service.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.qms.dto.group.InspectionStandardsGroupDTO;
import com.hvisions.qms.dto.group.QueryStandardsGroupDTO;
import com.hvisions.qms.entity.HvQmInspectionStandardsGroup;
import com.hvisions.qms.enums.InspectionStandardExceptionEnum;
import com.hvisions.qms.repository.InspectionStandardRepository;
import com.hvisions.qms.repository.InspectionStandardsGroupRepository;
import com.hvisions.qms.service.InspectionStandardsGroupService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title: InspectionStandardsGroupServiceImpl</p >
 * <p>Description: 质量标准检测分组实现类</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/10/09</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
public class InspectionStandardsGroupServiceImpl implements InspectionStandardsGroupService {

    private final InspectionStandardsGroupRepository groupRepository;
    private final InspectionStandardRepository standardRepository;

    @Autowired
    public InspectionStandardsGroupServiceImpl(InspectionStandardsGroupRepository groupRepository, InspectionStandardRepository standardRepository) {
        this.groupRepository = groupRepository;
        this.standardRepository = standardRepository;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int addStandardsGroup(InspectionStandardsGroupDTO inspectionStandardsGroupDTO) {
        return groupRepository.save(DtoMapper.convert(inspectionStandardsGroupDTO, HvQmInspectionStandardsGroup.class)).getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void removeStandardsGroupById(Integer id) {
        if (standardRepository.existsByStandardGroupId(id)) {
            throw new BaseKnownException(InspectionStandardExceptionEnum.GROUP_IN_USE);
        } else {
            groupRepository.deleteById(id);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void removeBatchStandardsGroupByIds(List<Integer> ids) {
        for (Integer id : ids) {
            removeStandardsGroupById(id);
        }
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public InspectionStandardsGroupDTO findStandardsGroupById(Integer id) {
        return groupRepository.findById(id).map(groupDto -> DtoMapper.convert(groupDto, InspectionStandardsGroupDTO.class)).orElse(null);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<InspectionStandardsGroupDTO> findStandardsGroupByIds(List<Integer> ids) {
        List<InspectionStandardsGroupDTO> standardsGroups = new ArrayList<>();
        for (Integer id : ids) {
            standardsGroups.add(findStandardsGroupById(id));
        }
        return standardsGroups;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<InspectionStandardsGroupDTO> findStandardsGroupByCodes(List<String> codes) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int updateStandardsGroup(InspectionStandardsGroupDTO inspectionStandardsGroupDTO) {
            return groupRepository.save(DtoMapper.convert(inspectionStandardsGroupDTO, HvQmInspectionStandardsGroup.class)).getId();

    }


    /**
     * {@inheritDoc}
     */
    @Override
    public Page<InspectionStandardsGroupDTO> findStandardsGroup(QueryStandardsGroupDTO queryDto) {
        String groupCode = queryDto.getStandardsGroupCode();
        String groupName = queryDto.getStandardsGroupName();
        Specification<HvQmInspectionStandardsGroup> specification = (Specification<HvQmInspectionStandardsGroup>) (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            if (StringUtils.isNotBlank(groupCode)) {
                list.add(criteriaBuilder.like(root.get("standardsGroupCode").as(String.class), "%" + groupCode + "%"));
            }
            if (StringUtils.isNotBlank(groupName)) {
                list.add(criteriaBuilder.like(root.get("standardsGroupName").as(String.class), "%" + groupName + "%"));
            }
            Predicate[] p = list.toArray(new Predicate[0]);
            return criteriaBuilder.and(p);
        };

        Page<HvQmInspectionStandardsGroup> all = groupRepository.findAll(specification, queryDto.getRequest());
        return DtoMapper.convertPage(groupRepository.findAll(specification, queryDto.getRequest()), InspectionStandardsGroupDTO.class);
    }

}
