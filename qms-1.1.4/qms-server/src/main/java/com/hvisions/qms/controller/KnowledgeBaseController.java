package com.hvisions.qms.controller;

import com.hvisions.qms.dto.knowledge.InspectionKnowledgeBaseDTO;
import com.hvisions.qms.dto.knowledge.InspectionKnowledgeBaseLayeredDTO;
import com.hvisions.qms.dto.knowledge.QueryKnowledgeBaseDTO;
import com.hvisions.qms.dto.knowledge.QueryResultKnowledgeBaseDTO;
import com.hvisions.qms.service.InspectionKnowledgeBaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: KnowledgeBaseController、</p >
 * <p>Description: 质检知识库管理控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/10/8</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/standardsKnowledgeBase")
@Slf4j
@Api(description = "质检标准知识库控制器")
public class KnowledgeBaseController {

    private final InspectionKnowledgeBaseService inspectionKnowledgeBase;

    @Autowired
    public KnowledgeBaseController(InspectionKnowledgeBaseService inspectionKnowledgeBase) {
        this.inspectionKnowledgeBase = inspectionKnowledgeBase;
    }

    /**
     * 新增质检知识库
     *
     * @param inspectionKnowledgeBaseDTO 质检知识库
     * @return 质检知识库
     */
    @ApiOperation(value = " 新增质检知识库")
    @PostMapping(value = "/createKnowledgeBase")
    public int createKnowledgeBase(@RequestBody InspectionKnowledgeBaseDTO inspectionKnowledgeBaseDTO) {
        return inspectionKnowledgeBase.addKnowledgeBase(inspectionKnowledgeBaseDTO);
    }

    /**
     * 删除质检知识库、根据Id
     *
     * @param id 根据Id
     */
    @ApiOperation(value = " 删除质检知识库、根据Id")
    @DeleteMapping(value = "/deleteStandardsTypeById/{id}")
    public void deleteKnowledgeBaseById(@PathVariable Integer id) {
        inspectionKnowledgeBase.removeKnowledgeBaseById(id);
    }

    /**
     * 批量删除质检知识库、根据Id
     *
     * @param ids Id列表
     */
    @ApiOperation(value = " 批量删除质检知识库、根据Id")
    @DeleteMapping(value = "/deleteBatchKnowledgeBaseByIds")
    public void deleteBatchKnowledgeBaseByIds(@RequestBody List<Integer> ids) {
        inspectionKnowledgeBase.removeBatchKnowledgeBaseByIds(ids);
    }

    /**
     * 更新质检知识库
     *
     * @param inspectionKnowledgeBaseDTO 质检知识库
     * @return 主键
     */
    @ApiOperation(value = "更新质检知识库")
    @PutMapping(value = "/editKnowledgeBase")
    public int editKnowledgeBase(@RequestBody InspectionKnowledgeBaseDTO inspectionKnowledgeBaseDTO) {
        return inspectionKnowledgeBase.updateKnowledgeBase(inspectionKnowledgeBaseDTO);
    }

    /**
     * 根据质检知识库id、获取质检知识库
     *
     * @param id 标准类型id
     * @return 质检知识库
     */
    @ApiOperation(value = "根据质检知识库id、获取质检知识库")
    @GetMapping(value = "/getKnowledgeBaseById/{id}")
    public QueryResultKnowledgeBaseDTO getKnowledgeBaseById(@PathVariable Integer id) {
        return inspectionKnowledgeBase.findKnowledgeBaseById(id);
    }
    /**
     * 获取质检知识库、根据Id列表
     *
     * @param ids Id列表
     * @return 质检知识库
     */
    @ApiOperation(value = "获取质检知识库、根据Id列表")
    @PostMapping(value = "/getKnowledgeBaseByIds")
    public List<InspectionKnowledgeBaseDTO> getKnowledgeBaseByIds(@RequestBody List<Integer> ids) {
        return inspectionKnowledgeBase.findKnowledgeBaseByIds(ids);
    }
    /**
     * 多条件分页获取质检知识库
     *
     * @param queryStandardsTypeDTO 查询条件
     * @return 质检知识库
     */
    @ApiOperation(value = "多条件分页获取质检知识库")
    @PostMapping(value = "/getKnowledgeBase")
    public Page<InspectionKnowledgeBaseDTO> getKnowledgeBase(@RequestBody QueryKnowledgeBaseDTO queryStandardsTypeDTO) {
        return inspectionKnowledgeBase.getKnowledgeBase(queryStandardsTypeDTO);
    }
    /**
     * 根据质检知识库id、获取质检知识库
     *
     * @param id 标准知识库id
     * @return 质检知识库
     */
    @ApiOperation(value = "查询下一层级所有信息，根据层级节点ID查询")
    @GetMapping(value = "/findAllByParentId/{id}")
    public List<InspectionKnowledgeBaseDTO> findAllByParentId(@PathVariable Integer id) {
        return inspectionKnowledgeBase.findAllByParentId(id);
    }
    /**
     * 查询所有层级，并分层显示
     *
     * @return 质检知识库
     */
    @ApiOperation(value = "查询所有层级，并分层显示")
    @GetMapping(value = "/findAllAndLayered")
    public List<InspectionKnowledgeBaseLayeredDTO> findAllAndLayered() {
        return inspectionKnowledgeBase.findAllAndLayered();
    }
}