package com.hvisions.qms.repository;

import com.hvisions.qms.entity.HvQmQualityInspectionOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: CheckListRepository</p >
 * <p>Description: 质检单repository</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/15</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface InspectionOrderRepository extends JpaRepository<HvQmQualityInspectionOrder, Integer> {


    List<HvQmQualityInspectionOrder> findByInspectionOrderTypeId(Integer integer);

    /**
     * 根据质检单编码/查询质检单信息
     *
     * @param inspectionCode 质检单编码
     * @return 质检单信息
     */
    HvQmQualityInspectionOrder findByInspectionCode(String inspectionCode);


    /**
     * 根据工单查询对应的质检单
     *
     * @param workOrderCode 工单编号
     * @return 质检单列表
     */
    List<HvQmQualityInspectionOrder> findByWorkOrderCodeContaining(String workOrderCode);


    /**
     * 不带质检单查询
     * 根据时间范围查询 年份 质检单数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 检查结果
     */
    @Query(value = "" +
        "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(4),c.create_time,120) as checkCountTime FROM hv_qm_quality_inspection_order as c " +
        "where c.hide = 0 and c.create_time between :startTime and :endTime " +
        "GROUP BY convert(varchar(4),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerYearAll(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 带质检单查询
     * 根据时间范围查询 年份 质检单数
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param inspectionType 检测状态
     * @return 检查结果
     */
    @Query(value = "" +
        "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(4),c.create_time,120) as checkCountTime FROM hv_qm_quality_inspection_order as c " +
        "where c.hide = 0 and c.create_time between :startTime and :endTime " +
        "and c.inspection_order_type_id=:inspectionType " +
        "GROUP BY convert(varchar(4),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerYearInspection(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("inspectionType") Integer inspectionType);


    /**
     * 不带质检单查询
     * 根据时间范围查询 年份 质检单数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 检查结果
     */
    @Query(value = "" +
        "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(7),c.create_time,120) as checkCountTime FROM hv_qm_quality_inspection_order as c " +
        "where c.hide = 0 and c.create_time between :startTime and :endTime " +
        "GROUP BY convert(varchar(7),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerMonthsAll(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 带质检单查询和质检单状态
     * 根据时间范围查询 年份 质检单数
     *
     * @param startTime        开始时间
     * @param endTime          结束时间
     * @param inspectionStatus 质检单状态
     * @param inspectionType   质检单类型
     * @return 检查结果
     */
    @Query(value = "" + "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(4),c.create_time,120) as checkCountTime FROM hv_qm_quality_inspection_order as c " +
        "where c.hide = 0 and c.create_time between :startTime and :endTime " +
        "and c.inspection_order_type_id=:inspectionType " +
        "and c.inspection_status=:inspectionStatus " +
        "GROUP BY convert(varchar(4),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerYearsByInspectionType(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("inspectionType") Integer inspectionType, @Param("inspectionStatus") Integer inspectionStatus);

    /**
     * 带质检单查询
     * 根据时间范围查询月份质检单数
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param inspectionType 检测状态
     * @return 检查结果
     */
    @Query(value = "" +
        "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(7),c.create_time,120) as checkCountTime FROM hv_qm_quality_inspection_order as c " +
        "where c.hide = 0 and c.create_time between :startTime and :endTime " +
        "and c.inspection_order_type_id=:inspectionType " +
        "GROUP BY convert(varchar(7),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerMouthsInspection(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("inspectionType") Integer inspectionType);


    /**
     * 不带质检单查询
     * 根据时间范围查询月份质检单数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 检查结果
     */
    @Query(value = "" +
        "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(10),c.create_time,120) as checkCountTime FROM hv_qm_quality_inspection_order as c " +
        "where c.hide = 0 and c.create_time between :startTime and :endTime " +
        "GROUP BY convert(varchar(10),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerDaysAll(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 带质检单查询
     * 根据时间范围查询 月份 质检单数
     *
     * @param startTime        开始时间
     * @param endTime          结束时间
     * @param inspectionStatus 质检单状态
     * @param inspectionType   质检单类型
     * @return 检查结果
     */
    @Query(value = "" +
        "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(7),c.create_time,120) as  checkCountTime FROM hv_qm_quality_inspection_order as c " +
        "where c.hide = 0 and c.create_time between :startTime and :endTime " +
        "and c.inspection_order_type_id=:inspectionType " +
        "and c.inspection_status=:inspectionStatus " +
        "GROUP BY convert(varchar(7),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerMouthsByInspectionType(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("inspectionType") Integer inspectionType, @Param("inspectionStatus") Integer inspectionStatus);


    /**
     * 带质检单查询
     * 根据时间范围查询天 质检单数
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param inspectionType 检测状态
     * @return 检查结果
     */
    @Query(value = "" +
        "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(10),c.create_time,120) as checkCountTime FROM hv_qm_quality_inspection_order as c " +
        "where c.hide = 0 and c.create_time between :startTime and :endTime " +
        "and c.inspection_order_type_id=:inspectionType " +
        "GROUP BY convert(varchar(10),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerDayInspection(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("inspectionType") Integer inspectionType);


    /**
     * 带质检单查询
     * 根据时间范围查询 天 质检单数
     *
     * @param startTime        开始时间
     * @param endTime          结束时间
     * @param inspectionStatus 质检单状态
     * @param inspectionType   质检单类型
     * @return 检查结果
     */
    @Query(value = "" +
        "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(10),c.create_time,120) checkCountTime FROM hv_qm_quality_inspection_order as c " +
        "where c.hide = 0 and c.create_time between :startTime and :endTime " +
        "and c.inspection_order_type_id=:inspectionType " +
        "and c.inspection_status=:inspectionStatus " +
        "GROUP BY convert(varchar(10),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerDayByInspectionType(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("inspectionType") Integer inspectionType, @Param("inspectionStatus") Integer inspectionStatus);


    /**
     * 查询质检单类型查询
     *
     * @param inspectionType 类型
     * @return 结果
     */
    @Query(nativeQuery = true, value = "SELECT COUNT(*)  as checkCountNum ,c.inspection_order_type_id  as checkCountTime FROM hv_qm_quality_inspection_order as c where  c.hide=false and c.inspection_order_type_id=?1  GROUP BY c.inspection_order_type_id")
    List<Map<String, Object>> findCountBySqlServerInspectionType(Integer inspectionType);


    /**
     * 查询所有类型 带检测状态
     * 根据时间范围查询 天 质检单数
     *
     * @param startTime        开始时间
     * @param endTime          结束时间
     * @param inspectionStatus 质检单状态
     * @return 检查结果
     */
    @Query(value = "" +
        "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(10),c.create_time,120) checkCountTime FROM hv_qm_quality_inspection_order as c " +
        "where  c.hide = 0 and c.create_time between :startTime and :endTime " +
        "and c.inspection_status=:inspectionStatus " +
        "GROUP BY convert(varchar(10),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerAllDayByStatus(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("inspectionStatus") Integer inspectionStatus);

    /**
     * 查询所有类型 带检测状态
     * 根据时间范围查询 月份 质检单数
     *
     * @param startTime        开始时间
     * @param endTime          结束时间
     * @param inspectionStatus 质检单状态
     * @return 检查结果
     */
    @Query(value = "" +
        "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(7),c.create_time,120) checkCountTime FROM hv_qm_quality_inspection_order as c " +
        "where  c.hide = 0 and c.create_time between :startTime and :endTime " +
        "and c.inspection_status=:inspectionStatus " +
        "GROUP BY convert(varchar(7),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerAllMouthsByStatus(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("inspectionStatus") Integer inspectionStatus);

    /**
     * 查询所有类型 带检测状态
     * 根据时间范围查询 年份 质检单数
     *
     * @param startTime        开始时间
     * @param endTime          结束时间
     * @param inspectionStatus 质检单状态
     * @return 检查结果
     */
    @Query(value = "" +
        "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(4),c.create_time,120) checkCountTime FROM hv_qm_quality_inspection_order as c " +
        "where  c.hide = 0 and c.create_time between :startTime and :endTime " +
        "and c.inspection_status=:inspectionStatus " +
        "GROUP BY convert(varchar(4),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerAllYearsByStatus(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("inspectionStatus") Integer inspectionStatus);


    /**
     * 不带质检单查询
     * 根据时间范围查询 年份 质检单数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param num       时间字符长度
     * @return 检查结果
     */
    @Query(value = "" +
        "SELECT COUNT(c.id) as checkCountNum ,substring(c.create_time,1,:num) as checkCountTime FROM hv_qm_quality_inspection_order as c " +
        "where  c.hide = 0 and c.create_time between :startTime and :endTime " +
        "and c.inspection_result in (1,2)" +
        " GROUP BY substring(c.create_time,1,:num)", nativeQuery = true)
    List<Map<String, Object>> findCountByMysqlYearAll(@Param("startTime") Date startTime, @Param("endTime") Date endTime,
                                                      @Param("num") Integer num
    );

    /**
     * 带质检单查询
     * 根据时间范围查询 年份 质检单数
     *
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param inspectionType 检测状态
     * @param num            时间字符长度
     * @return 检查结果
     */
    @Query(value = "" +
        "SELECT COUNT(c.id) as checkCountNum ,substring(c.create_time,1,:num) as checkCountTime FROM hv_qm_quality_inspection_order as c " +
        "where  c.hide = 0 and c.create_time between :startTime and :endTime " +
        "and c.inspection_order_type_id=:inspectionType " +
        "and c.inspection_result in (1,2)" +
        " GROUP BY substring(c.create_time,1,:num)", nativeQuery = true)
    List<Map<String, Object>> findCountByMysqlYearInspection(@Param("startTime") Date startTime,
                                                             @Param("endTime") Date endTime,
                                                             @Param("inspectionType") Integer inspectionType,
                                                             @Param("num") Integer num);


    /**
     * 带质检单查询和质检单状态
     * 根据时间范围查询 年份 质检单数
     *
     * @param startTime        开始时间
     * @param endTime          结束时间
     * @param inspectionStatus 质检单状态
     * @param inspectionType   质检单类型
     * @param num              时间字符长度
     * @return 检查结果
     */
    @Query(value = "" + "SELECT COUNT(c.id) as checkCountNum ,substring(c.create_time,1,:num) as checkCountTime FROM hv_qm_quality_inspection_order as c " +
        "where  c.hide = 0 " +
        "and c.create_time between :startTime and :endTime " +
        "and c.inspection_order_type_id=:inspectionType " +
        "and c.inspection_status=:inspectionStatus " +
        "and c.inspection_result =:inspectionResult" +
        " GROUP BY substring(c.create_time,1,:num)", nativeQuery = true)
    List<Map<String, Object>> findCountByMysqlYearsByInspectionType(@Param("startTime") Date startTime,
                                                                    @Param("endTime") Date endTime,
                                                                    @Param("inspectionType") Integer inspectionType,
                                                                    @Param("inspectionStatus") Integer inspectionStatus,
                                                                    @Param("num") Integer num,
                                                                    @Param("inspectionResult") Integer result
    );


    /**
     * 查询质检单类型查询
     *
     * @param inspectionType 类型
     * @return 结果
     */
    @Query(nativeQuery = true, value = "SELECT COUNT(*)  as checkCountNum ,c.inspection_order_type_id  as checkCountTime FROM hv_qm_quality_inspection_order as c where  c.hide=false and c.inspection_order_type_id=?1  GROUP BY c.inspection_order_type_id")
    List<Map<String, Object>> findCountByMysqlinspectionType(Integer inspectionType);


    /**
     * 查询所有类型 带检测状态
     * 根据时间范围查询 年份 质检单数
     *
     * @param startTime        开始时间
     * @param endTime          结束时间
     * @param inspectionStatus 质检单状态
     * @param num              时间字符长度
     * @return 检查结果
     */
    @Query(value = "" +
        "SELECT COUNT(c.id) as checkCountNum ,substring(c.create_time,1,:num) checkCountTime FROM hv_qm_quality_inspection_order as c " +
        "where  c.hide=false and c.create_time between :startTime and :endTime " +
        "and c.inspection_status=:inspectionStatus " +
        " GROUP BY substring(c.create_time,1,:num)", nativeQuery = true)
    List<Map<String, Object>> findCountByMysqlAllYearsByStatus(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("inspectionStatus") Integer inspectionStatus,
                                                               @Param("num") Integer num);

}
