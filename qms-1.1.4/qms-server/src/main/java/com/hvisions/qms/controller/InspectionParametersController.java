package com.hvisions.qms.controller;

import com.hvisions.qms.dto.parameter.InspectionParameterValueBatchDTO;
import com.hvisions.qms.service.InspectionParameterInstanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>Title: testParameterInstance</p >
 * <p>Description: 质检单工艺参数检测控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/15</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RequestMapping("/testParameterInstance")
@RestController
@Api(description = "质检单工艺参数检测控制器")
public class InspectionParametersController {
    private final InspectionParameterInstanceService testParameterService;

    @Autowired
    public InspectionParametersController(InspectionParameterInstanceService testParameterService) {
        this.testParameterService = testParameterService;
    }

    /**
     * 新增
     *
     * @param inspectionStandardsParametersDTO 传入的对象
     * @return 添加后的返回实体Id
     */
    @ApiOperation(value = "批量新增")
    @RequestMapping(value = "/createTestParameterInstanceMore", method = RequestMethod.POST)
    public int createTestParameterInstanceMore(@RequestBody InspectionParameterValueBatchDTO inspectionStandardsParametersDTO) {
        return testParameterService.createTestParameterInstanceMore(inspectionStandardsParametersDTO);
    }


    /**
     * 删除
     *
     * @param id 实体id
     */
    @ApiOperation(value = "根据ID删除参数数据")
    @RequestMapping(value = "/deleteTestParameterInstanceById/{id}", method = RequestMethod.DELETE)
    public void deleteTestParameterInstanceById(@PathVariable int id) {
        testParameterService.deleteById(id);
    }



}