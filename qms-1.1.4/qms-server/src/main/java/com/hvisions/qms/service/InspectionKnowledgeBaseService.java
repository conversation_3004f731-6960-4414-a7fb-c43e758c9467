package com.hvisions.qms.service;

import com.hvisions.qms.dto.knowledge.InspectionKnowledgeBaseDTO;
import com.hvisions.qms.dto.knowledge.InspectionKnowledgeBaseLayeredDTO;
import com.hvisions.qms.dto.knowledge.QueryKnowledgeBaseDTO;
import com.hvisions.qms.dto.knowledge.QueryResultKnowledgeBaseDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface InspectionKnowledgeBaseService {

    /**
     * 新增质检标准类型
     *
     * @param InspectionKnowledgeBaseDTO 质检标准类型
     * @return 质检标准类型
     */
    int addKnowledgeBase(InspectionKnowledgeBaseDTO InspectionKnowledgeBaseDTO);

    /**
     * 更新质检标准类型
     *
     * @param InspectionKnowledgeBaseDTO 质检标准类型
     * @return 主键
     */
    int updateKnowledgeBase(InspectionKnowledgeBaseDTO InspectionKnowledgeBaseDTO);

    /**
     * 删除质检标准类型、根据Id
     *
     * @param id 根据Id
     */
    void removeKnowledgeBaseById(Integer id);

    /**
     * 批量删除质检标准类型、根据Id
     *
     * @param ids Id列表
     */
    void removeBatchKnowledgeBaseByIds(List<Integer> ids);

    /**
     * 根据质检标准类型id、获取质检标准类型
     *
     * @param id 标准类型id
     * @return 质检标准类型
     */
    QueryResultKnowledgeBaseDTO findKnowledgeBaseById(Integer id);

    /**
     * 获取质检标准类型、根据Id列表
     *
     * @param ids Id列表
     * @return 质检标准类型
     */
    List<InspectionKnowledgeBaseDTO> findKnowledgeBaseByIds(List<Integer> ids);


    /**
     * 多条件分页获取质检标准类型
     *
     * @param queryStandardsTypeDTO 查询条件
     * @return 质检标准类型
     */
    Page<InspectionKnowledgeBaseDTO> getKnowledgeBase(QueryKnowledgeBaseDTO queryStandardsTypeDTO);

    List<InspectionKnowledgeBaseDTO> findAllByParentId(Integer id);

    List<InspectionKnowledgeBaseLayeredDTO> findAllAndLayered();
}
