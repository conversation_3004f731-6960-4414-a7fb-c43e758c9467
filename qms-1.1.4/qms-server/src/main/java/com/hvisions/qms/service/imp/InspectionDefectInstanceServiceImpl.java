package com.hvisions.qms.service.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.SerialUtil;
import com.hvisions.qms.dto.defects.InspectionDefectInstanceBatchDTO;
import com.hvisions.qms.dto.defects.InspectionDefectInstanceDTO;
import com.hvisions.qms.entity.HvQmDefectInstance;
import com.hvisions.qms.entity.HvQmDefectLog;
import com.hvisions.qms.entity.HvQmQualityInspectionOrder;
import com.hvisions.qms.enums.InspectionExceptionEnum;
import com.hvisions.qms.enums.InspectionStatusEnum;
import com.hvisions.qms.enums.LogTypeEnum;
import com.hvisions.qms.enums.RepairStateEnum;
import com.hvisions.qms.repository.DefectLogRepository;
import com.hvisions.qms.repository.InspectionDefectInstanceRepository;
import com.hvisions.qms.repository.InspectionOrderRepository;
import com.hvisions.qms.service.InspectionDefectInstanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title: TestDefectInstanceServiceImpl</p >
 * <p>Description: 在线ServiceImpl</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/15</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class InspectionDefectInstanceServiceImpl implements InspectionDefectInstanceService {
    private final InspectionDefectInstanceRepository inspectionDefectInstanceRepository;
    private final InspectionOrderRepository inspectionOrderRepository;
    private final DefectLogRepository defectLogRepository;
    @Autowired
    SerialUtil serialUtil;

    public InspectionDefectInstanceServiceImpl(InspectionDefectInstanceRepository inspectionDefectInstanceRepository, InspectionOrderRepository inspectionOrderRepository, DefectLogRepository defectLogRepository) {
        this.inspectionDefectInstanceRepository = inspectionDefectInstanceRepository;
        this.inspectionOrderRepository = inspectionOrderRepository;
        this.defectLogRepository = defectLogRepository;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public Integer add(InspectionDefectInstanceDTO inspectionDefectInstanceDTO) {
        inspectionDefectInstanceRepository.save(DtoMapper.convert(inspectionDefectInstanceDTO, HvQmDefectInstance.class)).getId();
        return 0;
    }

    /**
     * 对比数据和数据库数据是否相同
     *
     * @param entity 数据库实体
     * @param dto    传入数据
     * @return 是否相等
     */
    private boolean equal(HvQmDefectInstance entity, InspectionDefectInstanceDTO dto) {
        if (entity.getDefectId() == null || dto.getDefectId() == null || (!dto.getDefectId().equals(entity.getDefectId()))) {
            return false;
        }
        int entityPointId = entity.getPointId() == null ? 0 : entity.getPointId();
        int entityPositionId = entity.getPositionId() == null ? 0 : entity.getPositionId();
        int dtoPointId = dto.getPointId() == null ? 0 : dto.getPointId();
        int dtoPositionId = dto.getPositionId() == null ? 0 : dto.getPositionId();
        return entityPointId == dtoPointId && entityPositionId == dtoPositionId;
    }
    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addAll(InspectionDefectInstanceBatchDTO testDefectInstanceDTO) {
        if (null == testDefectInstanceDTO.getInspectionDefectInstanceDTOS() || testDefectInstanceDTO.getInspectionDefectInstanceDTOS().isEmpty()) {
            throw new BaseKnownException(InspectionExceptionEnum.ENTRY_DEFECT_DOES_NOT_EXIST);
        }
        Optional<HvQmQualityInspectionOrder> inspectionOrder = inspectionOrderRepository.findById(testDefectInstanceDTO.getInspectionOrderId());
        if (!inspectionOrder.isPresent()) {
            return 0;
        }
        //检测质检单状态、提交：检测完成，未提交：检测中
        if (inspectionOrder.get().getCommit()) {
            throw new BaseKnownException(InspectionExceptionEnum.THE_INSPECTION_FORM_HAS_BEEN_TESTED_PLEASE_DO_NOT_SUBMIT_IT_AGAIN);
        } else {
            inspectionOrder.get().setInspectionStatus(InspectionStatusEnum.IN_CHECKING.getCode());
        }
        //保存质检单状态
        inspectionOrderRepository.save(inspectionOrder.get());
        //查找质检单所有缺陷数据，如果传入的数据没有对应的数据，新增。如果有更新
        List<HvQmDefectInstance> defects = inspectionDefectInstanceRepository.findByInspectionId(testDefectInstanceDTO.getInspectionOrderId());
        List<HvQmDefectLog> hvQmDefectLogs = new ArrayList<>();
        //更新了几条数据
        for (InspectionDefectInstanceDTO dto : testDefectInstanceDTO.getInspectionDefectInstanceDTOS()) {
            //日志记录
            HvQmDefectLog hvQmDefectLog = new HvQmDefectLog();
            hvQmDefectLog.setInspectionId(testDefectInstanceDTO.getInspectionOrderId());
            hvQmDefectLog.setCreateTime(new Date());
            hvQmDefectLog.setDefectId(dto.getDefectId());
            hvQmDefectLog.setPointId(dto.getPointId());
            hvQmDefectLog.setPositionId(dto.getPositionId());
            hvQmDefectLog.setOperationCode(testDefectInstanceDTO.getOperationCode());
            hvQmDefectLog.setLogType(LogTypeEnum.DEFECT.getCode());
            hvQmDefectLog.setQuantityOperation(dto.getQuantityOperation());
            hvQmDefectLog.setCreatorId(dto.getCreatorId());
            //返修日志
            HvQmDefectLog hvQmDefectLog1 = new HvQmDefectLog();
            hvQmDefectLog1.setInspectionId(testDefectInstanceDTO.getInspectionOrderId());
            hvQmDefectLog1.setPositionId(dto.getPositionId());
            hvQmDefectLog1.setPointId(dto.getPointId());
            hvQmDefectLog1.setDefectId(dto.getDefectId());
            hvQmDefectLog1.setCreateTime(new Date());
            hvQmDefectLog1.setOperationCode(testDefectInstanceDTO.getOperationCode());
            hvQmDefectLog1.setLogType(LogTypeEnum.REPAIR.getCode());
            Optional<HvQmDefectInstance> first = defects.stream().filter(t -> equal(t, dto)).findFirst();
            if (first.isPresent()) {
                BeanUtils.copyProperties(dto, first.get(), "id");
                hvQmDefectLog1.setRepairState(dto.getRepairState());
                hvQmDefectLogs.add(hvQmDefectLog1);
                first.get().setHvQmQualityInspectionOrder(inspectionOrder.get());
                first.get().setIsSave(true);
                if (dto.getQuantity() <= 0) {
                    first.get().setRepairState(null);
                } else {
                    if (dto.getRepairState() == null) {
                        first.get().setRepairState(RepairStateEnum.ONLINE_REPAIR.getCode());
                    }
                }
                inspectionDefectInstanceRepository.save(first.get());
            } else {
                HvQmDefectInstance newDefect = DtoMapper.convert(dto, HvQmDefectInstance.class);
                newDefect.setHvQmQualityInspectionOrder(inspectionOrder.get());
                if (dto.getQuantity() <= 0) {
                    newDefect.setRepairState(null);
                } else {
                    if (dto.getRepairState() == null) {
                        newDefect.setRepairState(RepairStateEnum.ONLINE_REPAIR.getCode());
                    }
                }
                inspectionDefectInstanceRepository.save(newDefect);
                if (newDefect.getRepairState() != null) {
                    if (dto.getQuantity() > 0) {
                        hvQmDefectLog1.setRepairState(dto.getRepairState());
                    }
                    hvQmDefectLogs.add(hvQmDefectLog1);
                }
            }
            if (dto.getQuantityOperation() != null) {
                hvQmDefectLogs.add(hvQmDefectLog);
            }
        }
        defectLogRepository.saveAll(hvQmDefectLogs);
        return testDefectInstanceDTO.getInspectionDefectInstanceDTOS().size();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void deleteById(Integer id) {
        inspectionDefectInstanceRepository.deleteById(id);
    }

    /**
     * * 修改返修状态
     *
     * @param inspectionId 质检单
     * @param pointId      区域Id
     * @param positionId   位置
     * @param state        返修状态
     */
    @Override
    public void updateRepairState(Integer inspectionId, Integer pointId, Integer positionId, Integer state,
                                  String operationCode) {
        List<HvQmDefectInstance> defects = inspectionDefectInstanceRepository.findByInspectionId(inspectionId);
        pointId = pointId == null ? 0 : pointId;
        positionId = positionId == null ? 0 : positionId;
        for (HvQmDefectInstance defect : defects) {
            if (defect.getPointId().equals(pointId) && defect.getPositionId().equals(positionId)) {
                defect.setRepairState(state);
                inspectionDefectInstanceRepository.save(defect);
                createLog(inspectionId, pointId, positionId, state, operationCode, defect.getDefectId(),
                        LogTypeEnum.REPAIR.getCode());
            }
        }
    }

    private void createLog(Integer inspectionId, Integer pointId, Integer positionId, Integer state,
                           String operationCode, Integer defectId, Integer type) {
        HvQmDefectLog hvQmDefectLog = new HvQmDefectLog();
        hvQmDefectLog.setRepairState(state);
        hvQmDefectLog.setInspectionId(inspectionId);
        hvQmDefectLog.setPositionId(positionId);
        hvQmDefectLog.setPointId(pointId);
        hvQmDefectLog.setDefectId(defectId);
        hvQmDefectLog.setCreateTime(new Date());
        hvQmDefectLog.setOperationCode(operationCode);
        hvQmDefectLog.setLogType(type);
        defectLogRepository.save(hvQmDefectLog);
    }

}