package com.hvisions.qms.configuration;

import com.hvisions.common.dto.ExtendInfoParam;
import com.hvisions.common.service.BaseExtendService;
import com.hvisions.common.utils.SqlFactoryUtil;
import com.hvisions.qms.consts.DbConst;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>Title: DefectExtendConfig</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/7/23</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Configuration
public class DefectExtendConfig {

    /**
     * @return 获取扩展服务工厂对象
     */
    @Bean
    SqlFactoryUtil getSqlFactory() {
        return new SqlFactoryUtil();
    }


    @Bean(value = "defect_extend")
    BaseExtendService getDefectExtendService(SqlFactoryUtil getSqlFactory) {
        ExtendInfoParam extendInfoParam = new ExtendInfoParam();
        extendInfoParam.setOriginTableName(DbConst.DEFECT_TABLE_NAME);
        extendInfoParam.setOriginTableIdName(DbConst.DEFECT_EXTEND_ID);
        extendInfoParam.setExtendTableName(DbConst.DEFECT_EXTEND_TABLE_NAME);
        return getSqlFactory.getSqlBridge(extendInfoParam);
    }
}