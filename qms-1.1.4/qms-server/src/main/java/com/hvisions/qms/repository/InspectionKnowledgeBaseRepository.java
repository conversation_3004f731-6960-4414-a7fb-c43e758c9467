package com.hvisions.qms.repository;

import com.hvisions.qms.entity.HvQmInspectionKnowledgeBase;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: InspectionStandardsTypeRepository</p >
 * <p>Description: 质检标准类型持久化层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/10/09</p >
 *
 * <AUTHOR>
 * @version :2.0.0
 */
@Repository
public interface InspectionKnowledgeBaseRepository extends JpaRepository<HvQmInspectionKnowledgeBase, Integer> {
    /**
     * 查询所有质检标准类型
     *
     * @param specification 查询条件
     * @param request       分页数据
     * @return 分页数据
     */
    Page<HvQmInspectionKnowledgeBase> findAll(Specification<HvQmInspectionKnowledgeBase> specification, Pageable request);

    List<HvQmInspectionKnowledgeBase> findByParentId(Integer id);


}
