package com.hvisions.qms.repository;

import com.hvisions.qms.entity.HvQmQualityDefectRouteOperation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface DefectRouteOperationRepository extends JpaRepository<HvQmQualityDefectRouteOperation, Integer> {


    /**
     * 根据缺陷id 删除缺陷-工艺步骤数据
     *
     * @param defectId 缺陷id
     */
    @Modifying
    @Transactional(rollbackFor = Exception.class)
    @Query(value = "delete from hv_qm_quality_defect_route_operation where defect_id = ?1", nativeQuery = true)
    void deleteByDefectId(Integer defectId);

    /**
     * 根据缺陷id获取工艺步骤id列表
     *
     * @param defectId 缺陷id
     * @return 工艺步骤id列表
     */
    List<HvQmQualityDefectRouteOperation> findByDefectId(Integer defectId);

    /**
     * 查询工艺缺陷
     *
     * @param operationId 工艺操作id
     * @param defectId    缺陷id
     * @return 工艺缺陷
     */
    HvQmQualityDefectRouteOperation findByRouteOperationIdAndDefectId(Integer operationId, Integer defectId);

}
