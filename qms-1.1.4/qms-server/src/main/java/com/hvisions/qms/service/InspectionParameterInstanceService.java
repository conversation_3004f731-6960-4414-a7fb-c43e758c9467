package com.hvisions.qms.service;

import com.hvisions.qms.dto.parameter.InspectionParameterValueBatchDTO;

/**
 * <p>Title: TestParameterInstanceService</p >
 * <p>Description: 参数检查service</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/15</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface InspectionParameterInstanceService {


    /**
     * 根据id删除参数结果
     *
     * @param id 参数结果id
     */
    void deleteById(int id);

    /**
     * 批量保存参数结果
     *
     * @param InspectionStandardsParametersDTO 参数结果DTO结合
     * @return 保存条数
     */
    int createTestParameterInstanceMore(InspectionParameterValueBatchDTO InspectionStandardsParametersDTO);


}