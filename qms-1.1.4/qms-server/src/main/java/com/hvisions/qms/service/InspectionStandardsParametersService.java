package com.hvisions.qms.service;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.item.InspectionStandardsParametersDTO;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface InspectionStandardsParametersService  {
    /**
     * 新增标准参数
     *
     * @param inspectionStandardsParametersDTO 标准参数
     * @return 主键
     */
    int addStandardsParameter(InspectionStandardsParametersDTO inspectionStandardsParametersDTO);

    /**
     * 批量新增标准参数
     *
     * @param inspectionStandardsParametersDTOS 标准参数
     */
    void addBatchStandardsParameter(List<InspectionStandardsParametersDTO> inspectionStandardsParametersDTOS);


    /**
     * 批量新增标准参数
     *
     * @param inspectionStandardsParametersDTOS 标准参数
     */
    void importBatchStandardsParameter(List<InspectionStandardsParametersDTO> inspectionStandardsParametersDTOS);
    /**
     * 更新标准参数
     *
     * @param inspectionStandardsParametersDTO 标准参数
     * @return 标准参数
     */
    int updateStandardsParameters(InspectionStandardsParametersDTO inspectionStandardsParametersDTO);

    /**
     * 删除标准参数、根据Id
     *
     * @param id 主键
     */
    void removeStandardsParametersById(Integer id);

    /**
     * 批量删除标准参数、根据Id列表
     *
     * @param ids id列表
     */
    void removeStandardsParametersByIds(List<Integer> ids);

    /**
     * 获取标准参数、根据标准参数id
     *
     * @param id 标准参数id
     * @return 标准参数
     */
    InspectionStandardsParametersDTO findStandardsParametersByStandardById(Integer id);

    /**
     * 获取标准参数、根据编码列表
     *
     * @param codes 编码列表
     * @return 标准参数
     */
    List<InspectionStandardsParametersDTO> findStandardsParametersByStandardByCodes(List<String> codes);

    /**
     * 获取标准参数、根据标准参数Id列表
     *
     * @param ids 获取标准参数、根据标准参数Id列表
     * @return 标准参数Id列表
     */
    List<InspectionStandardsParametersDTO> findStandardsParametersByStandardByIds(List<Integer> ids);
    /**
     * 获取标准参数、根据编码列表
     *
     * @param standardId 质检标准id
     * @return 标准参数
     */
    List<InspectionStandardsParametersDTO> getStandardsParametersByStandardId(int standardId);
    /**文件导出
     * @param standardId 质检标准ID
     * @return 导出文件
     * @throws IOException IO异常
     * @throws IllegalAccessException 非法访问异常
     */
    ResultVO<ExcelExportDto> exportParameter(Integer standardId) throws IOException,IllegalAccessException;
    /**文件导出
     * @param standardId 质检标准ID
     * @return 导出二进制
     * @throws IOException IO异常
     * @throws IllegalAccessException 非法访问异常
     */
    ResponseEntity<byte[]> exportParameterLink(Integer standardId)  throws IOException,IllegalAccessException;
    /**
     * 文件导入
     * @param file 文件信息
     * @param id 质检标准ID
     * @return 文件录入显示
     * @throws IllegalAccessException 非法访问异常
     * @throws ParseException 数据格式转换异常
     * @throws IOException IO异常
     */
    ImportResult importParameter(MultipartFile file, Integer id)  throws IllegalAccessException, ParseException, IOException;
    /**
     * 调整质检标准参数顺序
     *
     * @param index 参数顺序
     */
    void adjustIndex(Map<Integer, Integer> index);
}
