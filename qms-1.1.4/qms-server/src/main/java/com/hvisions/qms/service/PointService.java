package com.hvisions.qms.service;

import com.hvisions.qms.dto.position.PointDTO;

import java.util.List;

/**
 * <p>Title: PointService</p >
 * <p>Description: 质量位置点服务层接口</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/16</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface PointService {

    /**
     * 添加实体
     *
     * @param pointDTO 实体对象
     * @return id
     */
    int save(PointDTO pointDTO);

    /**
     * 删除实体
     *
     * @param id 实体id
     */
    void deleteById(int id);

    /**
     * 获取所有
     *
     * @return 所有实体列表
     */
    List<PointDTO> getAll();

    /**
     * 根据id获取点
     *
     * @param id 点id
     * @return 点
     */
    PointDTO getPointById(Integer id);

    /**
     * 根据id列表获取点列表
     *
     * @param ids id列表
     * @return 点列表
     */
    List<PointDTO> getPointsByIds(List<Integer> ids);

}
