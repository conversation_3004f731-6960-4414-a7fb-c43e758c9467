package com.hvisions.qms.service;
import com.hvisions.qms.dto.step.InspectionStandardsStepDTO;
import com.hvisions.qms.dto.step.QueryStandardsStepDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface InspectionStandardsStepService {


    /**
     * 操作绑定质检标准
     * @param stepId 工位id
     * @param stepName 工位名称
     * @param standardId 质检标准id
     * @return 是否成功
     */
    int addingInspectionStandards(Integer stepId, String stepName, Integer standardId);

    /**
     * 操作解绑质检标准
     * @param stepId 工位id
     * @param standardId 质检标准id
     */
    void unbindingInspectionStandards(Integer stepId, Integer standardId);

    List<InspectionStandardsStepDTO> getBindStandardStepList(QueryStandardsStepDTO stepDTO);
}
