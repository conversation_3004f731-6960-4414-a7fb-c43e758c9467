package com.hvisions.qms.service;

import com.hvisions.qms.dto.DefectTypeDTO;
import com.hvisions.qms.dto.DefectTypeQueryDTO;
import com.hvisions.qms.entity.HvQmQualityDefectType;
import org.springframework.data.domain.Page;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface DefectTypeService {


    /**
     *新增缺陷责任部门信息
     * @param defectTypeDTO 缺陷责任部门信息
     * @return 缺陷责任部门信息id
     */
    Integer addDefectType(DefectTypeDTO defectTypeDTO);

    /**
     * 修改缺陷责任部门信息
     * @param defectTypeDTO 缺陷责任部门信息
     * @return 缺陷责任部门信息id
     */
    Integer updateDefectType(DefectTypeDTO defectTypeDTO);

    /**
     * 根据id删除缺陷责任部门信息
     * @param id 缺陷责任部门信息id
     */
    void deleteDefectTypeById(Integer id);

    /**
     * 缺陷责任部门信息分页查询
     * @param defectTypeQueryDTO 缺陷责任部门信息分页查询条件
     * @return 分页查询结果列表
     */
    Page<DefectTypeDTO> getDefectTypePageQuery(DefectTypeQueryDTO defectTypeQueryDTO);

    /**
     * 获取所有缺陷责任部门
     * @return 缺陷责任部门信息列表
     */
    List<DefectTypeDTO> getAllDefectType();

    /**
     * 根据责任部门名称获取责任部门对象
     * @param name 名称
     * @return 责任部门对象
     */
    HvQmQualityDefectType getByName(String name);

    /**
     * 根据id获取责任部门信息
     * @param id 主键id
     * @return 责任部门信息
     */
    DefectTypeDTO getById(Integer id);
    DefectTypeDTO getDefectTypeByCode(String code);
}
