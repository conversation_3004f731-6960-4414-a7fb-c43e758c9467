package com.hvisions.qms.repository;

import com.hvisions.qms.entity.HvQmDefectInstance;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: CheckListRepository</p >
 * <p>Description: 质检单repository</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/15</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface InspectionDefectInstanceRepository extends JpaRepository<HvQmDefectInstance, Integer> {

    /**
     * 查询在线数据
     *
     * @param inspectionOrderId 质检单id
     * @return 查询在线数据
     */
    List<HvQmDefectInstance> findByInspectionId(int inspectionOrderId);


    /**
     * 根据位置点和区域查询在线数据
     *
     * @param pointId    根据位置点
     * @param positionId 区域
     * @return 在线数据HvQmTestDefectInstance
     */
    HvQmDefectInstance findByPointIdAndPositionId(int pointId, int positionId);


    /**
     * 查询 合格
     * 根据时间范围查询年份 在线检测
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(10),c.create_time,120) as checkCountTime,SUM(c.quantity) as checkAllNumber FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity =0 " +
            "GROUP BY convert(varchar(10),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerYears(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 合格
     * 根据时间范围查询月份 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(10),c.create_time,120) as checkCountTime ,SUM(c.quantity) as checkAllNumber FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity =0 " +
            "GROUP BY convert(varchar(10),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerMouths(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 合格
     * 根据时间范围查询 天数 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(10),c.create_time,120) as checkCountTime,SUM(c.quantity) as checkAllNumber FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity =0 " +
            "GROUP BY convert(varchar(10),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerDays(@Param("startTime") Date startTime, @Param("endTime") Date endTime);


    /**
     * 查询 不合格
     * 根据时间范围查询年份 在线检测
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(4),c.create_time,120) as checkCountTime,SUM(c.quantity) as checkAllNumber FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >0 " +
            "GROUP BY convert(varchar(4),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerYearsNo(@Param("startTime") Date startTime, @Param("endTime") Date endTime);


    /**
     * 查询 不合格
     * 根据时间范围查询年份 在线检测
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(4),c.create_time,120) as checkCountTime,SUM(c.quantity) as checkAllNumber FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >=0 " +
            "GROUP BY convert(varchar(4),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerYearsAll(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 不合格
     * 根据时间范围查询月份 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(7),c.create_time,120) as checkCountTime,SUM(c.quantity) as checkAllNumber FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >0 " +
            "GROUP BY convert(varchar(7),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerMouthsNo(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 所有
     * 根据时间范围查询月份 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(7),c.create_time,120) as checkCountTime,SUM(c.quantity) as checkAllNumber FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >=0 " +
            "GROUP BY convert(varchar(7),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerMouthsAll(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 不合格
     * 根据时间范围查询 天数 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(10),c.create_time,120) as checkCountTime,SUM(c.quantity) as checkAllNumber FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >0 " +
            "GROUP BY convert(varchar(10),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerDaysNo(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 所有
     * 根据时间范围查询 天数 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT COUNT(c.id) as checkCountNum ,convert(varchar(10),c.create_time,120) as checkCountTime,SUM(c.quantity) as checkAllNumber FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >=0 " +
            "GROUP BY convert(varchar(10),c.create_time,120)", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerDaysAll(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 不合格缺陷 统计排行
     * 根据时间范围查询 天数 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT c.defect_name as defectName, COUNT(c.id) as defectNumber ,convert(varchar(10),c.create_time,120) as checkCountTime FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >0 " +
            "GROUP BY convert(varchar(10),c.create_time,120),c.defect_name", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerOrderDays(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 不合格数量 统计排行
     * 根据时间范围查询 天数 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT c.defect_name as defectName, SUM(c.quantity) as defectNumber ,convert(varchar(10),c.create_time,120) as checkCountTime FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >0 " +
            "GROUP BY convert(varchar(10),c.create_time,120),c.defect_name", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerALLNumOrderDays(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 不合格缺陷 统计排行
     * 根据时间范围查询 天数 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT c.defect_name as defectName, COUNT(c.id) as defectNumber ,convert(varchar(7),c.create_time,120) as checkCountTime FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >0 " +
            "GROUP BY convert(varchar(7),c.create_time,120),c.defect_name", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerOrderMonths(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 不合格数量 统计排行
     * 根据时间范围查询 月数 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT c.defect_name as defectName, SUM(c.quantity) as defectNumber ,convert(varchar(7),c.create_time,120) as checkCountTime FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >0 " +
            "GROUP BY convert(varchar(7),c.create_time,120),c.defect_name", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerALLNumOrderMonths(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 不合格缺陷 统计排行
     * 根据时间范围查询 年数 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT c.defect_name as defectName, COUNT(c.id) as defectNumber ,convert(varchar(4),c.create_time,120) as checkCountTime FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >0 " +
            "GROUP BY convert(varchar(4),c.create_time,120),c.defect_name", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerOrderYears(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 不合格数量 统计排行
     * 根据时间范围查询 年数 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT c.defect_name as defectName, SUM(c.quantity) as defectNumber ,convert(varchar(4),c.create_time,120) as checkCountTime FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >0 " +
            "GROUP BY convert(varchar(4),c.create_time,120),c.defect_name", nativeQuery = true)
    List<Map<String, Object>> findCountBySqlServerALLNumOrderYears(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 合格
     * 根据时间范围查询年份 在线检测
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT COUNT(c.id) as checkCountNum ,substring(c.create_time,1,4) as checkCountTime,SUM(c.quantity) as checkAllNumber FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity =0 " +
            "GROUP BY substring(c.create_time,1,4)", nativeQuery = true)
    List<Map<String, Object>> findCountByMysqlYears(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 合格
     * 根据时间范围查询月份 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT COUNT(c.id) as checkCountNum ,substring(c.create_time,1,7) as checkCountTime ,SUM(c.quantity) as checkAllNumber FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity =0 " +
            "GROUP BY substring(c.create_time,1,7)", nativeQuery = true)
    List<Map<String, Object>> findCountByMysqlMouths(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 合格
     * 根据时间范围查询 天数 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT COUNT(c.id) as checkCountNum ,substring(c.create_time,1,10) as checkCountTime,SUM(c.quantity) as checkAllNumber FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity =0 " +
            "GROUP BY substring(c.create_time,1,10)", nativeQuery = true)
    List<Map<String, Object>> findCountByMysqlDays(@Param("startTime") Date startTime, @Param("endTime") Date endTime);


    /**
     * 查询 不合格
     * 根据时间范围查询年份 在线检测
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT COUNT(c.id) as checkCountNum ,substring(c.create_time,1,4) as checkCountTime,SUM(c.quantity) as checkAllNumber FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >0 " +
            "GROUP BY substring(c.create_time,1,4)", nativeQuery = true)
    List<Map<String, Object>> findCountByMysqlYearsNo(@Param("startTime") Date startTime, @Param("endTime") Date endTime);


    /**
     * 查询 不合格
     * 根据时间范围查询年份 在线检测
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT COUNT(c.id) as checkCountNum ,substring(c.create_time,1,4) as checkCountTime,SUM(c.quantity) as checkAllNumber FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >=0 " +
            "GROUP BY substring(c.create_time,1,4)", nativeQuery = true)
    List<Map<String, Object>> findCountByMysqlYearsAll(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 不合格
     * 根据时间范围查询月份 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT COUNT(c.id) as checkCountNum ,substring(c.create_time,1,7) as checkCountTime,SUM(c.quantity) as checkAllNumber FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >0 " +
            "GROUP BY substring(c.create_time,1,7)", nativeQuery = true)
    List<Map<String, Object>> findCountByMysqlMouthsNo(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 所有
     * 根据时间范围查询月份 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT COUNT(c.id) as checkCountNum ,substring(c.create_time,1,7) as checkCountTime,SUM(c.quantity) as checkAllNumber FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >=0 " +
            "GROUP BY substring(c.create_time,1,7)", nativeQuery = true)
    List<Map<String, Object>> findCountByMysqlMouthsAll(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 不合格
     * 根据时间范围查询 天数 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT COUNT(c.id) as checkCountNum ,substring(c.create_time,1,10) as checkCountTime,SUM(c.quantity) as checkAllNumber FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >0 " +
            "GROUP BY substring(c.create_time,1,10)", nativeQuery = true)
    List<Map<String, Object>> findCountByMysqlDaysNo(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 所有
     * 根据时间范围查询 天数 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT COUNT(c.id) as checkCountNum ,substring(c.create_time,1,10) as checkCountTime,SUM(c.quantity) as checkAllNumber FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >=0 " +
            "GROUP BY substring(c.create_time,1,10)", nativeQuery = true)
    List<Map<String, Object>> findCountByMysqlDaysAll(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 不合格缺陷 统计排行
     * 根据时间范围查询 天数 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT c.defect_name as defectName, COUNT(c.id) as defectNumber ,substring(c.create_time,1,10) as checkCountTime FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >0 " +
            "GROUP BY substring(c.create_time,1,10),c.defect_name", nativeQuery = true)
    List<Map<String, Object>> findCountByMysqlOrderDays(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 不合格数量 统计排行
     * 根据时间范围查询 天数 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT c.defect_name as defectName, SUM(c.quantity) as defectNumber ,substring(c.create_time,1,10) as checkCountTime FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >0 " +
            "GROUP BY substring(c.create_time,1,10),c.defect_name", nativeQuery = true)
    List<Map<String, Object>> findCountByMysqlALLNumOrderDays(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 不合格缺陷 统计排行
     * 根据时间范围查询 天数 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT c.defect_name as defectName, COUNT(c.id) as defectNumber ,substring(c.create_time,1,7) as checkCountTime FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >0 " +
            "GROUP BY substring(c.create_time,1,7),c.defect_name", nativeQuery = true)
    List<Map<String, Object>> findCountByMysqlOrderMonths(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 不合格数量 统计排行
     * 根据时间范围查询 月数 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT c.defect_name as defectName, SUM(c.quantity) as defectNumber ,substring(c.create_time,1,7) as checkCountTime FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >0 " +
            "GROUP BY substring(c.create_time,1,7),c.defect_name", nativeQuery = true)
    List<Map<String, Object>> findCountByMysqlALLNumOrderMonths(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 不合格缺陷 统计排行
     * 根据时间范围查询 年数 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT c.defect_name as defectName, COUNT(c.id) as defectNumber ,substring(c.create_time,1,4) as checkCountTime FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >0 " +
            "GROUP BY substring(c.create_time,1,4),c.defect_name", nativeQuery = true)
    List<Map<String, Object>> findCountByMysqlOrderYears(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询 不合格数量 统计排行
     * 根据时间范围查询 年数 在线检测缺陷
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    @Query(value = "" +
            "SELECT c.defect_name as defectName, SUM(c.quantity) as defectNumber ,substring(c.create_time,1,4) as checkCountTime FROM hv_qm_defect_instance as c " +
            "where c.create_time between :startTime and :endTime " +
            "and c.quantity >0 " +
            "GROUP BY substring(c.create_time,1,4),c.defect_name", nativeQuery = true)
    List<Map<String, Object>> findCountByMysqlALLNumOrderYears(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

}
