package com.hvisions.qms.controller;

import com.hvisions.qms.dto.step.InspectionStandardsStepDTO;
import com.hvisions.qms.dto.step.QueryStandardsStepDTO;
import com.hvisions.qms.service.InspectionStandardsStepService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: StandardsStepController</p >
 * <p>Description: 检测工位与质检标准控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/10/9</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/standardsStep")
@Slf4j
@Api(description = "检测工位与质检标准控制器")
public class StandardsStepController {
    private final InspectionStandardsStepService inspectionStandardsStep;

    @Autowired
    public StandardsStepController(InspectionStandardsStepService inspectionStandardsStep) {
        this.inspectionStandardsStep = inspectionStandardsStep;
    }

    /**
     * 操作绑定质检标准
     *
     * @param stepId     工位id
     * @param stepName   工位名称
     * @param standardId 质检标准id
     * @return 是否成功
     */
    @ApiOperation(value = "操作绑定质检标准")
    @PutMapping(value = "/bindingInspectionStandards/{stepId}/{stepName}/{standardId}")
    public int bindingInspectionStandards(@PathVariable Integer stepId, @PathVariable String stepName, @PathVariable Integer standardId) {
        return inspectionStandardsStep.addingInspectionStandards(stepId, stepName, standardId);
    }

    /**
     * 操作解绑质检标准
     *
     * @param stepId     工位id
     * @param standardId 质检标准id
     */
    @ApiOperation(value = "操作解绑质检标准")
    @DeleteMapping(value = "/unbindingInspectionStandards/{stepId}/{standardId}")
    public void unbindingInspectionStandards(@PathVariable Integer stepId, @PathVariable Integer standardId) {
        inspectionStandardsStep.unbindingInspectionStandards(stepId, standardId);
    }


    /**
     * 获取已绑定质检标准的工位
     *
     * @param stepDTO 工位查询DTO
     * @return 质检标准信息
     */
    @ApiOperation(value = "获取已绑定质检标准的工位")
    @PostMapping(value = "/getBindStandardStepList")
    public List<InspectionStandardsStepDTO> getBindStandardStepList(@RequestBody QueryStandardsStepDTO stepDTO) {
       return inspectionStandardsStep.getBindStandardStepList(stepDTO);
    }


}

