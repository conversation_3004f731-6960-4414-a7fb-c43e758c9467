package com.hvisions.qms.service;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.service.BaseService;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.DefectBindOperationDTO;
import com.hvisions.qms.entity.HvQmQualityDefectRouteOperation;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.util.List;

public interface DefectRouteStepService  extends BaseService<HvQmQualityDefectRouteOperation,Integer> {

    /**
     * 新增缺陷-工艺步骤数据
     * @param defectId 缺陷id
     * @param routeStepIds 工艺步骤id列表
     */
    void saveByDefectIdAndOperationIds(Integer defectId, List<Integer> routeStepIds);



    /**
     * 删除缺陷id对应的缺陷-工艺步骤数据
     * @param defectId 缺陷id
     */
    void deleteByDefectId(Integer defectId);




    /**
     * 保存或更新缺陷工艺绑定数据
     * @param id 缺陷id
     * @param routeStepIds 工艺步骤id列表
     */
    void saveOrUpdateRouteBind(Integer id, List<Integer> routeStepIds);

    /**
     *根据缺陷id获取工艺操作id列表
     * @param defectId 缺陷id
     * @return 缺陷id对应的工艺操作id列表
     */
    List<Integer> getRouteOperationIdsByDefectId(Integer defectId);


    void bindOperation(DefectBindOperationDTO defectDTO);

    ResponseEntity<byte[]> export() throws IOException, IllegalAccessException;


    ResultVO<ExcelExportDto> exportDefect() throws IOException, IllegalAccessException;
}
