package com.hvisions.qms.repository;

import com.hvisions.qms.entity.HvQmParameterValue;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: 参数值持久层</p >
 * <p>Description: 参数值</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/15</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface InspectionParameterValueRepository extends JpaRepository<HvQmParameterValue, Integer> {
    /**
     * 根据质检单id查询所有的参数值列表
     *
     * @param orderId 质检单id
     * @return 参数值列表
     */
    List<HvQmParameterValue> findAllByOrderId(Integer orderId);

}
