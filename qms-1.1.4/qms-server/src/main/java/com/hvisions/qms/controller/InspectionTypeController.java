package com.hvisions.qms.controller;

import com.hvisions.qms.dto.type.InspectionOrderTypeDTO;
import com.hvisions.qms.dto.type.QueryInspectionTypeDTO;
import com.hvisions.qms.service.InspectionOrderTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: InspectionOrderTypeController</p >
 * <p>Description: 质检单类型管理控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/10/8</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/InspectionOrderType")
@Slf4j
@Api(description = "质检单类型控制器")
public class InspectionTypeController {

    private final InspectionOrderTypeService inspectionOrderType;

    @Autowired
    public InspectionTypeController(InspectionOrderTypeService inspectionOrderType) {
        this.inspectionOrderType = inspectionOrderType;
    }

    /**
     * 新增质检单类型
     *
     * @param inspectionOrderTypeDTO 质检单类型
     * @return 质检单类型
     */
    @ApiOperation(value = " 新增质检单类型")
    @PostMapping(value = "/addInspectionOrderType")
    public int addInspectionOrderType(@RequestBody InspectionOrderTypeDTO inspectionOrderTypeDTO) {
        return inspectionOrderType.addInspectionOrderType(inspectionOrderTypeDTO);
    }

    /**
     * 删除质检单类型、根据Id
     *
     * @param id 根据Id
     */
    @ApiOperation(value = " 删除质检单类型、根据Id")
    @DeleteMapping(value = "/removeInspectionOrderTypeById/{id}")
    public void removeInspectionOrderTypeById(@PathVariable Integer id) {
        inspectionOrderType.removeInspectionOrderTypeById(id);
    }

    /**
     * 批量删除质检单类型、根据Id
     *
     * @param ids Id列表
     */
    @ApiOperation(value = " 批量删除质检单类型、根据Id")
    @DeleteMapping(value = "/removeBatchInspectionOrderTypeByIds")
    public void removeBatchInspectionOrderTypeByIds(@RequestBody List<Integer> ids) {
        inspectionOrderType.removeBatchInspectionOrderTypeByIds(ids);
    }

    /**
     * 更新质检单类型
     *
     * @param inspectionOrderTypeDTO 质检单类型
     * @return 主键
     */
    @ApiOperation(value = "更新质检单类型")
    @PutMapping(value = "/updateInspectionOrderType")
    public int updateInspectionOrderType(@RequestBody InspectionOrderTypeDTO inspectionOrderTypeDTO) {
        return inspectionOrderType.updateInspectionOrderType(inspectionOrderTypeDTO);
    }


    /**
     * 根据质检单类型id、获取质检单类型
     *
     * @param id 标准类型id
     * @return 质检单类型
     */
    @ApiOperation(value = "根据质检单类型id、获取质检单类型")
    @GetMapping(value = "/findInspectionOrderTypeById/{id}")
    public InspectionOrderTypeDTO findInspectionOrderTypeById(@PathVariable Integer id) {
        return inspectionOrderType.findInspectionOrderTypeById(id);
    }

    /**
     * 获取质检单类型、根据Id列表
     *
     * @param ids Id列表
     * @return 质检单类型
     */
    @ApiOperation(value = "获取质检单类型、根据Id列表")
    @PostMapping(value = "/findInspectionOrderTypeByIds")
    public List<InspectionOrderTypeDTO> findInspectionOrderTypeByIds(@RequestBody List<Integer> ids) {
        return inspectionOrderType.findInspectionOrderTypeByIds(ids);
    }




    /**
     * 多条件分页获取质检单类型
     *
     * @param queryOrderTypeDTO 查询条件
     * @return 质检单类型
     */
    @ApiOperation(value = "多条件分页获取质检单类型")
    @PostMapping(value = "/getInspectionOrderType")
    public Page<InspectionOrderTypeDTO> getInspectionOrderType(@RequestBody QueryInspectionTypeDTO queryOrderTypeDTO) {
        return inspectionOrderType.getInspectionOrderType(queryOrderTypeDTO);
    }

}