package com.hvisions.qms.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.knowledge.info.InspectionKnowledgeParametersDTO;
import com.hvisions.qms.service.InspectionKnowledgeParametersService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <p>Title: StandardsParametersController</p >
 * <p>Description: 质量参数知识库控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/10/9</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/knowledgeParameters")
@Slf4j
@Api(description = "质检标准检测项参数控制器")
public class KnowledgeBaseParametersController {

    private final InspectionKnowledgeParametersService inspectionStandardsParameters;

    @Autowired
    public KnowledgeBaseParametersController(InspectionKnowledgeParametersService inspectionStandardsParameters) {
        this.inspectionStandardsParameters = inspectionStandardsParameters;
    }

    /**
     * 新增参数知识库
     *
     * @param InspectionKnowledgeParametersDTO 参数知识库
     * @return 主键
     */
    @ApiOperation(value = "新增参数知识库")
    @PostMapping(value = "/createKnowledgeBaseParameters")
    public int createStandardsParameter(@RequestBody InspectionKnowledgeParametersDTO InspectionKnowledgeParametersDTO) {
        return inspectionStandardsParameters.addStandardsParameter(InspectionKnowledgeParametersDTO);
    }

    /**
     * 批量新增参数知识库
     *
     * @param InspectionKnowledgeParametersDTOS 参数知识库
     */
    @ApiOperation(value = "批量新增参数知识库")
    @PostMapping(value = "/createBatchKnowledgeBaseParameters")
    public void createBatchStandardsParameter(@RequestBody List<InspectionKnowledgeParametersDTO> InspectionKnowledgeParametersDTOS) {
        inspectionStandardsParameters.addBatchStandardsParameter(InspectionKnowledgeParametersDTOS);
    }


    /**
     * 删除参数知识库、根据Id
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除参数知识库、根据Id")
    @DeleteMapping(value = "/deleteKnowledgeBaseParametersById/{id}")
    public void deleteStandardsParametersById(@PathVariable Integer id) {
        inspectionStandardsParameters.removeStandardsParametersById(id);
    }

    /**
     * 批量删除参数知识库、根据Id列表
     *
     * @param ids id列表
     */
    @ApiOperation(value = " 批量删除参数知识库、根据Id列表")
    @DeleteMapping(value = "/deleteKnowledgeBaseParametersByIds")
    public void deleteStandardsParametersByIds(@RequestBody List<Integer> ids) {
        inspectionStandardsParameters.removeStandardsParametersByIds(ids);
    }

    /**
     * 更新参数知识库
     *
     * @param InspectionKnowledgeParametersDTO 参数知识库
     * @return 参数知识库
     */
    @ApiOperation(value = "更新参数知识库")
    @PutMapping(value = "/editKnowledgeBaseParameters")
    public int editStandardsParameters(@RequestBody InspectionKnowledgeParametersDTO InspectionKnowledgeParametersDTO) {
        return inspectionStandardsParameters.updateStandardsParameters(InspectionKnowledgeParametersDTO);
    }

    /**
     * 获取参数知识库、根据参数知识库id
     *
     * @param id 参数知识库id
     * @return 参数知识库
     */
    @ApiOperation(value = " 获取参数知识库、根据参数知识库id")
    @GetMapping(value = "/getKnowledgeBaseParametersByStandardById/{id}")
    public InspectionKnowledgeParametersDTO getStandardsParametersByStandardById(@PathVariable Integer id) {
        return inspectionStandardsParameters.findStandardsParametersByStandardById(id);
    }

    /**
     * 获取参数知识库、根据参数知识库Id列表
     *
     * @param ids 获取参数知识库、根据参数知识库Id列表
     * @return 参数知识库Id列表
     */
    @ApiOperation(value = "获取参数知识库、根据参数知识库Id列表")
    @PostMapping(value = "/getKnowledgeBaseParametersByStandardByIds")
    public List<InspectionKnowledgeParametersDTO> getStandardsParametersByStandardByIds(@RequestBody List<Integer> ids) {
        return inspectionStandardsParameters.findStandardsParametersByStandardByIds(ids);
    }

    /**
     * 获取参数知识库、根据编码列表
     *
     * @param codes 编码列表
     * @return 参数知识库
     */
    @ApiOperation(value = "获取参数知识库、根据编码列表")
    @PostMapping(value = "/getKnowledgeBaseParametersByStandardByCodes")
    public List<InspectionKnowledgeParametersDTO> getStandardsParametersByStandardByCodes(@RequestBody List<String> codes) {
        return inspectionStandardsParameters.findStandardsParametersByStandardByCodes(codes);
    }

    /**
     * 获取参数知识库、根据编码列表
     *
     * @param standardId 质检标准id
     * @return 参数知识库
     */
    @ApiOperation(value = "获取参数知识库、根据质检标准id")
    @GetMapping(value = "/getKnowledgeBaseParametersByStandardId/{standardId}")
    public List<InspectionKnowledgeParametersDTO> getStandardsParametersByStandardId(@PathVariable int standardId) {
        return inspectionStandardsParameters.getStandardsParametersByStandardId(standardId);
    }
    /**
     * 导入参数信息
     *
     * @param file 参数文档
     * @param inspectionKnowledgeBaseId 质检标准ID
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @PostMapping(value = "/importParameterLink")
    @ApiOperation(value = "导入参数信息(参数编码存在则更新，不存在则新增)")
    public ImportResult importParameter(@RequestParam("file") MultipartFile file, @RequestParam("inspectionKnowledgeBaseId") Integer inspectionKnowledgeBaseId) throws IllegalAccessException, ParseException, IOException {
        return inspectionStandardsParameters.importParameter(file,inspectionKnowledgeBaseId);
    }
    /**
     * 质检参数知识库信息
     * @param standardId 质检标准ID
     * @return 参数信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportParameter/{standardId}")
    @ApiOperation(value = "参数、二进制数据导出")
    public ResultVO<ExcelExportDto> exportParameter(@PathVariable Integer standardId) throws IOException, IllegalAccessException {
        return inspectionStandardsParameters.exportParameter(standardId);
    }

    /**
     * 质检参数知识库信息
     * @param standardId 质检标准ID
     * @return 参数信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportParameterLink/{standardId}")
    @ApiOperation(value = "参数、超链接导出")
    public ResponseEntity<byte[]> exportParameterLink(@PathVariable Integer standardId) throws IOException, IllegalAccessException {
        return inspectionStandardsParameters.exportParameterLink(standardId);
    }
}