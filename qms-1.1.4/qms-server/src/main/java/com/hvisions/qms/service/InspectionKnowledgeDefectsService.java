package com.hvisions.qms.service;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.EntitySaver;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.knowledge.info.ExportKnowledgeDefectsDTO;
import com.hvisions.qms.dto.knowledge.info.InspectionKnowledgeDefectsDTO;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <p>Title: InspectionKnowledgeBaseDefectsService</p >
 * <p>Description: 质检标准缺陷</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/13</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface InspectionKnowledgeDefectsService extends EntitySaver<ExportKnowledgeDefectsDTO> {

    /**
     * 新增标准缺陷
     *
     * @param InspectionKnowledgeDefectsDTO 标准缺陷
     * @return 主键
     */
    int addKnowledgeBaseDefects(InspectionKnowledgeDefectsDTO InspectionKnowledgeDefectsDTO);

    /**
     * 批量新增标准缺陷
     *
     * @param InspectionKnowledgeDefectsDTO 标准缺陷列表
     */
    void addBatchKnowledgeBaseDefects(List<InspectionKnowledgeDefectsDTO> InspectionKnowledgeDefectsDTO);

    /**
     * 更新标准缺陷
     *
     * @param InspectionKnowledgeDefectsDTO 标准缺陷
     * @return 主键
     */
    int updateKnowledgeBaseDefects(InspectionKnowledgeDefectsDTO InspectionKnowledgeDefectsDTO);

    /**
     * 删除标准缺陷、根据Id
     *
     * @param id 标准缺陷Id
     */
    void removeKnowledgeBaseDefectsById(Integer id);

    /**
     * 批量删除标准缺陷、根据Id
     *
     * @param ids Id列表
     */
    void deleteBatchKnowledgeBaseDefectsByIds(List<Integer> ids);

    /**
     * 根据标准缺陷id获取获取标准缺陷
     *
     * @param id 标准缺陷id
     * @return 标准缺陷
     */
    InspectionKnowledgeDefectsDTO findKnowledgeBaseDefectsByStandardById(Integer id);

    /**
     * 获取标准缺陷、根据Id列表
     *
     * @param ids 标准缺陷id列表
     * @return 标准缺陷
     */
    List<InspectionKnowledgeDefectsDTO> findKnowledgeBaseDefectsByStandardByIds(List<Integer> ids);
    /**文件导出
     * @param standardId 质检标准ID
     * @return 导出文件
     * @throws IOException IO异常
     * @throws IllegalAccessException 非法访问异常
     */
    ResultVO<ExcelExportDto> exportDefect(Integer standardId)   throws IOException,IllegalAccessException;
    /**文件导出
     * @param standardId 质检标准ID
     * @return 导出二进制
     * @throws IOException IO异常
     * @throws IllegalAccessException 非法访问异常
     */
    ResponseEntity<byte[]> exportDefectLink(Integer standardId)  throws IOException,IllegalAccessException;

    /**
     * 文件导入
     * @param file 文件信息
     * @param id 质检标准ID
     * @return 文件录入显示
     * @throws IllegalAccessException 非法访问异常
     * @throws ParseException 数据格式转换异常
     * @throws IOException IO异常
     */
    ImportResult importDefect(MultipartFile file, Integer id)  throws IllegalAccessException, ParseException, IOException;

}
