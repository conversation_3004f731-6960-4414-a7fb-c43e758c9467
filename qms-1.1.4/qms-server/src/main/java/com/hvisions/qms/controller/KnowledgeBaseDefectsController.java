package com.hvisions.qms.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.knowledge.info.InspectionKnowledgeDefectsDTO;
import com.hvisions.qms.service.InspectionKnowledgeDefectsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * <p>Title: KnowledgeBaseDefectsController</p >
 * <p>Description: 质量缺陷知识库控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/10/9</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/knowledgeDefects")
@Slf4j
@Api(description = "知识库检测项缺陷控制器")
public class KnowledgeBaseDefectsController {

    private final InspectionKnowledgeDefectsService inspectionKnowledgeBaseDefects;

    @Autowired
    public KnowledgeBaseDefectsController(InspectionKnowledgeDefectsService inspectionKnowledgeBaseDefects) {
        this.inspectionKnowledgeBaseDefects = inspectionKnowledgeBaseDefects;
    }


    /**
     * 新增缺陷知识库
     *
     * @param InspectionKnowledgeDefectsDTO 缺陷知识库
     * @return 主键
     */
    @ApiOperation(value = "新增缺陷知识库")
    @PostMapping(value = "/createBatchKnowledgeBaseDefect")
    public int createKnowledgeBaseParameter(@RequestBody InspectionKnowledgeDefectsDTO InspectionKnowledgeDefectsDTO) {
        return inspectionKnowledgeBaseDefects.addKnowledgeBaseDefects(InspectionKnowledgeDefectsDTO);
    }

    /**
     * 批量新增缺陷知识库
     *
     * @param InspectionKnowledgeDefectsDTO 缺陷知识库列表
     */
    @ApiOperation(value = "批量新增缺陷知识库")
    @PostMapping(value = "/createBatchKnowledgeBaseDefects")
    public void createBatchKnowledgeBaseParameter(@RequestBody List<InspectionKnowledgeDefectsDTO> InspectionKnowledgeDefectsDTO) {
        inspectionKnowledgeBaseDefects.addBatchKnowledgeBaseDefects(InspectionKnowledgeDefectsDTO);
    }

    /**
     * 获取缺陷知识库、根据Id列表
     *
     * @param ids 缺陷知识库id列表
     * @return 缺陷知识库
     */
    @ApiOperation(value = "获取缺陷知识库、根据Id列表")
    @PostMapping(value = "/getKnowledgeBaseDefectsByStandardByIds")
    public List<InspectionKnowledgeDefectsDTO> getKnowledgeBaseDefectsByStandardByIds(@RequestBody List<Integer> ids) {
        return inspectionKnowledgeBaseDefects.findKnowledgeBaseDefectsByStandardByIds(ids);
    }


    /**
     * 根据缺陷知识库id获取获取缺陷知识库
     *
     * @param id 缺陷知识库id
     * @return 缺陷知识库
     */
    @ApiOperation(value = " 获取缺陷知识库、根据缺陷知识库id")
    @GetMapping(value = "/getKnowledgeBaseDefectsByStandardById/{id}")
    public InspectionKnowledgeDefectsDTO getKnowledgeBaseDefectsByStandardById(@PathVariable Integer id) {
        return inspectionKnowledgeBaseDefects.findKnowledgeBaseDefectsByStandardById(id);
    }

    /**
     * 更新缺陷知识库
     *
     * @param InspectionKnowledgeDefectsDTO 缺陷知识库
     * @return 主键
     */
    @ApiOperation(value = "更新缺陷知识库")
    @PutMapping(value = "/editKnowledgeBaseDefects")
    public int editKnowledgeBaseDefects(@RequestBody InspectionKnowledgeDefectsDTO InspectionKnowledgeDefectsDTO) {
        return inspectionKnowledgeBaseDefects.updateKnowledgeBaseDefects(InspectionKnowledgeDefectsDTO);
    }

    /**
     * 删除缺陷知识库、根据Id
     *
     * @param id 缺陷知识库Id
     */
    @ApiOperation(value = "删除缺陷知识库、根据Id")
    @DeleteMapping(value = "/deleteKnowledgeBaseDefectsById/{id}")
    public void deleteKnowledgeBaseDefectsById(@PathVariable Integer id) {
        inspectionKnowledgeBaseDefects.removeKnowledgeBaseDefectsById(id);
    }

    /**
     * 批量删除缺陷知识库、根据Id
     *
     * @param ids Id列表
     */
    @ApiOperation(value = " 批量删除缺陷知识库、根据Id")
    @DeleteMapping(value = "/deleteBatchKnowledgeBaseDefectsByIds")
    public void deleteBatchKnowledgeBaseDefectsByIds(@RequestBody List<Integer> ids) {
        inspectionKnowledgeBaseDefects.deleteBatchKnowledgeBaseDefectsByIds(ids);
    }
    /**
     * 导入缺陷信息
     *
     * @param file 缺陷文档
     * @param inspectionKnowledgeBaseId 知识库ID
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @PostMapping(value = "/importDefectLink")
    @ApiOperation(value = "导入缺陷信息(缺陷编码存在则更新，不存在则新增)")
    public ImportResult importDefectLink(@RequestParam("file") MultipartFile file, @RequestParam("inspectionKnowledgeBaseId") Integer inspectionKnowledgeBaseId) throws IllegalAccessException, ParseException, IOException {
        return inspectionKnowledgeBaseDefects.importDefect(file,inspectionKnowledgeBaseId);
    }
    /**
     * 质检缺陷知识库信息
     * @param inspectionKnowledgeBaseId 知识库ID
     * @return 缺陷信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportDefect/{inspectionKnowledgeBaseId}")
    @ApiOperation(value = "缺陷、二进制数据导出")
    public ResultVO<ExcelExportDto> exportDefect(@PathVariable Integer inspectionKnowledgeBaseId) throws IOException, IllegalAccessException {
        return inspectionKnowledgeBaseDefects.exportDefect(inspectionKnowledgeBaseId);
    }

    /**
     * 质检缺陷知识库信息
     * @param inspectionKnowledgeBaseId 知识库ID
     * @return 缺陷信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportDefectLink/{inspectionKnowledgeBaseId}")
    @ApiOperation(value = "缺陷、超链接导出")
    public ResponseEntity<byte[]> exportDefectLink(@PathVariable Integer inspectionKnowledgeBaseId) throws IOException, IllegalAccessException {
        return inspectionKnowledgeBaseDefects.exportDefectLink(inspectionKnowledgeBaseId);
    }
}