package com.hvisions.qms.service.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.qms.dto.type.InspectionOrderTypeDTO;
import com.hvisions.qms.dto.type.QueryInspectionTypeDTO;
import com.hvisions.qms.entity.HvQmInspectionOrderType;
import com.hvisions.qms.enums.InspectionExceptionEnum;
import com.hvisions.qms.repository.InspectionOrderRepository;
import com.hvisions.qms.repository.InspectionOrderTypeRepository;
import com.hvisions.qms.service.InspectionOrderTypeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title: InspectionInspectionOrderTypeServiceImpl</p >
 * <p>Description: 质量标准检测类型实现类</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/10/10</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Slf4j
@Service
public class InspectionOrderTypeServiceImpl implements InspectionOrderTypeService {
    @Autowired
    InspectionOrderTypeRepository typeRepository;
    @Autowired
    InspectionOrderRepository inspectionOrderRepository;


    /**
     * {@inheritDoc}
     */
    @Override
    public int addInspectionOrderType(InspectionOrderTypeDTO inspectionInspectionOrderTypeDTO) {
        log.debug(inspectionInspectionOrderTypeDTO.toString());
        return typeRepository.save(DtoMapper.convert(inspectionInspectionOrderTypeDTO, HvQmInspectionOrderType.class)).getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int updateInspectionOrderType(InspectionOrderTypeDTO inspectionInspectionOrderTypeDTO) {
        return addInspectionOrderType(inspectionInspectionOrderTypeDTO);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void removeInspectionOrderTypeById(Integer id) {
        if (inspectionOrderRepository.findByInspectionOrderTypeId(id).size()>0) {
            throw new BaseKnownException(InspectionExceptionEnum.TYPE_IN_USE);
        }
        typeRepository.deleteById(id);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void removeBatchInspectionOrderTypeByIds(List<Integer> ids) {
        for (Integer id : ids) {
            removeInspectionOrderTypeById(id);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public InspectionOrderTypeDTO findInspectionOrderTypeById(Integer id) {
        Optional<HvQmInspectionOrderType> hvQmInspectionInspectionOrderType = typeRepository.findById(id);
        return hvQmInspectionInspectionOrderType.map(qmInspectionInspectionOrderType -> DtoMapper.convert(qmInspectionInspectionOrderType, InspectionOrderTypeDTO.class)).orElse(null);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<InspectionOrderTypeDTO> findInspectionOrderTypeByIds(List<Integer> ids) {
        List<InspectionOrderTypeDTO> InspectionOrderTypes = new ArrayList<>();
        for (Integer id : ids) {
            InspectionOrderTypes.add(findInspectionOrderTypeById(id));
        }
        return InspectionOrderTypes;
    }



    /**
     * {@inheritDoc}
     */
    @Override
    public Page<InspectionOrderTypeDTO> getInspectionOrderType(QueryInspectionTypeDTO typeDTO) {
        String InspectionOrderTypeCode = typeDTO.getInspectionOrderTypeCode();
        String InspectionOrderTypeName = typeDTO.getInspectionOrderTypeName();

        Specification<HvQmInspectionOrderType> specification = (Specification<HvQmInspectionOrderType>) (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            if (StringUtils.isNotBlank(InspectionOrderTypeCode)) {
                list.add(criteriaBuilder.like(root.get("InspectionOrderTypeCode").as(String.class), "%" + InspectionOrderTypeCode + "%"));
            }
            if (StringUtils.isNotBlank(InspectionOrderTypeName)) {
                list.add(criteriaBuilder.like(root.get("InspectionOrderTypeName").as(String.class), "%" + InspectionOrderTypeName + "%"));
            }
            Predicate[] p = list.toArray(new Predicate[0]);
            return criteriaBuilder.and(p);
        };

        return DtoMapper.convertPage(typeRepository.findAll(specification, typeDTO.getRequest()), InspectionOrderTypeDTO.class);

    }
}
