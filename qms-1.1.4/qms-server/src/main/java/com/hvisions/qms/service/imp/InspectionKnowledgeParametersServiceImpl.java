package com.hvisions.qms.service.imp;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.knowledge.info.ExportKnowledgeParametersDTO;
import com.hvisions.qms.dto.knowledge.info.InspectionKnowledgeParametersDTO;
import com.hvisions.qms.entity.HvQmInspectionKnowledgeBase;
import com.hvisions.qms.entity.HvQmInspectionKnowledgeParameters;
import com.hvisions.qms.enums.InspectionStandardExceptionEnum;
import com.hvisions.qms.repository.InspectionKnowledgeBaseRepository;
import com.hvisions.qms.repository.InspectionKnowledgeParametersRepository;
import com.hvisions.qms.service.InspectionKnowledgeParametersService;
import com.hvisions.qms.service.KnowledgeParameterConst;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>Title: InspectionStandardsParametersServiceImpl</p >
 * <p>Description: 质检标准参数实现类</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/10/10</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
public class InspectionKnowledgeParametersServiceImpl implements InspectionKnowledgeParametersService {
    private Integer inspectionKnowledgeBaseId;
    private final InspectionKnowledgeParametersRepository parameterRepository;
    private final InspectionKnowledgeBaseRepository knowledgeBaseRepository;


    @Autowired
    public InspectionKnowledgeParametersServiceImpl(InspectionKnowledgeParametersRepository parameterRepository, InspectionKnowledgeBaseRepository knowledgeBaseRepository) {
        this.parameterRepository = parameterRepository;
        this.knowledgeBaseRepository = knowledgeBaseRepository;

    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addStandardsParameter(InspectionKnowledgeParametersDTO InspectionKnowledgeParametersDTO) {
        Optional<HvQmInspectionKnowledgeBase> inspectionKnowledgeBase = knowledgeBaseRepository.findById(InspectionKnowledgeParametersDTO.getInspectionKnowledgeBaseId());

        //如果是新增，并且质检标准中已经存在相同的编码参数，则直接跳过
        if (null == InspectionKnowledgeParametersDTO.getId() &&
                inspectionKnowledgeBase.get().getHvQmInspectionKnowledgeParameters().stream().anyMatch(t -> t.getParameterCode().equals(InspectionKnowledgeParametersDTO.getParameterCode()))) {
            throw new BaseKnownException(InspectionStandardExceptionEnum.DUPLICATE_ENTRY_OF_DEFECT_CODE);
        }
        if (null != InspectionKnowledgeParametersDTO) {
            if (null != InspectionKnowledgeParametersDTO.getParameterMaxValue() && null != InspectionKnowledgeParametersDTO.getParameterMinValue()) {
                if (InspectionKnowledgeParametersDTO.getParameterMaxValue().compareTo(InspectionKnowledgeParametersDTO.getParameterMinValue()) == -1) {
                    throw new BaseKnownException(InspectionStandardExceptionEnum.MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM);
                }
            }
            if (null != InspectionKnowledgeParametersDTO.getControlMaxValue() && null != InspectionKnowledgeParametersDTO.getControlMinValue()) {
                if (InspectionKnowledgeParametersDTO.getControlMaxValue().compareTo(InspectionKnowledgeParametersDTO.getControlMinValue()) == -1) {
                    throw new BaseKnownException(InspectionStandardExceptionEnum.MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM);
                }
            }
            if (null != InspectionKnowledgeParametersDTO.getWarnMaxValue() && null != InspectionKnowledgeParametersDTO.getWarnMinValue()) {
                if (InspectionKnowledgeParametersDTO.getWarnMaxValue().compareTo(InspectionKnowledgeParametersDTO.getWarnMinValue()) == -1) {
                    throw new BaseKnownException(InspectionStandardExceptionEnum.MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM);
                }
            }
            if (null != InspectionKnowledgeParametersDTO.getFailMaxValue() && null != InspectionKnowledgeParametersDTO.getFailMinValue()) {
                if (InspectionKnowledgeParametersDTO.getFailMaxValue().compareTo(InspectionKnowledgeParametersDTO.getFailMinValue()) == -1) {
                    throw new BaseKnownException(InspectionStandardExceptionEnum.MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM);
                }
            }


        }
        HvQmInspectionKnowledgeParameters hvQmInspectionKnowledgeParameters = DtoMapper.convert(InspectionKnowledgeParametersDTO, HvQmInspectionKnowledgeParameters.class);
        hvQmInspectionKnowledgeParameters.setHvQmInspectionKnowledgeBase(inspectionKnowledgeBase.get());
        return parameterRepository.save(hvQmInspectionKnowledgeParameters).getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBatchStandardsParameter(List<InspectionKnowledgeParametersDTO> InspectionKnowledgeParametersDTOS) {
        List<String> parameterCodes = new ArrayList<>();
        for (InspectionKnowledgeParametersDTO InspectionKnowledgeParametersDTO : InspectionKnowledgeParametersDTOS) {
            //相同编码的参数只能添加一次
            if (parameterCodes.stream().anyMatch(t -> t.equals(InspectionKnowledgeParametersDTO.getParameterCode()))) {
                continue;
            }
            addStandardsParameter(InspectionKnowledgeParametersDTO);
            parameterCodes.add(InspectionKnowledgeParametersDTO.getParameterCode());
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int updateStandardsParameters(InspectionKnowledgeParametersDTO InspectionKnowledgeParametersDTO) {
        return addStandardsParameter(InspectionKnowledgeParametersDTO);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeStandardsParametersById(Integer id) {
        Optional<HvQmInspectionKnowledgeParameters> parameter = parameterRepository.findById(id);
        if (parameter.isPresent()) {
            parameterRepository.deleteById(id);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void removeStandardsParametersByIds(List<Integer> ids) {
        for (Integer id : ids) {
            removeStandardsParametersById(id);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public InspectionKnowledgeParametersDTO findStandardsParametersByStandardById(Integer id) {
        return parameterRepository.findById(id).map(parameters -> DtoMapper.convert(parameters, InspectionKnowledgeParametersDTO.class)).orElse(null);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<InspectionKnowledgeParametersDTO> findStandardsParametersByStandardByCodes(List<String> codes) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<InspectionKnowledgeParametersDTO> findStandardsParametersByStandardByIds(List<Integer> ids) {
        List<InspectionKnowledgeParametersDTO> parameters = new ArrayList<>();
        for (Integer id : ids) {
            parameters.add(findStandardsParametersByStandardById(id));
        }
        return parameters;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<InspectionKnowledgeParametersDTO> getStandardsParametersByStandardId(int standardId) {
        return null;
    }

    @Override
    public ImportResult importParameter(MultipartFile file, Integer id) throws IllegalAccessException, ParseException, IOException {
        inspectionKnowledgeBaseId = id;
        return ExcelUtil.importEntity(file, ExportKnowledgeParametersDTO.class, this);

    }

    @Override
    public void saveOrUpdate(ExportKnowledgeParametersDTO exportKnowledgeParametersDTO) {
        if (StringUtils.isBlank(exportKnowledgeParametersDTO.getParameterCode())) {
            throw new BaseKnownException(InspectionStandardExceptionEnum.PARAMETER_ENCODING_CANNOT_BE_NULL);
        }
        HvQmInspectionKnowledgeParameters inspectionKnowledgeParameters = DtoMapper.convert(exportKnowledgeParametersDTO, HvQmInspectionKnowledgeParameters.class);
        Optional<HvQmInspectionKnowledgeBase> inspectionKnowledgeBase = knowledgeBaseRepository.findById(inspectionKnowledgeBaseId);
        if (inspectionKnowledgeBase.isPresent()) {
            List<HvQmInspectionKnowledgeParameters> parameters = inspectionKnowledgeBase.get().getHvQmInspectionKnowledgeParameters();
            parameters.forEach(parameter -> {
                if (parameter.getParameterCode().equals(inspectionKnowledgeParameters.getParameterCode())) {
                    parameter.setParameterName(inspectionKnowledgeParameters.getParameterName());
                    parameter.setParameterStandardsValue(inspectionKnowledgeParameters.getParameterStandardsValue());
                    parameter.setParameterMaxValue(inspectionKnowledgeParameters.getParameterMaxValue());
                    parameter.setParameterMinValue(inspectionKnowledgeParameters.getParameterMinValue());
                    parameter.setDataType(inspectionKnowledgeParameters.getDataType());
                    parameter.setParameterUnit(inspectionKnowledgeParameters.getParameterUnit());
                    parameter.setParameterDescription(inspectionKnowledgeParameters.getParameterDescription());
                    parameter.setFailMinValue(inspectionKnowledgeParameters.getFailMinValue());
                    parameter.setFailMaxValue(inspectionKnowledgeParameters.getFailMaxValue());
                    parameter.setControlMinValue(inspectionKnowledgeParameters.getControlMinValue());
                    parameter.setControlMaxValue(inspectionKnowledgeParameters.getControlMaxValue());
                    parameter.setWarnMinValue(inspectionKnowledgeParameters.getWarnMinValue());
                    parameter.setWarnMaxValue(inspectionKnowledgeParameters.getWarnMaxValue());
                    parameter.setPValue(inspectionKnowledgeParameters.getPValue());
                    parameter.setZValue(inspectionKnowledgeParameters.getZValue());
                    parameter.setCpValue(inspectionKnowledgeParameters.getCpValue());
                    parameter.setCpkValue(inspectionKnowledgeParameters.getCpkValue());
                    parameter.setPpValue(inspectionKnowledgeParameters.getPpValue());
                    parameter.setPpkValue(inspectionKnowledgeParameters.getPpkValue());
                    parameter.setPpmValue(inspectionKnowledgeParameters.getPpmValue());
                    parameter.setHvQmInspectionKnowledgeBase(inspectionKnowledgeBase.get());
                    if (null != parameter) {
                        if (null != parameter.getParameterMaxValue() && null != parameter.getParameterMinValue()) {
                            if (parameter.getParameterMaxValue().compareTo(parameter.getParameterMinValue()) == -1 || parameter.getParameterMaxValue().compareTo(parameter.getParameterMinValue()) == 0) {
                                throw new BaseKnownException(InspectionStandardExceptionEnum.MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM);
                            }
                            if (null != parameter.getParameterStandardsValue()) {
                                if (parameter.getParameterMaxValue().compareTo(parameter.getParameterStandardsValue()) == -1 || parameter.getParameterMinValue().compareTo(parameter.getParameterStandardsValue()) == 1) {
                                    throw new BaseKnownException(InspectionStandardExceptionEnum.MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM);
                                }
                            }
                        }
                        if (null != parameter.getControlMaxValue() && null != parameter.getControlMinValue()) {
                            if (parameter.getControlMaxValue().compareTo(parameter.getControlMinValue()) == -1 || parameter.getControlMaxValue().compareTo(parameter.getControlMinValue()) == 0) {
                                throw new BaseKnownException(InspectionStandardExceptionEnum.MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM);
                            }
                        }
                        if (null != parameter.getWarnMaxValue() && null != parameter.getWarnMinValue()) {
                            if (parameter.getWarnMaxValue().compareTo(parameter.getWarnMinValue()) == -1 || parameter.getWarnMaxValue().compareTo(parameter.getWarnMinValue()) == 0) {
                                throw new BaseKnownException(InspectionStandardExceptionEnum.MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM);
                            }
                        }
                        if (null != parameter.getFailMaxValue() && null != parameter.getFailMinValue()) {
                            if (parameter.getFailMaxValue().compareTo(parameter.getFailMinValue()) == -1 || parameter.getFailMaxValue().compareTo(parameter.getFailMinValue()) == 0) {
                                throw new BaseKnownException(InspectionStandardExceptionEnum.MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM);
                            }
                        }

                        parameterRepository.save(parameter);
                    }

                }
            });
            List<HvQmInspectionKnowledgeParameters> collect = parameters.stream().filter(parameter -> parameter.getParameterCode().equals(inspectionKnowledgeParameters.getParameterCode())).collect(Collectors.toList());
            if (collect.isEmpty()) {
                inspectionKnowledgeParameters.setHvQmInspectionKnowledgeBase(inspectionKnowledgeBase.get());
                if (null != inspectionKnowledgeParameters) {
                    if (null != inspectionKnowledgeParameters.getParameterMaxValue() && null != inspectionKnowledgeParameters.getParameterMinValue()) {

                        System.out.println(inspectionKnowledgeParameters.getParameterMaxValue().compareTo(inspectionKnowledgeParameters.getParameterStandardsValue()) == -1);
                        System.out.println(inspectionKnowledgeParameters.getParameterMinValue().compareTo(inspectionKnowledgeParameters.getParameterStandardsValue()) == 1);

                        if (inspectionKnowledgeParameters.getParameterMaxValue().compareTo(inspectionKnowledgeParameters.getParameterMinValue()) == -1 || inspectionKnowledgeParameters.getParameterMaxValue().compareTo(inspectionKnowledgeParameters.getParameterMinValue()) == 0) {
                            throw new BaseKnownException(InspectionStandardExceptionEnum.MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM);
                        }
                        if (null != inspectionKnowledgeParameters.getParameterStandardsValue()) {
                            if (inspectionKnowledgeParameters.getParameterMaxValue().compareTo(inspectionKnowledgeParameters.getParameterStandardsValue()) == -1 || inspectionKnowledgeParameters.getParameterMinValue().compareTo(inspectionKnowledgeParameters.getParameterStandardsValue()) == 1) {
                                throw new BaseKnownException(InspectionStandardExceptionEnum.MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM);
                            }
                        }
                    }
                }
                parameterRepository.save(inspectionKnowledgeParameters);
            }
        }
    }

    @Override
    public ResultVO<ExcelExportDto> exportParameter(Integer standardId) throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> result = exportParameterLink(standardId);
        if (null == result) {
            return null;
        }
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName(KnowledgeParameterConst.PARAMETER_FILE_NAME);
        return ResultVO.success(excelExportDto);

    }

    @Override
    public ResponseEntity<byte[]> exportParameterLink(Integer standardId) throws IOException, IllegalAccessException {
        Optional<HvQmInspectionKnowledgeBase> inspectionKnowledgeBase = knowledgeBaseRepository.findById(standardId);
        if (inspectionKnowledgeBase.isPresent()) {
            List<ExportKnowledgeParametersDTO> exportDefectsDTOS = DtoMapper.convertList(inspectionKnowledgeBase.get().getHvQmInspectionKnowledgeParameters(), ExportKnowledgeParametersDTO.class);
            ResponseEntity<byte[]> result = ExcelUtil.generateImportFile(exportDefectsDTOS
                    , KnowledgeParameterConst.PARAMETER_FILE_NAME, ExportKnowledgeParametersDTO.class);
            return result;

        }

        return null;
    }
}