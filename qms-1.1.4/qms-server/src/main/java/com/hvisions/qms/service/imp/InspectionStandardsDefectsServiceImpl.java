package com.hvisions.qms.service.imp;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.item.ExportDefectsDTO;
import com.hvisions.qms.dto.item.InspectionStandardsDefectsDTO;
import com.hvisions.qms.dto.standard.QueryStandardDTO;
import com.hvisions.qms.entity.HvQmDefectInspection;
import com.hvisions.qms.entity.HvQmInspectionStandard;
import com.hvisions.qms.repository.DefectInspectionRepository;
import com.hvisions.qms.repository.InspectionStandardRepository;
import com.hvisions.qms.service.DefectConst;
import com.hvisions.qms.service.InspectionStandardsDefectsService;
import com.hvisions.qms.service.StandardService;
import com.hvisions.qms.service.ValidService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>Title: InspectionStandardsDefectsServiceImpl</p >
 * <p>Description: 质量标准检测缺陷实现类</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/10/09</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
public class InspectionStandardsDefectsServiceImpl implements InspectionStandardsDefectsService {


    @Autowired
    InspectionStandardRepository standardRepository;
    @Autowired
    StandardService standardService;
    @Autowired
    ValidService validService;
    @Autowired
    DefectInspectionRepository defectInspectionRepository;
    @Autowired
    RemoteApiClient remoteApiClient;

    private Integer standardImportId;


    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addStandardsDefects(InspectionStandardsDefectsDTO inspectionStandardsDefectsDTO) {
        List<HvQmDefectInspection> byInspectionId = defectInspectionRepository.getByInspectionId(inspectionStandardsDefectsDTO.getInspectionStandardId());
        defectInspectionRepository.deleteInBatch(byInspectionId);
        Integer a = null;
        HvQmInspectionStandard standard = validService.canBeModify(inspectionStandardsDefectsDTO.getInspectionStandardId());
        //如果是新增，并且检查标准中已经有对应编码的缺陷。则直接跳过参数
        List<Integer> defectInspection = createDefectInspection(inspectionStandardsDefectsDTO.getInspectionStandardId(),
                inspectionStandardsDefectsDTO.getDefectIds());
        if (defectInspection.size() > 0) {
            a = defectInspection.get(0);
        }
        return a;
    }


    private List<Integer> createDefectInspection(int inspectionStandardId, List<Integer> defectIds) {
        List<HvQmDefectInspection> inspections = new ArrayList<>();
        for (Integer integer : defectIds) {
            HvQmDefectInspection hvQmDefectInspection = new HvQmDefectInspection();
            hvQmDefectInspection.setInspectionId(inspectionStandardId);
            hvQmDefectInspection.setDefectId(integer);
            inspections.add(hvQmDefectInspection);
        }
        List<HvQmDefectInspection> inspections1 = defectInspectionRepository.saveAll(inspections);
        return inspections1.stream().map(HvQmDefectInspection::getId).collect(Collectors.toList());
    }

    /**
     * {@inheritDoc}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addBatchStandardsDefects(List<InspectionStandardsDefectsDTO> inspectionStandardsDefectsDTO) {
        List<String> codes = new ArrayList<>();
        for (InspectionStandardsDefectsDTO standardsDefectsDTO : inspectionStandardsDefectsDTO) {
            addStandardsDefects(standardsDefectsDTO);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int updateStandardsDefects(InspectionStandardsDefectsDTO inspectionStandardsDefectsDTO) {
        return addStandardsDefects(inspectionStandardsDefectsDTO);
    }

    /**
     * 删除标准缺陷、根据Id
     *
     * @param defectId    标准缺陷Id
     * @param standardsId 质检标准iD
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeStandardsDefectsByDefectIdAndStandardsId(Integer defectId, Integer standardsId) {
        defectInspectionRepository.deleteByDefectIdAndInspectionId(defectId, standardsId);
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public void deleteBatchStandardsDefectsByIds(List<Integer> ids) {

    }

    /**
     * {@inheritDoc}
     */
    @Override
    public InspectionStandardsDefectsDTO findStandardsDefectsByStandardById(Integer id) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<InspectionStandardsDefectsDTO> findStandardsDefectsByStandardByIds(List<Integer> ids) {
        List<InspectionStandardsDefectsDTO> defects = new ArrayList<>();
        for (Integer id : ids) {
            defects.add(findStandardsDefectsByStandardById(id));
        }
        return defects;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResultVO<ExcelExportDto> exportDefect(Integer standardId) throws IOException, IllegalAccessException {

        ResponseEntity<byte[]> result = exportDefectLink(standardId);
        if (null == result) {
            return null;
        }
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName(DefectConst.DEFECT_FILE_NAME);
        return ResultVO.success(excelExportDto);

    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResponseEntity<byte[]> exportDefectLink(Integer standardId) throws IOException, IllegalAccessException {
        QueryStandardDTO inspectionStandard = standardService.findInspectionStandardById(standardId);
        if (null != inspectionStandard) {
            List<ExportDefectsDTO> exportDefectsDTOS = DtoMapper.convertList(inspectionStandard.getDefects(), ExportDefectsDTO.class);
            ResponseEntity<byte[]> result = ExcelUtil.generateImportFile(exportDefectsDTOS
                    , DefectConst.DEFECT_FILE_NAME, ExportDefectsDTO.class);
            return result;

        }
        return null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ImportResult importDefect(MultipartFile file, Integer id) throws IllegalAccessException, ParseException, IOException {
        standardImportId = id;
        return ExcelUtil.importEntity(file, ExportDefectsDTO.class, this);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void adjustIndex(Map<Integer, Integer> index) {
        List<HvQmDefectInspection> standardDefect = defectInspectionRepository.findAllById(index.keySet());
        for (HvQmDefectInspection defectInspection : standardDefect) {
            defectInspection.setSortIndex(index.get(defectInspection.getId()));
        }
        defectInspectionRepository.saveAll(standardDefect);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void saveOrUpdate(ExportDefectsDTO importDefectDTO) {

    }
}

