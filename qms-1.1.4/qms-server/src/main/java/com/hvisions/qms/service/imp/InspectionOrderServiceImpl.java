package com.hvisions.qms.service.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.utils.SerialUtil;
import com.hvisions.qms.configuration.Config;
import com.hvisions.qms.dao.InspectionMapper;
import com.hvisions.qms.dao.StatisticsMapper;
import com.hvisions.qms.dto.count.CountValueDTO;
import com.hvisions.qms.dto.count.HistoryCountValueDTO;
import com.hvisions.qms.dto.defects.*;
import com.hvisions.qms.dto.inspection.*;
import com.hvisions.qms.dto.parameter.HistoricalParameterValueDTO;
import com.hvisions.qms.dto.parameter.InspectionParameterValueBatchDTO;
import com.hvisions.qms.dto.parameter.InspectionParameterValueDTO;
import com.hvisions.qms.dto.statistics.InspectionOrderCountParamDTO;
import com.hvisions.qms.dto.statistics.InspectionOrderQueryCountDateDTO;
import com.hvisions.qms.dto.type.InspectionOrderTypeDTO;
import com.hvisions.qms.entity.*;
import com.hvisions.qms.enums.InspectionExceptionEnum;
import com.hvisions.qms.enums.InspectionResultEnum;
import com.hvisions.qms.enums.InspectionStatusEnum;
import com.hvisions.qms.enums.QualityInspectionExceptionEnum;
import com.hvisions.qms.repository.*;
import com.hvisions.qms.service.InspectionDefectInstanceService;
import com.hvisions.qms.service.InspectionOrderService;
import com.hvisions.qms.service.InspectionParameterInstanceService;
import com.hvisions.qms.service.imp.count.MysqlInspectionOrder;
import com.hvisions.qms.service.imp.count.SqlServerInspectionOrder;
import com.hvisions.qms.utils.ExternalInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title: QualityCheckListServiceImpl</p >
 * <p>Description: 质检单ServiceImpl</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/15</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class InspectionOrderServiceImpl implements InspectionOrderService {
    private final static String MYSQL = "MYSQL";
    private final static String SQLSERVER = "SQLSERVER";

    private final SqlServerInspectionOrder sqlServerInspectionOrder;
    private final MysqlInspectionOrder mysqlInspectionOrder;
    private final InspectionOrderRepository inspectionOrderRepository;
    private final InspectionOrderTypeRepository inspectionOrderTypeRepository;
    private final InspectionDefectInstanceRepository inspectionDefectInstanceRepository;
    private final Config config;
    private final InspectionParameterInstanceRepository parameterInstanceRepository;
    private final InspectionParameterValueRepository valueRepository;
    private final ExternalInterface api;
    private final InspectionCountValueRepository countValueRepository;
    private final InspectionDefectInstanceService inspectionDefectInstanceService;
    private final InspectionParameterInstanceService testParameterService;
    private final StatisticsMapper statisticsMapper;
    @Autowired
    SerialUtil serialUtil;
    @Autowired
    InspectionMapper inspectionMapper;

    @Autowired
    public InspectionOrderServiceImpl(SqlServerInspectionOrder sqlServerInspectionOrder,
                                      MysqlInspectionOrder mysqlInspectionOrder,
                                      InspectionOrderRepository inspectionOrderRepository,
                                      InspectionOrderTypeRepository inspectionOrderTypeRepository, InspectionDefectInstanceRepository inspectionDefectInstanceRepository,
                                      Config config,
                                      InspectionParameterInstanceRepository parameterInstanceRepository,
                                      InspectionParameterValueRepository valueRepository,
                                      ExternalInterface api,
                                      InspectionCountValueRepository countValueRepository,
                                      InspectionDefectInstanceService inspectionDefectInstanceService,
                                      InspectionParameterInstanceService testParameterService,
                                      StatisticsMapper statisticsMapper) {
        this.sqlServerInspectionOrder = sqlServerInspectionOrder;
        this.mysqlInspectionOrder = mysqlInspectionOrder;
        this.inspectionOrderRepository = inspectionOrderRepository;
        this.inspectionOrderTypeRepository = inspectionOrderTypeRepository;
        this.inspectionDefectInstanceRepository = inspectionDefectInstanceRepository;
        this.config = config;
        this.parameterInstanceRepository = parameterInstanceRepository;
        this.valueRepository = valueRepository;
        this.api = api;
        this.countValueRepository = countValueRepository;
        this.inspectionDefectInstanceService = inspectionDefectInstanceService;
        this.testParameterService = testParameterService;
        this.statisticsMapper = statisticsMapper;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveInspection(InspectionOrderDTO inspectionOrderDTO) {
        if (inspectionOrderDTO.getStandardId() == null) {
            throw new BaseKnownException(QualityInspectionExceptionEnum.STANDARDS_ID_NOT_NULL);
        }
        api.serviceInvocationStandardByStandardsId(inspectionOrderDTO.getStandardId());
        HvQmQualityInspectionOrder inspectionOrder = DtoMapper.convert(inspectionOrderDTO, HvQmQualityInspectionOrder.class);
        //创建流水号
        inspectionOrder.setInspectionCode(serialUtil.getSerialNumber("quality-inspection"));
        //设置状态为未检验
        inspectionOrder.setInspectionStatus(InspectionStatusEnum.NOT_CHECK.getCode());
        //设置状态为未检验
        inspectionOrder.setInspectionResult(InspectionResultEnum.NOT_CHECK.getCode());
        inspectionOrder.setCommit(false);
        //设置类型为对应的标准类型
        if (null != inspectionOrderDTO.getInspectionOrderTypeId()) {
            inspectionOrder.setInspectionOrderType(inspectionOrderTypeRepository.getOne(inspectionOrderDTO.getInspectionOrderTypeId()));
        }
        //保存质检单信息
        return inspectionOrderRepository.save(inspectionOrder).getId();
    }


    /**
     * 新建质检单
     *
     * @param inspectionOrderDTO 质检单DTO
     * @return 新增质检单id
     */
    @Override
    public Integer createInspectionOrderClient(InspectionOrderClientDTO inspectionOrderDTO) {
        HvQmQualityInspectionOrder inspectionOrder = DtoMapper.convert(inspectionOrderDTO, HvQmQualityInspectionOrder.class);
        //创建流水号
        inspectionOrder.setInspectionCode(serialUtil.getSerialNumber("quality-inspection"));
        inspectionOrder.setCommit(false);
        //设置类型为对应的标准类型
        if (null != inspectionOrderDTO.getInspectionOrderTypeId()) {
            inspectionOrder.setInspectionOrderType(inspectionOrderTypeRepository.getOne(inspectionOrderDTO.getInspectionOrderTypeId()));
        }
        //保存质检单信息
        HvQmQualityInspectionOrder order = inspectionOrderRepository.save(inspectionOrder);
        InspectionParameterValueBatchDTO parameterValueBatchDTO = inspectionOrderDTO.getInspectionParameterValueBatchDTOS();
        InspectionDefectInstanceBatchDTO defectInstances = inspectionOrderDTO.getInspectionDefectInstanceDTOS();
        //保存参数数据
        if (null != parameterValueBatchDTO) {
            List<InspectionParameterValueDTO> parameterValues = parameterValueBatchDTO.getInspectionParameterValueDTOS();

            List<HvQmParameterInstance> parameterInstances = DtoMapper.convertList(parameterValues, HvQmParameterInstance.class);
            parameterInstances.forEach(t -> t.setHvQmQualityInspectionOrder(order));
            List<HvQmParameterInstance> qmParameterInstances = parameterInstanceRepository.saveAll(parameterInstances);

            InspectionParameterValueBatchDTO inspectionParameterValueBatchDTO = new InspectionParameterValueBatchDTO();
            inspectionParameterValueBatchDTO.setInspectionOrderId(order.getId());
            inspectionParameterValueBatchDTO.setInspectionParameterValueDTOS(DtoMapper.convertList(qmParameterInstances, InspectionParameterValueDTO.class));
            testParameterService.createTestParameterInstanceMore(inspectionParameterValueBatchDTO);
        }
        //保存缺陷数据
        if (null != defectInstances) {
            defectInstances.setInspectionOrderId(order.getId());
            inspectionDefectInstanceService.addAll(defectInstances);
        }
        return order.getId();
    }

    /**
     * 根据时间段查询缺陷数量
     *
     * @param stepDefectQueryDTO 查询条件
     * @return 缺陷信息
     */
    @Override
    public List<StepDefectDTO> getDefectCountByTime(StepDefectQueryDTO stepDefectQueryDTO) {
        return statisticsMapper.getDefectCountByTime(stepDefectQueryDTO);
    }

    /**
     * 查询缺陷数量
     *
     * @param beginTime 起始时间
     * @param endTime   结束时间
     * @return 缺陷数量
     */
    @Override
    public List<DefectCountDTO> getCountByTime(Date beginTime, Date endTime) {
        return statisticsMapper.getCountByTime(beginTime, endTime);
    }

    /**
     * 查询物料缺陷
     *
     * @param materialDefectQueryDTO 查询条件
     * @return 缺陷信息
     */
    @Override
    public List<MaterialDefectCountDTO> getMaterialDefectByQuery(MaterialDefectQueryDTO materialDefectQueryDTO) {
        return statisticsMapper.getMaterialDefectByQuery(materialDefectQueryDTO);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addInspectionOrderAndInspectionDefect(InspectionOrderDetailAndDefectDTO inspectionOrderDetailAndDefectDTO) {
        Integer inspectionId = saveInspection(inspectionOrderDetailAndDefectDTO.getInspectionOrderDTO());
        inspectionOrderDetailAndDefectDTO.getInspectionDefectInstanceBatchDTO().setInspectionOrderId(inspectionId);
        inspectionDefectInstanceService.addAll(inspectionOrderDetailAndDefectDTO.getInspectionDefectInstanceBatchDTO());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void delete(Integer id) {
        Optional<HvQmQualityInspectionOrder> order = inspectionOrderRepository.findById(id);
        if (order.isPresent()) {
            if (order.get().getInspectionStatus().equals(InspectionStatusEnum.NOT_CHECK.getCode())) {
                order.get().setHide(true);
                inspectionOrderRepository.save(order.get());
            } else {
                throw new BaseKnownException(InspectionExceptionEnum.NOT_DELETE);
            }
        }
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public void updateInspection(InspectionOrderUpdateDTO updateDTO) {
        Optional<HvQmQualityInspectionOrder> order = inspectionOrderRepository.findById(updateDTO.getInspectionId());
        if (order.isPresent()) {
            HvQmQualityInspectionOrder inspectionOrder = order.get();
            if (inspectionOrder.getInspectionStatus().equals(InspectionStatusEnum.ALREADY_CHECKED.getCode())) {
                throw new BaseKnownException(InspectionExceptionEnum.IT_CANNOT_BE_MODIFIED_AFTER_DETECTION);
            }
            if (null != updateDTO.getInspectionDescription()) {
                inspectionOrder.setInspectionDescription(updateDTO.getInspectionDescription());
            }
            if (null != updateDTO.getInspectionUser()) {
                inspectionOrder.setInspectionUser(updateDTO.getInspectionUser());
            }
            if (null != updateDTO.getInspectionResult()) {
                inspectionOrder.setInspectionResult(updateDTO.getInspectionResult());
            }
            if (null != updateDTO.getEndTime()) {
                inspectionOrder.setInspectionEndTime(updateDTO.getEndTime());
            }
            if (null != updateDTO.getStartTime()) {
                inspectionOrder.setInspectionStartTime(updateDTO.getStartTime());
            }
            if (null != updateDTO.getCommit()) {
                inspectionOrder.setCommit(updateDTO.getCommit());
                if (updateDTO.getCommit()) {
                    inspectionOrder.setInspectionStatus(InspectionStatusEnum.ALREADY_CHECKED.getCode());
                }
            }
            inspectionOrderRepository.save(inspectionOrder);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Page<ResultInspectionOrderDTO> findByCondition(InspectionOrderQueryDTO inspectionOrderQueryDTO) {
        return PageHelperUtil.getPage(inspectionMapper::findByQuery, inspectionOrderQueryDTO);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public InspectionOrderCountParamDTO checkCountAll(InspectionOrderQueryCountDateDTO countDateDTO) {
        //统计 根据数据库驱动URL确定数据库执行的SQL
        if (config.getUrl().toUpperCase().contains(MYSQL)) {
            return mysqlInspectionOrder.checkMysqlCountAll(countDateDTO);
        }
        if (config.getUrl().toUpperCase().contains(SQLSERVER)) {
            return sqlServerInspectionOrder.checkSqlServerCountAll(countDateDTO);
        } else {
            log.error("质检单统计（根据年月日，质检状态结果，及其检测项获取）,接口出错:数据库连接出错，只支持mysql。sqlserver");
            return null;
        }
    }

    /**
     * 判断两个对象的区域和位置点都一样
     *
     * @param instance    数据库实例
     * @param instanceDTO 对象实例
     * @return 是否相同
     */
    private boolean equal(HvQmDefectInstance instance, InspectionDefectInstanceDTO instanceDTO) {
        if (instance.getDefectCode() == null || instanceDTO.getDefectCode() == null) {
            return false;
        }
        if (!instance.getDefectCode().equals(instanceDTO.getDefectCode())) {
            return false;
        }
        int instancePoint;
        int instancePosition;
        int dtoPoint;
        int dtoPosition;
        instancePoint = instance.getPointId() == null ? 0 : instance.getPointId();
        instancePosition = instance.getPositionId() == null ? 0 : instance.getPositionId();
        dtoPoint = instanceDTO.getPointId() == null ? 0 : instanceDTO.getPointId();
        dtoPosition = instanceDTO.getPositionId() == null ? 0 : instanceDTO.getPositionId();
        return instancePoint == dtoPoint && instancePosition == dtoPosition;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResultInspectionOrderDTO findInspectionOrderById(Integer inspectionId) {
        //获取质检单
        Optional<HvQmQualityInspectionOrder> orderResult = inspectionOrderRepository.findById(inspectionId);
        if (!orderResult.isPresent()) {
            return null;
        }
        HvQmQualityInspectionOrder order = orderResult.get();
        //根据质检标准获取录入模板
        ResultInspectionOrderDTO result = api.createSampleData(order.getStandardId());
        BeanUtils.copyProperties(order, result, "defects", "parameters", "inspectionOrderType");
        //质检单类型
        result.setInspectionOrderType(DtoMapper.convert(order.getInspectionOrderType(), InspectionOrderTypeDTO.class));
        //将质检单的缺陷录入值添加进去
        List<HvQmDefectInstance> defects = inspectionDefectInstanceRepository.findByInspectionId(inspectionId);
        for (HvQmDefectInstance defect : defects) {
            for (InspectionDefectInstanceDTO resultDefect : result.getDefects()) {
                if (equal(defect, resultDefect)) {
                    BeanUtils.copyProperties(defect, resultDefect, "hvQmQualityInspectionOrder");
                }
            }
        }
        //参数历史参数数据列表,首先每次录入都有一套数据，都是以参数数据为模板，然后把参数值填充进来
        List<HistoricalParameterValueDTO> history = new ArrayList<>();
        List<HvQmParameterValue> allValues = valueRepository.findAllByOrderId(inspectionId);
        //遍历所有历史数据，如果已经存在了分组，就直接更新分组数据，如果没有，新建一个分组,并且以参数标准作为模板
        for (HvQmParameterValue parameterValue : allValues) {
            Integer time = parameterValue.getTimes();
            HistoricalParameterValueDTO timeValues;
            Optional<HistoricalParameterValueDTO> first = history.stream().filter(t -> t.getTimes().equals(time)).findFirst();
            if (first.isPresent()) {
                timeValues = first.get();
            } else {
                timeValues = new HistoricalParameterValueDTO();
                timeValues.setTimes(time);
                List<InspectionParameterValueDTO> parametersValues = DtoMapper.convertList(result.getParametersSpecimen(), InspectionParameterValueDTO.class);
                timeValues.setInspectionParameterValues(parametersValues);
                history.add(timeValues);
            }
            //找到分组中的对应的参数值，用数据库中的数据进行补充
            Optional<InspectionParameterValueDTO> resultParam = timeValues.getInspectionParameterValues().stream()
                    .filter(t -> equal(parameterValue, t))
                    .findFirst();
            resultParam.ifPresent(t -> {
                t.setParameterNote(parameterValue.getParameterNote());
                t.setParameterValue(parameterValue.getParameterValue());
                t.setParameterValueId(parameterValue.getId());
            });
        }
        result.setHistoricalParameterValues(history);
        //计算结果数据
        List<HvQmCountValue> countValue = countValueRepository.findByCountCheckId(inspectionId);
        List<HistoryCountValueDTO> listValueDto = new ArrayList<>();
        if (null != countValue || !countValue.isEmpty()) {
            Map<Integer, List<HvQmCountValue>> mapCountRules = countValue.stream().collect(Collectors.groupingBy(HvQmCountValue::getCountCheckNum));
            for (Map.Entry<Integer, List<HvQmCountValue>> entry : mapCountRules.entrySet()) {
                HistoryCountValueDTO countValueDTO = new HistoryCountValueDTO();
                countValueDTO.setTimes(entry.getKey());
                countValueDTO.setCountValueDTOS(DtoMapper.convertList(entry.getValue(), CountValueDTO.class));
                listValueDto.add(countValueDTO);
            }
        }
        result.setHistoryCountValueDTOS(listValueDto);
        return result;
    }

    /**
     * 判断参数值实体和传入的参数是否相同
     *
     * @param entity 实体
     * @param dto    传入的数据
     * @return 是否相同
     */
    private boolean equal(HvQmParameterValue entity, InspectionParameterValueDTO dto) {
        if (entity.getParameterCode() == null || dto.getParameterCode() == null || (!dto.getParameterCode().equals(entity.getParameterCode()))) {
            return false;
        }
        int entityPointId = entity.getPointId() == null ? 0 : entity.getPointId();
        int entityPositionId = entity.getPositionId() == null ? 0 : entity.getPositionId();
        int dtoPointId = dto.getPointId() == null ? 0 : dto.getPointId();
        int dtoPositionId = dto.getPositionId() == null ? 0 : dto.getPositionId();
        return entityPointId == dtoPointId && entityPositionId == dtoPositionId;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<InspectionOrderDetailDTO> findInspectionOrderByWorkOrderCode(String workOrderCode) {
        return DtoMapper.convertList(inspectionOrderRepository.findByWorkOrderCodeContaining(workOrderCode), InspectionOrderDetailDTO.class);
    }

}