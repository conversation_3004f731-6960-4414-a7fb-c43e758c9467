package com.hvisions.qms.repository;

import com.hvisions.qms.entity.HvQmQualityDefectType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;


@Repository
public interface DefectTypeRepository extends JpaRepository<HvQmQualityDefectType, Integer> {

    /**
     * 根据责任部门名称获取责任部门对象
     *
     * @param name 名称
     * @return 责任部门对象
     */
    HvQmQualityDefectType findByName(String name);

    HvQmQualityDefectType findByCode(String code);
}
