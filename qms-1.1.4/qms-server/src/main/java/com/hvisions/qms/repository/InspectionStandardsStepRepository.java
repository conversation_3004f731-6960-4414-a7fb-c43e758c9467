package com.hvisions.qms.repository;


import com.hvisions.qms.entity.HvQmInspectionStandardsStep;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: InspectionStandardsStepRepository</p >
 * <p>Description: 质检标准与工艺步骤持久化层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/16</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface InspectionStandardsStepRepository extends JpaRepository<HvQmInspectionStandardsStep, Integer> {
    /**
     * 查询质检标准和工位关系
     *
     * @param stepId     工位id
     * @param standardId 质检标准id
     */
    @Modifying
    void deleteAllByStepIdAndStandardsId(Integer stepId, Integer standardId);



    /**
     * 根据主键查询关联关系
     *
     * @param id 关联关系id
     * @return 关联列表
     */
    List<HvQmInspectionStandardsStep> findByStandardsId(Integer id);

    HvQmInspectionStandardsStep findByStandardsIdAndStepId(int standardId, int stepId);

    List<HvQmInspectionStandardsStep> findAll(Specification<HvQmInspectionStandardsStep> specification);

    List<HvQmInspectionStandardsStep> findByStandardsIdIn(List<Integer> idList);

}
