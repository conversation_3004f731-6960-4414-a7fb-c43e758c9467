package com.hvisions.qms.controller;

import com.hvisions.qms.dto.group.InspectionStandardsGroupDTO;
import com.hvisions.qms.dto.group.QueryStandardsGroupDTO;
import com.hvisions.qms.service.InspectionStandardsGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: InspectionStandardsGroupController</p >
 * <p>Description: 质检标准分组控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/10/8</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/standardsGroup")
@Slf4j
@Api(description = "质检标准分组控制器")
public class StandardsGroupController {

    private final InspectionStandardsGroupService inspectionStandardsGroup;

    @Autowired
    public StandardsGroupController(InspectionStandardsGroupService inspectionStandardsGroup) {
        this.inspectionStandardsGroup = inspectionStandardsGroup;
    }


    /**
     * 新增质检标准分组
     *
     * @param inspectionStandardsGroupDTO 质检标准分组
     * @return 主键
     */
    @ApiOperation(value = " 新增质检标准分组")
    @PostMapping(value = "/createStandardsGroup")
    public int createStandardsGroup(@RequestBody InspectionStandardsGroupDTO inspectionStandardsGroupDTO) {
        return inspectionStandardsGroup.addStandardsGroup(inspectionStandardsGroupDTO);
    }

    /**
     * 删除质检标准分组、根据Id
     *
     * @param id 质检标准分组Id
     */
    @ApiOperation(value = " 删除质检标准分组、根据Id")
    @DeleteMapping(value = "/deleteStandardsGroupById/{id}")
    public void deleteStandardsGroupById(@PathVariable Integer id) {
        inspectionStandardsGroup.removeStandardsGroupById(id);
    }

    /**
     * 批量删除质检标准分组、根据Id
     *
     * @param ids Id列表
     */
    @ApiOperation(value = " 批量删除质检标准分组、根据Id")
    @DeleteMapping(value = "/deleteBatchStandardsGroupByIds")
    public void deleteBatchStandardsGroupByIds(@RequestBody List<Integer> ids) {
        inspectionStandardsGroup.removeBatchStandardsGroupByIds(ids);
    }

    /**
     * 更新质检标准分组
     *
     * @param inspectionStandardsGroupDTO 质检标准分组
     * @return 主键
     */
    @ApiOperation(value = "更新质检标准分组")
    @PutMapping(value = "/editStandardsGroup")
    public int editStandardsGroup(@RequestBody InspectionStandardsGroupDTO inspectionStandardsGroupDTO) {
        return inspectionStandardsGroup.updateStandardsGroup(inspectionStandardsGroupDTO);
    }

    /**
     * 根据质检标准分组id、获取质检标准分组
     *
     * @param id 标准分组id
     * @return 质检标准分组
     */
    @ApiOperation(value = "根据质检标准分组id、获取质检标准分组")
    @GetMapping(value = "/getStandardsGroupById/{id}")
    public InspectionStandardsGroupDTO getStandardsGroupById(@PathVariable Integer id) {
        return inspectionStandardsGroup.findStandardsGroupById(id);
    }


    /**
     * 获取质检标准分组、根据Id列表
     *
     * @param ids Id列表
     * @return 质检标准分组
     */
    @ApiOperation(value = "获取质检标准分组、根据Id列表")
    @PostMapping(value = "/getStandardsGroupByIds")
    public List<InspectionStandardsGroupDTO> getStandardsGroupByIds(@RequestBody List<Integer> ids) {
        return inspectionStandardsGroup.findStandardsGroupByIds(ids);
    }

    /**
     * 获取质检标准分组、根据编码列表
     *
     * @param codes 编码列表
     * @return 质检标准分组
     */
    @ApiOperation(value = "获取质检标准分组、根据编码列表")
    @PostMapping(value = "/getStandardsGroupByCodes")
    public List<InspectionStandardsGroupDTO> getStandardsGroupByCodes(@RequestBody List<String> codes) {
        return inspectionStandardsGroup.findStandardsGroupByCodes(codes);
    }


    /**
     * 多条件分页获取质检标准分组
     *
     * @param queryDto 条件
     * @return 质检标准分组
     */
    @ApiOperation(value = "多条件分页获取质检标准分组")
    @PostMapping(value = "/getStandardsGroup")
    public Page<InspectionStandardsGroupDTO> getStandardsGroup(@RequestBody QueryStandardsGroupDTO queryDto) {
        return inspectionStandardsGroup.findStandardsGroup(queryDto);
    }

}