package com.hvisions.qms.repository;

import com.hvisions.qms.entity.HvQmDefectInspection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: DefectInspectionRepository</p >
 * <p>Description: 缺陷与质检标准关联关系 </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/7/27</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface DefectInspectionRepository extends JpaRepository<HvQmDefectInspection, Integer> {

    /**
     * 根据缺陷ID删除 关联关系
     *
     * @param defectId 缺陷Id
     * @param inspectionId 质检标准id
     */
    @Modifying
    @Query(value = "delete from HvQmDefectInspection h where h.defectId = ?1 and h.inspectionId =?2 ")
    void deleteByDefectIdAndInspectionId(int defectId, int inspectionId);

    /**
     * 根绝质检标准查询缺陷
     *
     * @param inspectionId 质检标准id
     * @return 缺陷列表
     */
    List<HvQmDefectInspection> getByInspectionId(int inspectionId);
}