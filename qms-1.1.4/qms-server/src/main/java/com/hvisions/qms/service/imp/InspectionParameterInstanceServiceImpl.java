package com.hvisions.qms.service.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.qms.dto.count.CountRulesDTO;
import com.hvisions.qms.dto.count.CountRulesRelationDTO;
import com.hvisions.qms.dto.parameter.InspectionParameterValueBatchDTO;
import com.hvisions.qms.dto.parameter.InspectionParameterValueDTO;
import com.hvisions.qms.dto.parameter.InspectionStandardsParametersSpecimenDTO;
import com.hvisions.qms.entity.HvQmCountValue;
import com.hvisions.qms.entity.HvQmParameterValue;
import com.hvisions.qms.entity.HvQmQualityInspectionOrder;
import com.hvisions.qms.enums.InspectionExceptionEnum;
import com.hvisions.qms.enums.InspectionObjectTypeEnum;
import com.hvisions.qms.enums.InspectionStatusEnum;
import com.hvisions.qms.enums.ParameterDataTypeEnum;
import com.hvisions.qms.repository.InspectionCountValueRepository;
import com.hvisions.qms.repository.InspectionOrderRepository;
import com.hvisions.qms.repository.InspectionParameterInstanceRepository;
import com.hvisions.qms.repository.InspectionParameterValueRepository;
import com.hvisions.qms.service.InspectionParameterInstanceService;
import com.hvisions.qms.service.imp.count.EvaluateExpression;
import com.hvisions.qms.utils.ExternalInterface;
import com.hvisions.qms.dto.rule.ResultCountRulesDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title: TestParameterInstanceService</p >
 * <p>Description: 离线检查service</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/15</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class InspectionParameterInstanceServiceImpl implements InspectionParameterInstanceService {
    private final InspectionParameterInstanceRepository inspectionParameterInstanceRepository;
    private final InspectionOrderRepository inspectionOrderRepository;
    private final InspectionCountValueRepository inspectionCountValueRepository;
    private final InspectionParameterValueRepository parameterValueRepository;
    private final ExternalInterface api;


    @Autowired
    public InspectionParameterInstanceServiceImpl(InspectionParameterInstanceRepository inspectionParameterInstanceRepository, InspectionOrderRepository inspectionOrderRepository, InspectionCountValueRepository inspectionCountValueRepository, InspectionParameterValueRepository parameterValueRepository, ExternalInterface api) {
        this.inspectionParameterInstanceRepository = inspectionParameterInstanceRepository;
        this.inspectionOrderRepository = inspectionOrderRepository;
        this.inspectionCountValueRepository = inspectionCountValueRepository;
        this.parameterValueRepository = parameterValueRepository;
        this.api = api;

    }

    @Override
    public void deleteById(int id) {
        inspectionParameterInstanceRepository.deleteById(id);
    }

    /**
     * 判断参数值实体和传入的参数是否相同
     *
     * @param entity 实体
     * @param dto    传入的数据
     * @return 是否相同
     */
    private boolean equal(HvQmParameterValue entity, InspectionParameterValueDTO dto) {
        if (entity.getParameterCode() == null || dto.getParameterCode() == null || (!dto.getParameterCode().equals(entity.getParameterCode()))) {
            return false;
        }
        int entityPointId = entity.getPointId() == null ? 0 : entity.getPointId();
        int entityPositionId = entity.getPositionId() == null ? 0 : entity.getPositionId();
        int dtoPointId = dto.getPointId() == null ? 0 : dto.getPointId();
        int dtoPositionId = dto.getPositionId() == null ? 0 : dto.getPositionId();
        return entityPointId == dtoPointId && entityPositionId == dtoPositionId;
    }

    /**
     * 新增
     *
     * @param parameterValueSpecimenDTOS 质检单参数信息
     * @return 添加后的返回实体Id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createTestParameterInstanceMore(InspectionParameterValueBatchDTO parameterValueSpecimenDTOS) {
        //todo  要求质检结果出来以后还能进行录入。把那个限制去掉
        if (null == parameterValueSpecimenDTOS.getInspectionParameterValueDTOS() || parameterValueSpecimenDTOS.getInspectionParameterValueDTOS().isEmpty()) {
            throw new BaseKnownException(InspectionExceptionEnum.ENTRY_PARAMETER_DOES_NOT_EXIST);
        }
        List<HvQmParameterValue> orderValues = parameterValueRepository.findAllByOrderId(parameterValueSpecimenDTOS.getInspectionOrderId());
        int time;
        boolean isUpdate = false;
        if (parameterValueSpecimenDTOS.getTime() != null && parameterValueSpecimenDTOS.getTime() > 0) {
            //如果传递了录入次数，说明是更新操作
            time = parameterValueSpecimenDTOS.getTime();
            isUpdate = true;
        } else {
            //计算是第几次录入，如果数据库没有，设置为1，如果有数据，拿出所有的数据，time去重后计算个数，总体数量加1
            if (orderValues == null || orderValues.isEmpty()) {
                time = 1;
            } else {
                time = orderValues.stream().map(HvQmParameterValue::getTimes)
                        .distinct().collect(Collectors.toList()).size() + 1;
            }
        }
        List<HvQmParameterValue> arrayList = new ArrayList<>();
        List<HvQmParameterValue> valueEntitys = orderValues
                .stream().filter(t -> t.getTimes().equals(time)).collect(Collectors.toList());
        boolean finalIsUpdate = isUpdate;
        parameterValueSpecimenDTOS.getInspectionParameterValueDTOS().forEach(parameterValueSpecimen -> {
            HvQmParameterValue parameterValue;
            if (finalIsUpdate) {
                parameterValue = valueEntitys.stream().filter(t -> equal(t, parameterValueSpecimen))
                        .findFirst().orElseThrow(() -> new BaseKnownException(10000, "录入次数数据传输错误,没有找到后台对应的数据"));
            } else {
                parameterValue = new HvQmParameterValue();
            }
            ParameterDataTypeEnum dataTypeEnum = ParameterDataTypeEnum.valueOf(parameterValueSpecimen.getDataType());
            if (null != parameterValueSpecimen.getParameterValue()) {
                switch (dataTypeEnum) {
                    case INT:
                    case FLOAT:
                    case DECIMAL:
                        parameterValue.setActualValueDecimal(new BigDecimal(parameterValueSpecimen.getParameterValue()));
                    case DATE:
                    case DATETIME:
                    case STRING:
                        parameterValue.setActualValueString(parameterValueSpecimen.getParameterValue());
                        break;
                    case BOOLEAN:
                        parameterValue.setActualValueBoolean(Boolean.valueOf(parameterValueSpecimen.getParameterValue()));
                        break;
                    default:
                        parameterValue.setActualValueString(parameterValueSpecimen.getParameterValue());
                        break;
                }
            }
            parameterValue.setTimes(time);
            parameterValue.setPointId(parameterValueSpecimen.getPointId());
            parameterValue.setPositionId(parameterValueSpecimen.getPositionId());
            parameterValue.setOrderId(parameterValueSpecimenDTOS.getInspectionOrderId());
            parameterValue.setParameterCode(parameterValueSpecimen.getParameterCode());
            parameterValue.setParameterValue(parameterValueSpecimen.getParameterValue());
            parameterValue.setParameterNote(parameterValueSpecimen.getParameterNote());
            arrayList.add(parameterValue);
        });
        parameterValueRepository.saveAll(arrayList);
        parameterValueRepository.flush();
        Optional<HvQmQualityInspectionOrder> inspectionOrder = inspectionOrderRepository.findById(parameterValueSpecimenDTOS.getInspectionOrderId());
        if (inspectionOrder.isPresent()) {
            //检测质检单状态、提交：检测完成，未提交：检测中
            if (inspectionOrder.get().getCommit()) {
                throw new BaseKnownException(InspectionExceptionEnum.THE_INSPECTION_FORM_HAS_BEEN_TESTED_PLEASE_DO_NOT_SUBMIT_IT_AGAIN);
            } else {
                inspectionOrder.get().setInspectionStatus(InspectionStatusEnum.IN_CHECKING.getCode());
            }
            inspectionOrderRepository.save(inspectionOrder.get());
            List<ResultCountRulesDTO> resultCountRulesDTOS = api.serviceInvocationStandardCountRuleByStandardsId(inspectionOrder.get().getStandardId());
            List<CountRulesDTO> countRulesDTOList = new ArrayList<>();
            resultCountRulesDTOS.forEach(resultCount -> {
                CountRulesDTO rulesDTO = new CountRulesDTO();
                rulesDTO.setCountRulesId(resultCount.getId());
                rulesDTO.setCountType(resultCount.getCountType());
                rulesDTO.setCountLength(resultCount.getPointDTOS().size());
                List<CountRulesRelationDTO> listCount = new ArrayList<>();
                resultCount.getPointDTOS().forEach(pointDTO -> {
                    resultCount.getInspectionStandardsParametersDTOS().forEach(parameter -> {
                        CountRulesRelationDTO count = new CountRulesRelationDTO();
                        count.setParameterId(parameter.getId());
                        count.setPointId(pointDTO.getId());
                        listCount.add(count);
                    });

                });
                rulesDTO.setCountRulesDTOList(listCount);
                countRulesDTOList.add(rulesDTO);
            });
            countData(countRulesDTOList, parameterValueSpecimenDTOS.getInspectionParameterValueDTOS(), parameterValueSpecimenDTOS.getInspectionOrderId(), arrayList);
        }
        return arrayList.size();
    }


    private Map<String, List<InspectionStandardsParametersSpecimenDTO>> toCountParamValue
            (Set<Integer> countingMid, List<InspectionStandardsParametersSpecimenDTO> parameterInstances) {
        Set<InspectionStandardsParametersSpecimenDTO> list = new HashSet<>();
        for (Integer pointId : countingMid) {
            for (InspectionStandardsParametersSpecimenDTO parameterInstance : parameterInstances) {
                if (pointId.equals(parameterInstance.getPointId())) {
                    list.add(parameterInstance);
                }
            }
        }
        Map<String, List<InspectionStandardsParametersSpecimenDTO>> map = new HashMap<>(1000);
        for (InspectionStandardsParametersSpecimenDTO param : list) {
            if (map.containsKey(param.getParameterCode())) {
                map.get(param.getParameterCode()).add(param);
            } else {
                List<InspectionStandardsParametersSpecimenDTO> paramList = new ArrayList<>();
                paramList.add(param);
                map.put(param.getParameterCode(), paramList);
            }
        }
        return map;
    }


    private void countData(List<CountRulesDTO> listCount, List<InspectionParameterValueDTO> parameterValueSpecimenDTOS, Integer inspectionId, List<HvQmParameterValue> arrayList) {
        HvQmCountValue countValue = new HvQmCountValue();
        for (CountRulesDTO countRulesDTO : listCount) {
            List<InspectionParameterValueDTO> parameterValueSpecimens = new ArrayList<>();

            countRulesDTO.getCountRulesDTOList().forEach(count -> {
                parameterValueSpecimens.addAll(parameterValueSpecimenDTOS.stream().filter(paramValue -> count.getPointId().equals(paramValue.getPointId()) && count.getParameterId().equals(paramValue.getParameterId())).collect(Collectors.toList()));
            });
            Map<Integer, List<InspectionParameterValueDTO>> collect = parameterValueSpecimens.stream().collect(Collectors.groupingBy(InspectionParameterValueDTO::getParameterId));
            for (Map.Entry<Integer, List<InspectionParameterValueDTO>> listEntry : collect.entrySet()) {
                HvQmCountValue value = new HvQmCountValue();
                switch (countRulesDTO.getCountType()) {
                    case 1:
                        value.setCountParamData(toSum(listCount, listEntry.getValue()));
                        break;
                    case 2:
                        value.setCountParamData(toAverage(listCount, listEntry.getValue(), countRulesDTO.getCountLength()));
                        break;
                    case 3:
                        value.setCountParamData(toMax(listCount, listEntry.getValue()));
                        break;
                    case 4:
                        value.setCountParamData(toMin(listCount, listEntry.getValue()));
                        break;
                    default:
                        break;
                }
                if (!parameterValueSpecimens.isEmpty()) {
                    value.setParamUnit(listEntry.getValue().get(0).getParameterUnit());
                    value.setCountCheckId(inspectionId);
                    value.setCountParamCode(listEntry.getValue().get(0).getParameterCode());
                    value.setPointId(listEntry.getValue().get(0).getPointId());
                    value.setPointName(listEntry.getValue().get(0).getPointName());
                    value.setCountCheckNum(arrayList.get(0).getTimes());
                    value.setCountParamName(listEntry.getValue().get(0).getParameterName());
                    value.setCountingId(countRulesDTO.getCountRulesId());
                    value.setCountingType(countRulesDTO.getCountType());
                }
                inspectionCountValueRepository.save(value);
            }


        }
    }

    /**
     * @param size
     * @return 返回参数值结果集
     */
    private BigDecimal toAverage(List<CountRulesDTO> listCount, List<InspectionParameterValueDTO> parameterValueSpecimenDTOS, Integer size) {

        StringBuffer valueParam;
        String s = "0";
        valueParam = new StringBuffer();
        for (InspectionParameterValueDTO parameterInstanceDTO : parameterValueSpecimenDTOS) {
            Integer dataType = parameterInstanceDTO.getDataType();
            //满足数学型才计算
            if (dataType.equals(ParameterDataTypeEnum.INT.getCode()) || dataType.equals(ParameterDataTypeEnum.FLOAT.getCode()) || dataType.equals(ParameterDataTypeEnum.DECIMAL.getCode())) {
                valueParam.append(parameterInstanceDTO.getParameterValue() + "+");
            }
        }
        log.info(valueParam.toString());
        String replace = valueParam.toString().replace("null", "0");

        if (replace.length() > 1) {
            s = "(" + replace.substring(0, replace.length() - 1) + ")" + "/" + size;

        }
        return new EvaluateExpression(s).evaluate();

    }

    /**
     * @return HvQmCountValue 返回参数值结果集
     */
    private BigDecimal toSum(List<CountRulesDTO> listCount, List<InspectionParameterValueDTO> parameterValueSpecimenDTOS) {
        String s = "0";
        String valueParam = "";
        for (InspectionParameterValueDTO parameterInstanceDTO : parameterValueSpecimenDTOS) {
            Integer dataType = parameterInstanceDTO.getDataType();
            //满足 数学型才计算
            if (dataType == ParameterDataTypeEnum.INT.getCode() || dataType == ParameterDataTypeEnum.FLOAT.getCode() || dataType == ParameterDataTypeEnum.DECIMAL.getCode()) {
                valueParam += (parameterInstanceDTO.getParameterValue() + "+");
            }
        }
        log.info(valueParam.toString());
        String replace = valueParam.replace("null", "0");
        if (replace.length() > 1) {
            s = replace.substring(0, replace.length() - 1);

        }
        return new EvaluateExpression(s).evaluate();

    }

    private BigDecimal toMax(List<CountRulesDTO> listCount, List<InspectionParameterValueDTO> parameterValueSpecimenDTOS) {
        List<BigDecimal> bigDecimals = new ArrayList<>();
        for (InspectionParameterValueDTO parameterInstanceDTO : parameterValueSpecimenDTOS) {
            if (null != parameterInstanceDTO.getParameterValue() && !"".equals(parameterInstanceDTO.getParameterValue())) {
                bigDecimals.add(new BigDecimal(parameterInstanceDTO.getParameterValue()));
            }
        }
        if (!bigDecimals.isEmpty()) {
            log.info("数据max" + bigDecimals);
            return Collections.max(bigDecimals);
        }
        return new BigDecimal(InspectionObjectTypeEnum.DEFAULTVALUE.getRow());

    }

    private BigDecimal toMin(List<CountRulesDTO> listCount, List<InspectionParameterValueDTO> parameterValueSpecimenDTOS) {
        List<BigDecimal> bigDecimals = new ArrayList<>();
        for (InspectionParameterValueDTO parameterInstanceDTO : parameterValueSpecimenDTOS) {
            if (null != parameterInstanceDTO.getParameterValue() && !"".equals(parameterInstanceDTO.getParameterValue())) {
                bigDecimals.add(new BigDecimal(parameterInstanceDTO.getParameterValue()));
            }
        }
        if (!bigDecimals.isEmpty()) {
            log.info("数据min" + bigDecimals);
            return Collections.min(bigDecimals);
        }
        return new BigDecimal(InspectionObjectTypeEnum.DEFAULTVALUE.getRow());
    }


}