package com.hvisions.qms.service;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.EntitySaver;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.item.ExportDefectsDTO;
import com.hvisions.qms.dto.item.InspectionStandardsDefectsDTO;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: InspectionStandardsDefectsService</p >
 * <p>Description: 质检标准缺陷</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/13</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface InspectionStandardsDefectsService extends EntitySaver<ExportDefectsDTO> {

    /**
     * 新增标准缺陷
     *
     * @param inspectionStandardsDefectsDTO 标准缺陷
     * @return 主键
     */
    Integer addStandardsDefects(InspectionStandardsDefectsDTO inspectionStandardsDefectsDTO);

    /**
     * 批量新增标准缺陷
     *
     * @param inspectionStandardsDefectsDTO 标准缺陷列表
     */
    void addBatchStandardsDefects(List<InspectionStandardsDefectsDTO> inspectionStandardsDefectsDTO);

    /**
     * 更新标准缺陷
     *
     * @param inspectionStandardsDefectsDTO 标准缺陷
     * @return 主键
     */
    int updateStandardsDefects(InspectionStandardsDefectsDTO inspectionStandardsDefectsDTO);

    /**
     * 删除标准缺陷、根据Id
     *
     * @param defectId    标准缺陷Id
     * @param standardsId 质检标准iD
     */
    void removeStandardsDefectsByDefectIdAndStandardsId(Integer defectId, Integer standardsId);



    /**
     * 批量删除标准缺陷、根据Id
     *
     * @param ids Id列表
     */
    void deleteBatchStandardsDefectsByIds(List<Integer> ids);

    /**
     * 根据标准缺陷id获取获取标准缺陷
     *
     * @param id 标准缺陷id
     * @return 标准缺陷
     */
    InspectionStandardsDefectsDTO findStandardsDefectsByStandardById(Integer id);

    /**
     * 获取标准缺陷、根据Id列表
     *
     * @param ids 标准缺陷id列表
     * @return 标准缺陷
     */
    List<InspectionStandardsDefectsDTO> findStandardsDefectsByStandardByIds(List<Integer> ids);

    /**
     * 文件导出
     *
     * @param standardId 质检标准ID
     * @return 导出文件
     * @throws IOException            IO异常
     * @throws IllegalAccessException 非法访问异常
     */
    ResultVO<ExcelExportDto> exportDefect(Integer standardId) throws IOException, IllegalAccessException;

    /**
     * 文件导出
     *
     * @param standardId 质检标准ID
     * @return 导出二进制
     * @throws IOException            IO异常
     * @throws IllegalAccessException 非法访问异常
     */
    ResponseEntity<byte[]> exportDefectLink(Integer standardId) throws IOException, IllegalAccessException;

    /**
     * 文件导入
     *
     * @param file 文件信息
     * @param id   质检标准ID
     * @return 文件录入显示
     * @throws IllegalAccessException 非法访问异常
     * @throws ParseException         数据格式转换异常
     * @throws IOException            IO异常
     */
    ImportResult importDefect(MultipartFile file, Integer id) throws IllegalAccessException, ParseException, IOException;

    /**
     * 调整质检标准参数顺序
     *
     * @param index 参数顺序
     */
    void adjustIndex(Map<Integer, Integer> index);
}
