package com.hvisions.qms.controller;

import com.hvisions.qms.dto.standard.*;
import com.hvisions.qms.service.StandardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/standards")
@Slf4j
@Api(description = "质检标准控制器")
public class StandardController {
    private final StandardService standardService;

    @Autowired
    public StandardController(StandardService standardService) {
        this.standardService = standardService;
    }

    /**
     * 新增质检标准
     *
     * @param inspectionStandardDTO 质检标准
     * @return 主键
     */
    @ApiOperation(value = "新增质检标准")
    @PostMapping(value = "/createInspectionStandard")
    public int createInspectionStandard(@RequestBody InspectionStandardDTO inspectionStandardDTO) {
        return standardService.addInspectionStandard(inspectionStandardDTO);
    }

    /**
     * 克隆质检标准
     *
     * @param cloneDTO 克隆信息
     * @return 新的标准主键
     */
    @ApiOperation(value = "克隆质检标准")
    @PostMapping(value = "/cloneInspectionStandard")
    public int cloneInspectionStandard(@RequestBody CloneInspectionStandardDTO cloneDTO) {
        return standardService.cloneInspectionStandard(cloneDTO);
    }


    /**
     * 更新质检标准
     *
     * @param inspectionStandardDTO 质检标准
     */
    @ApiOperation(value = "更新质检标准")
    @PutMapping(value = "/editInspectionStandard")
    public void editInspectionStandard(@RequestBody InspectionStandardDTO inspectionStandardDTO) {
        standardService.updateInspectionStandard(inspectionStandardDTO);
    }

    /**
     * 质检标准生效
     *
     * @param standardId 质检标准id
     */
    @ApiOperation(value = "质检标准生效")
    @GetMapping(value = "/updateInspectionStandardState/{standardId}")
    public void updateInspectionStandardState(@PathVariable Integer standardId) {
        standardService.updateInspectionStandardState(standardId);
    }

    /**
     * 质检标准归档
     *
     * @param standardId 质检标准id
     */
    @ApiOperation(value = "质检标准归档")
    @GetMapping(value = "/updateInspectionStandardArchiveState/{standardId}")
    public void updateInspectionStandardArchiveState(@PathVariable Integer standardId) {
        standardService.updateInspectionStandardArchiveState(standardId);
    }


    /**
     * 删除质检标准
     *
     * @param id 质检标准id
     */
    @ApiOperation(value = "删除质检标准")
    @DeleteMapping(value = "/deleteStandardById/{id}")
    public void deleteStandardById(@PathVariable int id) {
        standardService.deleteStandardById(id);
    }

    /**
     * 批量删除质检标准
     *
     * @param ids 质检标准id列表
     */
    @ApiOperation(value = "批量删除质检标准")
    @DeleteMapping(value = "/deleteInspectionStandardByIds")
    public void deleteInspectionStandardByIds(@RequestBody List<Integer> ids) {
        standardService.deleteInspectionStandardByIds(ids);
    }

    /**
     * 根据多条件分页查询，获取质检标准列表"
     *
     * @param templateDTO 查询条件
     * @return 质检标准分页信息
     */
    @ApiOperation(value = "根据多条件分页查询，获取质检标准列表")
    @PostMapping(value = "/getInspectionStandardPageByConditions")
    public Page<QueryStandardDTO> getInspectionStandardPageByConditions(@RequestBody InspectionStandardQueryDTO templateDTO) {
        return standardService.findInspectionStandardPageByConditions(templateDTO);
    }

    /**
     * 根据质检标准id、获取质检标准结果集"
     *
     * @param id 质检标准id
     * @return 质检标准结果集"
     */
    @ApiOperation(value = "根据质检标准id、获取质检标准结果集")
    @GetMapping(value = "/getInspectionStandardById/{id}")
    public QueryStandardDTO getInspectionStandardById(@PathVariable Integer id) {
        return standardService.findInspectionStandardById(id);

    }

    /**
     * 根据质检标准id列表、获取质检标准结果集"
     *
     * @param ids 质检标准id列表
     * @return 质检标准结果集"
     */
    @ApiOperation(value = "根据质检标准id、获取质检标准结果集")
    @GetMapping(value = "/getInspectionStandardByIds")
    public List<InspectionStandardDTO> getInspectionStandardById(@RequestParam List<Integer> ids) {
        return standardService.findInspectionStandardByIds(ids);
    }


    /**
     * 根据标准编码和版本号获取质检标准
     *
     * @param code    标准编码
     * @param version 版本号获
     * @return 质检标准
     */
    @ApiOperation(value = "根据标准编码和版本号获取质检标准")
    @GetMapping(value = "/getInspectionStandardByCodeAndVersion/{code}/{version}")
    public QueryStandardDTO getInspectionStandardByCodeAndVersion(@PathVariable String code, @PathVariable String version) {
        return standardService.getInspectionStandardByCodeAndVersion(code, version);
    }

    /**
     * 根据检测工位，获取质检标准
     *
     * @param operationId 工艺操作id
     * @param materialId  物料id
     * @param active      版本状态
     * @return 质检标准列表
     */
    @ApiOperation(value = "根据检测工位,物料，获取质检标准")
    @GetMapping(value = "/getStandards")
    public List<InspectionStandardDTO> getStandards(@RequestParam(required = false) Integer operationId,
                                                    @RequestParam(required = false) Integer materialId,
                                                    @RequestParam(required = false) Integer active) {
        return standardService.findStandardsByOperationIdAndMaterialId(operationId, materialId, active);
    }



    /**
     * 根据质检标准id和质检知识库id、导入质检知识库数据"
     *
     * @param standardId 质检标准id
     *  @param knowledgeBaseId 知识库id
     *
     */
    @ApiOperation(value = "根据质检标准id和质检知识库id、导入质检知识库数据")
    @GetMapping(value = "/getInspectionKnowledgeById/{standardId}/{knowledgeBaseId}")
    public void getInspectionKnowledgeById(@PathVariable Integer standardId,@PathVariable Integer knowledgeBaseId) {
         standardService.importInspectionKnowledgeById(standardId,knowledgeBaseId);

    }



    /**
     * 质检标准，左侧物料列表，只查询已经存在质检标准的物料。并且新增的时候显示物料信息。模糊搜索下拉选择。
     * @param pageInfo 物料分页查询
     * @return 查询结果
     */
    @ApiOperation(value = "获取已经存在质检标准的物料",notes = "质检标准，左侧物料列表，只查询已经存在质检标准的物料")
    @PostMapping(value = "/getInspectionStandardMaterial")
    public Page<InspectionStandardMaterialDTO> getInspectionStandardMaterial(@RequestBody InspectionStandardQueryDTO pageInfo) {
       return standardService.getInspectionStandardMaterial( pageInfo);

    }


}