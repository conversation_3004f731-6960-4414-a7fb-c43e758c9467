package com.hvisions.qms.repository;

import com.hvisions.qms.entity.HvQmCountRules;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: CountRuleRepository</p>
 * <p>Description:计算规则仓储层 </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/10/24</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface CountRuleRepository extends JpaRepository<HvQmCountRules, Integer> {
    /**
     * 根据质检标准id查询所有的计算规则
     * @param standardId 质检标准id
     * @return 所有的计算规则
     */
    List<HvQmCountRules> findAllByStandardId(Integer standardId);

}









