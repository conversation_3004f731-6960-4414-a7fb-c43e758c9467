package com.hvisions.qms.controller;

import com.hvisions.qms.dao.StatisticsMapper;
import com.hvisions.qms.dto.defects.*;
import com.hvisions.qms.dto.inspection.*;
import com.hvisions.qms.dto.statistics.InspectionOrderCountParamDTO;
import com.hvisions.qms.dto.statistics.InspectionOrderQueryCountDateDTO;
import com.hvisions.qms.service.InspectionOrderService;
import com.hvisions.qms.utils.ExternalInterface;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <p>Title: InspectionOrderController</p >
 * <p>Description: 质量检测质检单控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/13</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Api(description = "质量检测质检单控制器")
@RestController
@Slf4j
@RequestMapping("/inspection")
public class InspectionOrderController {
    private final InspectionOrderService inspectionOrderService;

    private final StatisticsMapper statisticsMapper;

    private final ExternalInterface api;

    @Autowired
    public InspectionOrderController(InspectionOrderService inspectionOrderService, StatisticsMapper statisticsMapper, ExternalInterface api) {
        this.inspectionOrderService = inspectionOrderService;
        this.statisticsMapper = statisticsMapper;
        this.api = api;
    }

    /**
     * 新建质检单
     *
     * @param checkOrderDTO 质检单DTO
     * @return 新增质检单id
     */
    @PostMapping("/createInspectionOrder")
    @ApiOperation(value = "新建质检单(服务端)")
    public Integer createInspection(@RequestBody InspectionOrderDTO checkOrderDTO) {
        return inspectionOrderService.saveInspection(checkOrderDTO);
    }


    /**
     * 新建质检单
     *
     * @param checkOrderDTO 质检单DTO
     * @return 新增质检单id
     */
    @PostMapping("/createInspectionOrderClient")
    @ApiOperation(value = "新建质检单（客户端）")
    public Integer createInspectionOrderClient(@RequestBody InspectionOrderClientDTO checkOrderDTO) {
        return inspectionOrderService.createInspectionOrderClient(checkOrderDTO);
    }

    /**
     * 更新质检单
     *
     * @param updateDTO 质检单更新对象
     */
    @PutMapping("/updateInspectionOrder")
    @ApiOperation(value = "更新质检单")
    public void updateCheckList(@RequestBody InspectionOrderUpdateDTO updateDTO) {
        inspectionOrderService.updateInspection(updateDTO);
    }

    /**
     * 删除质检单
     *
     * @param id 质检单id
     */
    @DeleteMapping("/deleteInspectionOrderById/{id}")
    @ApiOperation(value = "删除质检单")
    public void deleteCheckListById(@PathVariable Integer id) {
        inspectionOrderService.delete(id);
    }

    /**
     * 根据条件获取质检单
     *
     * @param inspectionOrderQueryDTO 质检单获取DTO
     * @return 分页结果
     */
    @PostMapping("/getByCondition")
    @ApiOperation(value = "多条件获取质检单")
    public Page<ResultInspectionOrderDTO> getByCondition(@RequestBody InspectionOrderQueryDTO inspectionOrderQueryDTO) {
        return inspectionOrderService.findByCondition(inspectionOrderQueryDTO);
    }

    /**
     * 通过工单编码获取质检单
     *
     * @param workOrderCode 工单workOrderCode
     * @return 获取质检的结果
     */
    @GetMapping("/getInspectionOrderByWorkOrderCode/{workOrderCode}")
    @ApiOperation(value = "获取质检单信息、根据工单编码")
    public List<InspectionOrderDetailDTO> getInspectionOrderByWorkOrderCode(@PathVariable String workOrderCode) {
        return inspectionOrderService.findInspectionOrderByWorkOrderCode(workOrderCode);
    }

    /**
     * 根据条件统计质检单
     *
     * @param countDateDTO 质检单统计DTO
     * @return 统计结果
     */

    @PostMapping("/getCountAll")
    @ApiOperation(value = "质检单统计（根据年月日，质检状态结果，及其检测项获取）")
    public InspectionOrderCountParamDTO getCountAll(@RequestBody InspectionOrderQueryCountDateDTO countDateDTO) {
        return inspectionOrderService.checkCountAll(countDateDTO);
    }

    /**
     * 根据id查询质检单
     *
     * @param inspectionId 质检单ID获取
     * @return 质检单信息
     */

    @GetMapping("/getInspectionOrderById/{inspectionId}")
    @ApiOperation(value = "获取质检单信息、根据质检单Id")
    public ResultInspectionOrderDTO getInspectionOrderById(@PathVariable Integer inspectionId) {
        return inspectionOrderService.findInspectionOrderById(inspectionId);
    }

    /**
     * 客户端缺陷检测
     *
     * @param standardId 客户端缺陷检测
     * @return 客户端缺陷检测
     */
    @GetMapping("/findClientDefectByStandardId/{standardId}")
    @ApiOperation(value = "客户端缺陷检测、根据质检标准ID")
    public List<InspectionDefectInstanceDTO> findClientDefectByStandardId(@PathVariable Integer standardId) {
        return api.createSampleData(standardId).getDefects();
    }

    /**
     * 客户端缺陷检测
     *
     * @param inspectionOrderDetailAndDefectDTO 客户端缺陷检测
     */
    @PostMapping("/createInspectionOrderAndInspectionDefect")
    @ApiOperation(value = "客户端创建质检单，并且录入检测缺陷")
    public void createInspectionOrderAndInspectionDefect(@RequestBody InspectionOrderDetailAndDefectDTO inspectionOrderDetailAndDefectDTO) {
        inspectionOrderService.addInspectionOrderAndInspectionDefect(inspectionOrderDetailAndDefectDTO);
    }

    /**
     * 返回给前端 创建样本数据
     *
     * @param standardId 质检标准id
     */
    @GetMapping("/createSampleData/{standardId}")
    @ApiOperation(value = "创建样本数据，根据质检标准Id")
    public ResultInspectionOrderDTO createSampleData(@PathVariable Integer standardId) {
        return api.createSampleData(standardId);
    }

    /**
     * 根据时间段查询工位缺陷数量
     *
     * @param stepDefectQueryDTO 查询条件
     * @return 缺陷信息
     */
    @PostMapping(value = "/getDefectCountByTime")
    @ApiOperation(value = "根据时间段查询工位缺陷数量")
    public List<StepDefectDTO> getDefectCountByTime(@RequestBody StepDefectQueryDTO stepDefectQueryDTO) {
        return inspectionOrderService.getDefectCountByTime(stepDefectQueryDTO);
    }


    /**
     * 查询缺陷数量
     *
     * @param beginTime 起始时间
     * @param endTime   结束时间
     * @return 缺陷数量
     */
    @GetMapping(value = "/getCountByTime")
    @ApiOperation(value = "根据时间段查询缺陷数量")
    public List<DefectCountDTO> getCountByTime(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date beginTime,
                                               @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return inspectionOrderService.getCountByTime(beginTime, endTime);
    }


    /**
     * 查询缺陷数量
     *
     * @param beginTime 起始时间
     * @param endTime   结束时间
     * @return 缺陷数量
     */
    @GetMapping(value = "/getTypeCountByTime")
    @ApiOperation(value = "查询缺陷类型数量")
    public List<DefectTypeCountDTO> getTypeCountByTime(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date beginTime,
                                                       @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return statisticsMapper.getTypeCount(beginTime, endTime);
    }

    /**
     * 查询物料缺陷
     *
     * @param materialDefectQueryDTO 查询条件
     * @return 缺陷信息
     */
    @PostMapping(value = "/getMaterialDefectByQuery")
    @ApiOperation(value = "查询物料缺陷数量 ")
    public List<MaterialDefectCountDTO> getMaterialDefectByQuery(@RequestBody MaterialDefectQueryDTO materialDefectQueryDTO) {
        return inspectionOrderService.getMaterialDefectByQuery(materialDefectQueryDTO);
    }

}