package com.hvisions.qms.service;

import com.hvisions.qms.dto.rule.CountRulesDTO;
import com.hvisions.qms.dto.rule.ResultCountRulesDTO;

import java.util.List;

/**
 * <p>Title: CountingRulesService</p >
 * <p>Description: 计算规则配置</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/10/10</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface CountingRulesService {
    /**
     * 创建计算规则
     *
     * @param countRulesDTO 计算规则
     * @return 主键
     */
    int createCountingRules(CountRulesDTO countRulesDTO);

    /**
     * 创建计算规则
     *
     * @param countRulesDTO 计算规则
     */
    void createCountingRulesList(List<ResultCountRulesDTO> countRulesDTO);

    /**
     * 根据质检标准Id查询计算规则
     *
     * @param standardId 质检标准id
     * @return 计算规则
     */
    List<ResultCountRulesDTO> findCountingRulesByStandardId(Integer standardId);

    /**
     * 删除计算规则
     *
     * @param countId 计算规则id
     */
    void deleteRulesById(Integer countId);

    /**
     * 更新计算规则
     *
     * @param countRulesDTO 计算规则对象
     */
    void updateRule(CountRulesDTO countRulesDTO);
}
