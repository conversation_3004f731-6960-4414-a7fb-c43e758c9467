package com.hvisions.qms.service.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.SerialUtil;
import com.hvisions.qms.dto.inspection.ResultInspectionOrderDTO;
import com.hvisions.qms.dto.repair.RepairDefectsDTO;
import com.hvisions.qms.dto.repair.RepairOrderDTO;
import com.hvisions.qms.dto.repair.RepairOrderPageDTO;
import com.hvisions.qms.dto.repair.ResultRepairOrderDTO;
import com.hvisions.qms.entity.HvQmDefectInstance;
import com.hvisions.qms.entity.HvQmQualityInspectionOrder;
import com.hvisions.qms.entity.HvQmQualityRepairOrder;
import com.hvisions.qms.entity.HvQmRepairStandardsDefects;
import com.hvisions.qms.enums.InspectionExceptionEnum;
import com.hvisions.qms.enums.InspectionStatusEnum;
import com.hvisions.qms.enums.RepairOrderStateEnum;
import com.hvisions.qms.enums.RepairStateEnum;
import com.hvisions.qms.repository.InspectionDefectInstanceRepository;
import com.hvisions.qms.repository.InspectionOrderRepository;
import com.hvisions.qms.repository.InspectionRepairDefectsRepository;
import com.hvisions.qms.repository.RepairOrderRepository;
import com.hvisions.qms.service.InspectionOrderService;
import com.hvisions.qms.service.InspectionReworkService;
import com.hvisions.qms.utils.ExternalInterface;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title: TestParameterInstanceService</p >
 * <p>Description: 返修serviceImpl</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/6/4</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Slf4j
@Service
public class InspectionReworkServiceImpl implements InspectionReworkService {
    private final InspectionRepairDefectsRepository inspectionRepairDefectsRepository;
    private final InspectionOrderRepository inspectionOrderRepository;
    private final RepairOrderRepository repairOrderRepository;
    private final InspectionOrderService orderService;
    private final InspectionDefectInstanceRepository defectInstanceRepository;
    @Autowired
    SerialUtil serialUtil;

    @Autowired
    public InspectionReworkServiceImpl(InspectionRepairDefectsRepository inspectionRepairDefectsRepository,
                                       InspectionOrderRepository inspectionOrderRepository, RepairOrderRepository repairOrderRepository, ExternalInterface api, InspectionOrderService orderService, InspectionDefectInstanceRepository defectInstanceRepository) {
        this.inspectionRepairDefectsRepository = inspectionRepairDefectsRepository;
        this.inspectionOrderRepository = inspectionOrderRepository;
        this.repairOrderRepository = repairOrderRepository;
        this.orderService = orderService;
        this.defectInstanceRepository = defectInstanceRepository;
    }

    @Override
    public void deleteById(Integer id) {
        Optional<HvQmQualityRepairOrder> hvQmQualityRepairOrder = repairOrderRepository.findById(id);
        if (hvQmQualityRepairOrder.isPresent()) {
            if (hvQmQualityRepairOrder.get().getRepairStatus().equals(RepairOrderStateEnum.WAIT_REPAIR.getCode())) {
                throw new BaseKnownException(InspectionExceptionEnum.DO_NOT_DELETE_DURING_REPAIR);
            }
        }
        repairOrderRepository.deleteById(id);
    }

    @Override
    public Page<RepairOrderDTO> findAllRepairPage(RepairOrderPageDTO repairOrderPageDTO) {
        //返修单编码
        String repairOrderCode = repairOrderPageDTO.getRepairOrderCode();
        //返修负责人
        String repairUser = repairOrderPageDTO.getRepairUser();
        //质检单编码
        String inspectionOrderCode = repairOrderPageDTO.getInspectionOrderCode();
        //状态
        Integer repairState = repairOrderPageDTO.getRepairState();
        //开始时间
        Date startTime = repairOrderPageDTO.getStartTime();
        //结束时间
        Date endTime = repairOrderPageDTO.getEndTime();

        Specification<HvQmQualityRepairOrder> specification = (Specification<HvQmQualityRepairOrder>) (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            if (StringUtils.isNotBlank(repairOrderCode)) {
                list.add(criteriaBuilder.like(root.get("repairOrderCode").as(String.class), "%" + repairOrderCode + "%"));
            }
            if (StringUtils.isNotBlank(inspectionOrderCode)) {
                list.add(criteriaBuilder.like(root.get("inspectionOrderCode").as(String.class), "%" + inspectionOrderCode + "%"));
            }
            if (StringUtils.isNotBlank(repairUser)) {
                list.add(criteriaBuilder.like(root.get("repairUser").as(String.class), "%" + repairUser + "%"));
            }
            if (null != repairState) {
                list.add(criteriaBuilder.equal(root.get("repairStatus").as(Integer.class), repairState));
            }
            try {
                if (null != startTime && null != endTime) {
                    list.add(criteriaBuilder.between(root.get("createTime"), startTime, endTime));
                }
            } catch (Exception e) {
                log.error("日期转换异常：" + e.getMessage());
            }
            Predicate[] p = list.toArray(new Predicate[0]);
            return criteriaBuilder.and(p);
        };
        Page<RepairOrderDTO> repairOrderPage = DtoMapper.convertPage(repairOrderRepository.findAll(specification, repairOrderPageDTO.getRequest()), RepairOrderDTO.class);
        //动态获取质检单信息
        repairOrderPage.forEach(repairOrderDTO -> {
            HvQmQualityInspectionOrder inspectionOrder = inspectionOrderRepository.findByInspectionCode(repairOrderDTO.getInspectionOrderCode());
            if (null != inspectionOrder) {
                repairOrderDTO.setMaterialId(inspectionOrder.getMaterialId());
                repairOrderDTO.setMaterialName(inspectionOrder.getMaterialName());
                repairOrderDTO.setStandardId(inspectionOrder.getStandardId());
                repairOrderDTO.setStepName(inspectionOrder.getStepName());
                repairOrderDTO.setWorkOrderCode(inspectionOrder.getWorkOrderCode());
            }

        });
        return repairOrderPage;

    }

    @Override
    public ResultRepairOrderDTO findRepairDefectsById(Integer repairId) {
        Optional<HvQmQualityRepairOrder> hvQmQualityRepairOrder = repairOrderRepository.findById(repairId);
        if (!hvQmQualityRepairOrder.isPresent()) {
            return null;
        }
        HvQmQualityInspectionOrder inspectionOrder = inspectionOrderRepository.findByInspectionCode(hvQmQualityRepairOrder.get().getInspectionOrderCode());
        //动态获取质检单信息
        ResultRepairOrderDTO repairOrderDTO = DtoMapper.convert(hvQmQualityRepairOrder.get(), ResultRepairOrderDTO.class);
        ResultInspectionOrderDTO inspectionOrderById = orderService.findInspectionOrderById(inspectionOrder.getId());
        if (inspectionOrderById.getPositionMoreDTOS() != null) {
            repairOrderDTO.setPositionMoreDTOS(inspectionOrderById.getPositionMoreDTOS());
        }
        if (null != inspectionOrder) {
            repairOrderDTO.setMaterialId(inspectionOrder.getMaterialId());
            repairOrderDTO.setMaterialName(inspectionOrder.getMaterialName());
            repairOrderDTO.setWorkOrderId(inspectionOrder.getWorkOrderId());
            repairOrderDTO.setStepName(inspectionOrder.getStepName());
            repairOrderDTO.setWorkOrderCode(inspectionOrder.getWorkOrderCode());
        }
        return repairOrderDTO;
    }

    /**
     * 创建返修单
     *
     * @param inspectionOrderId 质检单ID
     * @return 返修单Id
     */
    @Override
    public int addRepairOrder(Integer inspectionOrderId) {
        //根据质检单Id、创建返修单
        Optional<HvQmQualityInspectionOrder> inspectionOrder = inspectionOrderRepository.findById(inspectionOrderId);
        if (inspectionOrder.isPresent()) {
            HvQmQualityInspectionOrder qualityInspectionOrder = inspectionOrder.get();
            //判断质检单状态是否为返修中，返修中的质检单不能生成返修单
            if (qualityInspectionOrder.getInspectionStatus().equals(InspectionStatusEnum.IN_REPAIR.getCode())) {
                throw new BaseKnownException(InspectionExceptionEnum.QUALITY_INSPECTION_ORDER_IS_BEING_REPAIRED);
            }
            //判断是否存在返修缺陷、不存在则提示不能创建返修单
            List<HvQmDefectInstance> defects = qualityInspectionOrder.getDefects();
            if (defects.stream()
                .noneMatch(t -> RepairStateEnum.NEED_REPAIR.getCode().equals(t.getRepairState()))) {
                throw new BaseKnownException(InspectionExceptionEnum.REPAIR_DEFECTS_DO_NOT_EXIST);
            }
            HvQmQualityRepairOrder repairOrder = new HvQmQualityRepairOrder();
            repairOrder.setStandardId(inspectionOrder.get().getStandardId());
            repairOrder.setCreateTime(new Date());
            repairOrder.setInspectionOrderCode(qualityInspectionOrder.getInspectionCode());
            repairOrder.setRepairUser(qualityInspectionOrder.getInspectionUser());
            repairOrder.setRepairStatus(RepairOrderStateEnum.WAIT_REPAIR.getCode());
            repairOrder.setRepairOrderCode(serialUtil.getSerialNumber("inspection-repair"));
            repairOrder.setDefects(new ArrayList<>());

            HvQmQualityRepairOrder save = repairOrderRepository.save(repairOrder);
            defects.forEach(defect -> {
                if (RepairStateEnum.NEED_REPAIR.getCode().equals(defect.getRepairState())) {
                    HvQmRepairStandardsDefects repairDefects = DtoMapper.convert(defect, HvQmRepairStandardsDefects.class);
                    repairDefects.setId(null);
                    repairDefects.setIsSave(false);
                    repairDefects.setConfirm(false);
                    repairDefects.setHvQmQualityRepairOrder(save);
                    inspectionRepairDefectsRepository.save(repairDefects);
                }

            });
            //修改质检单状态、返修中
            qualityInspectionOrder.setInspectionStatus(InspectionStatusEnum.IN_REPAIR.getCode());
            inspectionOrderRepository.save(qualityInspectionOrder);
            return save.getId();
        }

        return 0;
    }

    @Override
    public void updateRepairOrder(RepairOrderDTO testReworkDTO) {
        Optional<HvQmQualityRepairOrder> repairOrder = repairOrderRepository.findById(testReworkDTO.getId());
        if (repairOrder.isPresent()) {
            repairOrder.get().setRepairDesc(testReworkDTO.getRepairDesc());
            repairOrder.get().setRepairStatus(testReworkDTO.getRepairStatus());
            repairOrder.get().setRepairUser(testReworkDTO.getRepairUser());
            HvQmQualityRepairOrder hvQmQualityRepairOrder = repairOrderRepository.save(repairOrder.get());
            //判断返修单状态、如果返修完成，更新质检单状态为返修完成
            if (hvQmQualityRepairOrder.getRepairStatus().equals(RepairOrderStateEnum.REPAIR_COMPLETE.getCode())) {
                HvQmQualityInspectionOrder inspectionCode = inspectionOrderRepository.findByInspectionCode(testReworkDTO.getInspectionOrderCode());
                if (!inspectionCode.getInspectionStatus().equals(InspectionStatusEnum.ALREADY_CHECKED.getCode())) {
                    HvQmQualityInspectionOrder inspectionOrder = inspectionOrderRepository.findByInspectionCode(hvQmQualityRepairOrder.getInspectionOrderCode());
                    inspectionOrder.setInspectionStatus(InspectionStatusEnum.ALREADY_REPAIR.getCode());
                    inspectionOrderRepository.save(inspectionOrder);
                    List<HvQmDefectInstance> byInspectionId = defectInstanceRepository.findByInspectionId(inspectionOrder.getId());
                    for (HvQmDefectInstance hvQmDefectInstance : byInspectionId) {
                        if (hvQmDefectInstance.getRepairState().equals(RepairStateEnum.NEED_REPAIR.getCode())) {
                            hvQmDefectInstance.setRepairState(RepairStateEnum.FIXED.getCode());
                        }
                    }
                    defectInstanceRepository.saveAll(byInspectionId);
                }
            }
        }
    }

    @Override
    public int createRepairDefects(RepairDefectsDTO repairStandardsDefectsDTO) {
        Optional<HvQmQualityRepairOrder> repairOrder = repairOrderRepository.findById(repairStandardsDefectsDTO.getRepairOrderId());
        if (!repairOrder.isPresent()) {
            return 0;
        }
        if (RepairOrderStateEnum.REPAIR_COMPLETE.getCode().equals(repairOrder.get().getRepairStatus())) {
            throw new BaseKnownException(InspectionExceptionEnum.REPAIR_HAS_BEEN_COMPLETED_PLEASE_DO_NOT_SUBMIT_AGAIN);
        }
        List<HvQmRepairStandardsDefects> repairStandardsDefects = DtoMapper.convertList(repairStandardsDefectsDTO.getRepairStandardsDefectsDTOS(), HvQmRepairStandardsDefects.class);
        repairStandardsDefects.forEach(repairStandardsDefect -> {
            repairStandardsDefect.setHvQmQualityRepairOrder(repairOrder.get());
            repairStandardsDefect.setIsSave(true);
        });
        repairOrder.get().setRepairDesc(repairStandardsDefectsDTO.getRepairDesc());
        repairOrderRepository.save(repairOrder.get());

        return inspectionRepairDefectsRepository.saveAll(repairStandardsDefects).size();
    }


}