package com.hvisions.qms.service;

import com.hvisions.qms.entity.HvQmInspectionStandard;

/**
 * <p>Title: ValidService</p>
 * <p>Description: 验证服务</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/10/30</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface ValidService {
    /**
     * 验证质检标准是否可修改
     *
     * @param standardId 质检标准id
     * @return 质检标准实体
     */
    HvQmInspectionStandard canBeModify(Integer standardId);
}









