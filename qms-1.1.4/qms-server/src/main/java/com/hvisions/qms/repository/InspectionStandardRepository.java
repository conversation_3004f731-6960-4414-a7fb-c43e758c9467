package com.hvisions.qms.repository;

import com.hvisions.qms.entity.HvQmInspectionStandard;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * <p>Title: InspectionStandardRepository</p >
 * <p>Description: 质检标准持久化层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/16</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface InspectionStandardRepository extends JpaRepository<HvQmInspectionStandard, Integer> {

    /**
     * 根据质检标准编码查找所有质检标准
     *
     * @param code 质检标准编码
     * @return 质检标准准列表
     */
    List<HvQmInspectionStandard> findAllByStandardCode(String code);

    /**
     * 是否已经使用某个质检标准分组
     *
     * @param groupId 质检标准分组id
     * @return 是否存在
     */
    boolean existsByStandardGroupId(Integer groupId);

    /**
     * 分页查询
     *
     * @param specification 查询条件
     * @param request       分页条件
     * @return 分页数据
     */
    Page<HvQmInspectionStandard> findAll(Specification<HvQmInspectionStandard> specification, Pageable request);



    /**
     * 根据质检标准编码和版本查询唯一的质检标准
     *
     * @param code    标准编码
     * @param version 版本号
     * @return 质检标准
     */
    HvQmInspectionStandard findByStandardCodeAndStandardVersion(String code, String version);

    /**
     * 根据质检标准Id和版本查询唯一的质检标准
     *
     * @param id      标准Id
     * @param version 版本号
     * @return 质检标准
     */
    Optional<HvQmInspectionStandard> findByIdAndStandardVersionState(Integer id, Integer version);




    /**
     * 归档更新版本状态
     *
     * @param standardVersionState 质检标准版本状态
     * @param id                   质检标准版本id
     */
    @Modifying
    @Transactional
    @Query("update HvQmInspectionStandard set standardVersionState=:standardVersionState where id=:id")
    void updateVersionState(@Param("standardVersionState") int standardVersionState, @Param("id") int id);


}
