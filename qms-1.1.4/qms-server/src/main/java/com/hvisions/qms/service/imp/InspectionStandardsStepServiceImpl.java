package com.hvisions.qms.service.imp;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.qms.dto.step.InspectionStandardsStepDTO;
import com.hvisions.qms.dto.step.QueryStandardsStepDTO;
import com.hvisions.qms.entity.HvQmInspectionStandard;
import com.hvisions.qms.entity.HvQmInspectionStandardsStep;
import com.hvisions.qms.enums.InspectionStandardExceptionEnum;
import com.hvisions.qms.repository.InspectionStandardRepository;
import com.hvisions.qms.repository.InspectionStandardsStepRepository;
import com.hvisions.qms.service.InspectionStandardsStepService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>Title: InspectionStandardsStepServiceImpl</p >
 * <p>Description: 质量标准检测与工位检测绑定实现类</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/10/10</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
public class InspectionStandardsStepServiceImpl implements InspectionStandardsStepService {

    private final InspectionStandardsStepRepository stepRepository;
    private final InspectionStandardRepository standardRepository;

    @Autowired
    public InspectionStandardsStepServiceImpl(InspectionStandardsStepRepository stepRepository, InspectionStandardRepository standardRepository) {
        this.stepRepository = stepRepository;
        this.standardRepository = standardRepository;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public int addingInspectionStandards(Integer stepId, String stepName, Integer standardId) {

        HvQmInspectionStandardsStep inspectionStandardsStep = stepRepository.findByStandardsIdAndStepId(standardId, stepId);
        if (null != inspectionStandardsStep) {
            throw new BaseKnownException(InspectionStandardExceptionEnum.STEP_ALREADY_EXIST);
        }
        Optional<HvQmInspectionStandard> byId =
                standardRepository.findById(standardId);
        if (byId.isPresent()) {
            if (!byId.get().getStandardVersionState().equals(2)) {
                throw new BaseKnownException(InspectionStandardExceptionEnum.STANDARD_NOT_EFFECTIVE);
            }
        }
        HvQmInspectionStandardsStep standardsStep = new HvQmInspectionStandardsStep();
        standardsStep.setStepId(stepId);
        standardsStep.setStepName(stepName);
        standardsStep.setStandardsId(standardId);
        return stepRepository.save(standardsStep).getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unbindingInspectionStandards(Integer stepId, Integer standardId) {
        stepRepository.deleteAllByStepIdAndStandardsId(stepId, standardId);
    }

    @Override
    public List<InspectionStandardsStepDTO> getBindStandardStepList(QueryStandardsStepDTO stepDTO) {
        Specification<HvQmInspectionStandardsStep> specification = (Specification<HvQmInspectionStandardsStep>) (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            if (StringUtils.isNotBlank(stepDTO.getStepName())) {
                list.add(criteriaBuilder.like(root.get("stepName").as(String.class), "%" + stepDTO.getStepName() + "%"));
            }
            Predicate[] p = list.toArray(new Predicate[0]);
            return criteriaBuilder.and(p);
        };
        List<HvQmInspectionStandardsStep> inspectionStandards = stepRepository.findAll(specification);
        List<InspectionStandardsStepDTO> ts = DtoMapper.convertList(inspectionStandards, InspectionStandardsStepDTO.class);
        ts.forEach(t -> {
            t.setStandardsId(null);
            t.setId(null);
        });
        return ts.stream().distinct().collect(Collectors.toList());
    }
}
