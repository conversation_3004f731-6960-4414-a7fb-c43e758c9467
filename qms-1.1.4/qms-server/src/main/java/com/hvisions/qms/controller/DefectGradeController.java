package com.hvisions.qms.controller;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.qms.dto.DefectGradeDTO;
import com.hvisions.qms.dto.DefectGradeQueryDTO;
import com.hvisions.qms.entity.HvQmQualityDefectGrade;
import com.hvisions.qms.enums.DefectExceptionEnum;
import com.hvisions.qms.service.DefectGradeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/defectGrade")
@Slf4j
@Api(description = "缺陷等级接口")
public class DefectGradeController {

    @Autowired
    private DefectGradeService defectGradeService;


    /**
     * 新增缺陷等级信息
     *
     * @param defectGradeDTO 缺陷等级信息
     * @return 缺陷等级信息id
     */
    @ApiOperation(value = "新增缺陷等级信息")
    @RequestMapping(value = "/addDefectGrade", method = RequestMethod.POST)
    public int add(@RequestBody DefectGradeDTO defectGradeDTO) {

        return defectGradeService.addDefectGrade(defectGradeDTO);
    }

    /**
     * 修改缺陷等级信息
     *
     * @param defectGradeDTO 缺陷等级信息
     * @return 缺陷等级信息id
     */
    @ApiOperation(value = "修改缺陷等级信息")
    @RequestMapping(value = "/updateDefectGrade", method = RequestMethod.PUT)
    public int update(@RequestBody DefectGradeDTO defectGradeDTO) {
        HvQmQualityDefectGrade grade = defectGradeService.getByName(defectGradeDTO.getName());
        if(grade!=null&&!grade.getId().equals(defectGradeDTO.getId())){
            throw new BaseKnownException(DefectExceptionEnum.GRADE_NAME_EXIST);
        }
        return defectGradeService.updateDefectGrade(defectGradeDTO);
    }

    /**
     * 根据id删除缺陷等级信息
     *
     * @param id 缺陷等级信息id
     */
    @ApiOperation(value = "删除缺陷等级信息")
    @RequestMapping(value = "/deleteDefectGradeById/{id}", method = RequestMethod.DELETE)
    public void delete(@PathVariable Integer id) {
        defectGradeService.deleteDefectGradeById(id);
    }

    /**
     * 分页查询
     *
     * @param defectGradeQueryDTO 缺陷等级信息分页查询条件
     * @return 分页信息
     */
    @PostMapping(value = "/getDefectGradePageQuery")
    @ApiOperation(value = "缺陷等级分页查询")
    public Page<DefectGradeDTO> getDefectGradePageQuery(@RequestBody DefectGradeQueryDTO defectGradeQueryDTO) {
        return defectGradeService.getDefectGradePageQuery(defectGradeQueryDTO);
    }

    /**
     * 获取所有缺陷等级
     * @return 缺陷等级列表
     */
    @ApiOperation(value = "获取所有缺陷等级")
    @RequestMapping(value = "/getAllDefectGrade", method = RequestMethod.GET)
    public List<DefectGradeDTO> getAllDefectGrade() {
        return defectGradeService.getAllDefectGrade();
    }

    /**
     * 根据id获取缺陷等级信息
     * @param id 主键id
     * @return 缺陷等级信息
     */
    @ApiOperation(value = "根据id获取缺陷等级")
    @RequestMapping(value = "/getGradeById/{id}",method = RequestMethod.GET)
    public DefectGradeDTO getGradeById(@PathVariable Integer id){
        return  defectGradeService.getGradeById(id);
    }

    /**
     * 根据编码查询获取等级
     * @param code 主键id
     * @return 类型信息
     */
    @ApiOperation(value = "根据code获取类型信息")
    @RequestMapping(value = "/getDefectGradeByCode/{code}", method = RequestMethod.GET)
    public DefectGradeDTO getDefectGradeByCode(@PathVariable String code) {
        return defectGradeService.getDefectGradeByCode(code);
    }


}

























