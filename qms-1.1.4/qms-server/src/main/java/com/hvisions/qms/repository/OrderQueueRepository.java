package com.hvisions.qms.repository;

import com.hvisions.qms.entity.HvQmOrderQueue;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <p>Title: OrderQueueRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/1</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface OrderQueueRepository extends JpaRepository<HvQmOrderQueue, Integer> {


    List<HvQmOrderQueue> getHvQmOrderQueueByStepIdAndCreateTimeAfter(int stepId, Date createTime);

    List<HvQmOrderQueue> getHvQmOrderQueueByStepId(int stepId);
}