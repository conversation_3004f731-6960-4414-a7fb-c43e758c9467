package com.hvisions.qms.repository;

import com.hvisions.qms.entity.HvQmPosition;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: PositionRepository</p >
 * <p>Description: 区域Dao层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/16</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface PositionRepository extends JpaRepository<HvQmPosition, Integer> {

    /**
     * 根据名称查询
     *
     * @param name 名称
     * @return 列表
     */
    List<HvQmPosition> findByName(String name);

    /**
     * 查询区域对象
     * @param id 区域id
     * @return 区域对象
     */
    @Query(value = "select p from HvQmPosition p where p.id=?1")
    HvQmPosition findByIdOne(Integer id);

}
