package com.hvisions.qms.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.item.InspectionStandardsParametersDTO;
import com.hvisions.qms.service.InspectionStandardsParametersService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: StandardsParametersController</p >
 * <p>Description: 质量标准参数控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/10/9</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/standardsParameters")
@Slf4j
@Api(description = "质检标准检测项参数控制器")
public class StandardsParametersController {

    private final InspectionStandardsParametersService inspectionStandardsParameters;

    @Autowired
    public StandardsParametersController(InspectionStandardsParametersService inspectionStandardsParameters) {
        this.inspectionStandardsParameters = inspectionStandardsParameters;
    }

    /**
     * 新增标准参数
     *
     * @param inspectionStandardsParametersDTO 标准参数
     * @return 主键
     */
    @ApiOperation(value = "新增标准参数")
    @PostMapping(value = "/createStandardsParameter")
    public int createStandardsParameter(@RequestBody InspectionStandardsParametersDTO inspectionStandardsParametersDTO) {
        return inspectionStandardsParameters.addStandardsParameter(inspectionStandardsParametersDTO);
    }

    /**
     * 批量新增标准参数
     *
     * @param inspectionStandardsParametersDTOS 标准参数
     */
    @ApiOperation(value = "批量新增标准参数")
    @PostMapping(value = "/createBatchStandardsParameter")
    public void createBatchStandardsParameter(@RequestBody List<InspectionStandardsParametersDTO> inspectionStandardsParametersDTOS) {
        inspectionStandardsParameters.addBatchStandardsParameter(inspectionStandardsParametersDTOS);
    }


    /**
     * 删除标准参数、根据Id
     *
     * @param id 主键
     */
    @ApiOperation(value = "删除标准参数、根据Id")
    @DeleteMapping(value = "/deleteStandardsParametersById/{id}")
    public void deleteStandardsParametersById(@PathVariable Integer id) {
        inspectionStandardsParameters.removeStandardsParametersById(id);
    }

    /**
     * 批量删除标准参数、根据Id列表
     *
     * @param ids id列表
     */
    @ApiOperation(value = " 批量删除标准参数、根据Id列表")
    @DeleteMapping(value = "/deleteStandardsParametersByIds")
    public void deleteStandardsParametersByIds(@RequestBody List<Integer> ids) {
        inspectionStandardsParameters.removeStandardsParametersByIds(ids);
    }

    /**
     * 更新标准参数
     *
     * @param inspectionStandardsParametersDTO 标准参数
     * @return 标准参数
     */
    @ApiOperation(value = "更新标准参数")
    @PutMapping(value = "/editStandardsParameters")
    public int editStandardsParameters(@RequestBody InspectionStandardsParametersDTO inspectionStandardsParametersDTO) {
        return inspectionStandardsParameters.updateStandardsParameters(inspectionStandardsParametersDTO);
    }

    /**
     * 获取标准参数、根据标准参数id
     *
     * @param id 标准参数id
     * @return 标准参数
     */
    @ApiOperation(value = " 获取标准参数、根据标准参数id")
    @GetMapping(value = "/getStandardsParametersByStandardById/{id}")
    public InspectionStandardsParametersDTO getStandardsParametersByStandardById(@PathVariable Integer id) {
        return inspectionStandardsParameters.findStandardsParametersByStandardById(id);
    }

    /**
     * 获取标准参数、根据标准参数Id列表
     *
     * @param ids 获取标准参数、根据标准参数Id列表
     * @return 标准参数Id列表
     */
    @ApiOperation(value = "获取标准参数、根据标准参数Id列表")
    @PostMapping(value = "/getStandardsParametersByStandardByIds")
    public List<InspectionStandardsParametersDTO> getStandardsParametersByStandardByIds(@RequestBody List<Integer> ids) {
        return inspectionStandardsParameters.findStandardsParametersByStandardByIds(ids);
    }

    /**
     * 获取标准参数、根据编码列表
     *
     * @param codes 编码列表
     * @return 标准参数
     */
    @ApiOperation(value = "获取标准参数、根据编码列表")
    @PostMapping(value = "/getStandardsParametersByStandardByCodes")
    public List<InspectionStandardsParametersDTO> getStandardsParametersByStandardByCodes(@RequestBody List<String> codes) {
        return inspectionStandardsParameters.findStandardsParametersByStandardByCodes(codes);
    }

    /**
     * 获取标准参数、根据编码列表
     *
     * @param standardId 质检标准id
     * @return 标准参数
     */
    @ApiOperation(value = "获取标准参数、根据质检标准id")
    @GetMapping(value = "/getStandardsParametersByStandardId/{standardId}")
    public List<InspectionStandardsParametersDTO> getStandardsParametersByStandardId(@PathVariable int standardId) {
        return inspectionStandardsParameters.getStandardsParametersByStandardId(standardId);
    }

    /**
     * 导入参数信息
     *
     * @param file       参数文档
     * @param standardId 质检标准ID
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @PostMapping(value = "/importParameterLink")
    @ApiOperation(value = "导入参数信息(参数编码存在则更新，不存在则新增)")
    public ImportResult importParameter(@RequestParam("file") MultipartFile file, @RequestParam("standardId") Integer standardId) throws IllegalAccessException, ParseException, IOException {
        return inspectionStandardsParameters.importParameter(file, standardId);
    }

    /**
     * 质检标准参数信息
     *
     * @param standardId 质检标准ID
     * @return 参数信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportParameter/{standardId}")
    @ApiOperation(value = "参数、二进制数据导出")
    public ResultVO<ExcelExportDto> exportParameter(@PathVariable Integer standardId) throws IOException, IllegalAccessException {
        return inspectionStandardsParameters.exportParameter(standardId);
    }

    /**
     * 质检标准参数信息
     *
     * @param standardId 质检标准ID
     * @return 参数信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportParameterLink/{standardId}")
    @ApiOperation(value = "参数、超链接导出")
    public ResponseEntity<byte[]> exportParameterLink(@PathVariable Integer standardId) throws IOException, IllegalAccessException {
        return inspectionStandardsParameters.exportParameterLink(standardId);
    }

    /**
     * 调整质检标准参数顺序
     *
     * @param index 参数顺序
     */
    @PutMapping(value = "/adjustIndex")
    @ApiOperation(value = "调整质检标准参数顺序")
    public void adjustIndex(@RequestBody Map<Integer, Integer> index) {
        inspectionStandardsParameters.adjustIndex(index);
    }
}