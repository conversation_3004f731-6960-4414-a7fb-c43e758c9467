package com.hvisions.qms.service.imp;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.knowledge.info.ExportKnowledgeDefectsDTO;
import com.hvisions.qms.dto.knowledge.info.InspectionKnowledgeDefectsDTO;
import com.hvisions.qms.entity.HvQmInspectionKnowledgeBase;
import com.hvisions.qms.entity.HvQmInspectionKnowledgeDefects;
import com.hvisions.qms.enums.InspectionKnowledgeBaseExceptionEnum;
import com.hvisions.qms.enums.InspectionStandardExceptionEnum;
import com.hvisions.qms.repository.InspectionKnowledgeBaseRepository;
import com.hvisions.qms.repository.InspectionKnowledgeDefectsRepository;
import com.hvisions.qms.service.InspectionKnowledgeDefectsService;
import com.hvisions.qms.service.KnowledgeDefectConst;
import com.hvisions.qms.service.ValidService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>Title: InspectionKnowledgeBaseDefectsServiceImpl</p >
 * <p>Description: 质量标准检测缺陷实现类</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/10/09</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
public class InspectionKnowledgeDefectsServiceImpl implements InspectionKnowledgeDefectsService {


    @Autowired
    InspectionKnowledgeDefectsRepository knowledgeDefectsRepository;
    @Autowired
    InspectionKnowledgeBaseRepository knowledgeBaseRepository;

    @Autowired
    ValidService validService;

    @Autowired
    RemoteApiClient remoteApiClient;

    private Integer inspectionKnowledgeBaseId;


    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addKnowledgeBaseDefects(InspectionKnowledgeDefectsDTO InspectionKnowledgeDefectsDTO) {
        Optional<HvQmInspectionKnowledgeBase> inspectionKnowledgeBase = knowledgeBaseRepository.findById(InspectionKnowledgeDefectsDTO.getInspectionKnowledgeBaseId());
        //如果是新增，并且检查标准中已经有对应编码的缺陷。则直接跳过参数
        if (!inspectionKnowledgeBase.isPresent()) {
            throw new BaseKnownException(InspectionKnowledgeBaseExceptionEnum.INSPECTION_KNOWLEDGE_BASE_DOES_NOT_EXIST);
        }
        if (null == InspectionKnowledgeDefectsDTO.getId() &&
                inspectionKnowledgeBase.get().getHvQmInspectionKnowledgeDefects().stream().anyMatch(t -> t.getDefectsCode().equals(InspectionKnowledgeDefectsDTO.getDefectsCode()))) {
            throw new BaseKnownException(InspectionStandardExceptionEnum.DUPLICATE_ENTRY_OF_DEFECT_CODE);
        }
        HvQmInspectionKnowledgeDefects hvQmInspectionKnowledgeBaseDefects = DtoMapper.convert(InspectionKnowledgeDefectsDTO, HvQmInspectionKnowledgeDefects.class);

        hvQmInspectionKnowledgeBaseDefects.setDefectGradeCode(remoteApiClient.findByDefectGradeId(InspectionKnowledgeDefectsDTO.getDefectGradeId()).getCode());
        hvQmInspectionKnowledgeBaseDefects.setDefectTypeCode(remoteApiClient.findByDefectTypeId(InspectionKnowledgeDefectsDTO.getDefectTypeId()).getCode());

        hvQmInspectionKnowledgeBaseDefects.setHvQmInspectionKnowledgeBase(inspectionKnowledgeBase.get());

        return knowledgeDefectsRepository.save(hvQmInspectionKnowledgeBaseDefects).getId();
    }

    /**
     * {@inheritDoc}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addBatchKnowledgeBaseDefects(List<InspectionKnowledgeDefectsDTO> InspectionKnowledgeDefectsDTO) {
        List<String> codes = new ArrayList<>();
        for (InspectionKnowledgeDefectsDTO KnowledgeBaseDefectsDTO : InspectionKnowledgeDefectsDTO) {
            if (codes.stream().anyMatch(t -> t.equals(KnowledgeBaseDefectsDTO.getDefectsCode()))) {
                continue;
            }
            addKnowledgeBaseDefects(KnowledgeBaseDefectsDTO);
            codes.add(KnowledgeBaseDefectsDTO.getDefectsCode());
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int updateKnowledgeBaseDefects(InspectionKnowledgeDefectsDTO InspectionKnowledgeDefectsDTO) {
        return addKnowledgeBaseDefects(InspectionKnowledgeDefectsDTO);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeKnowledgeBaseDefectsById(Integer id) {
        Optional<HvQmInspectionKnowledgeDefects> defects = knowledgeDefectsRepository.findById(id);
        if (defects.isPresent()) {
            knowledgeDefectsRepository.deleteById(id);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void deleteBatchKnowledgeBaseDefectsByIds(List<Integer> ids) {
        for (Integer id : ids) {
            removeKnowledgeBaseDefectsById(id);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public InspectionKnowledgeDefectsDTO findKnowledgeBaseDefectsByStandardById(Integer id) {
        return knowledgeDefectsRepository.findById(id).map(defects -> DtoMapper.convert(defects, InspectionKnowledgeDefectsDTO.class)).orElse(null);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<InspectionKnowledgeDefectsDTO> findKnowledgeBaseDefectsByStandardByIds(List<Integer> ids) {
        List<InspectionKnowledgeDefectsDTO> defects = new ArrayList<>();
        for (Integer id : ids) {
            defects.add(findKnowledgeBaseDefectsByStandardById(id));
        }
        return defects;
    }
    @Override
    public ResultVO<ExcelExportDto> exportDefect(Integer standardId) throws IOException, IllegalAccessException {

        ResponseEntity<byte[]> result = exportDefectLink(standardId);
        if (null==result){
            return null;
        }
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName(KnowledgeDefectConst.DEFECT_FILE_NAME);
        return ResultVO.success(excelExportDto);

    }
    @Override
    public ResponseEntity<byte[]> exportDefectLink(Integer standardId) throws IOException, IllegalAccessException {
        Optional<HvQmInspectionKnowledgeBase> inspectionKnowledgeBase = knowledgeBaseRepository.findById(standardId);
        if (inspectionKnowledgeBase.isPresent()) {
            List<ExportKnowledgeDefectsDTO> ExportKnowledgeDefectsDTOS = DtoMapper.convertList(inspectionKnowledgeBase.get().getHvQmInspectionKnowledgeDefects(), ExportKnowledgeDefectsDTO.class);
            ResponseEntity<byte[]> result = ExcelUtil.generateImportFile(ExportKnowledgeDefectsDTOS
                    , KnowledgeDefectConst.DEFECT_FILE_NAME, ExportKnowledgeDefectsDTO.class);
            return result;

        }
        return null;
    }
    @Override
    public ImportResult importDefect(MultipartFile file, Integer id) throws IllegalAccessException, ParseException, IOException {
        inspectionKnowledgeBaseId = id;
        return ExcelUtil.importEntity(file, ExportKnowledgeDefectsDTO.class, this);
    }

    @Override
    public void saveOrUpdate(ExportKnowledgeDefectsDTO importDefectDTO) {
        if (StringUtils.isBlank(importDefectDTO.getDefectsCode())){
            throw new BaseKnownException(InspectionStandardExceptionEnum.DEFECT_ENCODING_CANNOT_BE_NULL);
        }
        HvQmInspectionKnowledgeDefects inspectionStandardDefect = DtoMapper.convert(importDefectDTO, HvQmInspectionKnowledgeDefects.class);
        Optional<HvQmInspectionKnowledgeBase> inspectionKnowledgeBase = knowledgeBaseRepository.findById(inspectionKnowledgeBaseId);
        if (inspectionKnowledgeBase.isPresent()) {
            List<HvQmInspectionKnowledgeDefects> defects = inspectionKnowledgeBase.get().getHvQmInspectionKnowledgeDefects();
            defects.forEach(defect -> {
                if (defect.getDefectsCode().equals(inspectionStandardDefect.getDefectsCode())) {
                    defect.setDefectsCode(inspectionStandardDefect.getDefectsCode());
                    defect.setDefectTypeCode(inspectionStandardDefect.getDefectTypeCode());
                    defect.setDefectGradeCode(inspectionStandardDefect.getDefectGradeCode());
                    defect.setDefectGradeId(remoteApiClient.findByDefectGradeCode(inspectionStandardDefect.getDefectGradeCode()).getId());
                    defect.setDefectTypeId(remoteApiClient.findByDefectTypeCode(inspectionStandardDefect.getDefectTypeCode()).getId());
                    inspectionStandardDefect.setDefectGradeName(remoteApiClient.findByDefectGradeCode(inspectionStandardDefect.getDefectGradeCode()).getName());
                    inspectionStandardDefect.setDefectTypeName(remoteApiClient.findByDefectTypeCode(inspectionStandardDefect.getDefectTypeCode()).getName());
                    defect.setDefectsName(inspectionStandardDefect.getDefectsName());
                    defect.setDefectTypeName(inspectionStandardDefect.getDefectTypeName());
                    defect.setDefectGradeName(inspectionStandardDefect.getDefectGradeName());
                    defect.setDefectPoints(inspectionStandardDefect.getDefectPoints());
                    defect.setDefectPosition(inspectionStandardDefect.getDefectPosition());
                    defect.setDefectsUnit(inspectionStandardDefect.getDefectsUnit());
                    defect.setDefectsDescription(inspectionStandardDefect.getDefectsDescription());
                    defect.setHvQmInspectionKnowledgeBase(inspectionKnowledgeBase.get());
                    defect.setUpdateTime(new Date());

                    knowledgeDefectsRepository.save(defect);
                }
            });
            List<HvQmInspectionKnowledgeDefects> collect = defects.stream().filter(defect -> defect.getDefectsCode().equals(inspectionStandardDefect.getDefectsCode())).collect(Collectors.toList());
            if (collect.isEmpty()) {
                inspectionStandardDefect.setCreateTime(new Date());
                inspectionStandardDefect.setDefectGradeId(remoteApiClient.findByDefectGradeCode(inspectionStandardDefect.getDefectGradeCode()).getId());
                inspectionStandardDefect.setDefectTypeId(remoteApiClient.findByDefectTypeCode(inspectionStandardDefect.getDefectTypeCode()).getId());
                inspectionStandardDefect.setDefectGradeName(remoteApiClient.findByDefectGradeCode(inspectionStandardDefect.getDefectGradeCode()).getName());
                inspectionStandardDefect.setDefectTypeName(remoteApiClient.findByDefectTypeCode(inspectionStandardDefect.getDefectTypeCode()).getName());
                inspectionStandardDefect.setHvQmInspectionKnowledgeBase(inspectionKnowledgeBase.get());
                knowledgeDefectsRepository.save(inspectionStandardDefect);
            }
        }
    }

}
