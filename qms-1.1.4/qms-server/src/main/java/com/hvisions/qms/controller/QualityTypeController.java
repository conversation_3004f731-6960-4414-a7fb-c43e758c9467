package com.hvisions.qms.controller;

import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.qms.dto.DefectTypeDTO;
import com.hvisions.qms.dto.DefectTypeQueryDTO;
import com.hvisions.qms.entity.HvQmQualityDefectType;
import com.hvisions.qms.enums.DefectExceptionEnum;
import com.hvisions.qms.service.DefectTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/defectType")
@Slf4j
@Api(description = "缺陷类型接口")
public class QualityTypeController {

    @Autowired
    private DefectTypeService defectTypeService;
    /**
     * 新增缺陷类型信息
     *
     * @param defectTypeDTO 缺陷类型信息
     * @return 缺陷类型信息id
     */
    @ApiOperation(value = "新增缺陷类型信息")
    @RequestMapping(value = "/addDefectType", method = RequestMethod.POST)
    public int add(@RequestBody DefectTypeDTO defectTypeDTO) {
        //判断名称是否重复
        HvQmQualityDefectType dutyDepartment = defectTypeService.getByName(defectTypeDTO.getName());
        if (dutyDepartment != null) {
            throw new BaseKnownException(DefectExceptionEnum.DEFECT_TYPE_NAME_EXIST);
        }
        return defectTypeService.addDefectType(defectTypeDTO);
    }

    /**
     * 修改缺陷类型信息
     *
     * @param defectTypeDTO 缺陷类型信息
     * @return 缺陷类型信息id
     */
    @ApiOperation(value = "修改缺陷类型信息")
    @RequestMapping(value = "/updateDefectType", method = RequestMethod.PUT)
    public int update(@RequestBody DefectTypeDTO defectTypeDTO) {
        //判断名称是否重复
        HvQmQualityDefectType dutyDepartment = defectTypeService.getByName(defectTypeDTO.getName());
        if (dutyDepartment != null && !dutyDepartment.getId().equals(defectTypeDTO.getId())) {
            throw new BaseKnownException(DefectExceptionEnum.DEFECT_TYPE_NAME_EXIST);
        }
        return defectTypeService.updateDefectType(defectTypeDTO);
    }

    /**
     * 根据id删除缺陷类型信息
     *
     * @param id 缺陷类型信息id
     */
    @ApiOperation(value = "删除缺陷类型信息")
    @RequestMapping(value = "/deleteDefectTypeById/{id}", method = RequestMethod.DELETE)
    public void delete(@PathVariable Integer id) {
        defectTypeService.deleteDefectTypeById(id);
    }

    /**
     * 分页查询
     *
     * @param defectGradeQueryDTO 缺陷类型信息分页查询条件
     * @return 分页信息
     */
    @PostMapping(value = "/getDefectTypePageQuery")
    @ApiOperation(value = "缺陷类型分页查询")
    public Page<DefectTypeDTO> getDefectTypePageQuery(@RequestBody DefectTypeQueryDTO defectGradeQueryDTO) {
        return defectTypeService.getDefectTypePageQuery(defectGradeQueryDTO);
    }

    /**
     * 获取所有类型信息
     *
     * @return 类型信息列表
     */
    @ApiOperation(value = "获取所有缺陷类型")
    @RequestMapping(value = "/getAllDefectType", method = RequestMethod.GET)
    public List<DefectTypeDTO> getAllDefectType() {
        return defectTypeService.getAllDefectType();
    }

    /**
     * 根据id获取类型信息
     * @param id 主键id
     * @return 类型信息
     */
    @ApiOperation(value = "根据id获取类型信息")
    @RequestMapping(value = "/getDutyDepartmentById/{id}", method = RequestMethod.GET)
    public DefectTypeDTO getDutyDepartmentById(@PathVariable Integer id) {
        return defectTypeService.getById(id);
    }

    /**
     * 根据编码查询获取类型
     * @param code 主键id
     * @return 类型信息
     */
    @ApiOperation(value = "根据code获取类型信息")
    @RequestMapping(value = "/getDefectTypeByCode/{code}", method = RequestMethod.GET)
    public DefectTypeDTO getDefectTypeByCode(@PathVariable String code) {
        return defectTypeService.getDefectTypeByCode(code);
    }

}

























