package com.hvisions.qms.repository;

import com.hvisions.qms.entity.HvQmQualityRepairOrder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * <p>Title: RepairOrderRepository</p >
 * <p>Description: 返修单repository</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/16</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface RepairOrderRepository extends JpaRepository<HvQmQualityRepairOrder, Integer> {




    /**
     * 分页查询
     *
     * @param sec SQL条件拼接语句
     * @param pageable 分页
     * @return 获取返修单，根据多条件查询分页列表
     */
    Page<HvQmQualityRepairOrder> findAll(Specification<HvQmQualityRepairOrder> sec, Pageable pageable);
}
