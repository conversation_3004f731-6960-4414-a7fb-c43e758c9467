package com.hvisions.qms.service;

import com.hvisions.qms.dto.DefectGradeDTO;
import com.hvisions.qms.dto.DefectGradeQueryDTO;
import com.hvisions.qms.entity.HvQmQualityDefectGrade;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <p>Title: DefectGradeService</p>
 * <p>Description: 缺陷级别</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/12/30</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface DefectGradeService {


    /**
     * 新增缺陷等级信息
     *
     * @param defectGradeDTO 缺陷等级信息
     * @return 缺陷等级信息id
     */
    Integer addDefectGrade(DefectGradeDTO defectGradeDTO);

    /**
     * 修改缺陷等级信息
     *
     * @param defectGradeDTO 缺陷等级信息
     * @return 缺陷等级信息id
     */
    Integer updateDefectGrade(DefectGradeDTO defectGradeDTO);

    /**
     * 根据id删除缺陷等级信息
     *
     * @param id 缺陷等级信息id
     */
    void deleteDefectGradeById(Integer id);

    /**
     * 缺陷等级信息分页查询
     *
     * @param defectGradeQueryDTO 缺陷等级信息分页查询条件
     * @return 分页查询结果列表
     */
    Page<DefectGradeDTO> getDefectGradePageQuery(DefectGradeQueryDTO defectGradeQueryDTO);

    /**
     * 获取所有缺陷等级
     *
     * @return 缺陷等级信息列表
     */
    List<DefectGradeDTO> getAllDefectGrade();

    /**
     * 根据等级名称获取等级对象
     *
     * @param name 名称
     * @return 等级对象
     */
    HvQmQualityDefectGrade getByName(String name);

    /**
     * 根据id获取缺陷等级信息
     *
     * @param id 主键id
     * @return 缺陷等级信息
     */
    DefectGradeDTO getGradeById(Integer id);

    /**
     * 根据等级编码查询等级
     *
     * @param code 等级编码
     * @return 等级
     */
    DefectGradeDTO getDefectGradeByCode(String code);
}
