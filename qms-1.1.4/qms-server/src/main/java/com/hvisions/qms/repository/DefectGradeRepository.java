package com.hvisions.qms.repository;

import com.hvisions.qms.entity.HvQmQualityDefectGrade;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;


/**
 * <AUTHOR>
 */
@Repository
public interface DefectGradeRepository extends JpaRepository<HvQmQualityDefectGrade, Integer> {

    /**
     * 根据等级名称获取等级对象
     *
     * @param name 名称
     * @return 等级对象
     */
    HvQmQualityDefectGrade findByName(String name);

    HvQmQualityDefectGrade findByCode(String code);
}
