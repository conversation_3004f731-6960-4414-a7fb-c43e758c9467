package com.hvisions.qms.service;

import com.hvisions.qms.dto.position.PositionDTO;
import com.hvisions.qms.dto.position.PositionMoreDTO;
import com.hvisions.qms.entity.HvQmPosition;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <p>Title: PositionService</p >
 * <p>Description: 区域服务层接口</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/16</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface PositionService {

    /**
     * 保存区域
     *
     * @param positionDTO 区域DTO
     * @return 影戏行数
     */

    int save(PositionDTO positionDTO);


    /**
     * 根据区域查询区域内容
     *
     * @param dto 区域dto
     * @return 位置点
     */
    List<PositionDTO> findByName(PositionDTO dto);

    /**
     * 根据区域id、查询区域
     *
     * @param id 根据区域id
     * @return 查询区域
     */
    Optional<HvQmPosition> findById(int id);

    /**
     * 根据模板id查询区域、以及区域下的位置点
     *
     * @param id 根据模板id查询区域
     * @return 以及区域下的位置点
     */
    List<PositionMoreDTO> findByTemplateId(int id);

    /**
     * 根据id查询
     *
     * @param id 主键
     * @return 点信息
     */
    PositionMoreDTO findPositionMoreDTOById(int id);

    /**
     * 根据di删除点
     *
     * @param id 点id
     */
    void deleteById(int id);


    /**
     * 区域排序
     *
     * @param index 排序信息
     */
    void positionAdjustIndex(Map<Integer, Integer> index);
}
