package com.hvisions.qms.service;

import com.hvisions.qms.dto.type.InspectionOrderTypeDTO;
import com.hvisions.qms.dto.type.QueryInspectionTypeDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface InspectionOrderTypeService {

    /**
     * 新增质检单类型
     *
     * @param inspectionInspectionOrderTypeDTO 质检单类型
     * @return 质检单类型
     */
    int addInspectionOrderType(InspectionOrderTypeDTO inspectionInspectionOrderTypeDTO);

    /**
     * 更新质检单类型
     *
     * @param inspectionInspectionOrderTypeDTO 质检单类型
     * @return 主键
     */
    int updateInspectionOrderType(InspectionOrderTypeDTO inspectionInspectionOrderTypeDTO);

    /**
     * 删除质检单类型、根据Id
     *
     * @param id 根据Id
     */
    void removeInspectionOrderTypeById(Integer id);

    /**
     * 批量删除质检单类型、根据Id
     *
     * @param ids Id列表
     */
    void removeBatchInspectionOrderTypeByIds(List<Integer> ids);

    /**
     * 根据质检单类型id、获取质检单类型
     *
     * @param id 标准类型id
     * @return 质检单类型
     */
    InspectionOrderTypeDTO findInspectionOrderTypeById(Integer id);

    /**
     * 获取质检单类型、根据Id列表
     *
     * @param ids Id列表
     * @return 质检单类型
     */
    List<InspectionOrderTypeDTO> findInspectionOrderTypeByIds(List<Integer> ids);

    /**
     * 多条件分页获取质检单类型
     *
     * @param queryInspectionOrderTypeDTO 查询条件
     * @return 质检单类型
     */
    Page<InspectionOrderTypeDTO> getInspectionOrderType(QueryInspectionTypeDTO queryInspectionOrderTypeDTO);
}
