package com.hvisions.qms.service;

import com.hvisions.qms.dto.inspection.OrderQueueDTO;

import java.util.List;

/**
 * <p>Title: OrderQueueService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/1</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public interface OrderQueueService {


    /**
     * 创建质检单队列
     *
     * @param orderQueueDTO 质检单队列信息
     */
    void createOrderQueue(OrderQueueDTO orderQueueDTO);

    /**
     * 删除质检单队列
     *
     * @param id 队列Id
     */
    void deleteOrderQueue(int id);

    /**
     * 根据工位查询队列
     *
     * @param stepId 工位
     * @return 队列信息
     */
    List<OrderQueueDTO> getOrderQueueByStep(int stepId);


    /**
     * 根据工位查询队列
     *
     * @param stepId 工位
     * @return 队列信息
     */
    List<OrderQueueDTO> getNotFinishOrderQueueByStep(Integer stepId);


    /**
     * 设置质检单队列工位信息
     *
     * @param id       质检单队列id
     * @param stepId   工位Id
     * @param stepName 工位名称
     */
    void setOrderQueueStep(Integer id, Integer stepId, String stepName);

    /**
     * 设定队列信息为当前队列
     *
     * @param id 队列ID
     */
    void setQueueCurrent(int id);

}