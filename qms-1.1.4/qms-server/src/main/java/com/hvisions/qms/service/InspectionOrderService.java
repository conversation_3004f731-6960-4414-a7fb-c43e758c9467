package com.hvisions.qms.service;

import com.hvisions.qms.dto.defects.*;
import com.hvisions.qms.dto.inspection.*;
import com.hvisions.qms.dto.statistics.InspectionOrderCountParamDTO;
import com.hvisions.qms.dto.statistics.InspectionOrderQueryCountDateDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Page;

import java.util.Date;
import java.util.List;

/**
 * <p>Title: QualityCheckListService</p >
 * <p>Description: 质检单service</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/15</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface InspectionOrderService {

    /**
     * 新增
     *
     * @param checkOrderDTO 质检单DTO
     * @return 新增质检单id
     */
    Integer saveInspection(InspectionOrderDTO checkOrderDTO);

    /**
     * 删除
     *
     * @param id 质检单id
     */
    void delete(Integer id);

    /**
     * 更新质检单信息
     *
     * @param updateDTO 质检单信息
     */
    void updateInspection(InspectionOrderUpdateDTO updateDTO);

    /**
     * 根据条件查询
     *
     * @param inspectionOrderQueryDTO 质检单查询DTO
     * @return 分页集合
     */
    Page<ResultInspectionOrderDTO> findByCondition(InspectionOrderQueryDTO inspectionOrderQueryDTO);


    /**
     * 多条件查询计算规则
     *
     * @param countDateDTO 质检规则结果集
     * @return 质检规则结果集
     */
    InspectionOrderCountParamDTO checkCountAll(InspectionOrderQueryCountDateDTO countDateDTO);

    /**
     * 根据条件统计质检单
     *
     * @param inspectionId 质检单ID获取
     * @return 质检单信息
     */
    ResultInspectionOrderDTO findInspectionOrderById(Integer inspectionId);

    /**
     * 通过工单编码获取质检单
     *
     * @param workOrderCode 工单workOrderCode
     * @return 获取质检的结果
     */
    List<InspectionOrderDetailDTO> findInspectionOrderByWorkOrderCode(String workOrderCode);



    /**
     * 客户端缺陷检测
     *
     * @param inspectionOrderDetailAndDefectDTO 客户端缺陷检测
     */
    void addInspectionOrderAndInspectionDefect(InspectionOrderDetailAndDefectDTO inspectionOrderDetailAndDefectDTO);


    /**
     * 客户端质检录入
     *
     * @param checkOrderDTO 客户端质检提交
     * @return 质检检测
     */
    Integer createInspectionOrderClient(InspectionOrderClientDTO checkOrderDTO);

    /**
     * 根据时间段查询缺陷数量
     *
     * @param stepDefectQueryDTO 查询条件
     * @return 缺陷信息
     */
    List<StepDefectDTO> getDefectCountByTime(@Param("dto") StepDefectQueryDTO stepDefectQueryDTO);

    /**
     * 查询缺陷数量
     *
     * @param beginTime 起始时间
     * @param endTime   结束时间
     * @return 缺陷数量
     */
    List<DefectCountDTO> getCountByTime(@Param("beginTime") Date beginTime,
                                        @Param("endTime") Date endTime);

    /**
     * 查询物料缺陷
     *
     * @param materialDefectQueryDTO 查询条件
     * @return 缺陷信息
     */
    List<MaterialDefectCountDTO> getMaterialDefectByQuery(MaterialDefectQueryDTO materialDefectQueryDTO);

}
