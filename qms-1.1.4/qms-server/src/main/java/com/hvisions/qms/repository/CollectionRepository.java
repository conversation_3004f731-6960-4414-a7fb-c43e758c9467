package com.hvisions.qms.repository;

import com.hvisions.qms.entity.HvQmCollection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: CollectionRepository</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/7/28</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Repository
public interface CollectionRepository extends JpaRepository<HvQmCollection, Integer> {


    /**
     * 根据工艺参数标示号查询采集数据
     *
     * @param code 标示号
     * @return 数据
     */
    List<HvQmCollection> getByCollectionCode(String code);



}