package com.hvisions.qms.service.imp;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.ExcelUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.item.ExportParametersDTO;
import com.hvisions.qms.dto.item.InspectionStandardsParametersDTO;
import com.hvisions.qms.dto.standard.QueryStandardDTO;
import com.hvisions.qms.entity.HvQmInspectionStandard;
import com.hvisions.qms.entity.HvQmInspectionStandardsParameters;
import com.hvisions.qms.enums.InspectionStandardExceptionEnum;
import com.hvisions.qms.repository.InspectionStandardRepository;
import com.hvisions.qms.repository.StandardsParametersRepository;
import com.hvisions.qms.service.InspectionStandardsParametersService;
import com.hvisions.qms.service.ParameterConsts;
import com.hvisions.qms.service.StandardService;
import com.hvisions.qms.service.ValidService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <p>Title: InspectionStandardsParametersServiceImpl</p >
 * <p>Description: 质检标准参数实现类</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/10/10</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
public class InspectionStandardsParametersServiceImpl implements InspectionStandardsParametersService {
    private final StandardsParametersRepository parameterRepository;
    private final InspectionStandardRepository standardRepository;
    private final ValidService validService;
    @Autowired
    StandardService standardService;

    @Autowired
    public InspectionStandardsParametersServiceImpl(StandardsParametersRepository parameterRepository, InspectionStandardRepository standardRepository, ValidService validService) {
        this.parameterRepository = parameterRepository;
        this.standardRepository = standardRepository;
        this.validService = validService;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addStandardsParameter(InspectionStandardsParametersDTO inspectionStandardsParametersDTO) {
        HvQmInspectionStandard standard = validService.canBeModify(inspectionStandardsParametersDTO.getInspectionStandardId());
        //如果是新增，并且质检标准中已经存在相同的编码参数，则直接跳过
        if (null == inspectionStandardsParametersDTO.getId() &&
                standard.getParameters().stream().anyMatch(t -> t.getParameterCode().equals(inspectionStandardsParametersDTO.getParameterCode()))) {
            Optional<HvQmInspectionStandardsParameters> first = standard.getParameters().stream().filter(t -> t.getParameterCode().equals(inspectionStandardsParametersDTO.getParameterCode())).findFirst();
            if (first.isPresent()) {
                throw new BaseKnownException(InspectionStandardExceptionEnum.STANDARD_PARAMETER_EXISTS);
            }
        }
        if (null != inspectionStandardsParametersDTO) {
            if (null != inspectionStandardsParametersDTO.getParameterMaxValue() && null != inspectionStandardsParametersDTO.getParameterMinValue()) {
                if (inspectionStandardsParametersDTO.getParameterMaxValue().compareTo(inspectionStandardsParametersDTO.getParameterMinValue()) == -1) {
                    throw new BaseKnownException(InspectionStandardExceptionEnum.MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM);
                }
            }
            if (null != inspectionStandardsParametersDTO.getControlMaxValue() && null != inspectionStandardsParametersDTO.getControlMinValue()) {
                if (inspectionStandardsParametersDTO.getControlMaxValue().compareTo(inspectionStandardsParametersDTO.getControlMinValue()) == -1) {
                    throw new BaseKnownException(InspectionStandardExceptionEnum.MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM);
                }
            }
            if (null != inspectionStandardsParametersDTO.getWarnMaxValue() && null != inspectionStandardsParametersDTO.getWarnMinValue()) {
                if (inspectionStandardsParametersDTO.getWarnMaxValue().compareTo(inspectionStandardsParametersDTO.getWarnMinValue()) == -1) {
                    throw new BaseKnownException(InspectionStandardExceptionEnum.MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM);
                }
            }
            if (null != inspectionStandardsParametersDTO.getFailMaxValue() && null != inspectionStandardsParametersDTO.getFailMinValue()) {
                if (inspectionStandardsParametersDTO.getFailMaxValue().compareTo(inspectionStandardsParametersDTO.getFailMinValue()) == -1) {
                    throw new BaseKnownException(InspectionStandardExceptionEnum.MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM);
                }
            }


        }
        HvQmInspectionStandardsParameters hvQmInspectionStandardsParameters = DtoMapper.convert(inspectionStandardsParametersDTO, HvQmInspectionStandardsParameters.class);
        hvQmInspectionStandardsParameters.setInspectionStandard(standardRepository.getOne(inspectionStandardsParametersDTO.getInspectionStandardId()));
        return parameterRepository.save(hvQmInspectionStandardsParameters).getId();
    }


    @Override
    public void importBatchStandardsParameter(List<InspectionStandardsParametersDTO> inspectionStandardsParametersDTOS) {
        List<String> parameterCodes = new ArrayList<>();
        for (InspectionStandardsParametersDTO inspectionStandardsParametersDTO : inspectionStandardsParametersDTOS) {
            //相同编码的参数只能添加一次
            if (parameterCodes.stream().anyMatch(t -> t.equals(inspectionStandardsParametersDTO.getParameterCode()))) {
                continue;
            }
            importStandardsParameter(inspectionStandardsParametersDTO);
            parameterCodes.add(inspectionStandardsParametersDTO.getParameterCode());
        }
    }

    private int importStandardsParameter(InspectionStandardsParametersDTO inspectionStandardsParametersDTO) {
        HvQmInspectionStandard standard = validService.canBeModify(inspectionStandardsParametersDTO.getInspectionStandardId());
        //如果是新增，并且质检标准中已经存在相同的编码参数，则直接跳过
        if (null == inspectionStandardsParametersDTO.getId() &&
                standard.getParameters().stream().anyMatch(t -> t.getParameterCode().equals(inspectionStandardsParametersDTO.getParameterCode()))) {
            Optional<HvQmInspectionStandardsParameters> first = standard.getParameters().stream().filter(t -> t.getParameterCode().equals(inspectionStandardsParametersDTO.getParameterCode())).findFirst();
            if (first.isPresent()) {
                return first.get().getId();
            }
            return 0;
        }
        if (null != inspectionStandardsParametersDTO) {
            if (null != inspectionStandardsParametersDTO.getParameterMaxValue() && null != inspectionStandardsParametersDTO.getParameterMinValue()) {
                if (inspectionStandardsParametersDTO.getParameterMaxValue().compareTo(inspectionStandardsParametersDTO.getParameterMinValue()) == -1) {
                    throw new BaseKnownException(InspectionStandardExceptionEnum.MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM);
                }
            }
            if (null != inspectionStandardsParametersDTO.getControlMaxValue() && null != inspectionStandardsParametersDTO.getControlMinValue()) {
                if (inspectionStandardsParametersDTO.getControlMaxValue().compareTo(inspectionStandardsParametersDTO.getControlMinValue()) == -1) {
                    throw new BaseKnownException(InspectionStandardExceptionEnum.MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM);
                }
            }
            if (null != inspectionStandardsParametersDTO.getWarnMaxValue() && null != inspectionStandardsParametersDTO.getWarnMinValue()) {
                if (inspectionStandardsParametersDTO.getWarnMaxValue().compareTo(inspectionStandardsParametersDTO.getWarnMinValue()) == -1) {
                    throw new BaseKnownException(InspectionStandardExceptionEnum.MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM);
                }
            }
            if (null != inspectionStandardsParametersDTO.getFailMaxValue() && null != inspectionStandardsParametersDTO.getFailMinValue()) {
                if (inspectionStandardsParametersDTO.getFailMaxValue().compareTo(inspectionStandardsParametersDTO.getFailMinValue()) == -1) {
                    throw new BaseKnownException(InspectionStandardExceptionEnum.MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM);
                }
            }


        }
        HvQmInspectionStandardsParameters hvQmInspectionStandardsParameters = DtoMapper.convert(inspectionStandardsParametersDTO, HvQmInspectionStandardsParameters.class);
        hvQmInspectionStandardsParameters.setInspectionStandard(standardRepository.getOne(inspectionStandardsParametersDTO.getInspectionStandardId()));
        return parameterRepository.save(hvQmInspectionStandardsParameters).getId();
    }


    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBatchStandardsParameter(List<InspectionStandardsParametersDTO> inspectionStandardsParametersDTOS) {
        List<String> parameterCodes = new ArrayList<>();
        for (InspectionStandardsParametersDTO inspectionStandardsParametersDTO : inspectionStandardsParametersDTOS) {
            //相同编码的参数只能添加一次
            if (parameterCodes.stream().anyMatch(t -> t.equals(inspectionStandardsParametersDTO.getParameterCode()))) {
                continue;
            }
            addStandardsParameter(inspectionStandardsParametersDTO);
            parameterCodes.add(inspectionStandardsParametersDTO.getParameterCode());
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int updateStandardsParameters(InspectionStandardsParametersDTO inspectionStandardsParametersDTO) {
        return addStandardsParameter(inspectionStandardsParametersDTO);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeStandardsParametersById(Integer id) {
        Optional<HvQmInspectionStandardsParameters> parameter = parameterRepository.findById(id);
        if (parameter.isPresent()) {
            validService.canBeModify(parameter.get().getInspectionStandard().getId());
            parameterRepository.deleteById(id);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void removeStandardsParametersByIds(List<Integer> ids) {
        for (Integer id : ids) {
            removeStandardsParametersById(id);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public InspectionStandardsParametersDTO findStandardsParametersByStandardById(Integer id) {
        return parameterRepository.findById(id).map(parameters -> DtoMapper.convert(parameters, InspectionStandardsParametersDTO.class)).orElse(null);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<InspectionStandardsParametersDTO> findStandardsParametersByStandardByCodes(List<String> codes) {
        return null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<InspectionStandardsParametersDTO> findStandardsParametersByStandardByIds(List<Integer> ids) {
        List<InspectionStandardsParametersDTO> parameters = new ArrayList<>();
        for (Integer id : ids) {
            parameters.add(findStandardsParametersByStandardById(id));
        }
        return parameters;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<InspectionStandardsParametersDTO> getStandardsParametersByStandardId(int standardId) {
        Optional<HvQmInspectionStandard> standard = standardRepository.findById(standardId);
        if (standard.isPresent()) {
            List<HvQmInspectionStandardsParameters> parameters = standard.get().getParameters();
            return DtoMapper.convertList(parameters, InspectionStandardsParametersDTO.class);
        }
        return new ArrayList<>();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void adjustIndex(Map<Integer, Integer> index) {
        List<HvQmInspectionStandardsParameters> parameters = parameterRepository.findAllById(index.keySet());
        for (HvQmInspectionStandardsParameters parameter : parameters) {
            parameter.setSortIndex(index.get(parameter.getId()));
        }
        parameterRepository.saveAll(parameters);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ImportResult importParameter(MultipartFile file, Integer id) throws IllegalAccessException, IOException {
        List<ExportParametersDTO> entityList = ExcelUtil.getEntityList(file, 0, ExportParametersDTO.class);
        HvQmInspectionStandard inspectionStandard = standardRepository.findById(id).orElse(null);
        if (inspectionStandard != null) {
            int index = inspectionStandard.getParameters().size();
            for (ExportParametersDTO exportParametersDTO : entityList) {
                index++;
                saveOrUpdate(exportParametersDTO, inspectionStandard, index);
            }
        }
        return new ImportResult();
    }


    /**
     * 保存或更新参数信息
     *
     * @param exportParametersDTO 导入的数据
     * @param inspectionStandard  质检标准
     * @param index               序号
     */
    private void saveOrUpdate(ExportParametersDTO exportParametersDTO, HvQmInspectionStandard inspectionStandard, int index) {
        if (StringUtils.isBlank(exportParametersDTO.getParameterCode())) {
            throw new BaseKnownException(InspectionStandardExceptionEnum.PARAMETER_ENCODING_CANNOT_BE_NULL);
        }
        if (exportParametersDTO.getCollection() != null) {
            if (exportParametersDTO.getCollection().equals("是")) {
                exportParametersDTO.setIsCollection(true);
            } else {
                exportParametersDTO.setIsCollection(false);
            }
        } else {
            exportParametersDTO.setIsCollection(false);
        }
        HvQmInspectionStandardsParameters inspectionStandardsParameters = DtoMapper.convert(exportParametersDTO, HvQmInspectionStandardsParameters.class);
        validService.canBeModify(inspectionStandard.getId());
        List<HvQmInspectionStandardsParameters> parameters = inspectionStandard.getParameters();
        boolean isUpdate = false;
        for (int i = 0; i < parameters.size(); i++) {
            HvQmInspectionStandardsParameters parameter = parameters.get(i);
            if (parameter.getParameterCode().equals(inspectionStandardsParameters.getParameterCode())) {
                isUpdate = true;
                parameter.setParameterName(inspectionStandardsParameters.getParameterName());
                parameter.setParameterStandardsValue(inspectionStandardsParameters.getParameterStandardsValue());
                parameter.setParameterMaxValue(inspectionStandardsParameters.getParameterMaxValue());
                parameter.setParameterMinValue(inspectionStandardsParameters.getParameterMinValue());
                parameter.setDataType(inspectionStandardsParameters.getDataType());
                parameter.setParameterUnit(inspectionStandardsParameters.getParameterUnit());
                parameter.setParameterDescription(inspectionStandardsParameters.getParameterDescription());
                parameter.setFailMinValue(inspectionStandardsParameters.getFailMinValue());
                parameter.setFailMaxValue(inspectionStandardsParameters.getFailMaxValue());
                parameter.setControlMinValue(inspectionStandardsParameters.getControlMinValue());
                parameter.setControlMaxValue(inspectionStandardsParameters.getControlMaxValue());
                parameter.setWarnMinValue(inspectionStandardsParameters.getWarnMinValue());
                parameter.setWarnMaxValue(inspectionStandardsParameters.getWarnMaxValue());
                parameter.setPValue(inspectionStandardsParameters.getPValue());
                parameter.setZValue(inspectionStandardsParameters.getZValue());
                parameter.setCpValue(inspectionStandardsParameters.getCpValue());
                parameter.setInspectionStandard(inspectionStandard);
                parameter.setCpkValue(inspectionStandardsParameters.getCpkValue());
                parameter.setPpValue(inspectionStandardsParameters.getPpValue());
                parameter.setPpkValue(inspectionStandardsParameters.getPpkValue());
                parameter.setPpmValue(inspectionStandardsParameters.getPpmValue());
                parameter.setCollectionCode(inspectionStandardsParameters.getCollectionCode());
                parameter.setIsCollection(inspectionStandardsParameters.getIsCollection());
                //增加一个排序数据，每次排序都放到最后面。10000只是一个较大的数字。保证列表不会超过此数值
                parameterRepository.save(parameter);
            }
        }
        if (!isUpdate) {
            inspectionStandardsParameters.setInspectionStandard(inspectionStandard);
            inspectionStandardsParameters.setSortIndex(index);
            parameterRepository.save(inspectionStandardsParameters);
        }
    }

    @Override
    public ResultVO<ExcelExportDto> exportParameter(Integer standardId) throws IOException, IllegalAccessException {
        ResponseEntity<byte[]> result = exportParameterLink(standardId);
        if (null == result) {
            return null;
        }
        ExcelExportDto excelExportDto = new ExcelExportDto();
        excelExportDto.setBody(result.getBody());
        excelExportDto.setFileName(ParameterConsts.PARAMETER_FILE_NAME);
        return ResultVO.success(excelExportDto);

    }

    @Override
    public ResponseEntity<byte[]> exportParameterLink(Integer standardId) throws IOException, IllegalAccessException {
        QueryStandardDTO inspectionStandard = standardService.findInspectionStandardById(standardId);
        if (null != inspectionStandard) {
            List<ExportParametersDTO> exportDefectsDTOS = DtoMapper.convertList(inspectionStandard.getParameters(), ExportParametersDTO.class);
            for (ExportParametersDTO exportDefectsDTO : exportDefectsDTOS) {

                Boolean isCollection = exportDefectsDTO.getIsCollection();
                if (isCollection != null) {
                    if (isCollection) {
                        exportDefectsDTO.setCollection("是");
                    } else {
                        exportDefectsDTO.setCollection("否");
                    }
                }
            }
            ResponseEntity<byte[]> result = ExcelUtil.generateImportFile(exportDefectsDTOS
                    , ParameterConsts.PARAMETER_FILE_NAME, ExportParametersDTO.class);
            return result;

        }

        return null;
    }
}