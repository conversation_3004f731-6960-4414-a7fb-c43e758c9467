package com.hvisions.qms.service;

import com.hvisions.qms.dto.defects.InspectionDefectInstanceBatchDTO;
import com.hvisions.qms.dto.defects.InspectionDefectInstanceDTO;

/**
 * <p>Title: TestDefectInstanceSerivce</p >
 * <p>Description: 在线检查service</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/15</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface InspectionDefectInstanceService {
    /**
     * 新增
     *
     * @param inspectionDefectInstanceDTO 在线检测DTO
     * @return 新增在线检测实体id
     */
    Integer add(InspectionDefectInstanceDTO inspectionDefectInstanceDTO);

    /**
     * 批量
     *
     * @param testDefectInstanceDTO 批量
     * @return Integer 影响行数
     */
    Integer addAll(InspectionDefectInstanceBatchDTO testDefectInstanceDTO);

    /**
     * 根据ID删除在线检测实体
     *
     * @param id 在线检测实体id
     */
    void deleteById(Integer id);

    /**
     * * 修改返修状态
     *
     * @param inspectionId 质检单
     * @param pointId      区域Id
     * @param positionId   位置
     * @param state        返修状态
     * @param operationCode 工艺操作编码
     */
    void updateRepairState(Integer inspectionId, Integer pointId, Integer positionId, Integer state, String operationCode);

}