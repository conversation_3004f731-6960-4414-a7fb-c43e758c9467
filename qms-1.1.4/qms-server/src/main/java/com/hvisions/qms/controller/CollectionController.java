package com.hvisions.qms.controller;

import com.hvisions.qms.dao.CollectionMapper;
import com.hvisions.qms.dto.inspection.ParameterDTO;
import com.hvisions.qms.dto.inspection.StepMessageDTO;
import com.hvisions.qms.dto.inspection.StepParameterDTO;
import com.hvisions.qms.dto.parameter.CollectionDTO;
import com.hvisions.qms.dto.parameter.CollectionQueryDTO;
import com.hvisions.qms.service.CollectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Title: CollectionController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/7/29</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Api(description = "自动参数采集")
@RestController
@Slf4j
@RequestMapping("/collection")
public class CollectionController {

    @Autowired
    CollectionService collectionService;

    @Autowired
    CollectionMapper collectionMapper;

    /**
     * 自动采集数据添加接口
     *
     * @param collectionDTO 数据
     */
    @PostMapping(value = "/addCollection")
    @ApiOperation(value = "自动采集数据添加接口")
    public void addCollection(@RequestBody CollectionDTO collectionDTO) {
        collectionService.addCollection(collectionDTO);
    }

    /**
     * 自动采集数据更新接口
     *
     * @param collectionDTO 数据
     */
    @PutMapping(value = "/updateCollection")
    @ApiOperation(value = "自动采集数据更新接口")
    public void updateCollection(@RequestBody CollectionDTO collectionDTO) {
        collectionService.updateCollection(collectionDTO);
    }


    /**
     * 删除采集数据
     *
     * @param id
     */
    @DeleteMapping(value = "/deleteById/{id}")
    @ApiOperation(value = "删除采集数据")
    public void deleteById(@PathVariable int id) {
        collectionService.deleteById(id);
    }


    /**
     * 根据工艺参数标示号查询采集数据
     *
     * @param code 标示号
     * @return 数据
     */
    @GetMapping(value = "/getByCollectionCode/{code}")
    @ApiOperation(value = "根据工艺参数标示号查询采集数据")
    public List<CollectionDTO> getByCollectionCode(@PathVariable String code) {
        return collectionService.getByCollectionCode(code);
    }

    /**
     * 分页查询
     *
     * @param dto 查询条件
     * @return 数据信息
     */
    @PostMapping(value = "/getCollectionByQuery")
    @ApiOperation(value = "查询采集数据信息")
    public Page<CollectionDTO> getCollectionByQuery(@RequestBody CollectionQueryDTO dto) {
        return collectionService.getCollectionByQuery(dto);
    }


    /**
     * 根据工单号查询质检工位
     *
     * @param orderCode 工单号
     * @return 质检工位列表
     */
    @GetMapping(value = "/getStepByCode/{orderCode}")
    @ApiOperation(value = "根据工单号查询质检工位")
    public List<StepMessageDTO> getStepByCode(@PathVariable String orderCode) {
        return collectionMapper.getStepByCode(orderCode);
    }

    /**
     * 根据质检工位查询质检标准参数
     *
     * @param stepId 质检工位ID
     * @return 参数信息
     */
    @GetMapping(value = "/getParameterByStepId/{stepId}")
    @ApiOperation(value = "根据质检工位查询质检标准参数")
    List<ParameterDTO> getParameterByStepId(@PathVariable int stepId) {
        List<StepParameterDTO> parameterByStepId = collectionMapper.getParameterByStepId(stepId);

        List<String> distinct =
                parameterByStepId.stream().map(StepParameterDTO::getParameterName)
                        .collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<ParameterDTO> list = new ArrayList<>();
        for (String s : distinct) {
            ParameterDTO parameterDTO = new ParameterDTO();
            parameterDTO.setParameterName(s);
            parameterDTO.setParameterDTOS(new ArrayList<>());
            list.add(parameterDTO);
        }
        for (ParameterDTO parameterDTO : list) {
            for (StepParameterDTO stepParameterDTO : parameterByStepId) {
                if (parameterDTO.getParameterName().equals(stepParameterDTO.getParameterName())) {

                    parameterDTO.getParameterDTOS().add(stepParameterDTO);
                }
            }
        }
        return list;
    }

}