package com.hvisions.qms.service;

import com.hvisions.qms.dto.group.InspectionStandardsGroupDTO;
import com.hvisions.qms.dto.group.QueryStandardsGroupDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface InspectionStandardsGroupService {

    /**
     * 新增质检标准分组
     *
     * @param inspectionStandardsGroupDTO 质检标准分组
     * @return 主键
     */
    int addStandardsGroup(InspectionStandardsGroupDTO inspectionStandardsGroupDTO);

    /**
     * 分页查询质检标准分组
     *
     * @param queryDto 查询条件
     * @return 分页数据
     */
    Page<InspectionStandardsGroupDTO> findStandardsGroup(QueryStandardsGroupDTO queryDto);

    /**
     * 删除质检标准分组、根据Id
     *
     * @param id 质检标准分组Id
     */
    void removeStandardsGroupById(Integer id);

    /**
     * 批量删除质检标准分组、根据Id
     *
     * @param ids Id列表
     */
    void removeBatchStandardsGroupByIds(List<Integer> ids);

    /**
     * 根据质检标准分组id、获取质检标准分组
     *
     * @param id 标准分组id
     * @return 质检标准分组
     */
    InspectionStandardsGroupDTO findStandardsGroupById(Integer id);

    /**
     * 获取质检标准分组、根据Id列表
     *
     * @param ids Id列表
     * @return 质检标准分组
     */
    List<InspectionStandardsGroupDTO> findStandardsGroupByIds(List<Integer> ids);

    /**
     * 获取质检标准分组、根据编码列表
     *
     * @param codes 编码列表
     * @return 质检标准分组
     */
    List<InspectionStandardsGroupDTO> findStandardsGroupByCodes(List<String> codes);

    /**
     * 更新质检标准分组
     *
     * @param inspectionStandardsGroupDTO 质检标准分组
     * @return 主键
     */
    int updateStandardsGroup(InspectionStandardsGroupDTO inspectionStandardsGroupDTO);
}
