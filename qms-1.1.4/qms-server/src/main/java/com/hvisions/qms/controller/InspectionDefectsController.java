package com.hvisions.qms.controller;

import com.hvisions.common.utils.EnumUtil;
import com.hvisions.qms.dto.defects.InspectionDefectInstanceBatchDTO;
import com.hvisions.qms.dto.defects.InspectionDefectInstanceDTO;
import com.hvisions.qms.enums.LogTypeEnum;
import com.hvisions.qms.enums.RepairStateEnum;
import com.hvisions.qms.service.InspectionDefectInstanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <p>Title: InspectionDefectsController</p >
 * <p>Description: 质检单质量检测控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/15</p >
 *
 * <AUTHOR>
 * @version :2.0.0
 */
@Api(description = "质检单缺陷检测控制器")
@RequestMapping("testDefectInstance")
@RestController
public class InspectionDefectsController {
    private final InspectionDefectInstanceService inspectionDefectInstanceService;

    @Autowired
    public InspectionDefectsController(InspectionDefectInstanceService inspectionDefectInstanceService) {
        this.inspectionDefectInstanceService = inspectionDefectInstanceService;
    }

    /**
     * 批量新增
     *
     * @param testDefectInstanceDTO 缺陷检测DTO集合
     * @return 新建缺陷检测id集合
     */
    @PostMapping("/createListTestDefectInstance")
    @ApiOperation(value = "批量新增检测缺陷")
    public Integer createListTestDefectInstance(@RequestBody InspectionDefectInstanceBatchDTO testDefectInstanceDTO) {
        return inspectionDefectInstanceService.addAll(testDefectInstanceDTO);
    }

    /**
     * 更新
     *
     * @param inspectionDefectInstanceDTO 缺陷检测DTO
     */
    @PutMapping("/updateTestDefectInstance")
    @ApiOperation(value = "更新")
    public void updateTestDefectInstance(@RequestBody InspectionDefectInstanceDTO inspectionDefectInstanceDTO) {
        inspectionDefectInstanceService.add(inspectionDefectInstanceDTO);
    }

    /**
     * 删除
     *
     * @param id 缺陷检测id
     */
    @DeleteMapping("/deleteTestDefectInstanceById/{id}")
    @ApiOperation(value = "删除")
    public void deleteTestDefectInstanceById(@PathVariable Integer id) {
        inspectionDefectInstanceService.deleteById(id);
    }

    /**
     * * 修改返修状态
     *
     * @param inspectionId 质检单
     * @param pointId      区域Id
     * @param positionId   位置
     * @param state        返修状态
     */
    @PutMapping(value = "/updateRepairState")
    @ApiOperation(value = "修改返修状态")
    public void updateRepairState(@RequestParam Integer inspectionId, @RequestParam(required = false) Integer pointId,
                                  @RequestParam(required = false) Integer positionId, @RequestParam Integer state, @RequestParam String operationCode) {
        inspectionDefectInstanceService.updateRepairState(inspectionId, pointId, positionId, state, operationCode);
    }

    @GetMapping("/getLogType")
    @ApiOperation(value = "获取日志类型")
    public Map<Integer, String> getLogType() {
        return EnumUtil.enumToMap(LogTypeEnum.class);
    }

    @GetMapping("/getRepairState")
    @ApiOperation(value = "获取返修状态")
    public Map<Integer, String> getRepairState() {
        return EnumUtil.enumToMap(RepairStateEnum.class);
    }

}