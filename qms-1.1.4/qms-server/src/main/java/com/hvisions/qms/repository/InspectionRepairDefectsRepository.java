package com.hvisions.qms.repository;

import com.hvisions.qms.entity.HvQmRepairStandardsDefects;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * <p>Title: TestReworkRepository</p >
 * <p>Description: 返修TestReworkRepository</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/6/04</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public interface InspectionRepairDefectsRepository extends JpaRepository<HvQmRepairStandardsDefects, Integer>, JpaSpecificationExecutor<HvQmRepairStandardsDefects> {




    /**
     * 根据条件模糊查询 返修
     *
     * @param spec     查询条件
     * @param pageable 分页条件
     * @return 分页结果
     */
    @Override
    Page<HvQmRepairStandardsDefects> findAll(Specification<HvQmRepairStandardsDefects> spec, Pageable pageable);




}
