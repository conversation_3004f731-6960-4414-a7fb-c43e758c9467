package com.hvisions.qms.repository;

import com.hvisions.qms.entity.HvQmCountValue;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>Title: CountValueRepository</p >
 * <p>Description: 计算规则repository</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/5/15</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Repository
public interface InspectionCountValueRepository extends JpaRepository<HvQmCountValue, Integer> {
    List<HvQmCountValue> findByCountCheckId(Integer inspection);
}
