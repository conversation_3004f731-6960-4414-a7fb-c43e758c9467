package com.hvisions.qms.service;

import com.hvisions.qms.dto.parameter.CollectionDTO;
import com.hvisions.qms.dto.parameter.CollectionQueryDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <p>Title: CollectionService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/7/28</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public interface CollectionService {


    /**
     * 自动采集数据添加接口
     *
     * @param collectionDTO 数据
     */
    void addCollection(CollectionDTO collectionDTO);

    /**
     * 自动采集数据更新接口
     *
     * @param collectionDTO 数据
     */
    void updateCollection(CollectionDTO collectionDTO);


    /**
     * @param id 主键
     */
    void deleteById(int id);


    /**
     * 根据工艺参数标示号查询采集数据
     *
     * @param code 标示号
     * @return 数据
     */
    List<CollectionDTO> getByCollectionCode(String code);

    /**
     * 分页查询
     *
     * @param dto 查询条件
     * @return 分页结果
     */
    Page<CollectionDTO> getCollectionByQuery(CollectionQueryDTO dto);

}