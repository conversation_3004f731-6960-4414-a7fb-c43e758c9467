package com.hvisions.qms.controller;

import com.hvisions.qms.dto.inspection.OrderQueueDTO;
import com.hvisions.qms.service.OrderQueueService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: OrderQueueController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/2</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Api(description = "质检队列控制器")
@RequestMapping("/orderQueue")
@RestController
public class OrderQueueController {

    @Autowired
    OrderQueueService orderQueueService;

    /**
     * 创建质检单队列
     *
     * @param orderQueueDTO 质检单队列信息
     */
    @ApiOperation(value = "创建质检单队列")
    @PostMapping(value = "/createOrderQueue")
    public void createOrderQueue(@RequestBody OrderQueueDTO orderQueueDTO) {
        orderQueueService.createOrderQueue(orderQueueDTO);
    }

    /**
     * 删除质检单队列
     *
     * @param id 队列Id
     */
    @ApiOperation(value = "删除质检单队列")
    @DeleteMapping(value = "/deleteOrderQueue/{id}")
    public void deleteOrderQueue(@PathVariable int id) {
        orderQueueService.deleteOrderQueue(id);
    }

    /**
     * 根据工位查询队列
     *
     * @param stepId 工位
     * @return 队列信息
     */
    @ApiOperation(value = "根据工位查询队列")
    @GetMapping(value = "/getOrderQueueByStep/{stepId}")
    public List<OrderQueueDTO> getOrderQueueByStep(@PathVariable Integer stepId) {
        return orderQueueService.getOrderQueueByStep(stepId);
    }

    /**
     * 根据工位查询队列
     *
     * @param stepId 工位
     * @return 队列信息
     */
    @ApiOperation(value = "根据工位查询未完成队列")
    @GetMapping(value = "/getNotFinishOrderQueueByStep/{stepId}")
    public List<OrderQueueDTO> getNotFinishOrderQueueByStep(@PathVariable Integer stepId) {
        return orderQueueService.getNotFinishOrderQueueByStep(stepId);
    }

    /**
     * 设置质检单队列工位信息
     *
     * @param id     质检单队列id
     * @param stepId 工位Id
     */
    @ApiOperation(value = "根据工位查询未完成队列")
    @PutMapping(value = "/setOrderQueueStep/{id}/{stepId}/{stepName}")
    public void setOrderQueueStep(@PathVariable Integer id, @PathVariable Integer stepId,
                                  @PathVariable String stepName) {
        orderQueueService.setOrderQueueStep(id, stepId, stepName);
    }

    /**
     * 设定队列信息为当前队列
     *
     * @param id 队列ID
     */
    @PutMapping(value = "/setQueueCurrent/{id}")
    @ApiOperation(value = "设定队列信息为当前队列")
    public void setQueueCurrent(@PathVariable int id) {
        orderQueueService.setQueueCurrent(id);
    }
}