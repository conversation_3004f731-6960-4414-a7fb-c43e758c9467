package com.hvisions.qms.controller;

import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.qms.dao.StatisticsMapper;
import com.hvisions.qms.dto.statistics.ParameterInfo;
import com.hvisions.qms.dto.statistics.ParameterListQuery;
import com.hvisions.qms.dto.statistics.ParameterQuery;
import com.hvisions.qms.dto.statistics.ParameterValueInfo;
import com.hvisions.qms.enums.ParameterDataTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <p>Title: StatisticsController</p>
 * <p>Description: 统计控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/4/8</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@Api(description = "统计控制器")
@RequestMapping("/statistics")
@Slf4j
public class StatisticsController {
    @Autowired
    StatisticsMapper statisticsMapper;

    /**
     * 查询成品合格率,如果没有质检单，或者没有完工的质检单会返回null"
     *
     * @param beginTime 创建在此之后
     * @param endTime   创建在此之前
     * @return 合格率
     */
    @ApiOperation("查询成品合格率,如果没有质检单，或者没有完工的质检单会返回null")
    @GetMapping("/findProductQualificationRate")
    public Float findProductQualificationRate(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date beginTime,
                                              @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {

        try {
            return statisticsMapper.findRate("Product", beginTime, endTime);
        } catch (Exception ex) {
            log.error("查询出错，请检查物料数据库schema是否为materials,如果不是可以修改search.xml文件", ex);
            return 0f;
        }

    }

    /**
     * 查询半成品合格率,如果没有质检单，或者没有完工的质检单会返回null"
     *
     * @param beginTime 创建在此之后
     * @param endTime   创建在此之前
     * @return 合格率
     */
    @ApiOperation("查询半成品合格率，如果没有质检单，或者没有完工的质检单会返回null")
    @GetMapping("/findByProductQualificationRate")
    public Float findByProductQualificationRate(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date beginTime,
                                                @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        try {
            return statisticsMapper.findRate("By_Product", beginTime, endTime);
        } catch (Exception ex) {
            log.error("查询出错，请检查物料数据库schema是否为materials,如果不是可以修改search.xml文件", ex);
            return 0f;
        }
    }

    /**
     * 参数统计报表接口，查询物料某个质检标准下面的的分页数据
     *
     * @param query 查询条件
     * @return 参数分页数据
     */
    @PostMapping("/findParameterInfoPage")
    @ApiOperation("查询参数分页信息")
    public Page<ParameterInfo> findParameterInfoPage(@RequestBody ParameterQuery query) {
        Page<ParameterInfo> page = PageHelperUtil.getPage(statisticsMapper::findParameterInfoPage, query);
        page.getContent().forEach(
                t -> {
                    String dataType = t.getDataType();
                    try {
                        t.setDataType(ParameterDataTypeEnum.valueOf(Integer.valueOf(dataType)).getName());
                    } catch (Exception ex) {
                        t.setDataType("字符串");
                    }
                }
        );
        return page;

    }

    /**
     * 查询参数值
     *
     * @param query 查询条件
     * @return 参数数据
     */
    @PostMapping("/findParameterInfo")
    @ApiOperation("查询参数历史数")
    public List<ParameterValueInfo> findParameterInfo(@RequestBody ParameterListQuery query) {
        List<ParameterValueInfo> infos = statisticsMapper.findParameterInfo(query);
        return infos;
    }
}









