package com.hvisions.qms.repository;

import com.hvisions.qms.entity.HvQmQualityDefect;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface DefectRepository extends JpaRepository<HvQmQualityDefect, Integer> {

    /**
     * 根据缺陷名称获取缺陷对象
     *
     * @param name 名称
     * @return 缺陷对象
     */
    HvQmQualityDefect findByName(String name);



    HvQmQualityDefect findByCodeEquals(String code);

    List<HvQmQualityDefect> findByGradeId(Integer gradeId);

    List<HvQmQualityDefect> findByTypeId(Integer typeId);


}
