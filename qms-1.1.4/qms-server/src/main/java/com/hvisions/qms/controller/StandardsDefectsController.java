package com.hvisions.qms.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.item.InspectionStandardsDefectsDTO;
import com.hvisions.qms.service.InspectionStandardsDefectsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: StandardsDefectsController</p >
 * <p>Description: 质量标准缺陷控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/10/9</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@RestController
@RequestMapping(value = "/standardsDefects")
@Slf4j
@Api(description = "质检标准检测项缺陷控制器")
public class StandardsDefectsController {

    private final InspectionStandardsDefectsService inspectionStandardsDefects;

    @Autowired
    public StandardsDefectsController(InspectionStandardsDefectsService inspectionStandardsDefects) {
        this.inspectionStandardsDefects = inspectionStandardsDefects;
    }


    /**
     * 新增标准缺陷
     *
     * @param inspectionStandardsDefectsDTO 标准缺陷
     * @return 主键
     */
    @ApiOperation(value = "新增标准缺陷")
    @PostMapping(value = "/createBatchStandardsDefect")
    public Integer createStandardsParameter(@RequestBody InspectionStandardsDefectsDTO inspectionStandardsDefectsDTO) {
        return inspectionStandardsDefects.addStandardsDefects(inspectionStandardsDefectsDTO);
    }

    /**
     * 批量新增标准缺陷
     *
     * @param inspectionStandardsDefectsDTO 标准缺陷列表
     */
    @ApiOperation(value = "批量新增标准缺陷")
    @PostMapping(value = "/createBatchStandardsDefects")
    public void createBatchStandardsParameter(@RequestBody List<InspectionStandardsDefectsDTO> inspectionStandardsDefectsDTO) {
        inspectionStandardsDefects.addBatchStandardsDefects(inspectionStandardsDefectsDTO);
    }

    /**
     * 获取标准缺陷、根据Id列表
     *
     * @param ids 标准缺陷id列表
     * @return 标准缺陷
     */
    @ApiOperation(value = "获取标准缺陷、根据Id列表")
    @PostMapping(value = "/getStandardsDefectsByStandardByIds")
    public List<InspectionStandardsDefectsDTO> getStandardsDefectsByStandardByIds(@RequestBody List<Integer> ids) {
        return inspectionStandardsDefects.findStandardsDefectsByStandardByIds(ids);
    }


    /**
     * 根据标准缺陷id获取获取标准缺陷
     *
     * @param id 标准缺陷id
     * @return 标准缺陷
     */
    @ApiOperation(value = " 获取标准缺陷、根据标准缺陷id")
    @GetMapping(value = "/getStandardsDefectsByStandardById/{id}")
    public InspectionStandardsDefectsDTO getStandardsDefectsByStandardById(@PathVariable Integer id) {
        return inspectionStandardsDefects.findStandardsDefectsByStandardById(id);
    }

    /**
     * 更新标准缺陷
     *
     * @param inspectionStandardsDefectsDTO 标准缺陷
     * @return 主键
     */
    @ApiOperation(value = "更新标准缺陷")
    @PutMapping(value = "/editStandardsDefects")
    public int editStandardsDefects(@RequestBody InspectionStandardsDefectsDTO inspectionStandardsDefectsDTO) {
        return inspectionStandardsDefects.updateStandardsDefects(inspectionStandardsDefectsDTO);
    }

    /**
     * 删除标准缺陷、根据Id
     */
    @ApiOperation(value = "删除标准缺陷、根据Id")
    @DeleteMapping(value = "/deleteStandardsDefectsById/{defectId}/{standardsId}")
    public void deleteStandardsDefectsById(@PathVariable Integer defectId, @PathVariable Integer standardsId) {
        inspectionStandardsDefects.removeStandardsDefectsByDefectIdAndStandardsId(defectId, standardsId);
    }

    /**
     * 批量删除标准缺陷、根据Id
     *
     * @param ids Id列表
     */
    @ApiOperation(value = " 批量删除标准缺陷、根据Id")
    @DeleteMapping(value = "/deleteBatchStandardsDefectsByIds")
    public void deleteBatchStandardsDefectsByIds(@RequestBody List<Integer> ids) {
        inspectionStandardsDefects.deleteBatchStandardsDefectsByIds(ids);
    }

    /**
     * 导入缺陷信息
     *
     * @param file       缺陷文档
     * @param standardId 质检标准ID
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @PostMapping(value = "/importDefectLink")
    @ApiOperation(value = "导入缺陷信息(缺陷编码存在则更新，不存在则新增)")
    public ImportResult importDefectLink(@RequestParam("file") MultipartFile file, @RequestParam("standardId") Integer standardId) throws IllegalAccessException, ParseException, IOException {
        return inspectionStandardsDefects.importDefect(file, standardId);
    }

    /**
     * 质检标准缺陷信息
     *
     * @param standardId 质检标准ID
     * @return 缺陷信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportDefect/{standardId}")
    @ApiOperation(value = "缺陷、二进制数据导出")
    public ResultVO<ExcelExportDto> exportDefect(@PathVariable Integer standardId) throws IOException, IllegalAccessException {
        return inspectionStandardsDefects.exportDefect(standardId);
    }

    /**
     * 质检标准缺陷信息
     *
     * @param standardId 质检标准ID
     * @return 缺陷信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportDefectLink/{standardId}")
    @ApiOperation(value = "缺陷、超链接导出")
    public ResponseEntity<byte[]> exportDefectLink(@PathVariable Integer standardId) throws IOException, IllegalAccessException {
        return inspectionStandardsDefects.exportDefectLink(standardId);
    }
    /**
     * 调整质检标准参数顺序
     *
     * @param index 参数顺序
     */
    @PutMapping(value = "/adjustIndex")
    @ApiOperation(value = "调整质检标准参数顺序")
    public void adjustIndex(@RequestBody Map<Integer, Integer> index) {
        inspectionStandardsDefects.adjustIndex(index);
    }
}