package com.hvisions.qms.repository;

import com.hvisions.qms.entity.HvQmInspectionStandardsGroup;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <p>Title: InspectionStandardsGroupRepository</p >
 * <p>Description: 质检标准分组持久化层</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/10/09</p >
 *
 * <AUTHOR>
 * @version :2.0.0
 */
@Repository
public interface InspectionStandardsGroupRepository extends JpaRepository<HvQmInspectionStandardsGroup, Integer> {

    /**
     * 查询分页数据
     *
     * @param specification 查询条件
     * @param pageInfo      分页数据
     * @return 分页数据
     */
    Page<HvQmInspectionStandardsGroup> findAll(Specification<HvQmInspectionStandardsGroup> specification, Pageable pageInfo);
}
