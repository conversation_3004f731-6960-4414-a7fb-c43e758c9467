package com.hvisions.qms.service;

import com.hvisions.common.interfaces.EntitySaver;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.qms.dto.*;
import com.hvisions.qms.entity.HvQmQualityDefect;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Set;


public interface DefectService extends EntitySaver<ExportDefectsDTO> {


    /**
     * 新增缺陷信息
     *
     * @param defectDTO 缺陷信息
     * @return 缺陷信息id
     */
    Integer addDefect(DefectDTO defectDTO);

    /**
     * 修改缺陷信息
     *
     * @param defectDTO 缺陷信息
     * @return 缺陷信息id
     */
    Integer updateDefect(DefectDTO defectDTO);

    /**
     * 根据id删除缺陷信息
     *
     * @param id 缺陷信息id
     */
    void deleteDefectById(Integer id);

    /**
     * 缺陷信息分页查询
     *
     * @param defectQueryDTO 缺陷信息分页查询条件
     * @return 分页查询结果列表
     */
    Page<DefectDTO> getDefectPageQuery(DefectQueryDTO defectQueryDTO);

    /**
     * 获取所有缺陷
     *
     * @return 缺陷信息列表
     */
    List<DefectDTO> getAllDefect();

    /**
     * 根据工艺步骤id获取缺陷信息列表
     *
     * @param routeStepId 工艺步骤id
     * @return 工艺步骤id对应的缺陷信息列表
     */
    List<DefectDTO> getDefectsByRouteStepId(Integer routeStepId);

    /**
     * 根据缺陷名称获取缺陷对象
     *
     * @param name 名称
     * @return 缺陷对象
     */
    HvQmQualityDefect getByName(String name);

    /**
     * 根据缺陷id获取缺陷信息
     *
     * @param id 主键id
     * @return 缺陷信息
     */
    DefectDTO getDefectById(Integer id);

    List<HvQmQualityDefectRouteOperationDTO> getDefectByConQuery(DefectRouteDTO defectQueryDTO);

    ImportResult importDefect(MultipartFile file) throws IllegalAccessException, ParseException, IOException;

    Set<DefectDTO> getDefectByConMoreQuery(DefectRouteMoreDTO defectQueryDTO);


    /**
     * 获取所有的质量缺陷信息，根据工艺操作进行分组
     *
     * @return 所有的质量缺陷信息
     */
    List<DefectGroup> getAllDefectGroupByOperation();
}
