package com.hvisions.qms.controller;

import com.hvisions.qms.dto.position.PointDTO;
import com.hvisions.qms.dto.position.PositionDTO;
import com.hvisions.qms.dto.position.PositionMoreDTO;
import com.hvisions.qms.entity.HvQmPosition;
import com.hvisions.qms.service.PointService;
import com.hvisions.qms.service.PositionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/standardsPosition")
@Slf4j
@Api(description = "区域检测点控制器")
public class StandardsPositionController {
    private final PositionService positionService;
    private final PointService pointEntityService;

    @Autowired
    public StandardsPositionController(PositionService positionService, PointService pointEntityService) {
        this.positionService = positionService;
        this.pointEntityService = pointEntityService;
    }

    /**
     * 新增
     *
     * @param positionDTO 传入的对象
     * @return 添加后的实体Id
     */
    @ApiOperation(value = "新增质检区域")
    @RequestMapping(value = "/createPosition", method = RequestMethod.POST)
    public int createPosition(@RequestBody PositionDTO positionDTO) {
        return positionService.save(positionDTO);
    }

    /**
     * 删除
     *
     * @param id 实体id
     */
    @ApiOperation(value = "删除区域信息")
    @RequestMapping(value = "/deletePositionById/{id}", method = RequestMethod.DELETE)
    public void deletePositionById(@PathVariable int id) {
        positionService.deleteById(id);
    }

    /**
     * 查询
     *
     * @param positionDTO 传入的对象
     * @return 查询后的位置点以及区域图片等
     */
    @ApiOperation(value = "根据名称，获取区域信息")
    @RequestMapping(value = "/getPositionList", method = RequestMethod.POST)
    public List<PositionDTO> getPositionList(@RequestBody PositionDTO positionDTO) {
        return positionService.findByName(positionDTO);
    }

    /**
     * 查询
     *
     * @param id 传入的对象
     * @return 查询后的位置点以及区域图片等
     */
    @ApiOperation(value = "根据Id，获取区域信息")
    @GetMapping(value = "/getPositionList/{id}")
    public Optional<HvQmPosition> getPositionList(@PathVariable int id) {
        return positionService.findById(id);
    }

    /**
     * 查询
     *
     * @param id 传入的对象
     * @return 区域
     */
    @ApiOperation(value = "通过模板Id查询相关区域")
    @GetMapping("/getPositionListByTemplateId/{id}")
    public List<PositionMoreDTO> getPositionListByTemplateId(@PathVariable int id) {
        return positionService.findByTemplateId(id);
    }

    /**
     * 查询区域对象
     *
     * @param id 传入区域id
     * @return 区域对象
     */
    @ApiOperation(value = "通过区域Id查询区域,返回区域DTO")
    @GetMapping("/getPositionListById/{id}")
    public PositionMoreDTO getPositionListById(@PathVariable int id) {
        return positionService.findPositionMoreDTOById(id);
    }

    /**
     * 新增
     *
     * @param pointDTO 传入的对象
     * @return 添加后的实体Id
     */
    @ApiOperation(value = "新增检测点")
    @PostMapping(value = "/createPointOne")
    public int createPointOne(@RequestBody PointDTO pointDTO) {
        return pointEntityService.save(pointDTO);
    }

    /**
     * 新增
     *
     * @param pointDTO 传入的对象
     * @return 添加后的实体Id
     */
    @ApiOperation(value = "所有区域复制位置点")
    @RequestMapping(value = "/createPointTwo", method = RequestMethod.POST)
    public int createPointTwo(@RequestBody List<PointDTO> pointDTO) {
        List<Integer> list = new ArrayList<>();
        for (PointDTO dto : pointDTO) {
            list.add(pointEntityService.save(dto));
        }
        return list.size();
    }

    /**
     * 修改
     *
     * @param pointDTO 传入的对象
     * @return 添加后的实体Id
     */
    @ApiOperation(value = "更新检测点")
    @PutMapping(value = "/updatePoint")
    public int updatePoint(@RequestBody PointDTO pointDTO) {
        return pointEntityService.save(pointDTO);
    }

    /**
     * 删除
     *
     * @param id 实体id
     */
    @ApiOperation(value = "删除检测点")
    @DeleteMapping(value = "/deletePointById/{id}")
    public void deletePointById(@PathVariable int id) {
        pointEntityService.deleteById(id);
    }

    /**
     * 查询
     *
     * @return 实体列表
     */
    @ApiOperation(value = "获取所有检测点信息")
    @GetMapping(value = "/getPointList")
    public List<PointDTO> getPointList() {
        return pointEntityService.getAll();
    }

    /**
     * 区域排序
     *
     * @param index 排序信息
     */
    @ApiOperation(value = "区域排序")
    @PutMapping(value = "/positionAdjustIndex")
    public void positionAdjustIndex(@RequestBody Map<Integer, Integer> index) {
        positionService.positionAdjustIndex(index);
    }
}