# suppress inspection "UnusedProperty" for whole file
# éç¨å¼å¸¸ä¿¡æ¯
SUCCESS=SUCCESS
SERVER_ERROR=SERVER_ERROR
JSON_PARSE_ERROR=JSON_PARSE_ERROR
ILLEGAL_STRING=ILLEGAL_STRING
NULL_RESULT=NULL_RESULT
VIOLATE_INTEGRITY=VIOLATE_INTEGRITY
IMPORT_FILE_NO_SUPPORT=IMPORT_FILE_NO_SUPPORT
IMPORT_SHEET_IS_NULL=IMPORT_SHEET_IS_NULL
ENTITY_PROPERTY_NOT_SUPPORT=ENTITY_PROPERTY_NOT_SUPPORT
SAVE_SHOULD_NO_IDENTITY=SAVE_SHOULD_NO_IDENTITY
UPDATE_SHOULD_HAVE_IDENTITY=UPDATE_SHOULD_HAVE_IDENTITY
CONST_VIOLATE=CONST_VIOLATE
NO_SUCH_ELEMENT=NO_SUCH_ELEMENT
ENTITY_NOT_EXISTS=ENTITY_NOT_EXISTS
DATA_INTEGRITY_VIOLATION=DATA_INTEGRITY_VIOLATION
COLUMN_PATTERN_ILLEGAL=COLUMN_PATTERN_ILLEGAL
#èªå®ä¹å¼å¸¸ä¿¡æ¯
TEMPLATE_DOES_NOT_EXIST=TEMPLATE_DOES_NOT_EXIST
DEMO_EXCEPTION_ENUM=DEMO_EXCEPTION_ENUM
DATA_FORMAT_EXCEPTION=DATA_FORMAT_EXCEPTION
ABNORMAL_PRODUCT_PROCESS_SERVICE=ABNORMAL_PRODUCT_PROCESS_SERVICE
DEFECT_SERVICE_EXCEPTION=DEFECT_SERVICE_EXCEPTION
TEMPLATE_SERVICE_EXCEPTION=TEMPLATE_SERVICE_EXCEPTION
ENTRY_DEFECTS_ALREADY_EXIST=ENTRY_DEFECTS_ALREADY_EXIST
REPAIR_DEFECTS_DO_NOT_EXIST=REPAIR_DEFECTS_DO_NOT_EXIST
ENTRY_DEFECT_DOES_NOT_EXIST=ENTRY_DEFECT_DOES_NOT_EXIST
ENTRY_PARAMETER_DOES_NOT_EXIST=ENTRY_PARAMETER_DOES_NOT_EXIST
QUALITY_INSPECTION_ORDER_IS_BEING_REPAIRED=QUALITY_INSPECTION_ORDER_IS_BEING_REPAIRED
DO_NOT_DELETE_DURING_REPAIR=DO_NOT_DELETE_DURING_REPAIR
TYPE_IN_USE=TYPE_IN_USE
THE_INSPECTION_FORM_HAS_BEEN_TESTED_PLEASE_DO_NOT_SUBMIT_IT_AGAIN=THE_INSPECTION_FORM_HAS_BEEN_TESTED_PLEASE_DO_NOT_SUBMIT_IT_AGAIN
IT_CANNOT_BE_MODIFIED_AFTER_DETECTION=IT_CANNOT_BE_MODIFIED_AFTER_DETECTION
REPAIR_HAS_BEEN_COMPLETED_PLEASE_DO_NOT_SUBMIT_AGAIN=REPAIR_HAS_BEEN_COMPLETED_PLEASE_DO_NOT_SUBMIT_AGAIN
CURRENT_QUEUE_IS_ALREADY=CURRENT_QUEUE_IS_ALREADY
STANDARDS_ID_NOT_NULL=STANDARDS_ID_NOT_NULL
ORDER_QUEUE_NOT_FOUND=ORDER_QUEUE_NOT_FOUND
STANDARD_NOT_EXISTS=STANDARD_NOT_EXISTS
STATE_ERROR=STATE_ERROR
NOT_DELETE=NOT_DELETE
TEMPLATES_ALREADY_EXIST=TEMPLATES_ALREADY_EXIST
POINT_CODE_EXIST=POINT_CODE_EXIST
ROUTE_NOT_EXISTS=ROUTE_NOT_EXISTS
PLEASE_CONFIGURE_TEMPLATE_PICTURE=PLEASE_CONFIGURE_TEMPLATE_PICTURE
INFORMATION_LOCKED=INFORMATION_LOCKED
STEP_ALREADY_EXIST=STEP_ALREADY_EXIST
DUPLICATE_ENTRY_OF_DEFECT_CODE=DUPLICATE_ENTRY_OF_DEFECT_CODE
MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM=MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM
INSPECTION_KNOWLEDGE_BASE_DOES_NOT_EXIST=INSPECTION_KNOWLEDGE_BASE_DOES_NOT_EXIST
THE_KNOWLEDGE_BASE_HAS_BEEN_USED_DELETION_FAILED=THE_KNOWLEDGE_BASE_HAS_BEEN_USED_DELETION_FAILED
DEFECT_ENCODING_CANNOT_BE_NULL=DEFECT_ENCODING_CANNOT_BE_NULL
PARAMETER_ENCODING_CANNOT_BE_NULL=PARAMETER_ENCODING_CANNOT_BE_NULL
SUB_LEVEL_EXISTS_DELETE_FAILED=SUB_LEVEL_EXISTS_DELETE_FAILED
ONLY_EFFECTIVE_TEMPLATE_CAN_BE_UPDATE=ONLY_EFFECTIVE_TEMPLATE_CAN_BE_UPDATE
STANDARD_NOT_EFFECTIVE=STANDARD_NOT_EFFECTIVE
STANDARD_PARAMETER_EXISTS=STANDARD_PARAMETER_EXISTS
DEFECT_TYPE__NAME_EXIST=defect type name is already exist
GRADE_NAME_EXIST=grade name is already exist
DEFECT_NAME_EXIST=defect name is already exist
DEFECT_TYPES_ARE_INTRODUCED=defect types have been introduced
DEFECT_GRADES_ARE_INTRODUCED=defect grades have been introduced
DEFECT_USED=DEFECT_USED

