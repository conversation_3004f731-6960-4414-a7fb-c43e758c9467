# suppress inspection "UnusedProperty" for whole file
#éç¨å¼å¸¸
SUCCESS=æå
SERVER_ERROR=æå¡å¨å¼å¸¸
JSON_PARSE_ERROR=Jsonè§£æéè¯¯
ILLEGAL_STRING=Jsonè§£æåºéï¼è¯·æ£æ¥jsonç»æ
NULL_RESULT=æ¥è¯¢ä¸ºç©º
VIOLATE_INTEGRITY=è¿åéå®ï¼è¯·æ£æ¥æ¯å¦æéå¤æ°æ®
IMPORT_FILE_NO_SUPPORT=æä»¶ç±»åä¸æ¯æ
IMPORT_SHEET_IS_NULL=æä»¶sheetè¡¨ä¸å­å¨
ENTITY_PROPERTY_NOT_SUPPORT=å®ä½å±æ§ä¸æ¯æï¼è¯·æ£æ¥å¯¼å¥æ°æ®
SAVE_SHOULD_NO_IDENTITY=ä¿å­ä¸åºè¯¥æä¸»é®
UPDATE_SHOULD_HAVE_IDENTITY=æ´æ°åºè¯¥æä¸»é®
CONST_VIOLATE=è¿åéå¶ï¼è¯·æ£æ¥æ°æ®åº
NO_SUCH_ELEMENT=æ°æ®æ¥è¯¢ä¸å­å¨
ENTITY_NOT_EXISTS=å¾æ©å±å¯¹è±¡ä¸å­å¨ï¼è¯·æ¥è¯¢åå¯¹è±¡æ¯å¦å­å¨
DATA_INTEGRITY_VIOLATION=æ°æ®å®æ´æ§éªè¯åºé
COLUMN_PATTERN_ILLEGAL=æ©å±åæ ¼å¼éæ³ï¼åªåè®¸æ°å­ï¼ä¸åçº¿ï¼è±æå­ç¬¦
#èªå®ä¹å¼å¸¸
DEMO_EXCEPTION_ENUM=ç¤ºä¾å¼å¸¸ç±»å
TEMPLATE_DOES_NOT_EXIST=æ¨¡æ¿ä¸å­å¨!
DATA_FORMAT_EXCEPTION=æ°æ®ç±»åè½¬æ¢å¼å¸¸
TEMPLATE_NAME_EXIST=è¯¥å·¥ä½ä¸ç©ææªéç½®æ¨¡æ¿ï¼è¯·éç½®æ¨¡æ¿ï¼
ABNORMAL_PRODUCT_PROCESS_SERVICE=è´¨éäº§åå·¥èºæå¡å¨æçº¿äºï¼
DEFECT_SERVICE_EXCEPTION=è´¨éç¼ºé·æå¡å¨æçº¿äº!
TEMPLATE_SERVICE_EXCEPTION=è´¨éæ¨¡æ¿æå¡å¨æçº¿äº!
ENTRY_DEFECTS_ALREADY_EXIST=ç¼ºé·æ£æµå·²å½å¥ï¼è¯·å¿éå¤æä½ï¼
REPAIR_DEFECTS_DO_NOT_EXIST=è´¨æ£è¿ä¿®ç¼ºé·ä¸å­å¨ãè¿ä¿®ååå»ºå¤±è´¥ï¼
ENTRY_DEFECT_DOES_NOT_EXIST=å½å¥å¤±è´¥ï¼è´¨æ£ç¼ºé·ä¸å­å¨ï¼
ENTRY_PARAMETER_DOES_NOT_EXIST=å½å¥å¤±è´¥ï¼è´¨æ£åæ°ä¸å­å¨ï¼
QUALITY_INSPECTION_ORDER_IS_BEING_REPAIRED=è´¨æ£åæ­£å¨è¿ä¿®ãè¿ä¿®ååå»ºå¤±è´¥!
DO_NOT_DELETE_DURING_REPAIR=è¿ä¿®åæ­£å¨è¿ä¿®ä¸å¯å é¤ãå é¤å¤±è´¥ï¼
TYPE_IN_USE=ç±»åå·²ä½¿ç¨ãå é¤å¤±è´¥ï¼
THE_INSPECTION_FORM_HAS_BEEN_TESTED_PLEASE_DO_NOT_SUBMIT_IT_AGAIN=è´¨æ£åå·²æ£æµå®æãæä½å¤±è´¥ï¼
IT_CANNOT_BE_MODIFIED_AFTER_DETECTION=æ£æµå®æãä¿®æ¹å¤±è´¥!
REPAIR_HAS_BEEN_COMPLETED_PLEASE_DO_NOT_SUBMIT_AGAIN=è¿ä¿®å·²å®æãè¯·å¿éå¤æäº¤!
CURRENT_QUEUE_IS_ALREADY=å½åå·²æéåå¨æ£,æ æ³åå»ºå½åéåçè´¨æ£éå
STANDARDS_ID_NOT_NULL=è´¨æ£æ åä¸è½ä¸ºç©º
ORDER_QUEUE_NOT_FOUND=æªæ¥è¯¢å°è´¨æ£éå
STANDARD_NOT_EXISTS=è´¨æ£æ åä¸å­å¨
STATE_ERROR=è¿ä¿®ç¶æä¸åè®¸åå»ºè¿ä¿®å
NOT_DELETE=å·²æ£éªå®æãæ£éªä¸­çè´¨æ£åä¸åè®¸è¢«å é¤
TEMPLATES_ALREADY_EXIST=å·²æå·¥èºæ­¥éª¤ç¸å³çæ¨¡æ¿å­å¨!
POINT_CODE_EXIST=è¯¥æ¨¡æ¿å·²å­å¨é¨ä½ç¹ç¼ç !
ROUTE_NOT_EXISTS=å·¥èºè·¯çº¿ä¸å­å¨
PLEASE_CONFIGURE_TEMPLATE_PICTURE=æ¨¡æ¿å¾çä¸è½ä¸ºç©º
INFORMATION_LOCKED=å·²å½æ¡£ï¼
STEP_ALREADY_EXIST=å½åå·¥ä½ï¼å·²å­å¨è¯¥è´¨æ£æ å!
DUPLICATE_ENTRY_OF_DEFECT_CODE=ç¼ç éå¤å½å¥!
PARAMETER_ENCODING_CANNOT_BE_NULL=åæ°ç¼ç ä¸è½ä¸ºç©º!
DEFECT_ENCODING_CANNOT_BE_NULL=ç¼ºé·ç¼ç ä¸è½ä¸ºç©º!
MAXIMUM_CANNOT_BE_LESS_THAN_THE_MINIMUM=æå¤§å¼ä¸è½å°äºæèæ¯ç­äºæå°å¼!
INSPECTION_KNOWLEDGE_BASE_DOES_NOT_EXIST=ä¸å­å¨è´¨æ£ç¥è¯åº!
SUB_LEVEL_EXISTS_DELETE_FAILED=æ­¤è´¨æ£æ åç¥è¯åºå­å¨å­ç¥è¯åºï¼
STANDARD_NOT_EFFECTIVE=ä¸æ¯çææ åä¸è½ç»å®
STANDARD_PARAMETER_EXISTS=å·²å­å¨ç¸ååæ°ç¼ç 
DEFECT_TYPE_NAME_EXIST=ç¼ºé·ç±»ååç§°å·²å­å¨
GRADE_NAME_EXIST=ç­çº§åç§°å·²å­å¨
DEFECT_NAME_EXIST=ç¼ºé·åç§°å·²å­å¨
DEFECT_GRADES_ARE_INTRODUCED=ç¼ºé·ç­çº§å·²ç»è¢«ä½¿ç¨ï¼ä¸è½å é¤ï¼
DEFECT_TYPES_ARE_INTRODUCED=ç¼ºé·ç±»åå·²ç»è¢«ä½¿ç¨ï¼ä¸è½å é¤ï¼
DEFECT_USED=ç¼ºé·å®ä¹å·²è¢«ä½¿ç¨,ä¸è½å é¤ï¼

