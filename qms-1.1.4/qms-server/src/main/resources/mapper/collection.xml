<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.qms.dao.CollectionMapper">
    <resultMap id="step" type="com.hvisions.qms.dto.inspection.StepMessageDTO">
        <result column="step_id" property="stepId"/>
        <result column="step_name" property="stepName"/>
    </resultMap>

    <select id="getStepByCode" resultMap="step" >
        SELECT
        DISTINCT (s.step_id) as step_id,
        s.step_name as step_name
        FROM
        quality_inspection.hv_qm_collection co
        JOIN hv_qm_inspection_standards_parameters p on co.collection_code = p.collection_code
        JOIN hv_qm_inspection_standards_step s on p.inspection_standard_id = s.standards_id
        <where>
            co.order_code = #{code}
        </where>
    </select>

    <resultMap id="getValue" type="com.hvisions.qms.dto.inspection.StepParameterDTO">
        <result column="num_value" property="numValue"/>
        <result column="collection_time" property="collectionTime"/>
        <result column="parameter_name" property="parameterName"/>
        <result column="collection_code" property="collectionCode"/>
    </resultMap>

    <select id="getParameterByStepId" resultMap="getValue" >
        SELECT
        co.num_value,co.collection_time,p.parameter_name,co.collection_code
        FROM
        quality_inspection.hv_qm_collection co
        JOIN hv_qm_inspection_standards_parameters p
        ON p.collection_code = co.collection_code
        JOIN hv_qm_inspection_standards_step s
        ON p.inspection_standard_id = s.standards_id
        <where>
            s.step_id = #{stepId}
        </where>
    </select>


</mapper>