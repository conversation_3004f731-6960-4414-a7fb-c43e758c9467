<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.qms.dao.HvQmQualityDefectRouteOperationMapper">
    <resultMap id="BaseResultMap" type="com.hvisions.qms.dao.DefectOperation">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="route_operation_id" property="routeOperationId" jdbcType="INTEGER"/>
        <result column="defect_id" property="defectId" jdbcType="INTEGER"/>
        <collection property="defects" ofType="com.hvisions.qms.entity.HvQmQualityDefect">
            <id column="id" property="id" jdbcType="INTEGER"/>
            <result column="name" property="name" jdbcType="VARCHAR"/>
            <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
            <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
            <result column="creator_id" property="creatorId" jdbcType="INTEGER"/>
            <result column="updater_id" property="updaterId" jdbcType="INTEGER"/>
            <result column="site_num" property="siteNum" jdbcType="VARCHAR"/>
            <result column="code" property="code" jdbcType="VARCHAR"/>
            <result column="grade_id" property="gradeId" jdbcType="INTEGER"/>
            <result column="type_id" property="typeId" jdbcType="INTEGER"/>
            <result column="position" property="position" jdbcType="VARCHAR"/>
            <result column="unit" property="unit" jdbcType="VARCHAR"/>
            <result column="points" property="points" jdbcType="VARCHAR"/>
            <result column="description" property="description" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <select id="selectAll" resultMap="BaseResultMap">
        select defect.name,
               defect.create_time,
               defect.update_time,
               defect.creator_id,
               defect.updater_id,
               defect.site_num,
               defect.code,
               defect.grade_id,
               defect.type_id,
               defect.position,
               defect.unit,
               defect.points,
               defect.description,
               operation.route_operation_id,
               operation.defect_id,
               operation.id
        from hv_qm_quality_defect_route_operation as operation
                 left join hv_qm_quality_defect as defect on defect.id = operation.defect_id
    </select>


    <!--  根据operation复杂查询-->
    <select id="complexQuery" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select o.*, d.*
        from hv_qm_quality_defect_route_operation as o
                 inner join hv_qm_quality_defect as d on d.id = o.defect_id
        where o.route_operation_id = #{route_operation_id,jdbcType=INTEGER}</select>

    <select id="complexQueryMore" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select o.defect_id,
               o.route_operation_id,
               d.id,
               d.name,
               d.create_time,
               d.update_time,
               d.creator_id,
               d.updater_id,
               d.site_num,
               d.code,
               d.grade_id,
               d.type_id,
               d.position,
               d.unit,
               d.points,
               d.description
        from hv_qm_quality_defect as d
                 join hv_qm_quality_defect_route_operation as o on d.id = o.defect_id
        where o.route_operation_id = #{route_operation_id,jdbcType=INTEGER}</select>
</mapper>