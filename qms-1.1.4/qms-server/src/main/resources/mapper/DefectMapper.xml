<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.qms.dao.DefectMapper">
    <resultMap id="rm" type="com.hvisions.qms.dto.DefectDTO">
        <!--如果是下划线转驼峰拼写。如果配置了 mybatis.configuration.map-underscore-to-camel-case=true 则不用配置 -->
    </resultMap>
    <select id="findAllDefect" resultMap="rm">
        SELECT t1.*, t2.name grade_name, t3.name type_name
        FROM hv_qm_quality_defect t1
                 LEFT JOIN hv_qm_quality_defect_grade t2 ON t1.grade_id = t2.id
                 LEFT JOIN hv_qm_quality_defect_type t3 on t1.type_id = t3.id

    </select>

    <resultMap id="route" type="com.hvisions.qms.dto.DefectDTO"></resultMap>

    <select id="getDefectDtoByRouteId" resultMap="route" parameterType="int" >
        SELECT t1.*, t2.name grade_name, t3.name type_name
        FROM
        hv_qm_quality_defect t1
        LEFT JOIN hv_qm_quality_defect_grade t2 ON t1.grade_id = t2.id
        LEFT JOIN hv_qm_quality_defect_type t3 on t1.type_id = t3.id
        <where>
            t1.id in (
            select d.defect_id from hv_qm_defect_inspection d
            where
            d.inspection_id in
            (select s.standards_id
            from hv_qm_inspection_standards_step s
            where
            s.step_id = #{stepId})
            )
        </where>
    </select>

    <resultMap id="getByStepId" type="com.hvisions.qms.dto.HvQmQualityDefectRouteOperationDTO">
        <result column="step_id" property="stepId"/>
        <result column="step_name" property="stepName"/>
        <collection property="defects" ofType="com.hvisions.qms.dto.DefectDTO">
            <id column="id" property="id" jdbcType="INTEGER"/>
            <result column="name" property="name" jdbcType="VARCHAR"/>
            <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
            <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
            <result column="creator_id" property="creatorId" jdbcType="INTEGER"/>
            <result column="updater_id" property="updaterId" jdbcType="INTEGER"/>
            <result column="site_num" property="siteNum" jdbcType="VARCHAR"/>
            <result column="code" property="code" jdbcType="VARCHAR"/>
            <result column="grade_id" property="gradeId" jdbcType="INTEGER"/>
            <result column="type_id" property="typeId" jdbcType="INTEGER"/>
            <result column="position" property="position" jdbcType="VARCHAR"/>
            <result column="unit" property="unit" jdbcType="VARCHAR"/>
            <result column="points" property="points" jdbcType="VARCHAR"/>
            <result column="description" property="description" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>
    <select id="getDefectByStepId" resultMap="getByStepId"
            parameterType="com.hvisions.qms.dto.DefectRouteDTO" >
        SELECT DISTINCT q.*,s.step_id,d.defect_id,s.step_name
        FROM
        hv_qm_quality_defect q
        join hv_qm_defect_inspection d on q.id = d.defect_id
        JOIN hv_qm_inspection_standards_step s on s.standards_id = d.inspection_id
        <where>
            <if test="dto.stepId !=null">
                and s.step_id = #{dto.stepId}
            </if>
        </where>
        group by q.id,s.step_name,s.step_id, d.defect_id
    </select>

    <resultMap id="getDefect" type="com.hvisions.qms.dto.DefectDTO">
    </resultMap>

    <select id="getDefectPageQuery" resultMap="getDefect" parameterType="com.hvisions.qms.dto.DefectQueryDTO" >
        select
        q.*,
        t.id as typeId,
        t.name as typeName,
        g.id as gradeId,
        g.name as gradeName
        from
        hv_qm_quality_defect q
        left join hv_qm_quality_defect_type t
        on q.type_id = t.id
        left join hv_qm_quality_defect_grade g
        on q.grade_id = g.id
        <where>
            <if test="dto.code !=null and dto.code != &apos;&apos;">
                and q.code like concat('%',#{dto.code},'%')
            </if>
            <if test="dto.name !=null and dto.name != &apos;&apos;">
                and q.name like concat('%',#{dto.name},'%')
            </if>
        </where>
    </select>
    <select id="findByStandardsId" resultMap="rm" parameterType="int" >
        SELECT t2.*, t3.name grade_name, t4.name type_name, t1.id as defect_inspection_id
        FROM hv_qm_defect_inspection t1
                 JOIN hv_qm_quality_defect t2 on t1.defect_id = t2.id
                 LEFT JOIN hv_qm_quality_defect_grade t3 ON t2.grade_id = t3.id
                 LEFT JOIN hv_qm_quality_defect_type t4 on t2.type_id = t4.id
        WHERE t1.inspection_id = #{standardsId}
        ORDER BY t1.sort_index
    </select>

    <select id="getIdsByCodes" parameterType="java.util.ArrayList" resultType="java.lang.Integer" >
        select t1.id
        FROM
        hv_qm_quality_defect t1
        where
        t1.code in
        <foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="existDefectCode" parameterType="java.lang.String" resultType="java.lang.Integer" >
        select count(1)
        from hv_qm_quality_defect d
        where d.code = #{code}
    </select>


    <select id="getDefectByCode" parameterType="java.lang.String" resultType="java.lang.Integer" >
        select t1.id
        FROM hv_qm_quality_defect t1
        where t1.code = #{code}
    </select>
</mapper>