<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.qms.dao.HvQmQualityDefectMapper">
    <resultMap id="BaseResultMap" type="com.hvisions.qms.entity.HvQmQualityDefect">
    </resultMap>


    <select id="complexQueryWithMultipleConditions" parameterType="com.hvisions.qms.dto.DefectQueryDTO"
            resultMap="BaseResultMap">
        select * from(
        SELECT row_number() OVER( ORDER BY a.id DESC )AS rowNum, COUNT(1) OVER() AS totalElements ,a.*,b.defect_id
        FROM hv_qm_quality_defect as a JOIN hv_qm_quality_defect_route_operation AS b on
        a.id=b.defect_id
        <where>
            <if test="name!=null">
                and a.name LIKE CONCAT('%',#{name},'%')
            </if>
            <if test="code!=null">
                and a.code LIKE CONCAT('%',#{code},'%')
            </if>
            <if test="operationId!=null">
                and b.route_operation_id= #{operationId}
            </if>
        </where>
        ) as tb where rownum between #{page} and #{pageSize}
    </select>

    <select id="complexQueryWithMultipleConditionsMysql"
            parameterType="com.hvisions.qms.entity.HvQmQualityDefect" resultMap="BaseResultMap">
        select
        distinct d.*
        from hv_qm_quality_defect as d
        <where>
            <if test="name!=null">
                and d.name LIKE CONCAT('%',#{name},'%')
            </if>
            <if test="code!=null">
                and d.code LIKE CONCAT('%' #{code} '%')
            </if>
        </where>
        GROUP BY d.id, d.name, d.create_time,d.update_time, d.creator_id,d.updater_id, d.site_num,d.code, d.type_id,
        d.position, d.unit, d.points, d.description
        order by d.id DESC
    </select>


    <select id="findStandardDefectById" resultType="java.lang.Integer" parameterType="java.lang.Integer">
        select count(*)
        from hv_qm_defect_inspection t
        where t.defect_id = #{defectId}
    </select>

</mapper>