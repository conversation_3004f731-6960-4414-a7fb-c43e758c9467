<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.qms.dao.QueueMapper">

    <resultMap id="quqeue" type="com.hvisions.qms.dto.inspection.OrderQueueDTO">
        <result column="step_id" property="stepId"/>
        <result column="step_name" property="stepName"/>
        <result column="standard_id" property="standardId"/>
        <result column="material_id" property="materialId"/>
        <result column="material_name" property="materialName"/>
        <result column="work_order_code" property="workOrderCode"/>
        <result column="inspection_order_id" property="inspectionOrderId"></result>
        <result column="is_current_order" property="isCurrentOrder"></result>
    </resultMap>

    <select id="getNotFinishOrderQueueByStepId" resultMap="quqeue" parameterType="java.lang.Integer">
        select q.*
        from hv_qm_order_queue q
                 left join hv_qm_quality_inspection_order i on q.inspection_order_id = i.id
        where q.step_id = #{stepId}
          and i.inspection_status in (0, 1)
        order by q.update_time, q.create_time
    </select>

</mapper>