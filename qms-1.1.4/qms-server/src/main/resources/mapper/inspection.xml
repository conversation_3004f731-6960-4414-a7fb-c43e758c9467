<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.hvisions.qms.dao.InspectionMapper">

    <resultMap id="getInspection" type="com.hvisions.qms.dto.inspection.ResultInspectionOrderDTO">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="creator_id" property="creatorId"/>
        <result column="site_num" property="siteNum"/>
        <result column="update_time" property="updateTime"/>
        <result column="updater_id" property="updaterId"/>
        <result column="hide" property="hide"/>
        <result column="inspection_code" property="inspectionCode"/>
        <result column="inspection_description" property="inspectionDescription"/>
        <result column="inspection_order_type_id" property="inspectionOrderTypeId"/>
        <result column="inspection_result" property="inspectionResult"/>
        <result column="inspection_status" property="inspectionStatus"/>
        <result column="inspection_user" property="inspectionUser"/>
        <result column="material_id" property="materialId"/>
        <result column="material_name" property="materialName"/>
        <result column="standard_id" property="standardId"/>
        <result column="standard_name" property="standardName"/>
        <result column="standard_version" property="standardVersion"/>
        <result column="step_name" property="stepName"/>
        <result column="work_order_code" property="workOrderCode"/>
        <result column="work_order_id" property="workOrderId"/>
        <result column="submit" property="commit"/>
        <association property="inspectionOrderType"
                     javaType="com.hvisions.qms.dto.type.InspectionOrderTypeDTO">
            <id property="id" column="inspection_type"/>
            <result column="order_type_id" property="id"/>
            <result column="inspection_order_type_code" property="inspectionOrderTypeCode"/>
            <result column="inspection_order_type_name" property="inspectionOrderTypeName"/>
            <result column="inspection_order_type_description" property="inspectionOrderTypeDescription"/>
        </association>
    </resultMap>

    <select id="findByQuery" resultMap="getInspection"
            parameterType="com.hvisions.qms.dto.inspection.InspectionOrderQueryDTO" databaseId="mysql">
        select
        io.id,
        io.create_time,
        io.creator_id,
        io.site_num,
        io.update_time,
        io.updater_id,
        io.hide,
        io.inspection_code,
        io.inspection_description,
        io.inspection_end_time,
        io.inspection_order_type_id,
        io.inspection_result,
        io.inspection_start_time,
        io.inspection_status,
        io.inspection_user,
        io.material_id,
        io.material_name,
        io.standard_id,
        io.step_name,
        io.work_order_code,
        io.work_order_id,
        io.submit,
        ot.inspection_order_type_code,
        ot.inspection_order_type_name,
        ot.id as order_type_id,
        ot.inspection_order_type_description,
        qs.standard_name,
        qs.standard_version,
        qs.operation_according,
        qs.operation_manual,
        qs.operation_manual_name,
        qs.operation_according_name
        from hv_qm_quality_inspection_order io
        left join hv_qm_inspection_order_type ot
        on io.inspection_order_type_id = ot.id
        left join hv_qm_inspection_standard qs
        on qs.id = io.standard_id
        <where>
            <if test="dto.checkListCode !=null and dto.checkListCode != &apos;&apos;">
                and io.inspection_code like concat('%',#{dto.checkListCode},'%')
            </if>
            <if test="dto.materialName !=null and dto.materialName != &apos;&apos;">
                and io.material_name like concat('%',#{dto.materialName},'%')
            </if>
            <if test="dto.inspectionState !=null">
                and io.inspection_status =#{dto.inspectionState}
            </if>
            <if test="dto.checkType !=null">
                and io.inspection_order_type_id = #{dto.checkType}
            </if>
            <if test="dto.startTime !=null and dto.endTime != null">
                and io.create_time between #{dto.startTime} and #{dto.endTime}
            </if>
            <if test="dto.hide !=null">
                and io.hide = #{dto.hide}
            </if>
            <if test="dto.workOrderCode !=null and dto.workOrderCode !=  &apos;&apos;">
                and io.work_order_code like concat('%',#{dto.workOrderCode},'%')
            </if>
        </where>
    </select>
</mapper>