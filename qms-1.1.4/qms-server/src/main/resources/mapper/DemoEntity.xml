<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.qms.dao.StandardMapper">
    <resultMap id="rm" type="com.hvisions.qms.dto.standard.InspectionStandardDTO">
        <!--如果是下划线转驼峰拼写。如果配置了 mybatis.configuration.map-underscore-to-camel-case=true 则不用配置 -->
    </resultMap>

    <resultMap id="standardMaterials" type="com.hvisions.qms.dto.standard.InspectionStandardMaterialDTO">

    </resultMap>

    <select id="findStandards" resultMap="rm">
        SELECT
        t.*,
        g.standards_group_name
        FROM hv_qm_inspection_standard t
        LEFT JOIN hv_qm_inspection_standards_group g
        ON t.standard_group_id = g.id
        <where>
            <if test="materialId!=null">
                AND t.material_id = #{materialId,jdbcType=INTEGER}
            </if>
            <if test="active!=null">
                AND t.standard_version_state = #{active,jdbcType=INTEGER}
            </if>
            <if test="operationId!=null">
                AND exists
                (select 1 from hv_qm_inspection_standards_step s
                where s.standards_id = t.id
                AND s.step_id = #{operationId,jdbcType=INTEGER}
                )
            </if>
        </where>
    </select>

    <select id="findMaterials" resultMap="standardMaterials"
            parameterType="com.hvisions.qms.dto.standard.InspectionStandardQueryDTO" databaseId="mysql">
        select m.id as material_id,m.material_name
        from hiper_base.hv_bm_material m
        where EXISTS (select 1 from hv_qm_inspection_standard t where t.material_id = m.id)
        <if test="dto.materialName != null">
            AND m.material_name like concat('%', #{dto.materialName},'%')
        </if>
    </select>
    <select id="findMaterials" resultMap="standardMaterials"
            parameterType="com.hvisions.qms.dto.standard.InspectionStandardQueryDTO" databaseId="sqlserver">
        select m.id as material_id,m.material_name
        from hiper_base.dbo.hv_bm_material m
        where EXISTS (select 1 from hv_qm_inspection_standard t where t.material_id = m.id)
        <if test="dto.materialName != null">
            AND m.material_name like concat('%', #{dto.materialName},'%')
        </if>
    </select>

</mapper>