package com.hvisions.thirdparty.controller;

import com.hvisions.thirdparty.common.dto.ETLSteelCutTimeDTO;
import com.hvisions.thirdparty.service.ETLSteelCutTimeService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/15
 */
//@Api(tags ="钢板切割线切割时长")
@RestController
@RequestMapping("/ETLSteelCutTime")
public class ETLSteelCutTimeController {

    @Resource
    private ETLSteelCutTimeService etlSteelCutTimeService;

    //查询
    @PostMapping("/getSteelCutTimeList")
    public List<ETLSteelCutTimeDTO> getSteelCutTimeList() {
        return etlSteelCutTimeService.getSteelCutTimeList();
    }

//    @ApiOperation("批量新增")
    @PostMapping("/insertBatch")
    public void insertBatch(@RequestBody List<ETLSteelCutTimeDTO> etlSteelCutTimeDTOList) {
        etlSteelCutTimeService.insertBatch(etlSteelCutTimeDTOList);
    }

//    @ApiOperation("新增一条数据")
    @PostMapping("/insertOne")
    public void insertOne(@RequestBody ETLSteelCutTimeDTO etlSteelCutTimeDTO){
        etlSteelCutTimeService.insertOne(etlSteelCutTimeDTO);
    }


//    @ApiOperation("批量修改")
    @PostMapping("/updateBatch")
    public void updateBatch(@RequestBody List<ETLSteelCutTimeDTO> etlSteelCutTimeDTOList) {
        etlSteelCutTimeService.updateBatch(etlSteelCutTimeDTOList);
    }

//    @ApiOperation("修改一条数据")
    @PostMapping("/updateOne")
    public void updateOne(@RequestBody ETLSteelCutTimeDTO etlSteelCutTimeDTO){
        etlSteelCutTimeService.updateOne(etlSteelCutTimeDTO);
    }

//    @ApiOperation("获取所有数据")
    @GetMapping("/getAll")
    public List<ETLSteelCutTimeDTO> getAll() {
        return etlSteelCutTimeService.getAll();
    }

//    @ApiOperation("根据任务号获取数据")
    @GetMapping("/getSteelCutTimeBytaskNumber/{taskNumber}")
    public ETLSteelCutTimeDTO getSteelCutTimeBytaskNumber(@PathVariable String taskNumber) {
        return etlSteelCutTimeService.getSteelCutTimeBytaskNumber(taskNumber);
    }

}
