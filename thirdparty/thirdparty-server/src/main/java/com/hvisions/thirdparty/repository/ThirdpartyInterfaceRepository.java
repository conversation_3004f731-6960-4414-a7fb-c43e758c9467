package com.hvisions.thirdparty.repository;

import com.hvisions.thirdparty.entity.ThirdpartyInterface;
import com.hvisions.thirdparty.entity.ThirdpartyReceive;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ThirdpartyInterfaceRepository extends JpaRepository<ThirdpartyInterface,Integer> {

    ThirdpartyInterface getThirdpartyInterfaceByInterfaceCodeAndEnableAndLineId(String interfaceCode,Integer enable,Integer lineId);

    List<ThirdpartyInterface> getThirdpartyInterfaceBySystemId(Integer systemId);

    ThirdpartyInterface getThirdpartyInterfaceById(Integer id);

    ThirdpartyInterface getThirdpartyInterfaceByInterfaceCode(String interfaceCode);

    ThirdpartyInterface getThirdpartyInterfaceByLineIdAndEnableAndInterfaceCodeIn(Integer lineId,Integer enable,List<String> interfaceCodes);
}
