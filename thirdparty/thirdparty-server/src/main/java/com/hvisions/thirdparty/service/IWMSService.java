package com.hvisions.thirdparty.service;

import com.hvisions.thirdparty.common.dto.*;

import java.util.List;

public interface IWMSService extends ReceiveAndSendService{

    void pushMaterial(IWMSMaterialDTO materialDTO);

    void sendEmptyTask(IWMEmptyTaskDTO taskDTO);

    void sendFullMaterialInStockTask(IWMSInStockTaskDTO iwmsInStockTaskDTO);

    void outUnbind(IWMSOutStockUnbindDTO outStockUnbind);

    void updateAGVDispatchStatus(AGVStatusDTO agvStatusDTO);

    void outCallMaterial(IWMSCallMaterialDTO callMaterialDTO);

    void stockUpdate(StockInfoDTO stockInfoDTO);

    void sendRCMSByInterface(IWMSSendRCMSDTO rcmsDTO);

    void sendEmptyReturnTask(IWMSEmptyFrameTaskDTO emptyFrameTaskDTO);

    void frameReceiving(List<IWMSFrameDTO> iwmsFrameDTOS);

    StorageSlotsResultDTO sendEmptyStorageSlots(EmptyStorageSlotsDTO storageSlots);

    void processStockOutArrival(StockNotifyPositionAvailable notifyPositionAvailable);

    void processWarehouseLocation(List<WarehouseLocationDTO> warehouseLocationDTO);

    void agvCallback(agvCallbackDTO agvCallbackDTO);

    void pushEmptyPodAlert(EmptyPodAlertDTO emptyPodAlertDTO);

    void updateIntoStock(IntoStockDTO intoStockDTO);
}
