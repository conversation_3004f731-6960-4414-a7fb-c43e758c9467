package com.hvisions.thirdparty.controller;

import com.hvisions.thirdparty.common.dto.*;
import com.hvisions.thirdparty.common.query.ThirdpartyReceiveQuery;
import com.hvisions.thirdparty.entity.ThirdpartyReceive;
import com.hvisions.thirdparty.service.ThirdpartyReceiveRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2023/4/20 14:06
 * @Version 1.0
 */
@Api(tags = "第三方数据-接收记录")
@RestController
@RequestMapping("/receiveRecord")
public class ThirdpartyReceiveRecordController {
    @Resource
    private ThirdpartyReceiveRecordService thirdpartyReceiveRecordService;


    @ApiOperation(value = "id删除")
    @RequestMapping(value = {"/deleteTpReceiveById"}, method = RequestMethod.DELETE)
    public void deleteByIds(@RequestParam Long[] ids) {
        thirdpartyReceiveRecordService.deleteByIds(ids);
    }

    @ApiOperation(value = "id查询")
    @RequestMapping(value = "/findTpReceive/{id}", method = RequestMethod.GET)
    public Optional<ThirdpartyReceive> getTpReceiveById(@PathVariable("id") Long id) {
        return thirdpartyReceiveRecordService.getTpReceiveById(id);
    }

    @ApiOperation(value = "分页查询")
    @PostMapping(value = "/getTpReceivePage")
    public Page<ThirdpartyReceive> getTpReceivePage(@RequestBody ThirdpartyReceiveQuery thirdpartyReceiveQuery) {
        return thirdpartyReceiveRecordService.getTpReceivePage(thirdpartyReceiveQuery);
    }

    //yyyy-mm-dd hh:MM:ss
    @ApiOperation(value = "统计并返回id集合")
    @GetMapping(value = "/getCountAndIdList")
    public Map<String, Object> getCountAndIdList(@RequestParam Long date) {
        return thirdpartyReceiveRecordService.getCountAndIdList(new Date(date));
    }

    @ApiOperation(value = "接收数据集合")
    @GetMapping(value = "/processingStatusInfo")
    public List<ThirdpartyReceive> processingStatusInfo(@RequestParam Long date,
                                                        @RequestParam(value = "status", defaultValue = "2") Integer status,@RequestParam(value = "sendSkidInterfaceCodes")String sendSkidInterfaceCodes) {
        return thirdpartyReceiveRecordService.processingStatusInfo(new Date(date), status,sendSkidInterfaceCodes);
    }

    @ApiOperation(value = "批量重复执行")
    @PostMapping(value = "/redoBatch")
    public void redoBatch(@RequestBody ThirdpartyReceiveDTO[] receiveDTOS) {
        for (ThirdpartyReceiveDTO receiveDTO : receiveDTOS) {
            thirdpartyReceiveRecordService.redoBatch(receiveDTO);
        }
    }

}
