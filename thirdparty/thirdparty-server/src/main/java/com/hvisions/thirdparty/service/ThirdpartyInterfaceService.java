package com.hvisions.thirdparty.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.thirdparty.common.dto.ThirdpartyInterfaceDTO;
import com.hvisions.thirdparty.common.query.ThirdpartyInterfaceQuery;
import com.hvisions.thirdparty.entity.ThirdpartyInterface;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-08-29 10:39
 */
public interface ThirdpartyInterfaceService  {

    Page<ThirdpartyInterface> getTpInterfacePage(ThirdpartyInterfaceQuery tpInterfaceQuery);

    int addTpInterface(ThirdpartyInterface tpInterface);

    int updateTpInterface(ThirdpartyInterface tpInterface);

    //查询启用的接口-不考虑产线
    ThirdpartyInterface getEnableInterfaceByCode(String interfaceCode);
    //查询启用的接口-考虑产线
    ThirdpartyInterface getEnableInterfaceByCode(String interfaceCode,Integer lineId);

    boolean removeById(Integer id);

    String getTpInterfaceByCode(String code);

    boolean removeByIds(List<Integer> idList);

    boolean enable(Integer id, Integer enable);

    ThirdpartyInterface getTpInterfaceById(Integer id);

    ThirdpartyInterface getTpInterfaceByInterfaceCode(String interfaceCode);

    List<ThirdpartyInterface> getTpInterfaceByIds(List<Integer> ids);

    List<ThirdpartyInterface> getTpInterfaceList();

    String getEnableFeedBackInterfaceByLineId(Integer lineId);

    ThirdpartyInterface getTpInterfaceBySystemCodeAndInterCode(String systemCode, String interCode);
}
