package com.hvisions.thirdparty.service.imp;

import com.alibaba.fastjson.JSON;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.hiperbase.client.LocationExtendClient;
import com.hvisions.hiperbase.equipment.location.LocationDTO;
import com.hvisions.pms.client.HvPmMaterialPreparationClient;
import com.hvisions.pms.client.LineReportClient;
import com.hvisions.pms.client.MaterialCutPlanProcessRecordClient;
import com.hvisions.pms.client.XcMaterialOutStockPlanClient;
import com.hvisions.pms.dto.HvPmMaterialCutPlanProcessRecordDTO;
import com.hvisions.thirdparty.common.dto.*;
import com.hvisions.thirdparty.common.enums.FunctionEnum;
import com.hvisions.thirdparty.common.enums.InterfaceCodeEnum;
import com.hvisions.thirdparty.entity.ThirdpartyInterface;
import com.hvisions.thirdparty.service.MaterialCuttingLineService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class MaterialCuttingLineServiceImpl extends ReceiveAndSendServiceImpl implements MaterialCuttingLineService {
    @Autowired
    private LineReportClient lineReportClient;
    @Autowired
    private XcMaterialOutStockPlanClient xcMaterialOutStockPlanClient;
    @Autowired
    private HvPmMaterialPreparationClient hvPmMaterialPreparationClient;
    @Autowired
    private MaterialCutPlanProcessRecordClient materialCutPlanProcessRecordClient;
    @Resource
    private LocationExtendClient locationExtendClient;

    @Override
    public void sendOrderAndMaterial(MaterialCuttingLineOnLineDTO materialCuttingLineOnLineDTO) {
        ResultVO<LocationDTO> vo = locationExtendClient.getHvBmLocationByCode(materialCuttingLineOnLineDTO.getStation_id());
        LocationDTO locationDTO = vo.getData();
        if (locationDTO == null) {
            throw new BaseKnownException("工位编号：" + materialCuttingLineOnLineDTO.getStation_id() + "未配置！");
        }
        String stationId = materialCuttingLineOnLineDTO.getStation_id();
        ResultVO<LocationDTO> locationByCode = locationExtendClient.getLocationByCode(stationId);
        LocationDTO location = locationByCode.getData();
        if (location == null) {
            throw new BaseKnownException("线体编号：" + stationId + "未配置或不存在！");
        }
        sendByInterfaceCode(InterfaceCodeEnum.XC05.getCode(), location.getId(), materialCuttingLineOnLineDTO);
        //下发成功 更新产线流程步骤数据
        HvPmMaterialCutPlanProcessRecordDTO hvPmMaterialCutPlanProcessRecordDTO = new HvPmMaterialCutPlanProcessRecordDTO();
        String lineCode = locationExtendClient.getLineByStationCode(materialCuttingLineOnLineDTO.getStation_id()).getData();
        hvPmMaterialCutPlanProcessRecordDTO.setLineCode(lineCode);
        hvPmMaterialCutPlanProcessRecordDTO.setSequence(3);
        hvPmMaterialCutPlanProcessRecordDTO.setProcessId(3);
        materialCutPlanProcessRecordClient.updateData(hvPmMaterialCutPlanProcessRecordDTO);
    }

    @Override
    public void schedulingFeedback(LineSchedulingFeedBackDTO feedBackDTO) {
        if (feedBackDTO.getLineId() == null) {
            throw new BaseKnownException("产线ID不能为空！请验证发起方（发起调度的系统）的系统编号是否已配置产线！");
        }
        sendByInterfaceCode(InterfaceCodeEnum.XC11.getCode(), feedBackDTO.getLineId(), feedBackDTO);
    }

    @Override
    public void materialRequests(MaterialRequestsDTO materialRequestsDto) {
        ReceiveCommonDTO receiveCommonDTO = new ReceiveCommonDTO();
        receiveCommonDTO.setType(FunctionEnum.XC06.getCode());
        receiveCommonDTO.setData(materialRequestsDto);
        saveAndDoByType(receiveCommonDTO);
        //下发成功 更新产线流程步骤数据
        HvPmMaterialCutPlanProcessRecordDTO hvPmMaterialCutPlanProcessRecordDTO = new HvPmMaterialCutPlanProcessRecordDTO();
        String lineCode = locationExtendClient.getLineByStationCode(materialRequestsDto.getStationId()).getData();
        hvPmMaterialCutPlanProcessRecordDTO.setLineCode(lineCode);
        hvPmMaterialCutPlanProcessRecordDTO.setSequence(2);
        hvPmMaterialCutPlanProcessRecordDTO.setProcessId(3);
        materialCutPlanProcessRecordClient.updateData(hvPmMaterialCutPlanProcessRecordDTO);
    }

    @Override
    public void materialPreparationContent(MaterialPreparationContentDTO materialPreparationContentDTO) {
        ReceiveCommonDTO receiveCommonDTO = new ReceiveCommonDTO();
        receiveCommonDTO.setType(FunctionEnum.XC07.getCode());
        receiveCommonDTO.setData(materialPreparationContentDTO);
        saveAndDoByType(receiveCommonDTO);
    }

    @Override
    public void sendMaterialPreparationComplete(MaterialPreparationCompleteDTO materialPreparationCompleteDTO) {
        if (materialPreparationCompleteDTO.getLineId() == null) {
            throw new BaseKnownException("产线ID不能为空！请验证发起方的系统编号是否已配置产线！");
        }
        MaterialPreparationCompleteDetailDTO materialPreparationCompleteDetailDto = DtoMapper.convert(materialPreparationCompleteDTO, MaterialPreparationCompleteDetailDTO.class);
        sendByInterfaceCode(InterfaceCodeEnum.XC13.getCode(), materialPreparationCompleteDTO.getLineId(), materialPreparationCompleteDetailDto);
    }



    @Override
    public void issuedPlanSend(MaterialCuttingLinePlanDTO materialCuttingLinePlanDTO) {
        String stationId = materialCuttingLinePlanDTO.getStation_id();
        ResultVO<LocationDTO> vo = locationExtendClient.getHvBmLocationByCode(stationId);
        LocationDTO locationDTO = vo.getData();
        if (locationDTO == null) {
            throw new BaseKnownException("工位编号：" + stationId + "未配置！");
        }
        sendByInterfaceCode(InterfaceCodeEnum.XC01.getCode(), locationDTO.getParentId(), materialCuttingLinePlanDTO);

    }

    @Override
    public void profilePartOrderSend(ProfilePartsDTO profilePartsDTO) {
        ResultVO<LocationDTO> vo = locationExtendClient.getHvBmLocationByCode(profilePartsDTO.getStation_id());
        LocationDTO locationDTO = vo.getData();
        if (locationDTO == null) {
            throw new BaseKnownException("工位编号：" + profilePartsDTO.getStation_id() + "未配置！");
        }
        sendByInterfaceCode(InterfaceCodeEnum.XC01.getCode(), locationDTO.getParentId(), profilePartsDTO);
    }

    @Override
    public void sendProfileCodeUpdate(List<ProfileCodeUpdateDTO> profileCodeUpdateDTOS,Integer lineId) {
        if (lineId == null || lineId ==0) {
            throw new BaseKnownException("产线ID不能为空");
        }
        sendByInterfaceCode(InterfaceCodeEnum.XC14.getCode(), lineId, profileCodeUpdateDTOS);
    }

    @Override
    public void cancelProfileTask(ProfilePartsDTO profileTask, Integer lineId) {
        if (lineId == null || lineId == 0) {
            throw new BaseKnownException("产线ID不能为空");
        }
        sendByInterfaceCode(InterfaceCodeEnum.XC03.getCode(), lineId, profileTask);
    }

    @Override
    public void orderReceiving(MaterialCuttingLineReportDTO steelPlateDTO) {
        ReceiveCommonDTO receiveCommonDTO = new ReceiveCommonDTO();
        receiveCommonDTO.setType(FunctionEnum.XC04.getCode());
        receiveCommonDTO.setData(steelPlateDTO);
        saveAndDoByType(receiveCommonDTO);
    }

    @Override
    public ThirdpartResultDTO doByType(ReceiveCommonDTO receiveCommonDTO) {
        FunctionEnum functionEnum = FunctionEnum.getByCode(receiveCommonDTO.getType());
        if (functionEnum == null) {
            throw new BaseKnownException("功能编号：" + receiveCommonDTO.getType() + ":未找到对应的执行方法");
        }
        ResultVO<?> r = null;
        log.info(functionEnum.getDesc());
        switch (functionEnum) {
            case XC04:
                MaterialCuttingLineReportDTO steelPlateDTO = JSON.toJavaObject(JSON.parseObject(JSON.toJSONString(receiveCommonDTO.getData())),MaterialCuttingLineReportDTO.class);
                checkField(steelPlateDTO);
                //生成产线报工记录
                r = lineReportClient.lineReportExecute(steelPlateDTO);
                break;
            case XC06:
                //MaterialRequestsDto接收到的叫料信息，定义了需要那种物料
                MaterialRequestsDTO materialRequestsDto = JSON.toJavaObject(JSON.parseObject(JSON.toJSONString(receiveCommonDTO.getData())),MaterialRequestsDTO.class);
                //新增并将型材出库任务发给 型材立库
                r = xcMaterialOutStockPlanClient.createOutStockPlanByRequests(materialRequestsDto);
                break;
            case XC07:
                //备料内容接收
                MaterialPreparationContentDTO materialPreparationContentDTO = JSON.toJavaObject(JSON.parseObject(JSON.toJSONString(receiveCommonDTO.getData())),MaterialPreparationContentDTO.class);
                //添加备料计划
                r = hvPmMaterialPreparationClient.addMaterialPreparationPlan(materialPreparationContentDTO);
                break;
        }
        //job目前是false,如果后续数量量过大，可考虑使用job的方式，定时分批处理
        return checkAndReturn(false, r);
    }

    //发送消息后， 反馈内容解析
    @Override
    public ResultVO responseVo(ResponseEntity<String> r, ThirdpartyInterface thirdpartyInterface) {
        InterfaceCodeEnum interfaceCodeEnum = InterfaceCodeEnum.getByCode(thirdpartyInterface.getInterfaceCode());
        if (interfaceCodeEnum == null) {
            throw new BaseKnownException("接口：" + thirdpartyInterface.getInterfaceCode() + "未在系统内定义！");
        }
        switch (interfaceCodeEnum) {
            case XC01:
            case XC03:
            case XC05:
            case XC11:
            case XC13:
            case XC14:
                MaterialCuttingLineResultDTO resultDTO = JSON.toJavaObject(JSON.parseObject(r.getBody()), MaterialCuttingLineResultDTO.class);
                if (resultDTO.isSuccess()) {
                    return ResultVO.success(resultDTO);
                } else {
                    return ResultVO.error(interfaceCodeEnum.getDesc() + "执行异常!线控反馈:" + resultDTO.getMessage());
                }
            default:


        }
        return ResultVO.error("接口：" + interfaceCodeEnum.getDesc() + "未定义！");
    }


    private void checkField(MaterialCuttingLineReportDTO steelPlateDTO) {
        StringBuilder message = new StringBuilder();
        if (StringUtils.isBlank(steelPlateDTO.getOrderCode())) {
            message.append("工单号不能为空！");
        }
        if (steelPlateDTO.getActualStartTime() == null) {
            message.append("开始时间不能为空！");
        }
        if (steelPlateDTO.getActualCompletionTime() == null) {
            message.append("结束时间不能为空！");
        }
        if (steelPlateDTO.getReportTime() == null) {
            message.append("报工时间不能为空！");
        }
        if (StringUtils.isBlank(steelPlateDTO.getStationCode())) {
            message.append("工位不能为空！");
        }
        if (!StringUtils.isBlank(message.toString())) {
            throw new BaseKnownException(message.toString());
        }
    }
}
