package com.hvisions.thirdparty.service.imp;

import com.alibaba.fastjson.JSON;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.thirdparty.common.dto.ThirdpartySendDTO;
import com.hvisions.thirdparty.common.enums.InterfaceCodeEnum;
import com.hvisions.thirdparty.common.enums.ThirdpartyInfoStatusEnum;
import com.hvisions.thirdparty.common.query.ThirdpartySendQuery;
import com.hvisions.thirdparty.dao.ThirdpartySendMapper;
import com.hvisions.thirdparty.entity.ThirdpartySend;
import com.hvisions.thirdparty.repository.ThirdpartySendRepository;
import com.hvisions.thirdparty.service.SendService;
import com.hvisions.thirdparty.service.ThirdpartyInterfaceService;
import com.hvisions.thirdparty.service.ThirdpartySendRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class ThirdpartySendRecordServiceImpl implements ThirdpartySendRecordService {
    @Resource
    private ThirdpartySendRepository thirdpartySendRepository;
    @Resource
    private ThirdpartySendMapper thirdpartySendMapper;
    @Resource
    private SendService sendService;
    @Resource
    private ThirdpartyInterfaceService thirdpartyInterfaceService;

    @Override
    public ThirdpartySend createTpSend(String c, Object o) {
        ThirdpartySend hvTpSend = new ThirdpartySend();
        hvTpSend.setStatus(ThirdpartyInfoStatusEnum.WAIT.getCode());
        hvTpSend.setInfoType(c);
        hvTpSend.setCreateTime(new Date());
//        hvTpSend.setSendContent(JSONUtil.toJsonStr(o));
        hvTpSend.setSendContent(JSON.toJSONString(o));
        return thirdpartySendRepository.save(hvTpSend);
    }

    @Override
    public void deleteById(Long id) {
        thirdpartySendRepository.deleteById(id);
    }

    @Override
    public Optional<ThirdpartySend> getTpSendById(Long id) {
        return thirdpartySendRepository.findById(id);
    }

    @Override
    public Page<ThirdpartySend> getTpSendPage(ThirdpartySendQuery receiveQuery) {
        Page<ThirdpartySend> page = PageHelperUtil.getPage(this.thirdpartySendMapper::getList, receiveQuery, ThirdpartySend.class);
        for (ThirdpartySend thirdpartySend : page.getContent()) {
            InterfaceCodeEnum interfaceCodeEnum = InterfaceCodeEnum.getByCode(thirdpartySend.getInfoType());
            thirdpartySend.setInfoType(interfaceCodeEnum == null ? thirdpartySend.getInfoType() : interfaceCodeEnum.getDesc());
        }
        return page;
    }

    @Override
    public void update(ThirdpartySend thirdpartyReceive) {
        thirdpartySendRepository.save(thirdpartyReceive);
    }

    @Override
    @Transactional
    public void deleteByIds(Long[] ids) {
        thirdpartySendMapper.deleteInBatch(Arrays.asList(ids));
    }


    @Override
    public Map<String, Object> getCountAndIdList(Date date) {
        Map<String, Object> r = new HashMap<>();
        long count = thirdpartySendMapper.dateTimeCount(date);
        List<Long> ids = thirdpartySendMapper.selectTopIds(1000, date);
        r.put("size", count);
        r.put("ids", ids);
        return r;
    }

    @Override
    public List<ThirdpartySend> processingStatusInfo(Date date, Integer status, String sendSkidInterfaceCodes) {
        List<String> codeList = null;
        if (!StringUtils.isBlank(sendSkidInterfaceCodes))
            codeList = Arrays.asList(sendSkidInterfaceCodes.split(","));
        return thirdpartySendMapper.processingStatusInfo(date, status, codeList);
    }


    @Override
    public void redoBatch(ThirdpartySendDTO sendDTO) {
        InterfaceCodeEnum interfaceCodeEnum = InterfaceCodeEnum.getByDesc(sendDTO.getInfoType());
        if (interfaceCodeEnum == null) {
            throw new BaseKnownException("内容类型：" + sendDTO.getInfoType() + "未定义！");
        }
        String errorMsg = null;
        boolean flag = true;
        //发送
        try {
            sendService.send(interfaceCodeEnum.getCode(), sendDTO);
        } catch (Exception e) {
            flag = false;
            errorMsg = e.getMessage();
            log.error(e.getMessage(), e);
        }
        //更新发送次数
        if (sendDTO.getId() != null) {
            //状态
            sendDTO.setStatus(!flag ? ThirdpartyInfoStatusEnum.NG.getCode() : ThirdpartyInfoStatusEnum.OK.getCode());
            sendDTO.setUpdateTime(new Date());
            int count = sendDTO.getHandleQuality() + 1;
            sendDTO.setHandleQuality(count);
            if (!StringUtils.isBlank(errorMsg))
                sendDTO.setErrorMessage(sendDTO.getErrorMessage() + "/第" + count + "次:" + errorMsg);

            update(DtoMapper.convert(sendDTO,ThirdpartySend.class));
        }
    }

    @Override
    public void reDoDeleteSendRecord(Date date) {
        Map<String, Object> resultVOData = getCountAndIdList(date);
        long size = Long.parseLong(resultVOData.get("size") == null ? "0" : resultVOData.get("size").toString());
        List<Object> ids = (List<Object>) resultVOData.get("ids");
        if (ids.size() == 0) {
            return;
        }
        Long[] idsL = new Long[ids.size()];
        for (int i = 0; i < ids.size(); i++) {
            idsL[i] = Long.parseLong(ids.get(i).toString());
        }
        deleteByIds(idsL);
        if (size > ids.size()) {
            reDoDeleteSendRecord(date);
        }
    }
}
