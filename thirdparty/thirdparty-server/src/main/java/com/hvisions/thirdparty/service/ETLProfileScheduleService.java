package com.hvisions.thirdparty.service;

import com.hvisions.thirdparty.common.dto.ETLProfileGenDTO;
import com.hvisions.thirdparty.common.dto.ETLProfileScheduleDTO;

import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/15
 */
public interface ETLProfileScheduleService {

    void insertBatch(List<ETLProfileScheduleDTO> etlProfileScheduleDTOList);

    void updateBatch(List<ETLProfileScheduleDTO> etlProfileScheduleDTOList);

    List<ETLProfileScheduleDTO> getListByDate(String date);
}
