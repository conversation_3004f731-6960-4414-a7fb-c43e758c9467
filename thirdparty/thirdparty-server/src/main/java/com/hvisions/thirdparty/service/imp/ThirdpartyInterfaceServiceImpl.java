package com.hvisions.thirdparty.service.imp;

import cn.hutool.json.JSONUtil;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.thirdparty.common.dto.ThirdpartyInterfaceDTO;
import com.hvisions.thirdparty.common.enums.InterfaceCodeEnum;
import com.hvisions.thirdparty.common.query.ThirdpartyInterfaceQuery;
import com.hvisions.thirdparty.dao.ThirdpartyInterfaceMapper;
import com.hvisions.thirdparty.entity.ThirdpartyInterface;
import com.hvisions.thirdparty.entity.ThirdpartySystem;
import com.hvisions.thirdparty.repository.ThirdpartyInterfaceRepository;
import com.hvisions.thirdparty.repository.ThirdpartySystemRepository;
import com.hvisions.thirdparty.service.ThirdpartyInterfaceService;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.hvisions.thirdparty.common.constant.ThirdpartyConstant.THIRD_PARTY_INTERFACE;

/**
 * <AUTHOR>
 * @create 2023-08-29 10:40
 */
@Service
public class ThirdpartyInterfaceServiceImpl implements ThirdpartyInterfaceService {

    @Resource
    private ThirdpartyInterfaceMapper mapper;

    @Resource
    private ThirdpartyInterfaceRepository thirdpartyInterfaceRepository;

    @Resource
    private ThirdpartySystemRepository thirdpartySystemRepository;

    @Resource
    private RedisTemplate redisTemplate;

    @PostConstruct
    public void init() {
        List<ThirdpartyInterface> thirdpartyInterfaces = thirdpartyInterfaceRepository.findAll();

        for (ThirdpartyInterface thirdpartyInterface : thirdpartyInterfaces) {
            redisTemplate.opsForHash().put(THIRD_PARTY_INTERFACE, thirdpartyInterface.getInterfaceCode(), thirdpartyInterface);
        }
    }

    @Override
    public boolean removeById(Integer id) {
        ThirdpartyInterface tpInterface = thirdpartyInterfaceRepository.getOne(id);
        redisTemplate.opsForHash().put(THIRD_PARTY_INTERFACE, tpInterface.getInterfaceCode(), tpInterface);
        thirdpartyInterfaceRepository.deleteById(id);
        return true;
    }

    @Override
    public String getTpInterfaceByCode(String code) {
        return JSONUtil.toJsonStr(thirdpartyInterfaceRepository.getThirdpartyInterfaceByInterfaceCodeAndEnableAndLineId(code, 0, null));
    }

    @Override
    public boolean removeByIds(List<Integer> idList) {
        for (Integer integer : idList) {
            removeById(integer);
        }
        return true;
    }

    @Override
    public boolean enable(Integer id, Integer enable) {
        ThirdpartyInterface thirdpartyInterface = thirdpartyInterfaceRepository.getOne(id);
        thirdpartyInterface.setEnable(enable);
        thirdpartyInterfaceRepository.save(thirdpartyInterface);
        return true;
    }

    @Override
    public ThirdpartyInterface getTpInterfaceById(Integer id) {
        return thirdpartyInterfaceRepository.getThirdpartyInterfaceById(id);
    }

    @Override
    public ThirdpartyInterface getTpInterfaceByInterfaceCode(String interfaceCode) {
        return thirdpartyInterfaceRepository.getThirdpartyInterfaceByInterfaceCode(interfaceCode);
    }

    @Override
    public List<ThirdpartyInterface> getTpInterfaceByIds(List<Integer> ids) {
        return mapper.getTpInterfaceByIds(ids);
    }

    @Override
    public List<ThirdpartyInterface> getTpInterfaceList() {

        return thirdpartyInterfaceRepository.findAll();
    }

    @Override
    public String getEnableFeedBackInterfaceByLineId(Integer lineId) {
        List<String> interfaceCodeList = new ArrayList<>();
        interfaceCodeList.add(InterfaceCodeEnum.XC11.getCode());
        interfaceCodeList.add(InterfaceCodeEnum.XL11.getCode());
        interfaceCodeList.add(InterfaceCodeEnum.HJ10.getCode());
        ThirdpartyInterface thirdpartyInterface = thirdpartyInterfaceRepository.getThirdpartyInterfaceByLineIdAndEnableAndInterfaceCodeIn(lineId, 0, interfaceCodeList);
        return thirdpartyInterface == null ? null : thirdpartyInterface.getInterfaceCode();
    }

    @Override
    public ThirdpartyInterface getTpInterfaceBySystemCodeAndInterCode(String systemCode, String interfaceCode) {
        ThirdpartyInterface tpInterfaceBySystemCodeAndInterCode = mapper.getTpInterfaceBySystemCodeAndInterCode(systemCode, interfaceCode);
        return tpInterfaceBySystemCodeAndInterCode;
    }

    @Override
    public Page<ThirdpartyInterface> getTpInterfacePage(ThirdpartyInterfaceQuery tpInterfaceQuery) {
        return PageHelperUtil.getPage(this.mapper::getTpInterfacePage, tpInterfaceQuery, ThirdpartyInterface.class);
    }

    @Override
    @Transactional
    public int addTpInterface(ThirdpartyInterface tpInterface) {
        redisTemplate.opsForHash().put(THIRD_PARTY_INTERFACE, tpInterface.getInterfaceCode(), tpInterface);
        if (tpInterface.getEnable() == 0) {
            //查找相同接口的信息
            //查找其他相同接口的信息
            ThirdpartyInterface anInterface = thirdpartyInterfaceRepository.getThirdpartyInterfaceByInterfaceCodeAndEnableAndLineId(tpInterface.getInterfaceCode(), 0, tpInterface.getLineId());
            if (anInterface != null) {
                anInterface.setEnable(1);
                updateTpInterface(anInterface);
            }
        }
        ThirdpartySystem system = thirdpartySystemRepository.getOne(tpInterface.getSystemId());
        tpInterface.setLineId(system.getLineId());
        return thirdpartyInterfaceRepository.save(tpInterface).getId();
    }

    @Override
    @Transactional
    public int updateTpInterface(ThirdpartyInterface tpInterface) {
        redisTemplate.opsForHash().put(THIRD_PARTY_INTERFACE, tpInterface.getInterfaceCode(), tpInterface);
        //更新其他接口状态
        if (tpInterface.getEnable() == 0) {
            //查找其他相同接口的信息
            ThirdpartyInterface anInterface = thirdpartyInterfaceRepository.getThirdpartyInterfaceByInterfaceCodeAndEnableAndLineId(tpInterface.getInterfaceCode(), 0, tpInterface.getLineId());
            if (anInterface != null && !tpInterface.getId().equals(anInterface.getId())) {
                anInterface.setEnable(1);
                thirdpartyInterfaceRepository.save(anInterface);
            }

        }
        ThirdpartySystem system = thirdpartySystemRepository.getOne(tpInterface.getSystemId());
        tpInterface.setLineId(system.getLineId());
        return thirdpartyInterfaceRepository.save(tpInterface).getId();
    }

    @Override
    public ThirdpartyInterface getEnableInterfaceByCode(String interfaceCode) {
        return thirdpartyInterfaceRepository.getThirdpartyInterfaceByInterfaceCodeAndEnableAndLineId(interfaceCode, 0, null);
    }

    @Override
    public ThirdpartyInterface getEnableInterfaceByCode(String interfaceCode, Integer lineId) {
        return thirdpartyInterfaceRepository.getThirdpartyInterfaceByInterfaceCodeAndEnableAndLineId(interfaceCode, 0, lineId);
    }


}
