package com.hvisions.thirdparty.service;

import com.hvisions.thirdparty.common.dto.ETLSteelCutTimeDTO;

import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/15
 */
public interface ETLSteelCutTimeService {


    List<ETLSteelCutTimeDTO> getSteelCutTimeList();

    void insertBatch(List<ETLSteelCutTimeDTO> etlSteelCutTimeDTOList);

    void insertOne(ETLSteelCutTimeDTO etlSteelCutTimeDTO);

    void updateBatch(List<ETLSteelCutTimeDTO> etlSteelCutTimeDTOList);

    void updateOne(ETLSteelCutTimeDTO etlSteelCutTimeDTO);

    List<ETLSteelCutTimeDTO> getAll();

    ETLSteelCutTimeDTO getSteelCutTimeBytaskNumber(String taskNumber);

}
