package com.hvisions.thirdparty.service.imp;

import com.alibaba.fastjson.JSON;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.pms.client.HvPmAgvTaskRecordClient;
import com.hvisions.pms.client.HvPmAgvTaskRecordMaterialClient;
import com.hvisions.pms.client.HvPmPlanRouteInfoClient;
import com.hvisions.pms.client.WorkOrderClient;
import com.hvisions.thirdparty.common.dto.ReceiveCommonDTO;
import com.hvisions.thirdparty.common.dto.StockInfoDTO;
import com.hvisions.thirdparty.common.dto.ThirdpartResultDTO;
import com.hvisions.thirdparty.common.enums.FunctionEnum;
import com.hvisions.thirdparty.service.MaterialHandlingRoomService;
import com.hvisions.thirdparty.service.MesService;
import com.hvisions.thirdparty.service.StockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class MaterialHandlingRoomServiceImpl extends ReceiveAndSendServiceImpl implements MaterialHandlingRoomService {

    @Resource
    private MesService mesService;
    @Resource
    private StockService stockService;
    private final OrderProcessingService orderProcessingService;
    @Resource
    private HvPmAgvTaskRecordClient hvPmAgvTaskRecordClient;
    @Resource
    private HvPmAgvTaskRecordMaterialClient hvPmAgvTaskRecordMaterialClient;
    @Resource
    private WorkOrderClient workOrderClient;
    @Resource
    private HvPmPlanRouteInfoClient hvPmPlanRouteInfoClient;

    public MaterialHandlingRoomServiceImpl(OrderProcessingService orderProcessingService) {
        this.orderProcessingService = orderProcessingService;
    }


    @Override
    public void stockUpdate(StockInfoDTO stockInfoDTO) {
        ReceiveCommonDTO receiveCommonDTO = new ReceiveCommonDTO();
        receiveCommonDTO.setType(FunctionEnum.WMS18.getCode());
        receiveCommonDTO.setData(stockInfoDTO);
        saveAndDoByType(receiveCommonDTO);
    }


    @Override
    public ThirdpartResultDTO doByType(ReceiveCommonDTO receiveCommonDTO) {
        FunctionEnum functionEnum = FunctionEnum.getByCode(receiveCommonDTO.getType());
        if (functionEnum == null) {
            throw new BaseKnownException("功能编号：" + receiveCommonDTO.getType() + ":未找到对应的执行方法");
        }
        log.info(functionEnum.getDesc());
        switch (functionEnum){
            case WMS18:
                StockInfoDTO stockInfoDTO = JSON.toJavaObject(JSON.parseObject(JSON.toJSONString(receiveCommonDTO.getData())),StockInfoDTO.class);
                //更新库存
                stockService.updateStock(stockInfoDTO);
                orderProcessingService.orderStart(stockInfoDTO);
                break;
        }
        return new ThirdpartResultDTO(false, null);
    }
}
