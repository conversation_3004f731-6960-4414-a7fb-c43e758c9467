package com.hvisions.thirdparty.controller;

import com.hvisions.thirdparty.common.dto.ETLProfileGenDTO;
import com.hvisions.thirdparty.service.ETLProfileGenService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <P>型材切割线-每日型材产量 <P>
 *
 * <AUTHOR>
 * @date 2024/8/15
 */
//@Api(tags = "型材切割线-每日型材产量")
@RestController
@RequestMapping("/ETLProfileGen")
public class ETLProfileGenController {
    @Resource
    private ETLProfileGenService etlProfileGenService;

//    @ApiOperation("批量新增")
    @PostMapping("/insertBatch")
    public void insertBatch(@RequestBody List<ETLProfileGenDTO> etlProfileGenDTOList) {
        etlProfileGenService.insertBatch(etlProfileGenDTOList);
    }


//    @ApiOperation("批量修改")
    @PostMapping("/updateBatch")
    public void updateBatch(@RequestBody List<ETLProfileGenDTO> etlProfileGenDTOList) {
        etlProfileGenService.updateBatch(etlProfileGenDTOList);
    }

//    @ApiOperation("获取某日数据")
    @GetMapping("/getListByDate/{date}")
    public List<ETLProfileGenDTO> getListByDate(@PathVariable String date) {
        return etlProfileGenService.getListByDate(date);
    }

}
