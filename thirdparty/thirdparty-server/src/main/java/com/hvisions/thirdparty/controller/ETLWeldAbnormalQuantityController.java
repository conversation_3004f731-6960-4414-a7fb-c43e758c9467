package com.hvisions.thirdparty.controller;

import com.hvisions.thirdparty.common.dto.ETLWeldAbnormalQuantityDTO;
import com.hvisions.thirdparty.service.ETLWeldAbnormalQuantityService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/15
 */
//@Api(tags = "下料异常件个数")
@RestController
@RequestMapping("/ETLWeldAbnormalQuantity")
public class ETLWeldAbnormalQuantityController {

    @Resource
    private ETLWeldAbnormalQuantityService etlWeldAbnormalQuantityService;

//    @ApiOperation("批量新增")
    @PostMapping("/insertBatch")
    public void insertBatch(@RequestBody List<ETLWeldAbnormalQuantityDTO> etlWeldAbnormalQuantityDTOList) {
        etlWeldAbnormalQuantityService.insertBatch(etlWeldAbnormalQuantityDTOList);
    }

//    @ApiOperation("新增一条数据")
    @PostMapping("/insertOne")
    public void insertOne(@RequestBody ETLWeldAbnormalQuantityDTO etlWeldAbnormalQuantityDTO){
        etlWeldAbnormalQuantityService.insertOne(etlWeldAbnormalQuantityDTO);
    }


//    @ApiOperation("修改一条数据")
    @PostMapping("/updateOne")
    public void updateOne(@RequestBody ETLWeldAbnormalQuantityDTO etlWeldAbnormalQuantityDTO){
        etlWeldAbnormalQuantityService.updateOne(etlWeldAbnormalQuantityDTO);
    }


//    @ApiOperation("批量修改")
    @PostMapping("/updateBatch")
    public void updateBatch(@RequestBody List<ETLWeldAbnormalQuantityDTO> etlWeldAbnormalQuantityDTOList) {
        etlWeldAbnormalQuantityService.updateBatch(etlWeldAbnormalQuantityDTOList);
    }

//    @ApiOperation("获取所有数据")
    @GetMapping("/getAll")
    public List<ETLWeldAbnormalQuantityDTO> getAll() {
        return etlWeldAbnormalQuantityService.getAll();
    }

//    @ApiOperation("根据任务号获取数据")
    @GetMapping("/getDataByTaskNumber")
    public ETLWeldAbnormalQuantityDTO getDataByTaskNumber(@PathVariable String taskNumber){
        return etlWeldAbnormalQuantityService.getDataByTaskNumber(taskNumber);
    }
}
