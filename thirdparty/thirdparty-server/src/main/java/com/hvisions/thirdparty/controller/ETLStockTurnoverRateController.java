package com.hvisions.thirdparty.controller;

import com.hvisions.thirdparty.common.dto.ETLStockTurnoverRateDTO;
import com.hvisions.thirdparty.service.ETLStockTurnoverRateService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

//@Api(tags = "库存周转率")
@RestController
@RequestMapping("/ETLStockTurnoverRate")
public class ETLStockTurnoverRateController {

    @Resource
    private ETLStockTurnoverRateService etlStockTurnoverRateService;

//    @ApiOperation("批量新增")
    @PostMapping("/insertBatch")
    public void insertBatch(List<ETLStockTurnoverRateDTO> planFinishDTOList) {
        etlStockTurnoverRateService.insertBatch(planFinishDTOList);
    }


//    @ApiOperation("批量修改")
    @PostMapping("/updateBatch")
    public void updateBatch(List<ETLStockTurnoverRateDTO> planFinishDTOList) {
        etlStockTurnoverRateService.updateBatch(planFinishDTOList);
    }
}
