package com.hvisions.thirdparty.service.imp;

import com.hvisions.thirdparty.common.dto.ETLPreProductionStatisticalDTO;
import com.hvisions.thirdparty.dao.ETLPreProductionStatisticalMapper;
import com.hvisions.thirdparty.service.ETLPreProductionStatisticalService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ETLPreProductionStatisticalServiceImpl implements ETLPreProductionStatisticalService {

    @Resource
    private ETLPreProductionStatisticalMapper etlPreProductionStatisticalMapper;

    @Override
    public void insertBatch(List<ETLPreProductionStatisticalDTO> planFinishDTOList) {
        etlPreProductionStatisticalMapper.insertBatch(planFinishDTOList);
    }

    @Override
    public void updateBatch(List<ETLPreProductionStatisticalDTO> planFinishDTOList) {
        for (ETLPreProductionStatisticalDTO etlPreProductionStatisticalDTO : planFinishDTOList) {
            etlPreProductionStatisticalMapper.update(etlPreProductionStatisticalDTO);
        }
    }
}
