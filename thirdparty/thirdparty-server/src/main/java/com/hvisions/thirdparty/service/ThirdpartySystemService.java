package com.hvisions.thirdparty.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hvisions.thirdparty.common.query.ThirdpartySystemQuery;
import com.hvisions.thirdparty.entity.ThirdpartySystem;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-08-31 15:32
 */
public interface ThirdpartySystemService {

    Page<ThirdpartySystem> getthirdpartySystemPage(ThirdpartySystemQuery thirdpartySystemQuery);

    List<ThirdpartySystem> selectThirdpartySystem(String paramName, Object value);

    Integer insert(ThirdpartySystem thirdpartySystem);

    void removeByIds(List<Integer> idList);

    void updateById(ThirdpartySystem thirdpartySystem);

    ThirdpartySystem getById(Integer systemId);

    ThirdpartySystem getByCode(String requestSystem);

    List<ThirdpartySystem> getThirdpartySystemList();

    ThirdpartySystem getThirdpartySystemBySystemCode(String systemCode);
}
