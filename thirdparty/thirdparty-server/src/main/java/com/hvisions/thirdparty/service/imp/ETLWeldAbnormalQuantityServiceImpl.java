package com.hvisions.thirdparty.service.imp;

import com.hvisions.thirdparty.common.dto.ETLPlanFinishDTO;
import com.hvisions.thirdparty.common.dto.ETLWeldAbnormalQuantityDTO;
import com.hvisions.thirdparty.dao.ETLWeldAbnormalQuantityMapper;
import com.hvisions.thirdparty.service.ETLWeldAbnormalQuantityService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/15
 */
@Service
public class ETLWeldAbnormalQuantityServiceImpl implements ETLWeldAbnormalQuantityService {
    @Resource
    private ETLWeldAbnormalQuantityMapper etlWeldAbnormalQuantityMapper;

    @Override
    public void insertBatch(List<ETLWeldAbnormalQuantityDTO> etlWeldAbnormalQuantityDTOList) {
        etlWeldAbnormalQuantityMapper.insertBatch(etlWeldAbnormalQuantityDTOList);
    }

    @Override
    public void insertOne(ETLWeldAbnormalQuantityDTO etlWeldAbnormalQuantityDTO) {
        etlWeldAbnormalQuantityMapper.insertOne(etlWeldAbnormalQuantityDTO);
    }

    @Override
    public void updateOne(ETLWeldAbnormalQuantityDTO etlWeldAbnormalQuantityDTO) {
        etlWeldAbnormalQuantityMapper.update(etlWeldAbnormalQuantityDTO);
    }

    @Override
    public void updateBatch(List<ETLWeldAbnormalQuantityDTO> etlWeldAbnormalQuantityDTOList) {
        for (ETLWeldAbnormalQuantityDTO etlWeldAbnormalQuantityDTO : etlWeldAbnormalQuantityDTOList) {
            etlWeldAbnormalQuantityMapper.update(etlWeldAbnormalQuantityDTO);
        }
    }

    @Override
    public List<ETLWeldAbnormalQuantityDTO> getAll() {
        return etlWeldAbnormalQuantityMapper.getAll();
    }

    @Override
    public ETLWeldAbnormalQuantityDTO getDataByTaskNumber(String taskNumber) {
        return etlWeldAbnormalQuantityMapper.getDataByTaskNumber(taskNumber);
    }
}
