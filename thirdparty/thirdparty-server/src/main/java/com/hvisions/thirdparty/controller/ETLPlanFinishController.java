package com.hvisions.thirdparty.controller;

import com.hvisions.thirdparty.common.dto.ETLPlanFinishDTO;
import com.hvisions.thirdparty.service.ETLPlanFinishService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

//@Api(tags = "任务完成率")
@RestController
@RequestMapping("/ETLPlanFinish")
public class ETLPlanFinishController {

    @Resource
    private ETLPlanFinishService etlPlanFinishService;

//    @ApiOperation("批量新增")
    @PostMapping("/insertBatch")
    public void insertBatch(@RequestBody List<ETLPlanFinishDTO> planFinishDTOList) {
        etlPlanFinishService.insertBatch(planFinishDTOList);
    }


//    @ApiOperation("批量修改")
    @PostMapping("/updateBatch")
    public void updateBatch(@RequestBody List<ETLPlanFinishDTO> planFinishDTOList) {
        etlPlanFinishService.updateBatch(planFinishDTOList);
    }

//    @ApiOperation("获取某日数据")
    @GetMapping("/getListByDate/{date}")
    public List<ETLPlanFinishDTO> getListByDate(@PathVariable String date) {
        return etlPlanFinishService.getListByDate(date);
    }

}
