package com.hvisions.thirdparty.service;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.thirdparty.common.dto.ReceiveCommonDTO;
import com.hvisions.thirdparty.common.dto.ThirdpartResultDTO;
import com.hvisions.thirdparty.entity.ThirdpartyInterface;
import com.hvisions.thirdparty.entity.ThirdpartySystem;
import org.springframework.http.ResponseEntity;

/**
 * 注意不在直接注入该 bean
 */
public interface ReceiveAndSendService {

    /**
     * （接收的数据统一调用）根据接收内容调用 saveAndDoByType
     *
     * @param o 接收内容
     */
    void receiveAllAndDoByType(Object o);

    /**
     * 存储数据并根据功能类型执行 doByType
     *
     * @param info 接收内容
     * @return
     */
    Object saveAndDoByType(ReceiveCommonDTO info);

    /**
     * 根据接口编号发送数据
     *
     * @param interfaceCode 接口编号
     * @param data          发送内容
     */
    Object sendByInterfaceCode(String interfaceCode, Object data);
    /**
     * 根据接口编号、产线 发送数据
     *
     * @param interfaceCode 接口编号
     * @param data          发送内容
     */
    Object sendByInterfaceCode(String interfaceCode,Integer lineId , Object data);

    /**
     * 根据接口编号发送数据（不保存发送内容）
     *
     * @param interfaceCode 接口编号
     * @param data          发送内容
     */
    Object sendByInterfaceCodeNotSave(String interfaceCode, Object data);
    /**
     * 根据接口编号、产线 发送数据（不保存发送内容）
     *
     * @param interfaceCode 接口编号
     * @param data          发送内容
     */
    Object sendByInterfaceCodeNotSave(String interfaceCode,Integer lineId , Object data);
    /*
     *对应的实现类 需要重写 参考 MESReceiveAndSendServiceImpl
     * @param receiveCommonDTO 转换过的接收内容
     */
    ThirdpartResultDTO doByType(ReceiveCommonDTO receiveCommonDTO);




    /**
     * restful 发送
     * @param system 第三方系统
     * @param thirdpartyInterface 第三方接口
     * @param data 发送内容
     * @return 结果
     */
    ResponseEntity<String>  restFulSend(ThirdpartySystem system, ThirdpartyInterface thirdpartyInterface, Object data);

    /**
     * SOAP 发送
     * @param system 第三方系统
     * @param thirdpartyInterface 第三方接口
     * @param data 发送内容
     * @return 结果
     */
    ResultVO<?> SOAPSend(ThirdpartySystem system, ThirdpartyInterface thirdpartyInterface, Object data);


    /**
     * 发送反馈处理
     * @param r 反馈 ResponseEntity
     * @param thirdpartyInterface 第三方接口
     * @return 结果
     */
    ResultVO responseVo(ResponseEntity<String> r, ThirdpartyInterface thirdpartyInterface);

}
