package com.hvisions.thirdparty.controller;

import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.thirdparty.common.query.ThirdpartySystemQuery;
import com.hvisions.thirdparty.entity.ThirdpartySystem;
import com.hvisions.thirdparty.service.ThirdpartySystemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-08-31 15:30
 */
@Slf4j
@RestController
@Api(tags = "第三方系统管理")
@RequestMapping(value = "/thirdpartySystem")
public class ThirdpartySystemController {

    @Autowired
    private ThirdpartySystemService thirdpartySystemService;


    @ApiOperation(value = "新增")
    @PostMapping(value = "/addThirdpartySystem")
    public Integer addOrUpdatethirdpartySystem(@RequestBody ThirdpartySystem thirdpartySystem, @UserInfo @ApiIgnore UserInfoDTO userInfoDTO) {
        return thirdpartySystemService.insert(thirdpartySystem);
    }


    @ApiOperation(value = "删除")
    @DeleteMapping(value = "/delThirdpartySystem")
    public void delete(@RequestBody List<Integer> idList, @UserInfo @ApiIgnore UserInfoDTO userInfoDTO) {
        thirdpartySystemService.removeByIds(idList);

    }


    @ApiOperation(value = "修改")
    @PostMapping(value = "/updateThirdpartySystem")
    public void updateThirdpartySystem(@RequestBody ThirdpartySystem thirdpartySystem, @UserInfo @ApiIgnore UserInfoDTO userInfoDTO) {
        thirdpartySystemService.updateById(thirdpartySystem);
    }


    @ApiOperation(value = "查询")
    @PostMapping(value = "/selectThirdpartySystem")
    public List<ThirdpartySystem> selectThirdpartySystem(@RequestParam(value = "paramName") String paramName, @RequestParam(value = "value") Object value, @UserInfo @ApiIgnore UserInfoDTO userInfoDTO) {
        return thirdpartySystemService.selectThirdpartySystem(paramName, value);
    }


    @ApiOperation(value = "分页查询")
    @PostMapping(value = "/getThirdpartySystemPage")
    public Page<ThirdpartySystem> getthirdpartySystemPage(@RequestBody ThirdpartySystemQuery thirdpartySystemQuery, @UserInfo @ApiIgnore UserInfoDTO userInfoDTO) {
        return thirdpartySystemService.getthirdpartySystemPage(thirdpartySystemQuery);
    }

    @ApiOperation(value = "查询所有")
    @PostMapping(value = "/getThirdpartySystemList")
    public List<ThirdpartySystem> getThirdpartySystemList() {
        return thirdpartySystemService.getThirdpartySystemList();
    }

    @ApiOperation(value = "根据系统编号")
    @GetMapping(value = "/getThirdpartySystemBySystemCode/{systemCode}")
    public ThirdpartySystem getThirdpartySystemBySystemCode(@PathVariable("systemCode") String systemCode) {
        return thirdpartySystemService.getThirdpartySystemBySystemCode(systemCode);
    }
}
