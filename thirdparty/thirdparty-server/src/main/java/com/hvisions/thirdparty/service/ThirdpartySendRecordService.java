package com.hvisions.thirdparty.service;

import com.hvisions.thirdparty.common.query.ThirdpartySendQuery;
import com.hvisions.thirdparty.entity.ThirdpartySend;
import org.springframework.data.domain.Page;
import com.hvisions.thirdparty.common.dto.*;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2023/4/20 14:01
 * @Version 1.0
 */
public interface ThirdpartySendRecordService {

    ThirdpartySend createTpSend(String type, Object o);

    /**
     * 删除
     */
    void deleteById(Long id);

    /**
     * 查询
     */
    Optional<ThirdpartySend> getTpSendById(Long id);

    Page<ThirdpartySend> getTpSendPage(ThirdpartySendQuery receiveQuery);


    void update(ThirdpartySend thirdpartyReceive);

    void deleteByIds(Long[] id);

    Map<String, Object> getCountAndIdList(Date date);

    List<ThirdpartySend> processingStatusInfo(Date date, Integer status,String sendSkidInterfaceCodes);

    void redoBatch(ThirdpartySendDTO sendDTO);

    void reDoDeleteSendRecord(Date date);
}
