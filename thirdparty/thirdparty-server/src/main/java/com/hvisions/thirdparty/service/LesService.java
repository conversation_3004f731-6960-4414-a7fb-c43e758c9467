package com.hvisions.thirdparty.service;

import com.hvisions.thirdparty.common.dto.*;

public interface LesService {

    /**
     * 发送-西部车间到总装车间转运工单下发
     * @param lesTransportTaskApplyDTO
     */
    void transportTaskApplySend(LesTransportTaskApplyDTO lesTransportTaskApplyDTO);

    void transportTaskFeedBackSend(LesTransportTaskFeedBackDTO lesTransportTaskFeedBackDTO);

    void LesOutfittingSectionWorkOrderSend(LesOutfittingSectionWorkOrderDTO lesOutfittingSectionWorkOrderDTO);

    void lesOutfittingPalletArrivalFeedBack(LesOutfittingPalletArrivalFeedBackDTO lesOutfittingPalletArrivalFeedBackDTO);
}
