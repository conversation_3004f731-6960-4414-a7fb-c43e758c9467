package com.hvisions.thirdparty.service;

import com.hvisions.thirdparty.common.dto.*;

public interface ProfileCompactStorageService extends ReceiveAndSendService{

    void sendOutboundTask(ProfileOutboundTaskDTO profileOutboundInfoDTO);

    void OutboundInfo(ProfileOutboundInfoDTO profileOutboundInfoDTO);

    void stockUpdate(StockInfoDTO stockInfoDTO);

    void sendPreparationTask(MaterialPreparationTaskDTO materialPreparationTaskDTO);

    void receivePreparationResult(MaterialPreparationResultDTO materialPreparationResultDTO);
}
