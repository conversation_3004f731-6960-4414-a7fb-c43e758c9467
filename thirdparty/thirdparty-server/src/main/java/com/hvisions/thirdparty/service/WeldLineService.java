package com.hvisions.thirdparty.service;


import com.hvisions.thirdparty.common.dto.*;

import java.util.List;

public interface WeldLineService extends  ReceiveAndSendService{
    /**
     * 组立工单下发
     * @param assemblyWorkOrderDTO 组立工单
     */
    void assemblyWorkOrderSend(AssemblyWorkOrderDTO assemblyWorkOrderDTO);

    /**
     * 组立工单报工
     * @param assemblyWorkOrderReportingDTO 组立工单报工
     */
    void assemblyWorkOrderReporting(AssemblyWorkOrderReportingDTO assemblyWorkOrderReportingDTO);

    void repair(WeldLineRepairDTO weldLineRepairDTO);

    void schedulingFeedback(LineSchedulingFeedBackDTO feedBackDTO);

    void callMaterials(List<WeldLineCallMaterialsDTO> weldLineCallMaterialsDTOS);

    WeldLineResultDTO sendPolishingPlanFeedback(AssemblyWorkOrderDTO assemblyWorkOrderDTO);

    void receivePolishingWork(PolishingWorkReportingDTO reportingDTO);
}
