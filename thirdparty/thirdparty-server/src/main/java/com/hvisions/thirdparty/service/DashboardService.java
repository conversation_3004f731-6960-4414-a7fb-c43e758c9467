package com.hvisions.thirdparty.service;

import com.hvisions.thirdparty.common.dto.MaterialCuttingLineReportToZFDTO;
import com.hvisions.thirdparty.common.dto.PartOrderReportingDTO;
import com.hvisions.thirdparty.common.dto.SyncXcOrderReportDTO;
import com.hvisions.thirdparty.common.dto.XcOrderReportDTO;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/9/27
 */
public interface DashboardService extends ReceiveAndSendService {

    void partOrderReportingSend(PartOrderReportingDTO partOrderReportingDTO);

    void weldOrderReportingSend(MaterialCuttingLineReportToZFDTO materialCuttingLineReportToZFDTO);

    void xcOrderReportingSend(XcOrderReportDTO xcOrderReportDTO);

    void syncXcOrderReport(SyncXcOrderReportDTO syncXcOrderReportDTO);
}
