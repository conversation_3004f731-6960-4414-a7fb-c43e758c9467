package com.hvisions.thirdparty.service;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.thirdparty.common.dto.ETLSteelScheduleDTO;
import com.hvisions.thirdparty.common.query.ETLSteelScheduleQuery;
import org.springframework.data.domain.Page;

import java.io.IOException;
import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/15
 */
public interface ETLSteelScheduleService {

    Page<ETLSteelScheduleDTO> getPage(ETLSteelScheduleQuery etlSteelScheduleQuery);


    ResultVO<ExcelExportDto> exportEtlSteel(ETLSteelScheduleQuery etlSteelScheduleQuery)  throws IOException, IllegalAccessException;

    void insertBatch(List<ETLSteelScheduleDTO> eTLSteelScheduleDTOList);

    void updateBatch(List<ETLSteelScheduleDTO> eTLSteelScheduleDTOList);

    List<ETLSteelScheduleDTO> getListByDate(String date);


}
