package com.hvisions.thirdparty.service;

import com.hvisions.thirdparty.common.dto.*;

import java.util.List;

public interface MaterialCuttingLineService extends ReceiveAndSendService{
    
    void issuedPlanSend(MaterialCuttingLinePlanDTO dto);

    void orderReceiving(MaterialCuttingLineReportDTO steelPlateDTO);

    void sendOrderAndMaterial(MaterialCuttingLineOnLineDTO materialCuttingLineOnLineDTO);

    void schedulingFeedback(LineSchedulingFeedBackDTO feedBackDTO);

    void materialRequests(MaterialRequestsDTO materialRequestsDto);

    void materialPreparationContent(MaterialPreparationContentDTO materialPreparationContentDTO);

    void sendMaterialPreparationComplete(MaterialPreparationCompleteDTO materialPreparationCompleteDTO);

    void profilePartOrderSend(ProfilePartsDTO profilePartsDTO);

    void sendProfileCodeUpdate(List<ProfileCodeUpdateDTO> profileCodeUpdateDTOS,Integer lineId);

    void cancelProfileTask(ProfilePartsDTO profileTask, Integer lineId);
}
