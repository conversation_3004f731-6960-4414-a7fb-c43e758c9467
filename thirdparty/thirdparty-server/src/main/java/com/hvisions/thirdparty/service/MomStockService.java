package com.hvisions.thirdparty.service;

import com.hvisions.thirdparty.common.dto.*;

import java.util.List;

public interface MomStockService {
    void stockSync(MomStockSyncDTO momStockSyncDTO);

    /**
     * 料点数据同步通用接口，用于型材切割线、板材切割线的基础料框数据同步
     * @param momWarehouseLocationDTO
     */
    void wearHouseDataSync(MomWarehouseLocationDTO momWarehouseLocationDTO,String InterfaceCodeNum);

    /**
     * 料况基础数据同步通用接口，用于型材切割线、板材切割线的料框数据同步
     * @param momFrameDataDTO
     */
    void freamDataSync(List<MomFrameDataDTO> momFrameDataDTO, String InterfaceCodeNum);

    /**
     * 产线调度请求
     * @param receiveReqDTO
     * @param interfaceCodeNum
     */
    void schedulingReceiveReq(LineSchedulingReceiveReqDTO receiveReqDTO,String interfaceCodeNum);

    /**
     * 出入库数据同步-型材立库产线调度请求
     * @param xcStorageRequestDTO
     */
    void syncInOutboundData(XcStorageRequestDTO xcStorageRequestDTO);

    /**
     * 产线调度反馈
     */
    void schedulingFeedBack(LineSchedulingResponseDTO LineSchedulingResponseDTO);
}
