package com.hvisions.thirdparty.service.imp;

import com.hvisions.thirdparty.common.dto.ETLMaterialCompletenessDTO;
import com.hvisions.thirdparty.dao.ETLMaterialCompletenessMapper;
import com.hvisions.thirdparty.service.ETLMaterialCompletenessService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ETLMaterialCompletenessServiceImpl implements ETLMaterialCompletenessService {

    @Resource
    public ETLMaterialCompletenessMapper etlMaterialCompletenessMapper;

    @Override
    public void insertBatch(List<ETLMaterialCompletenessDTO> completenessDTOList) {
        etlMaterialCompletenessMapper.insertBatch(completenessDTOList);
    }

    @Override
    public void updateBatch(List<ETLMaterialCompletenessDTO> completenessDTOList) {
        for (ETLMaterialCompletenessDTO completenessDTO : completenessDTOList) {
            etlMaterialCompletenessMapper.update(completenessDTO);
        }
    }

    @Override
    public void insertOne(ETLMaterialCompletenessDTO completenessDTO) {
        etlMaterialCompletenessMapper.insertOne(completenessDTO);
    }

    @Override
    public void updateOne(ETLMaterialCompletenessDTO completenessDTO) {
        etlMaterialCompletenessMapper.update(completenessDTO);
    }

    @Override
    public ETLMaterialCompletenessDTO getOneByDate(String date) {
        return etlMaterialCompletenessMapper.getOneByDate(date);
    }
}
