package com.hvisions.thirdparty.service;

import com.hvisions.thirdparty.common.dto.ETLPlanFinishDTO;
import com.hvisions.thirdparty.common.dto.ETLPlanQualityDTO;

import java.util.List;

public interface ETLPlanFinishService {
    void insertBatch(List<ETLPlanFinishDTO> planFinishDTOList);

    void updateBatch(List<ETLPlanFinishDTO> planFinishDTOList);

    void lineTaskIndicators();

    List<ETLPlanFinishDTO> getListByDate(String date);
}
