package com.hvisions.thirdparty.service.imp;

import com.hvisions.thirdparty.common.dto.ETLStockTurnoverRateDTO;
import com.hvisions.thirdparty.dao.ETLStockTurnoverRateMapper;
import com.hvisions.thirdparty.service.ETLStockTurnoverRateService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ETLStockTurnoverRateServiceImpl implements ETLStockTurnoverRateService {
    @Resource
    private ETLStockTurnoverRateMapper etlStockTurnoverRateMapper;

    @Override
    public void insertBatch(List<ETLStockTurnoverRateDTO> planFinishDTOList) {
        etlStockTurnoverRateMapper.insertBatch(planFinishDTOList);
    }

    @Override
    public void updateBatch(List<ETLStockTurnoverRateDTO> planFinishDTOList) {
        for (ETLStockTurnoverRateDTO etlStockTurnoverRateDTO : planFinishDTOList) {
            etlStockTurnoverRateMapper.update(etlStockTurnoverRateDTO);
        }
    }
}
