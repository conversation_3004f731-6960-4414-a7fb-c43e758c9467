package com.hvisions.thirdparty.controller;

import com.hvisions.thirdparty.common.dto.ETLMaterialCompletenessDTO;
import com.hvisions.thirdparty.service.ETLMaterialCompletenessService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

//@Api(tags = "齐套率")
@RestController
@RequestMapping("/ETLMaterialCompletenessRote")
public class ETLMaterialCompletenessController {

    @Resource
    private ETLMaterialCompletenessService etlMaterialCompletenessService;

//    @ApiOperation("批量新增")
    @PostMapping("/insertBatch")
    public void insertBatch(List<ETLMaterialCompletenessDTO> completenessDTOList) {
        etlMaterialCompletenessService.insertBatch(completenessDTOList);
    }


//    @ApiOperation("批量修改")
    @PostMapping("/updateBatch")
    public void updateBatch(List<ETLMaterialCompletenessDTO> completenessDTOList) {
        etlMaterialCompletenessService.updateBatch(completenessDTOList);
    }


//    @ApiOperation("新增一条数据")
    @PostMapping("/insertOne")
    public void insertOne(@RequestBody ETLMaterialCompletenessDTO etlMaterialCompletenessDTO){
        etlMaterialCompletenessService.insertOne(etlMaterialCompletenessDTO);
    }


//    @ApiOperation("修改一条数据")
    @PostMapping("/updateOne")
    public void  updateOne(@RequestBody ETLMaterialCompletenessDTO etlMaterialCompletenessDTO){
        etlMaterialCompletenessService.updateOne(etlMaterialCompletenessDTO);
    }

//    @ApiOperation("获取某日数据")
    @GetMapping("/getOneByDate/{date}")
    public ETLMaterialCompletenessDTO getOneByDate(@PathVariable("date") String date){
        return etlMaterialCompletenessService.getOneByDate(date);
    }

}
