package com.hvisions.thirdparty.service;

import com.hvisions.thirdparty.common.dto.*;

import java.util.List;

public interface MaterialNestingService extends ReceiveAndSendService {
    /**
     * 接收-钢板切割任务
     *
     * @param materialCutPlanDTO 钢板切割
     */
    void materialCutPlan(List<MaterialCutPlanDTO> materialCutPlanDTO);

    void stockSend(NTKRequestDTO ntkRequestDTO);

    void reportCuttingTask(NestCuttingTaskDTO nestCuttingTaskDTO);

    void xcMaterialCutPlan(List<XcMaterialCutPlanDTO> xcMaterialCutPlanDTO);

    void xcStockSend(NTKRequestXcDTO requestDTO);
}
