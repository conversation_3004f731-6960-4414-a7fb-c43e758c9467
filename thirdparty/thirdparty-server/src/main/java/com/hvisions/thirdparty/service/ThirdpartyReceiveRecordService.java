package com.hvisions.thirdparty.service;

import com.hvisions.thirdparty.common.query.ThirdpartyReceiveQuery;
import com.hvisions.thirdparty.entity.ThirdpartyReceive;
import org.springframework.data.domain.Page;
import com.hvisions.thirdparty.common.dto.*;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2023/4/20 14:01
 * @Version 1.0
 */
public interface ThirdpartyReceiveRecordService {

    ThirdpartyReceive createTpReceive(String type, Object o);

    ThirdpartyReceive createTpReceive(String type, Object o, HttpServletRequest request);

    /**
     * 删除
     */
    public void deleteById(long id);

    /**
     * 查询
     */
    Optional<ThirdpartyReceive> getTpReceiveById(Long id);

    Page<ThirdpartyReceive> getTpReceivePage(ThirdpartyReceiveQuery receiveQuery);


    void update(ThirdpartyReceive thirdpartyReceive);

    void deleteByIds(Long[] ids);

    Map<String, Object> getCountAndIdList(Date date);

    List<ThirdpartyReceive> processingStatusInfo(Date date, Integer status,String receiveSkidInterfaceCodes);

    void redoBatch(ThirdpartyReceiveDTO receiveDTO);

    void reDoDeleteReceiveRecord(Date date);
}
