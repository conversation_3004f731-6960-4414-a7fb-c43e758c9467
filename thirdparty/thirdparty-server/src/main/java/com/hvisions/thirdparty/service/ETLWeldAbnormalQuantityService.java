package com.hvisions.thirdparty.service;

import com.hvisions.thirdparty.common.dto.ETLSteelCutTimeDTO;
import com.hvisions.thirdparty.common.dto.ETLWeldAbnormalQuantityDTO;

import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/15
 */
public interface ETLWeldAbnormalQuantityService {

    void insertBatch(List<ETLWeldAbnormalQuantityDTO> etlWeldAbnormalQuantityDTOList);

    void insertOne(ETLWeldAbnormalQuantityDTO etlWeldAbnormalQuantityDTO);

    void updateOne(ETLWeldAbnormalQuantityDTO etlWeldAbnormalQuantityDTO);

    void updateBatch(List<ETLWeldAbnormalQuantityDTO> etlWeldAbnormalQuantityDTOList);

    List<ETLWeldAbnormalQuantityDTO> getAll();

    ETLWeldAbnormalQuantityDTO getDataByTaskNumber(String taskNumber);
}
