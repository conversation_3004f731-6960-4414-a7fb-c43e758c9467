package com.hvisions.thirdparty.controller;

import com.hvisions.thirdparty.common.dto.ETLReportAutomationDTO;
import com.hvisions.thirdparty.service.ETLReportAutomationService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

//@Api(tags = "报工自动化率")
@RestController
@RequestMapping("/ETLReportAutomation")
public class ETLReportAutomationController {


    @Resource
    private ETLReportAutomationService etlReportAutomationService;

//    @ApiOperation("批量新增")
    @PostMapping("/insertBatch")
    public void insertBatch(@RequestBody List<ETLReportAutomationDTO> reportAutomationDTOList) {
        etlReportAutomationService.insertBatch(reportAutomationDTOList);
    }


//    @ApiOperation("批量修改")
    @PostMapping("/updateBatch")
    public void updateBatch(@RequestBody List<ETLReportAutomationDTO> reportAutomationDTOList) {
        etlReportAutomationService.updateBatch(reportAutomationDTOList);
    }

//    @ApiOperation("获取某日数据")
    @GetMapping("/getListByDate/{date}")
    public List<ETLReportAutomationDTO> getListByDate(@PathVariable String date) {
        return etlReportAutomationService.getListByDate(date);
    }
}
