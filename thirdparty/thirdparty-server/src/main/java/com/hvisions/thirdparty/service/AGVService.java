package com.hvisions.thirdparty.service;

import com.hvisions.thirdparty.common.dto.AGVAlarmDTO;
import com.hvisions.thirdparty.common.dto.AGVSendTaskDTO;
import com.hvisions.thirdparty.common.dto.AGVStatusDTO;
import com.hvisions.thirdparty.common.dto.StockInfoDTO;
@Deprecated
public interface AGVService extends ReceiveAndSendService{
    
    String sendFullTask(AGVSendTaskDTO agvSendTaskDTO);

    String sendEmptyTask(AGVSendTaskDTO agvSendTaskDTO);

    String sendEmptyReturnTask(AGVSendTaskDTO agvSendTaskDTO);

    void updateStatus(AGVStatusDTO agvStatusDTO);

    void alarm(AGVAlarmDTO agvAlarmDTO);

    void stockUpdate(StockInfoDTO stockInfoDTO);
}

