package com.hvisions.thirdparty.controller;

import com.hvisions.thirdparty.common.dto.ETLProfileScheduleDTO;
import com.hvisions.thirdparty.service.ETLProfileScheduleService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/15
 */
//@Api(tags = "型材切割线-进度、计划零件产量和每日零件产量")
@RestController
@RequestMapping("/ETLProfileSchedule")
public class ETLProfileScheduleController {
    @Resource
    private ETLProfileScheduleService etlProfileScheduleService;

//    @ApiOperation("批量新增")
    @PostMapping("/insertBatch")
    public void insertBatch(@RequestBody List<ETLProfileScheduleDTO> etlProfileScheduleDTOList) {
        etlProfileScheduleService.insertBatch(etlProfileScheduleDTOList);
    }


//    @ApiOperation("批量修改")
    @PostMapping("/updateBatch")
    public void updateBatch(@RequestBody List<ETLProfileScheduleDTO> etlProfileScheduleDTOList) {
        etlProfileScheduleService.updateBatch(etlProfileScheduleDTOList);
    }

//    @ApiOperation("获取某日数据")
    @GetMapping("/getListByDate/{date}")
    public List<ETLProfileScheduleDTO> getListByDate(@PathVariable String date) {
        return etlProfileScheduleService.getListByDate(date);
    }

}
