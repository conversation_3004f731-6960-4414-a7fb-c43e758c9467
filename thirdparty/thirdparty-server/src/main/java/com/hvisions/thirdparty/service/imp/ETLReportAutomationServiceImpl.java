package com.hvisions.thirdparty.service.imp;

import com.hvisions.thirdparty.common.dto.ETLReportAutomationDTO;
import com.hvisions.thirdparty.common.dto.ETLReportAutomationDTO;
import com.hvisions.thirdparty.dao.ETLReportAutomationMapper;
import com.hvisions.thirdparty.service.ETLReportAutomationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
public class ETLReportAutomationServiceImpl implements ETLReportAutomationService {
 

    @Resource
    private ETLReportAutomationMapper etlReportAutomationMapper;

    @Override
    public void insertBatch(List<ETLReportAutomationDTO> reportAutomationDTOList) {
        etlReportAutomationMapper.insertBatch(reportAutomationDTOList);
    }

    @Override
    public void updateBatch(List<ETLReportAutomationDTO> reportAutomationDTOList) {
        for (ETLReportAutomationDTO etlReportAutomationDTO : reportAutomationDTOList) {
            etlReportAutomationMapper.update(etlReportAutomationDTO);
        }
    }

    @Override
    public List<ETLReportAutomationDTO> getListByDate(String date) {
        return etlReportAutomationMapper.getListByDate(date);
    }
}
