package com.hvisions.thirdparty.controller;

import com.hvisions.thirdparty.common.dto.*;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.thirdparty.common.query.ThirdpartySendQuery;
import com.hvisions.thirdparty.entity.ThirdpartySend;
import com.hvisions.thirdparty.service.ThirdpartySendRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2023/4/20 14:06
 * @Version 1.0
 */
@Api(tags = "第三方数据-发送记录")
@RestController
@RequestMapping("/sendRecord")
public class ThirdpartySendRecordController {
    @Resource
    private ThirdpartySendRecordService thirdpartySendRecordService;


    @ApiOperation(value = "id删除")
    @RequestMapping(value = {"/deleteByIds"}, method = RequestMethod.DELETE)
    public void deleteByIds(@RequestParam Long[] id) {
        thirdpartySendRecordService.deleteByIds(id);
    }

    @ApiOperation(value = "id查询")
    @RequestMapping(value = "/findById/{id}", method = RequestMethod.GET)
    public Optional<ThirdpartySend> getTpSendById(@PathVariable("id") Long id) {
        return thirdpartySendRecordService.getTpSendById(id);
    }

    @ApiOperation(value = "分页查询")
    @PostMapping(value = "/getPage")
    public Page<ThirdpartySend> getTpSendPage(@RequestBody ThirdpartySendQuery thirdpartyReceiveQuery, @UserInfo UserInfoDTO userInfoDTO) {
        return thirdpartySendRecordService.getTpSendPage(thirdpartyReceiveQuery);
    }

    //yyyy-mm-dd hh:MM:ss
    @ApiOperation(value = "统计并返回id集合")
    @GetMapping(value = "/getCountAndIdList")
    public Map<String, Object> getCountAndIdList(@RequestParam Long date) {
        return thirdpartySendRecordService.getCountAndIdList(new Date(date));
    }


    @ApiOperation(value = "发送的数据集合")
    @GetMapping(value = "/processingStatusInfo")
    public List<ThirdpartySend> processingStatusInfo(@RequestParam Long date,
                                                     @RequestParam(value = "status", defaultValue = "2") Integer status, @RequestParam(value = "sendSkidInterfaceCodes") String sendSkidInterfaceCodes) {
        return thirdpartySendRecordService.processingStatusInfo(new Date(date), status, sendSkidInterfaceCodes);
    }

}
