package com.hvisions.thirdparty.service.imp;

import com.alibaba.fastjson.JSON;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.pms.client.*;
import com.hvisions.thirdparty.common.dto.*;
import com.hvisions.thirdparty.common.enums.FunctionEnum;
import com.hvisions.thirdparty.common.enums.InterfaceCodeEnum;
import com.hvisions.thirdparty.entity.ThirdpartyInterface;
import com.hvisions.thirdparty.service.MaterialCuttingLineService;
import com.hvisions.thirdparty.service.MesService;
import com.hvisions.thirdparty.service.ProfileCompactStorageService;
import com.hvisions.thirdparty.service.StockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class ProfileCompactStorageServiceImpl extends ReceiveAndSendServiceImpl implements ProfileCompactStorageService {

    @Autowired
    private XcMaterialOutStockResultClient xcMaterialOutStockResultClient;
    @Resource
    private MaterialCuttingLineService materialCuttingLineService;
    @Resource
    private XCMaterialCuttingLineClient xcMaterialCuttingLineClient;
    @Resource
    private MesService mesService;
    @Resource
    private StockService stockService;
    @Resource
    private HvPmMaterialPreparationClient hvPmMaterialPreparationClient;
    @Resource
    private HvPmAgvTaskRecordClient hvPmAgvTaskRecordClient;
    @Resource
    private HvPmAgvTaskRecordMaterialClient hvPmAgvTaskRecordMaterialClient;
    @Resource
    private WorkOrderClient workOrderClient;
    @Resource
    private HvPmPlanRouteInfoClient hvPmPlanRouteInfoClient;

    @Override
    public void sendOutboundTask(ProfileOutboundTaskDTO profileOutboundInfoDTO) {
        if (profileOutboundInfoDTO.getDatas() == null) {
            throw new BaseKnownException("出库任务的物料信息 不能为空！");
        }
        sendByInterfaceCode(InterfaceCodeEnum.iWMS14.getCode(), profileOutboundInfoDTO);
    }

    @Override
    public ResultVO<?> responseVo(ResponseEntity<String> r, ThirdpartyInterface thirdpartyInterface) {
        InterfaceCodeEnum interfaceCodeEnum = InterfaceCodeEnum.getByCode(thirdpartyInterface.getInterfaceCode());
        if (interfaceCodeEnum == null) {
            throw new BaseKnownException("接口：" + thirdpartyInterface.getInterfaceCode() + "未在系统内定义！");
        }
        switch (interfaceCodeEnum) {
            case iWMS14:
                ProfileStorageResultDTO profileStorageResultDTO = JSON.toJavaObject(JSON.parseObject(r.getBody()), ProfileStorageResultDTO.class);
                if (profileStorageResultDTO.isSuccess()) {
                    log.info("发送成功啦！" + JSON.toJSONString(profileStorageResultDTO));
                    return ResultVO.success(profileStorageResultDTO);
                } else {
                    log.error("发送失败了！" + JSON.toJSONString(profileStorageResultDTO));
                    return ResultVO.error(profileStorageResultDTO.getMessage());
                }
            case iWMS15:
                MaterialPreparationResponseDTO materialPreparationResponseDTO = JSON.toJavaObject(JSON.parseObject(r.getBody()), MaterialPreparationResponseDTO.class);
                if(materialPreparationResponseDTO.isSuccess()){
                    log.info("发送成功啦！" + JSON.toJSONString(materialPreparationResponseDTO));
                    return ResultVO.success(materialPreparationResponseDTO);
                }else {
                    log.error("发送失败了！" + JSON.toJSONString(materialPreparationResponseDTO));
                    return ResultVO.error("执行异常!型材立库反馈:" +materialPreparationResponseDTO.getMsg());
                }
        }
        return ResultVO.error("未知错误,请稍后重试~");
    }

    @Override
    public void OutboundInfo(ProfileOutboundInfoDTO profileOutboundInfoDTO) {
        ReceiveCommonDTO receiveCommonDTO = new ReceiveCommonDTO();
        receiveCommonDTO.setType(FunctionEnum.WMS13.getCode());
        receiveCommonDTO.setData(profileOutboundInfoDTO);
        saveAndDoByType(receiveCommonDTO);
    }

    @Override
    public void stockUpdate(StockInfoDTO stockInfoDTO) {
        ReceiveCommonDTO receiveCommonDTO = new ReceiveCommonDTO();
        receiveCommonDTO.setType(FunctionEnum.WMS12.getCode());
        receiveCommonDTO.setData(stockInfoDTO);
        saveAndDoByType(receiveCommonDTO);
    }

    @Override
    public void sendPreparationTask(MaterialPreparationTaskDTO materialPreparationTaskDTO) {
        if(materialPreparationTaskDTO.getDatas().isEmpty()){
            throw new BaseKnownException("立库备料任务数据不能为空");
        }
        sendByInterfaceCode(InterfaceCodeEnum.iWMS15.getCode(), materialPreparationTaskDTO);
    }

    @Override
    public void receivePreparationResult(MaterialPreparationResultDTO materialPreparationResultDTO) {
        ReceiveCommonDTO receiveCommonDTO = new ReceiveCommonDTO();
        receiveCommonDTO.setType(FunctionEnum.iWMS16.getCode());
        receiveCommonDTO.setData(materialPreparationResultDTO);
        saveAndDoByType(receiveCommonDTO);
    }

    @Override
    public ThirdpartResultDTO doByType(ReceiveCommonDTO receiveCommonDTO) {
        FunctionEnum functionEnum = FunctionEnum.getByCode(receiveCommonDTO.getType());
        if (functionEnum == null) {
            throw new BaseKnownException("功能编号：" + receiveCommonDTO.getType() + ":未找到对应的执行方法");
        }
        log.info(functionEnum.getDesc());
        ResultVO<?> r = null;
        switch (functionEnum) {
            case WMS12:
                StockInfoDTO stockInfoDTO = JSON.toJavaObject(JSON.parseObject(JSON.toJSONString(receiveCommonDTO.getData())), StockInfoDTO.class);
                //更新库存
                stockService.updateStock(stockInfoDTO);
                for (StockDetailDTO stockDetailDTO : stockInfoDTO.getList()) {
                    stockDetailDTO.setLocationCode(null);
                }
                if ("ADD".equals(stockInfoDTO.getReqType()) && stockInfoDTO.getSyncType() == 1) {
                    mesService.stockSend(stockInfoDTO);
                }
                break;
            case WMS13:
                //接收出库信息
                ProfileOutboundInfoDTO profileOutboundInfoDTO = JSON.toJavaObject(JSON.parseObject(JSON.toJSONString(receiveCommonDTO.getData())), ProfileOutboundInfoDTO.class);
                //接收出库结果
                r = xcMaterialOutStockResultClient.addOutStockResultAndDetail(profileOutboundInfoDTO);
                if (!r.isSuccess()) {
                    throw new BaseKnownException(r.getMessage());
                }
                //上料点信息下发
                r = xcMaterialCuttingLineClient.sendCuttingLineOneLineInfo(profileOutboundInfoDTO);
                if (r.isSuccess()) {
                    //修改型材切割计划的状态为 "切割开始"
                    r  = xcMaterialOutStockResultClient.upXcCutPlanStatusByTaskCode(profileOutboundInfoDTO.getTaskCode());
                    if (!r.isSuccess()){
                        throw new BaseKnownException("上料点信息下发后，修改型材切割计划的状态失败，原因："+r.getMessage());
                    }
                }
                break;
            case iWMS16:
                //接收立库备料结果
                MaterialPreparationResultDTO materialPreparationResultDTO = JSON.toJavaObject(JSON.parseObject(JSON.toJSONString(receiveCommonDTO.getData())), MaterialPreparationResultDTO.class);
                //更新备料结果
//                r = hvPmMaterialPreparationClient.updatePreparation(materialPreparationResultDTO);
                break;
            default:
                throw new BaseKnownException(functionEnum.getDesc() + "未实现！");
        }
        //job目前是false,如果后续数量量过大，可考虑使用job的方式，定时分批处理
        return checkAndReturn(false, r);
    }
}
