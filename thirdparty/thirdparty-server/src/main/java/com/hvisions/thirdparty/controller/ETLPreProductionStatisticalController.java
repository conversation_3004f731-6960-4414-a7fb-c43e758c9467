package com.hvisions.thirdparty.controller;

import com.hvisions.thirdparty.common.dto.ETLPreProductionStatisticalDTO;
import com.hvisions.thirdparty.service.ETLPreProductionStatisticalService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

//@Api(tags = "生产准备时间")
@RestController
@RequestMapping("/ETLPreProductionStatistical")
public class ETLPreProductionStatisticalController {


    @Resource
    private ETLPreProductionStatisticalService etlPreProductionStatisticalService;

//    @ApiOperation("批量新增")
    @PostMapping("/insertBatch")
    public void insertBatch(List<ETLPreProductionStatisticalDTO> planFinishDTOList) {
        etlPreProductionStatisticalService.insertBatch(planFinishDTOList);
    }


//    @ApiOperation("批量修改")
    @PostMapping("/updateBatch")
    public void updateBatch(List<ETLPreProductionStatisticalDTO> planFinishDTOList) {
        etlPreProductionStatisticalService.updateBatch(planFinishDTOList);
    }

}
