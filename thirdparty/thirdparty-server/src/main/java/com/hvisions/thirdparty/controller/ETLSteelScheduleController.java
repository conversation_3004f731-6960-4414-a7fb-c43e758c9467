package com.hvisions.thirdparty.controller;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.thirdparty.common.dto.ETLSteelScheduleDTO;
import com.hvisions.thirdparty.common.query.ETLSteelScheduleQuery;
import com.hvisions.thirdparty.service.ETLSteelScheduleService;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/14
 */
//@Api(tags ="钢板切割线-进度、零件总重量和零件个数")
@RestController
@RequestMapping("/ETLSteelSchedule")
public class ETLSteelScheduleController {

    @Resource
    private ETLSteelScheduleService etlSteelScheduleService;

    /**
     * 分页查询
     *
     * @param etlSteelScheduleQuery
     * @return
     */
    @PostMapping("/getPage")
    public Page<ETLSteelScheduleDTO> getPage(@RequestBody ETLSteelScheduleQuery etlSteelScheduleQuery) {
        return etlSteelScheduleService.getPage(etlSteelScheduleQuery);
    }


    @ApiResultIgnore
    @PostMapping(value = "/exportEtlSteel")
    public ResultVO<ExcelExportDto> exportEtlSteel(@RequestBody ETLSteelScheduleQuery etlSteelScheduleQuery) throws IOException, IllegalAccessException {
        return etlSteelScheduleService.exportEtlSteel(etlSteelScheduleQuery);
    }




    //    @ApiOperation("批量新增")
    @PostMapping("/insertBatch")
    public void insertBatch(@RequestBody List<ETLSteelScheduleDTO> eTLSteelScheduleDTOList) {
        etlSteelScheduleService.insertBatch(eTLSteelScheduleDTOList);
    }


//    @ApiOperation("批量修改")
    @PostMapping("/updateBatch")
    public void updateBatch(@RequestBody List<ETLSteelScheduleDTO> eTLSteelScheduleDTOList) {
        etlSteelScheduleService.updateBatch(eTLSteelScheduleDTOList);
    }

//    @ApiOperation("获取某日数据")
    @GetMapping("/getListByDate/{date}")
    public List<ETLSteelScheduleDTO> getListByDate(@PathVariable String date) {
        return etlSteelScheduleService.getListByDate(date);
    }


}
