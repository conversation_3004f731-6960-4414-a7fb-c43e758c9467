package com.hvisions.thirdparty.service;

import com.hvisions.thirdparty.common.dto.ETLPlanFinishDTO;
import com.hvisions.thirdparty.common.dto.ETLWeldScheduleDTO;

import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/15
 */
public interface ETLWeldScheduleService {
    void insertBatch(List<ETLWeldScheduleDTO> etlWeldScheduleDTOList);

    void updateBatch(List<ETLWeldScheduleDTO> etlWeldScheduleDTOList);

    List<ETLWeldScheduleDTO> getListByDate(String date);
}
