package com.hvisions.thirdparty.service;

import com.hvisions.thirdparty.common.dto.*;


import javax.validation.Valid;
import java.util.List;

public interface MesService extends ReceiveAndSendService{

    /**
     * 发送-mes报工
     *
     * @param mesSendReportingDTO 发送内容
     */
    void sendMesReportingTask(List<MesSendReportingDTO> mesSendReportingDTO);

    /**
     * 接收-mes订单
     * @param mesOrderDTO mes订单内容
     */
    void OrderReceiving(List<MesOrderDTO> mesOrderDTO);

    /**
     * 接收-物料主数据
     * @param mesMaterialDTOS 物料主数据
     */
    void materialReceiving(List<MesMaterialDTO> mesMaterialDTOS);

    /**
     * 接收-工厂建模
     * @param mesFactoryDTOS 工厂建模数据
     */
    void factoryReceiving(List<MesFactoryDTO> mesFactoryDTOS);

    void MaterBomReceiving(List<MaterialBomDTO> materialBomDTO);

    void stockSend(StockInfoDTO stockInfoDTO);

    void orderStart(ProductionOrderStartDTO productionOrderStartDTO);

    void sendMesStockTransfer(MesStockTransferDTO mesStockTransferDTO);

    void xcSendProductionOrderStart(ProductionOrderStartDTO productionOrderStartDTO);

    void xcSendMesReportingTask(MesSendReportingDTO mesSendReportingDTO);

    void outboundTransport(List<OutboundTransportDTO>  outboundTransportDTOS);

    void processOutboundFrames(OutboundMaterialFramesDTO outboundMaterialFramesDTO);

    void receiveOutboundStock(OutboundStockDTO outboundStockDTO);

    void outCoordinationStandStockSyn(StockInfoDTO stockInfoDTO);

    void sendOutCoordinationCall(OutCoordinationCallDTO outCoordinationCallDTO);

    void outCoordinationFlatStock(OutCoordinationFlatStockDTO outCoordinationFlatStockDTO);

    void sendFullPallet(FullPalletDTO fullPalletDTO);

    void receiveCuttingEmptyFrames(List<CuttingEmptyFramesDTO> cuttingEmptyFramesDTOS);


    void receiveProductionWorkOrder(@Valid MesProductionWorkOrderDTO mesProductionWorkOrderDTO);

    void sendWorkOrderKittingFeedback(@Valid MesWorkOrderKittingFeedbackDTO mesWorkOrderKittingFeedbackDTO);

    void receiveWeldingEmptyFreamReturn(@Valid MesWeldingEmptyFreamReturnDTO mesWeldingEmptyFreamReturnDTO);

    void stockSend(MomStockSyncDTO momStockSyncDTO);

}
