package com.hvisions.thirdparty.service;

import com.hvisions.thirdparty.common.dto.*;

public interface SteelPlateLineService extends ReceiveAndSendService {
    // 发送订单
    void steelPartOrderSend(SteelPlatePartsDTO steelPlatePartsDTO, Integer lineId);

    // 报工
    void partOrderReporting(DetailReportDTO detailReportDTO);

    void schedulingFeedback(LineSchedulingFeedBackDTO feedBackDTO);

    void sendPartCutTaskCancel(PartCutTaskCancelDTO partCutTaskCancelDTO);


    void steelPartOrderSend(SteelPlateCuttingOrderDTO steelPlatePartsDTO, Integer lineId);

    void steelPartTaskReport(CuttingTaskReportDTO cuttingTaskReportDTO);
}
