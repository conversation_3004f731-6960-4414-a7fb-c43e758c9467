package com.hvisions.thirdparty.service.imp;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.thirdparty.common.dto.*;
import com.hvisions.thirdparty.common.query.*;
import com.hvisions.thirdparty.common.enums.FunctionEnum;
import com.hvisions.thirdparty.common.enums.ThirdpartyInfoStatusEnum;
import com.hvisions.thirdparty.dao.ThirdpartyReceiveMapper;
import com.hvisions.thirdparty.entity.ThirdpartyReceive;
import com.hvisions.thirdparty.repository.ThirdpartyReceiveRepository;
import com.hvisions.thirdparty.service.*;
import com.hvisions.thirdparty.util.NetworkUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

@Slf4j
@Service
public class ThirdpartyReceiveRecordServiceImpl implements ThirdpartyReceiveRecordService {
    @Resource
    private ThirdpartyReceiveRepository tpReceiveRepository;
    @Resource
    private ThirdpartyReceiveMapper thirdpartyReceiveMapper;

    @Override
    public ThirdpartyReceive createTpReceive(String c, Object o) {
        ThirdpartyReceive hvTpReceive = new ThirdpartyReceive();
        hvTpReceive.setStatus(ThirdpartyInfoStatusEnum.WAIT.getCode());
        hvTpReceive.setInfoType(c);
        hvTpReceive.setCreateTime(new Date());
        hvTpReceive.setReceiveContent(JSON.toJSONString(o));
        return tpReceiveRepository.save(hvTpReceive);
    }

    @Override
    public ThirdpartyReceive createTpReceive(String c, Object o, HttpServletRequest request) {
        ThirdpartyReceive hvTpReceive = new ThirdpartyReceive();
        hvTpReceive.setStatus(0);
        hvTpReceive.setInfoType(c);
        hvTpReceive.setCreateTime(new Date());
        hvTpReceive.setReceiveContent(JSON.toJSONString(o));
        hvTpReceive.setSendIp(NetworkUtil.getIpAddress(request));
        return tpReceiveRepository.save(hvTpReceive);
    }

    @Override
    public void deleteById(long id) {
        tpReceiveRepository.deleteById(id);
    }

    @Override
    public Optional<ThirdpartyReceive> getTpReceiveById(Long id) {
        return tpReceiveRepository.findById(id);
    }

    @Override
    public Page<ThirdpartyReceive> getTpReceivePage(ThirdpartyReceiveQuery receiveQuery) {
        Page<ThirdpartyReceive> page = PageHelperUtil.getPage(this.thirdpartyReceiveMapper::getList, receiveQuery, ThirdpartyReceive.class);
        for (ThirdpartyReceive thirdpartyReceive : page.getContent()) {
            FunctionEnum interfaceCodeEnum = FunctionEnum.getByCode(thirdpartyReceive.getInfoType());
            thirdpartyReceive.setInfoType(interfaceCodeEnum == null ? thirdpartyReceive.getInfoType() : interfaceCodeEnum.getDesc());
        }
        return page;
    }

    @Override
    public void update(ThirdpartyReceive thirdpartyReceive) {
        tpReceiveRepository.save(thirdpartyReceive);
    }

    @Override
    @Transactional
    public void deleteByIds(Long[] ids) {
        thirdpartyReceiveMapper.deleteInBatch(Arrays.asList(ids));
    }

    @Override
    public Map<String, Object> getCountAndIdList(Date date) {
        Map<String, Object> r = new HashMap<>();
        long count = thirdpartyReceiveMapper.dateTimeCount(date);
        List<Long> ids = thirdpartyReceiveMapper.selectTopIds(1000, date);
        r.put("size", count);
        r.put("ids", ids);
        return r;
    }

    @Override
    public List<ThirdpartyReceive> processingStatusInfo(Date date, Integer status, String sendSkidInterfaceCodes) {
        List<String> codeList = null;
        if (!StringUtils.isBlank(sendSkidInterfaceCodes))
            codeList = Arrays.asList(sendSkidInterfaceCodes.split(","));
        return thirdpartyReceiveMapper.processingStatusInfo(date, status, codeList);
    }

    @Resource
    private ReceiveService receiveService;

    @Override
    public void redoBatch(ThirdpartyReceiveDTO thirdpartyReceive) {

        ReceiveCommonDTO receiveCommonDTO = JSON.toJavaObject(JSON.parseObject(thirdpartyReceive.getReceiveContent()), ReceiveCommonDTO.class);

        String errorMsg = null;
        boolean flag = true;
        try {
            if (StringUtils.isEmpty(receiveCommonDTO.getType())) {
                throw new BaseKnownException("功能代码不能为空");
            }
            receiveService.doByType(receiveCommonDTO);
        } catch (Exception e) {
            flag = false;
            errorMsg = e.getMessage();
            log.error("重新执行异常！" + e.getMessage(), e);
        }
        //更新状态
        if (thirdpartyReceive.getId() != null) {
            thirdpartyReceive.setUpdateTime(new Date());
            //状态
            thirdpartyReceive.setStatus(!flag ? ThirdpartyInfoStatusEnum.NG.getCode() : ThirdpartyInfoStatusEnum.OK.getCode());
            int count = thirdpartyReceive.getHandleQuality() + 1;
            thirdpartyReceive.setHandleQuality(count);
            if (!StringUtils.isBlank(errorMsg))
                thirdpartyReceive.setErrorMessage(thirdpartyReceive.getErrorMessage()+"/第"+count+"次:"+errorMsg);

            update(DtoMapper.convert(thirdpartyReceive, ThirdpartyReceive.class));
        }
    }

    @Override
    public void reDoDeleteReceiveRecord(Date date) {
        Map<String, Object> resultVOData = getCountAndIdList(date);
        long size = Long.parseLong(resultVOData.get("size") == null ? "0" : resultVOData.get("size").toString());
        List<Object> ids = (List<Object>) resultVOData.get("ids");
        if (ids.size() == 0) {
            return;
        }
        Long[] idsL = new Long[ids.size()];
        for (int i = 0; i < ids.size(); i++) {
            idsL[i] = Long.parseLong(ids.get(i).toString());
        }
        deleteByIds(idsL);
        if (size > ids.size()) {
            reDoDeleteReceiveRecord(date);
        }
    }

}
