package com.hvisions.thirdparty.service;

import com.hvisions.thirdparty.common.dto.*;

public interface PartsStorageService {
    void stockUpdate(StockInfoDTO stockInfoDTO);

    void handleStockOutArrivalInfo(StockOutArrivalInfoDTO info);

    void processInboundArrivalInfo(StockMovementDTO info);

    void processStockOutPointStatus(StockPointStatusDTO info);

    void processOutboundTask(StockMovementDTO task);

    void processOutboundResultDispatch(StockMovementDTO info);

    void sendProfileMaterials(ProfileMaterialsDTO profileMaterials);

    PartsStorageResultDTO emptyStorageLocation(ProfileMaterialsDTO date);

    PartsStorageResultDTO emptyFrameInfo(ProfileMaterialsDTO date);
}
