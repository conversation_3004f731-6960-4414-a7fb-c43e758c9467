package com.hvisions.thirdparty.controller;

import com.hvisions.thirdparty.common.dto.ETLWeldScheduleDTO;
import com.hvisions.thirdparty.service.ETLWeldScheduleService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/15
 */
//@Api(tags = "组立-进度、计划和完成数量")
@RestController
@RequestMapping("/ETLWeldSchedule")
public class ETLWeldScheduleController {
    @Resource
    private ETLWeldScheduleService etlWeldScheduleService;

//    @ApiOperation("批量新增")
    @PostMapping("/insertBatch")
    public void insertBatch(@RequestBody List<ETLWeldScheduleDTO> etlWeldScheduleDTOList) {
        etlWeldScheduleService.insertBatch(etlWeldScheduleDTOList);
    }


//    @ApiOperation("批量修改")
    @PostMapping("/updateBatch")
    public void updateBatch(@RequestBody List<ETLWeldScheduleDTO> etlWeldScheduleDTOList) {
        etlWeldScheduleService.updateBatch(etlWeldScheduleDTOList);
    }

//    @ApiOperation("获取某日数据")
    @GetMapping("/getListByDate/{date}")
    public List<ETLWeldScheduleDTO> getListByDate(@PathVariable String date) {
        return etlWeldScheduleService.getListByDate(date);
    }

}
