package com.hvisions.thirdparty.controller;

import com.hvisions.thirdparty.common.dto.ETLPlanQualityDTO;
import com.hvisions.thirdparty.service.ETLPlanQualityService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

//@Api(tags = "计划和实际任务量")
@RestController
@RequestMapping("/ETLPlanQuality")
public class ETLPlanQualityController {

    @Resource
    private ETLPlanQualityService etlPlanQualityService;

//    @ApiOperation("批量新增")
    @PostMapping("/insertBatch")
    public void insertBatch(@RequestBody List<ETLPlanQualityDTO> planQualityDTOList) {
        etlPlanQualityService.insertBatch(planQualityDTOList);
    }


//    @ApiOperation("批量修改")
    @PostMapping("/updateBatch")
    public void updateBatch(@RequestBody List<ETLPlanQualityDTO> planQualityDTOList) {
        etlPlanQualityService.updateBatch(planQualityDTOList);
    }

//    @ApiOperation("获取某日数据")
    @GetMapping("/getListByDate/{date}")
    public List<ETLPlanQualityDTO> getListByDate(@PathVariable String date) {
       return etlPlanQualityService.getListByDate(date);
    }

}
