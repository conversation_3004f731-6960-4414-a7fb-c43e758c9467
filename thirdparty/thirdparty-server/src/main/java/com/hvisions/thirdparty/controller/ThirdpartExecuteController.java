package com.hvisions.thirdparty.controller;

import com.hvisions.thirdparty.common.dto.LineSchedulingFeedBackDTO;
import com.hvisions.thirdparty.service.PointTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024-05-15 9:42
 */

@Api(tags = "调度任务第三方接口-执行")
@RestController
@RequestMapping("/thirdpartExecute")
public class ThirdpartExecuteController {
    @Autowired
    private PointTaskService pointTaskService;

    @ApiOperation(value = "调度任务第三方接口发送")
    @PostMapping("/send")
    public void send(@RequestParam(value = "interfaceCode") String interfaceCode, @RequestBody LineSchedulingFeedBackDTO lineSchedulingFeedBackDTO) {
        pointTaskService.schedulingFeedback(interfaceCode, lineSchedulingFeedBackDTO);
    }
}
