package com.hvisions.thirdparty.service;

import com.hvisions.thirdparty.common.dto.RCSSendTaskDTO;
import com.hvisions.thirdparty.common.dto.RcsCancelTaskRequestDTO;
import com.hvisions.thirdparty.common.dto.RcsResult;
import com.hvisions.thirdparty.common.dto.RcsTaskReqDTO;
import com.hvisions.thirdparty.common.dto.rcs.CancelTaskRequest;
import com.hvisions.thirdparty.common.dto.rcs.GenAgvSchedulingTaskRequest;
import com.hvisions.thirdparty.common.dto.rcs.TaskExecutionNotificationRequest;
import com.hvisions.thirdparty.common.dto.rcs.TaskExecutionNotificationResponse;

import javax.validation.Valid;


public interface RcsService extends ReceiveAndSendService{
    RcsResult<String>  submitTask(RCSSendTaskDTO rcsSendTaskDTO);

    RcsResult<String> getReportTask(RcsTaskReqDTO rcsTaskReqDTO);

    RcsResult<String> cancelTask(RcsCancelTaskRequestDTO rcsCancelTaskRequestDTO);

    RcsResult<String> queryTask(String robotTaskCode);

    RcsResult<String> queryRobotStatus(String singleRobotCode);

    RcsResult<String> queryCarrierStatus(String carrierCode);

    void submitTask(@Valid GenAgvSchedulingTaskRequest genAgvSchedulingTaskRequest);

    void cancelTask(@Valid CancelTaskRequest cancelTaskRequest);

    TaskExecutionNotificationResponse liteQueryTask(TaskExecutionNotificationRequest taskExecutionNotificationRequest);
}
