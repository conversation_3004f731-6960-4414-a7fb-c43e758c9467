package com.hvisions.thirdparty.service;

import com.hvisions.thirdparty.common.dto.ETLMaterialCompletenessDTO;

import java.util.List;

public interface ETLMaterialCompletenessService {
    void insertBatch(List<ETLMaterialCompletenessDTO> completenessDTOList);

    void updateBatch(List<ETLMaterialCompletenessDTO> completenessDTOList);

    void insertOne(ETLMaterialCompletenessDTO completenessDTO);

    void updateOne(ETLMaterialCompletenessDTO completenessDTO);

    ETLMaterialCompletenessDTO getOneByDate(String date);

}
