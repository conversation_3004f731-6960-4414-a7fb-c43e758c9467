package com.hvisions.thirdparty.controller;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.hvisions.common.annotation.UserInfo;
import com.hvisions.common.dto.UserInfoDTO;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.thirdparty.common.dto.ThirdpartyInterfaceDTO;
import com.hvisions.thirdparty.common.query.ThirdpartyInterfaceQuery;
import com.hvisions.thirdparty.entity.ThirdpartyInterface;
import com.hvisions.thirdparty.service.ThirdpartyInterfaceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;


/**
 * <AUTHOR>
 * @create 2023-08-29 10:07
 */
@Slf4j
@Api(tags = "第三方接口")
@RestController
@RequestMapping("/tpInterfaceController")
public class ThirdpartyInterfaceController {

    @Autowired
    private ThirdpartyInterfaceService service;


    @ApiOperation(value = "新增/更新第三方接口")
    @PostMapping("/addOrUpdateTpInterface")
    public int addTpInterface(@RequestBody ThirdpartyInterface tpInterface, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        if (tpInterface.getId() != null && tpInterface.getId() != 0) {
            return service.updateTpInterface(tpInterface);
        } else {
            return service.addTpInterface(tpInterface);
        }
    }


    @ApiOperation(value = "删除第三方接口")
    @DeleteMapping("/delTpInterfaceById")
    public boolean delTpInterfaceById(@RequestBody List<Integer> idList, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        return service.removeByIds(idList);
    }


    @ApiOperation(value = "分页查询：第三方接口")
    @PostMapping("/getTpInterfacePage")
    public Page<ThirdpartyInterface> getTpInterfacePage(@RequestBody ThirdpartyInterfaceQuery tpInterfaceQuery, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        return service.getTpInterfacePage(tpInterfaceQuery);
    }


    @ApiOperation(value = "启用/停用第三方接口")
    @PostMapping("/setTpInterfaceEnable")
    public boolean setTpInterfaceEnable(@RequestParam(value = "id") Integer id, @RequestParam(value = "enable") Integer enable, @UserInfo @ApiIgnore UserInfoDTO userInfo) {
        return service.enable(id, enable);
    }

    @ApiOperation(value = "根据接口编号查询接口")
    @GetMapping("/getTpInterfaceByCode")
    public String getTpInterfaceByCode(@RequestParam(value = "code") String code) {
        return service.getTpInterfaceByCode(code);
    }

    @ApiOperation(value = "根据接口id查询接口")
    @GetMapping("/getTpInterfaceById")
    public ThirdpartyInterface getTpInterfaceById(@RequestParam(value = "id") Integer id) {
        return service.getTpInterfaceById(id);
    }

    @ApiOperation(value = "根据接口id查询接口集合")
    @PostMapping("/getTpInterfaceListByIds")
    public List<ThirdpartyInterface> getTpInterfaceByIds(@RequestBody List<Integer> ids) {
        return service.getTpInterfaceByIds(ids);
    }

    @ApiOperation(value = "根据接口编号查询接口")
    @GetMapping("/getTpInterfaceByInterfaceCode")
    public ThirdpartyInterface getTpInterfaceByInterfaceCode(@RequestParam(value = "interfaceCode") String interfaceCode) {
        return service.getTpInterfaceByInterfaceCode(interfaceCode);
    }

    @ApiOperation(value = "查询接口集合")
    @PostMapping("/getTpInterfaceList")
    public List<ThirdpartyInterface> getTpInterfaceList() {
        return service.getTpInterfaceList();
    }


    @ApiOperation(value = "根据产线找到对应的反馈接口编号")
    @GetMapping("/getFeedBackInterfaceByLineId")
    public String getFeedBackInterfaceByLineId(@RequestParam Integer lineId) {
       return service.getEnableFeedBackInterfaceByLineId(lineId);
    }
}
