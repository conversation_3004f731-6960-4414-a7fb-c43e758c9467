# suppress inspection "UnusedProperty" for whole file
# \u901A\u7528\u5F02\u5E38\u4FE1\u606F
SUCCESS=SUCCESS
COLUMN_EXISTS_VALUE=COLUMN_EXISTS_VALUE
COLUMN_EXISTS=COLUMN_EXSITS
SERVER_ERROR=SERVER_ERROR
JSON_PARSE_ERROR=JSON_PARSE_ERROR
ILLEGAL_STRING=ILLEGAL_STRING
NULL_RESULT=NULL_RESULT
VIOLATE_INTEGRITY=VIOLATE_INTEGRITY
IMPORT_FILE_NO_SUPPORT=IMPORT_FILE_NO_SUPPORT
IMPORT_SHEET_IS_NULL=IMPORT_SHEET_IS_NULL
ENTITY_PROPERTY_NOT_SUPPORT=ENTITY_PROPERTY_NOT_SUPPORT
SAVE_SHOULD_NO_IDENTITY=SAVE_SHOULD_NO_IDENTITY
UPDATE_SHOULD_HAVE_IDENTITY=UPDATE_SHOULD_HAVE_IDENTITY
CONST_VIOLATE=CONST_VIOLATE
NO_SUCH_ELEMENT=NO_SUCH_ELEMENT
ENTITY_NOT_EXISTS=ENTITY_NOT_EXISTS
DATA_INTEGRITY_VIOLATION=DATA_INTEGRITY_VIOLATION
COLUMN_PATTERN_ILLEGAL=COLUMN_PATTERN_ILLEGAL
#\u81EA\u5B9A\u4E49\u5F02\u5E38\u4FE1\u606F
OPERATION_STATE_NOT_SUPPORT=OPERATION_STATE_NOT_SUPPORT
OPERATION_STATE_NOT_MATCH=OPERATION_STATE_NOT_MATCH
ROUTE_SERVER_ERROR=ROUTE_SERVER_ERROR
MATERIAL_SERVER_ERROR=MATERIAL_SERVER_ERROR
NOT_EXIST_PLAN_CODE=NOT_EXIST_PLAN_CODE
ORDER_MANAGE_DELETE_NOT_ALLOWED=ORDER_MANAGE_DELETE_NOT_ALLOWED
MATERIAL_IS_NOT_BOM=MATERIAL_IS_NOT_BOM
NEED_ID=NEED_ID
ID_NOT_EXIST=ID_NOT_EXIST
EQUIPMENT_SERVER_ERROR=EQUIPMENT_SERVER_ERROR
MANY_EQUIPMENT=MANY_EQUIPMENT
FILE_SERVER_ERROR=FILE_SERVER_ERROR
MATERIAL_NOT_NULL=MATERIAL_NOT_NULL
WORK_ORDER_CODE_NOT_NULL=WORK_ORDER_CODE_NOT_NULL
OPERATION_NOT_MATCH=OPERATION_NOT_MATCH
NOT_END_WORK_ORDER_NOT_FINISH=NOT_END_WORK_ORDER_NOT_FINISH
ORDER_NOT_CREW=ORDER_NOT_CREW
ORDER_NOT_SHIFT=ORDER_NOT_CREW
ORDER_NOT_AREA=ORDER_NOT_CREW
ORDER_NOT_ROUTE=ORDER_NOT_CREW
ORDER_NOT_CELL=ORDER_NOT_CELL
LOGO_IN=LOGO_IN
CANNOT_MODIFY=CANNOT_MODIFY
AUTH_ERROR=AUTH_ERROR
WORK_CENTER_NOT_FOUND=WORK_CENTER_NOT_FOUND
PLEASE_PASS_IN_THE_QUERY_TIME=PLEASE_PASS_IN_THE_QUERY_TIME
FINISH_WORK_ORDER_NO_SCARP=FINISH_WORK_ORDER_NO_SCARP
SWITCH_ONLY_THE_EQUIPMENT_BOUND_BY_THE_ROUTE=SWITCH_ONLY_THE_EQUIPMENT_BOUND_BY_THE_ROUTE
OPERATION_NOT_PARAMETER=OPERATION_NOT_PARAMETER
OPERATION_NOT_READY=operation_not_ready
ROUTE_WORK_CENTER_NOT_FOUND=ROUTE_WORK_CENTER_NOT_FOUND
THERE_ARE_UNCONFIRMED_SHIFT_HANDOVER_RECORDS=there_are_unconfirmed_shift_handover_records
THIS_RECORD_DOES_NOT_ALLOW_SHIFT_CHANGE=this_record_does_not_allow_shift_change
NOT_RECORD=not_record
NOT_EXIST_ROUTE_STEP=NOT_EXIST_ROUTE_STEP
CHOOSE_OPERATION=CHOOSE_OPERATION
WORK_ORDER_FINISH=WORK_ORDER_FINISH
PLAN_ORDER_NOT_OPERATION=PLAN_ORDER_NOT_OPERATION
NOT_PLAN_FINISH=NOT_PLAN_FINISH
NOT_FOUND_ONLY_ROUTE=NOT_FOUND_ONLY_ROUTE
NOT_STEP=NOT_STEP
INSUFFICIENT_SKILL_LEVEL=INSUFFICIENT_SKILL_LEVEL
TASK_PARAMETER_ERROR=TASK_PARAMETER_ERROR
TOKEN_USER_NULL=TOKEN_USER_NULL
CHOOSE_OVER_MAN=CHOOSE_OVER_MAN
ORDER_TYPE_USED=ORDER_TYPE_USED
NOT_CREW=NOT_CREW
ERROR_COUNT=ERROR_COUNT
CHOOSE_ORDER=CHOOSE_ORDER
ORDER_ROUTE_DIFFERENT=ORDER_ROUTE_DIFFERENT
DEMO_EXCEPTION_ENUM=DEMO_EXCEPTION_ENUM
PLAN_IS_ISSUED=PLAN_IS_ISSUED
QUANTITY_OVOR_LIMIT=QUANTITY_OVOR_LIMIT
AREA_SERVER_ERROR=AREA_SERVER_ERROR
PRODUTION_LIEN_SERVER_ERROR=PRODUTION_LIEN_SERVER_ERROR
CREATE_ORDER_ERROR=CREATE_ORDER_ERROR
ORDER_NOT_FOUND=ORDER_NOT_FOUND
SERIAL_VOER_LIMIT=SERIAL_VOER_LIMIT
ISSUED_ERROR=ISSUED_ERROR
CELL_NOT_NULL=CELL_NOT_NULL
PLEASE_CHOOSE_THE_PLAN=PLEASE_CHOOSE_THE_PLAN
PLEASE_CHOOSE_NUM=PLEASE_CHOOSE_NUM
AREA_NOT_NULL=AREA_NOT_NULL
NOT_RE_ISSUE_PLAN=Can't repeat the issuance plan
PLAN_NOT_ISSUE=Plans not issued
THE_NUMBER_OF_DOWNLOADS_MUST_BE_A_POSITIVE_INTEGER=the_number_of_downloads_must_be_a_positive_integer
ORDER_FINISH_ERROR=ORDER_FINISH_ERROR
ALREADY_BOUND=ALREADY_BOUND
EXECUTOR_NOT=EXECUTOR_NOT
