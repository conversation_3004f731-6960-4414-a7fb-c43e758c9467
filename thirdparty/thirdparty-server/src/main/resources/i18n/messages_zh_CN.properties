# suppress inspection "UnusedProperty" for whole file
#\u901A\u7528\u5F02\u5E38
SUCCESS=\u6210\u529F
COLUMN_EXISTS_VALUE=\u5DF2\u6709\u6570\u636E\uFF0C\u4E0D\u53EF\u5220\u9664
COLUMN_EXISTS=\u6269\u5C55\u5C5E\u6027\u7F16\u7801\u5DF2\u7ECF\u5B58\u5728
SERVER_ERROR=\u670D\u52A1\u5668\u5F02\u5E38
JSON_PARSE_ERROR=Json\u89E3\u6790\u9519\u8BEF
ILLEGAL_STRING=Json\u89E3\u6790\u51FA\u9519\uFF0C\u8BF7\u68C0\u67E5json\u7ED3\u6784
NULL_RESULT=\u67E5\u8BE2\u4E3A\u7A7A
VIOLATE_INTEGRITY=\u8FDD\u53CD\u9650\u5B9A\uFF0C\u8BF7\u68C0\u67E5\u662F\u5426\u6709\u91CD\u590D\u6570\u636E
IMPORT_FILE_NO_SUPPORT=\u6587\u4EF6\u7C7B\u578B\u4E0D\u652F\u6301
IMPORT_SHEET_IS_NULL=\u6587\u4EF6sheet\u8868\u4E0D\u5B58\u5728
ENTITY_PROPERTY_NOT_SUPPORT=\u5B9E\u4F53\u5C5E\u6027\u4E0D\u652F\u6301\uFF0C\u8BF7\u68C0\u67E5\u5BFC\u5165\u6570\u636E
SAVE_SHOULD_NO_IDENTITY=\u4FDD\u5B58\u4E0D\u5E94\u8BE5\u6709\u4E3B\u952E
UPDATE_SHOULD_HAVE_IDENTITY=\u66F4\u65B0\u5E94\u8BE5\u6709\u4E3B\u952E
CONST_VIOLATE=\u8FDD\u53CD\u9650\u5236\uFF0C\u8BF7\u68C0\u67E5\u6570\u636E\u5E93
NO_SUCH_ELEMENT=\u6570\u636E\u67E5\u8BE2\u4E0D\u5B58\u5728
ENTITY_NOT_EXISTS=\u5F85\u6269\u5C55\u5BF9\u8C61\u4E0D\u5B58\u5728\uFF0C\u8BF7\u67E5\u8BE2\u539F\u5BF9\u8C61\u662F\u5426\u5B58\u5728
DATA_INTEGRITY_VIOLATION=\u6570\u636E\u5B8C\u6574\u6027\u9A8C\u8BC1\u51FA\u9519
COLUMN_PATTERN_ILLEGAL=\u6269\u5C55\u5217\u683C\u5F0F\u975E\u6CD5\uFF0C\u53EA\u5141\u8BB8\u6570\u5B57\uFF0C\u4E0B\u5212\u7EBF\uFF0C\u82F1\u6587\u5B57\u7B26
#\u81EA\u5B9A\u4E49\u5F02\u5E38
OPERATION_STATE_NOT_SUPPORT=\u5DE5\u5355\u6B65\u9AA4\u72B6\u6001\u9519\u8BEF
OPERATION_STATE_NOT_MATCH=\u5DE5\u5355\u72B6\u6001\u4E0D\u652F\u6301\u6B64\u64CD\u4F5C
ROUTE_SERVER_ERROR=\u5DE5\u827A\u670D\u52A1\u51FA\u9519
MATERIAL_SERVER_ERROR=\u7269\u6599\u670D\u52A1\u51FA\u9519
NOT_EXIST_PLAN_CODE=\u4E0D\u5B58\u5728\u7684\u5DE5\u5355\u8BA1\u5212\u7F16\u7801
ORDER_MANAGE_DELETE_NOT_ALLOWED=\u5DE5\u5355\u72B6\u6001\u4E0D\u5141\u8BB8\u5220\u9664
MATERIAL_IS_NOT_BOM=\u7269\u6599\u4E0B\u6CA1\u6709\u5173\u8054\u7684BOM
NEED_ID=\u8BF7\u9009\u62E9\u8981\u4FEE\u6539\u7684\u6570\u636E
ID_NOT_EXIST=ID\u4E0D\u5B58\u5728
EQUIPMENT_SERVER_ERROR=\u8BBE\u5907\u670D\u52A1\u51FA\u9519
MANY_EQUIPMENT=\u67E5\u8BE2\u5230\u591A\u53F0\u8BBE\u5907
FILE_SERVER_ERROR=\u6587\u4EF6\u670D\u52A1\u51FA\u9519
MATERIAL_NOT_NULL=\u7269\u6599\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A
WORK_ORDER_CODE_NOT_NULL=\u5DE5\u5355\u7F16\u7801\u4E0D\u80FD\u4E3A\u7A7A
OPERATION_NOT_MATCH=\u5F53\u524D\u5DE5\u5E8F\u4E0D\u9700\u8981\u6B64\u64CD\u4F5C
OPERATION_NOT_READY=\u5DE5\u5E8F\u672A\u5C31\u7EEA
NOT_END_WORK_ORDER_NOT_FINISH=\u5DE5\u5355\u72B6\u6001\u4E0D\u5141\u8BB8\u62A5\u5DE5
ORDER_NOT_CREW=\u5DE5\u5355\u6CA1\u6709\u6DFB\u52A0\u73ED\u7EC4\u4FE1\u606F
ORDER_NOT_SHIFT=\u5DE5\u5355\u6CA1\u6709\u89C4\u5212\u73ED\u6B21\u4FE1\u606F
ORDER_NOT_AREA=\u5DE5\u5355\u6CA1\u6709\u8BBE\u7F6E\u8F66\u95F4\u4FE1\u606F
ORDER_NOT_ROUTE=\u5DE5\u5355\u6CA1\u6709\u9009\u62E9\u5DE5\u827A\u8DEF\u7EBF
ORDER_NOT_CELL=\u5DE5\u5355\u6CA1\u6709\u9009\u62E9\u4EA7\u7EBF
LOGO_IN=\u8BF7\u767B\u5F55
AUTH_ERROR=\u4EBA\u5458\u670D\u52A1\u51FA\u9519
CANNOT_MODIFY=\u5DF2\u4E0B\u53D1
WORK_CENTER_NOT_FOUND=\u5DE5\u4F4D\u5339\u914D\u9519\u8BEF\uFF0C\u8BF7\u786E\u5B9A\u5DE5\u827A\u8DEF\u7EBF\u4E0E\u4EA7\u7EBF\u5173\u7CFB
FINISH_WORK_ORDER_NO_SCARP=\u5DF2\u62A5\u5DE5\u7684\u5DE5\u5355\u4E0D\u80FD\u62A5\u5E9F
PLEASE_PASS_IN_THE_QUERY_TIME=\u8BF7\u4F20\u5165\u67E5\u8BE2\u65F6\u95F4
SWITCH_ONLY_THE_EQUIPMENT_BOUND_BY_THE_ROUTE=\u53EA\u80FD\u5207\u6362\u5DE5\u827A\u4E0A\u6240\u7ED1\u5B9A\u7684\u8BBE\u5907
OPERATION_NOT_PARAMETER=\u5DE5\u5E8F\u4E0B\u6CA1\u6709\u53C2\u6570
ROUTE_WORK_CENTER_NOT_FOUND=\u5DE5\u827A\u672A\u7ED1\u5B9A\u5DE5\u4F4D
THERE_ARE_UNCONFIRMED_SHIFT_HANDOVER_RECORDS=\u6709\u672A\u786E\u8BA4\u4EA4\u73ED\u8BB0\u5F55
THIS_RECORD_DOES_NOT_ALLOW_SHIFT_CHANGE=\u8BE5\u8BB0\u5F55\u4E0D\u5141\u8BB8\u63A5\u73ED
NOT_RECORD=\u6CA1\u6709\u4EA4\u73ED\u8BB0\u5F55
NOT_EXIST_ROUTE_STEP=\u5DE5\u827A\u8DEF\u7EBF\u4E0D\u5B58\u5728\u5DE5\u827A\u6B65\u9AA4\u8BF7\u68C0\u67E5\u5DE5\u827A\u8DEF\u7EBF\u914D\u7F6E
CHOOSE_OPERATION=\u9009\u62E9\u5DE5\u5E8F
WORK_ORDER_FINISH=\u5DE5\u5355\u5DF2\u62A5\u5DE5\uFF0C\u4E0D\u5141\u8BB8\u64CD\u4F5C
PLAN_ORDER_NOT_OPERATION=\u8BA1\u5212\u5DE5\u5355\u4E0D\u5141\u8BB8\u64CD\u4F5C
NOT_PLAN_FINISH=\u624B\u52A8\u521B\u5EFA\u5DE5\u5355\u65E0\u6CD5\u5411\u8BA1\u5212\u62A5\u5DE5
NOT_FOUND_ONLY_ROUTE=\u672A\u627E\u5230\u552F\u4E00\u5DE5\u827A
NOT_STEP=\u672A\u627E\u5230\u4E0B\u4E00\u6B65\u5DE5\u827A\u6B65\u9AA4
INSUFFICIENT_SKILL_LEVEL=\u5458\u5DE5\u6280\u80FD\u7B49\u7EA7\u4E0D\u591F
TASK_PARAMETER_ERROR=\u751F\u4EA7\u4EFB\u52A1\u53C2\u6570\u4F20\u9012\u51FA\u9519
TOKEN_USER_NULL=\u672A\u627E\u5230\u767B\u9646\u4EBA\u5458
CHOOSE_OVER_MAN=\u8BF7\u9009\u62E9\u63A5\u73ED\u4EBA
ORDER_TYPE_USED=\u5DE5\u5355\u7C7B\u578B\u5DF2\u88AB\u4F7F\u7528
NOT_CREW=\u672A\u627E\u5230\u63A5\u73ED\u4EBA\u73ED\u7EC4
ERROR_COUNT=\u56DE\u6599\u6570\u91CF\u8D85\u8FC7\u5B9E\u9645\u5F55\u5165\u6570\u91CF
CHOOSE_ORDER=\u8BF7\u9009\u62E9\u5DE5\u5355
ORDER_ROUTE_DIFFERENT=\u6240\u9009\u5DE5\u5355\u5DE5\u827A\u8DEF\u7EBF\u4E0D\u540C,\u65E0\u6CD5\u540C\u65F6\u4E0B\u53D1
DEMO_EXCEPTION_ENUM=\u793A\u4F8B\u5F02\u5E38\u7C7B\u578B
PLAN_IS_ISSUED=\u8BA1\u5212\u5DF2\u7ECF\u4E0B\u53D1
QUANTITY_OVOR_LIMIT=\u6570\u91CF\u8D85\u8FC7\u4E0A\u9650
AREA_SERVER_ERROR=\u8F66\u95F4\u670D\u52A1\u5F02\u5E38
PRODUTION_LIEN_SERVER_ERROR=\u4EA7\u7EBF\u670D\u52A1\u5F02\u5E38
CREATE_ORDER_ERROR=\u521B\u5EFA\u5DE5\u5355\u5F02\u5E38
ORDER_NOT_FOUND=\u5DE5\u5355\u672A\u627E\u5230
ISSUED_ERROR=\u4E0B\u53D1\u5931\u8D25
SERIAL_VOER_LIMIT=\u6D41\u6C34\u53F7\u8D85\u8FC79999
CELL_NOT_NULL=\u4EA7\u7EBF\u4E0D\u80FD\u4E3A\u7A7A
PLEASE_CHOOSE_THE_PLAN=\u8BF7\u9009\u62E9\u751F\u4EA7\u8BA1\u5212
PLEASE_CHOOSE_NUM=\u8BF7\u9009\u62E9\u4E0B\u53D1\u6570\u91CF
AREA_NOT_NULL=\u8F66\u95F4\u4E0D\u80FD\u4E3A\u7A7A
NOT_RE_ISSUE_PLAN=\u8BA1\u5212\u5DF2\u6210\u529F\u4E0B\u53D1\uFF0C\u4E0D\u80FD\u91CD\u590D\u4E0B\u53D1\u8BA1\u5212
PLAN_NOT_ISSUE=\u8BA1\u5212\u672A\u4E0B\u53D1
THE_NUMBER_OF_DOWNLOADS_MUST_BE_A_POSITIVE_INTEGER=\u4E0B\u53D1\u6B21\u6570\u5FC5\u987B\u4E3A\u6574\u6570
ORDER_FINISH_ERROR=\u62A5\u5DE5\u5931\u8D25
ALREADY_BOUND=\u8BE5\u7269\u6599\u5DF2\u4E0E\u6B64\u7C7B\u578B\u7ED1\u5B9A
EXECUTOR_NOT=\u65E0\u6743\u6267\u884C\u8BE5\u4EFB\u52A1
