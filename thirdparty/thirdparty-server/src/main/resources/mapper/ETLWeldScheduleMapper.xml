<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.thirdparty.dao.ETLWeldScheduleMapper">

    <insert id="insertBatch">
        insert into tp_etl_weld_schedule
        (
        line_code,
        statistical_date,
        schedule,
        plan_quantity,
        finish_quantity

        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.lineCode},
            #{item.statisticalDate},
            #{item.schedule},
            #{item.planQuantity},
            #{item.finishQuantity}
            )
        </foreach>


    </insert>

    <update id="update">
        update tp_etl_weld_schedule
        set  schedule = #{schedule},
        plan_quantity = #{planQuantity},
        finish_quantity = #{finishQuantity}
        <where>
            statistical_date = #{statisticalDate}

            <if test="lineCode !=null and lineCode !=''">
                and line_code =#{lineCode}
            </if>
        </where>
    </update>
    <select id="getListByDate" resultType="com.hvisions.thirdparty.common.dto.ETLWeldScheduleDTO">
        select * from tp_etl_weld_schedule
        <where>
            statistical_date = #{date}
        </where>
    </select>
</mapper>