<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.thirdparty.dao.ETLSteelScheduleMapper">

    <select id="getPage" resultType="com.hvisions.thirdparty.common.dto.ETLSteelScheduleDTO">
        select s.*
        from tp_etl_steel_schedule s
        <where>
            <if test="query.lineCode != null and query.lineCode !=''">
                and line_code like concat('%',#{query.lineCode},'%')
            </if>

            <if test="query.beginTime != null">
                and statistical_date >= #{query.beginTime}
            </if>
            <if test="query.endTime != null">
                and statistical_date &lt;= #{query.endTime}
            </if>

        </where>
    </select>


    <insert id="insertBatch">
        insert into tp_etl_steel_schedule
        (
        line_code,
        statistical_date,
        schedule,
        total_part_weight,
        part_number

        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.lineCode},
            #{item.statisticalDate},
            #{item.schedule},
            #{item.totalPartWeight},
            #{item.partNumber}
            )
        </foreach>


    </insert>

    <update id="update">
        update tp_etl_steel_schedule
        set  schedule = #{schedule},
        total_part_weight = #{totalPartWeight},
        part_number = #{partNumber}
        <where>
            statistical_date = #{statisticalDate}
            <if test="lineCode !=null and lineCode !=''">
                and line_code =#{lineCode}
            </if>
        </where>
    </update>

    <select id="getListByDate" resultType="com.hvisions.thirdparty.common.dto.ETLSteelScheduleDTO">
        select * from tp_etl_steel_schedule
        <where>
            statistical_date = #{date}
        </where>
    </select>

</mapper>