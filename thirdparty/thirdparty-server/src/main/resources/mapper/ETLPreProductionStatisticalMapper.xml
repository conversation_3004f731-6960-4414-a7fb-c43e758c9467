<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.thirdparty.dao.ETLPreProductionStatisticalMapper">

    <insert id="insertBatch">
        insert into tp_etl_pre_production_statistical
        (
        statistical_date,
        line_code,
        equipment_code,
        act_value
        )
        values
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item.statisticalDate},
            #{line_code},
            #{equipment_code},
            #{act_value}
            }
        </foreach>


    </insert>

    <update id="update">
        update tp_etl_pre_production_statistical
        set act_value = #{actValue}
        <where>
            statistical_date = #{statisticalDate}
            <if test="lineCode !=null and lineCode !=''">
                and line_code =#{lineCode}
            </if>
            <if test="equipmentCode !=null and equipmentCode !=''">
                and equipment_code =#{equipmentCode}
            </if>
        </where>
    </update>
</mapper>