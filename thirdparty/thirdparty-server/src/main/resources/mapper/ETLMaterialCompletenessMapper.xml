<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.thirdparty.dao.ETLMaterialCompletenessMapper">

    <insert id="insertBatch">
        insert into tp_etl_material_completeness
        (
        statistical_date,
        act_value
        )
        values
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item.statisticalDate},
            #{item.actValue}
        </foreach>
    </insert>
    <insert id="insertOne">
        insert into tp_etl_material_completeness
        (
        statistical_date,
        act_value
        )
        values
        (
            #{dto.statisticalDate},
            #{dto.actValue}
        )

    </insert>

    <update id="update">
        update tp_etl_material_completeness
        set act_value =#{dto.actValue}
        <where>
            statistical_date = #{dto.statisticalDate}
        </where>
    </update>

    <select id="getOneByDate" resultType="com.hvisions.thirdparty.common.dto.ETLMaterialCompletenessDTO">
        select * from tp_etl_material_completeness where statistical_date = #{date}
    </select>
</mapper>