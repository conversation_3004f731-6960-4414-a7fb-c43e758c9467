<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.thirdparty.dao.ThirdpartyInterfaceMapper">


    <select id="getTpInterfacePage" parameterType="com.hvisions.thirdparty.common.query.ThirdpartyInterfaceQuery"
            resultType="com.hvisions.thirdparty.entity.ThirdpartyInterface">
        select * from tp_interface
        <trim prefix="where" suffixOverrides="," prefixOverrides="and">
            <if test="interfaceCode!=null and interfaceCode!=''">interface_code like #{interfaceCode}</if>
            <if test="systemId !=null">AND system_id=#{systemId}</if>
            <if test="requestOrResponse !=null and requestOrResponse!= 0">AND request_or_response = #{requestOrResponse}</if>
            <if test="queue !=null and queue!= ''">AND queue like #{queue}</if>
            <if test="topic !=null and topic!= ''">AND topic like #{topic}</if>
            <if test="communicationType !=null and communicationType!= 0">AND communication_type = #{communicationType}</if>
            <if test="enable !=null and enable!= 0">AND enable = #{enable}</if>
            <if test="startTime != null and startTime != ''  and endTime != null and endTime != '' ">
                AND create_time between #{startTime} and #{endTime}
            </if>
        </trim>
    </select>

    <select id="getTpInterfaceByIds" resultType="com.hvisions.thirdparty.entity.ThirdpartyInterface">
        select
        id,
        interface_code interfaceCode,
        line_id lineId
        from tp_interface
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getTpInterfaceBySystemCodeAndInterCode" resultType="com.hvisions.thirdparty.entity.ThirdpartyInterface">
        select * FROM tp_interface as ti WHERE ti.interface_code = #{interfaceCode} AND ti.system_id = (SELECT id FROM tp_system as ts WHERE ts.system_code=#{systemCode})
    </select>
</mapper>