<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.thirdparty.dao.ETLWeldAbnormalQuantityMapper">

    <insert id="insertBatch">
        insert into tp_etl_weld_abnormal_quantity
        (
        task_number,
        line_code,
        act_value
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.taskNumber},
            #{item.lineCode},
            #{item.actValue}
            )
        </foreach>


    </insert>
    <insert id="insertOne">
        insert into tp_etl_weld_abnormal_quantity
        (
        task_number,
        line_code,
        act_value
        )
        values
        (
        #{item.taskNumber},
        #{item.lineCode},
        #{item.actValue}
        )
    </insert>

    <update id="update">
        update tp_etl_weld_abnormal_quantity
        set act_value =#{actValue}
        <where>
            task_number = #{taskNumber}
        </where>
    </update>
    <select id="getAll" resultType="com.hvisions.thirdparty.common.dto.ETLWeldAbnormalQuantityDTO">
        select * from tp_etl_weld_abnormal_quantity
    </select>
    <select id="getDataByTaskNumber"
            resultType="com.hvisions.thirdparty.common.dto.ETLWeldAbnormalQuantityDTO">
        select * from tp_etl_weld_abnormal_quantity WHERE task_number = #{taskNumber}
    </select>
</mapper>