<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.thirdparty.dao.ThirdpartySendMapper">


    <select id="getList" resultType="com.hvisions.thirdparty.entity.ThirdpartySend">
        select id,create_time,update_time,info_type,send_content,status,handle_quality,error_message from tp_send
        <where>
            <if test="query.infoType != null and query.infoType !=''">
                and info_type =#{query.infoType}
            </if>
            <if test="query.contentInfo != null and query.contentInfo!=''">
                and send_content like concat('%',#{query.contentInfo},'%')
            </if>
            <if test="query.status != null ">
                and status = #{query.status}
            </if>
            <if test="query.beginTime != null">
                and create_time >= #{query.beginTime}
            </if>
            <if test="query.endTime != null">
                and create_time &lt;= #{query.endTime}
            </if>
            <if test="query.updateBeginTime != null">
                and update_time >= #{query.updateBeginTime}
            </if>
            <if test="query.updateEndTime != null">
                and update_time &lt;= #{query.updateEndTime}
            </if>
        </where>
    </select>


    <select id="selectTopIds" resultType="java.lang.Long">
        select id
        from tp_send
        where create_time &lt; #{createTime}
        order by create_time asc limit #{size}
    </select>

    <select id="dateTimeCount" resultType="java.lang.Long">
        select count(id)
        from tp_send
        where create_time &lt; #{createTime}
    </select>

    <select id="processingStatusInfo" resultType="com.hvisions.thirdparty.entity.ThirdpartySend">
        select id,create_time,update_time,info_type,send_content,status,handle_quality,error_message from tp_send
        <where>
            handle_quality &lt; 5
            <if test="codeList !=null">
                and info_type not in
                <foreach collection="codeList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="status !=null">
                and status =#{status}
            </if>
            <if test="date !=null">
                and update_time &gt; #{date}
            </if>
        </where>
    </select>

    <delete id="deleteInBatch">
        delete from tp_send where id in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </delete>
</mapper>