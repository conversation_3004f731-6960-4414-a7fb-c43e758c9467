<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.thirdparty.dao.ETLSteelCutTimeMapper">

    <select id="getSteelCutTimeList" resultType="com.hvisions.thirdparty.common.dto.ETLSteelCutTimeDTO">
        select t.*
        from tp_etl_steel_cut_time t
    </select>

    <insert id="insertBatch">
        insert into tp_etl_steel_cut_time
        (
        task_number,
        line_code,
        act_value
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.taskNumber},
            #{item.lineCode},
            #{item.actValue}
            )
        </foreach>
    </insert>

    <insert id="insertOne">
        insert into tp_etl_steel_cut_time
        (
        task_number,
        line_code,
        act_value
        )
        values
            (
            #{taskNumber},
            #{lineCode},
            #{actValue}
            )
    </insert>

    <update id="update">
        update tp_etl_steel_cut_time
        set act_value =#{actValue}
        <where>
            task_number = #{taskNumber}
        </where>
    </update>

    <select id="getAll" resultType="com.hvisions.thirdparty.common.dto.ETLSteelCutTimeDTO">
        select * from tp_etl_steel_cut_time
    </select>

    <select id="getSteelCutTimeBytaskNumber"
            resultType="com.hvisions.thirdparty.common.dto.ETLSteelCutTimeDTO">
        SELECT
            *
        FROM
            tp_etl_steel_cut_time
        WHERE
            task_number = #{taskNumber}
    </select>

</mapper>