<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.thirdparty.dao.ETLPlanFinishMapper">

    <insert id="insertBatch">
        insert into tp_etl_plan_finish
        (
        statistical_date,
        line_code,
        act_value
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.statisticalDate},
            #{item.lineCode},
            #{item.actValue}
            )
        </foreach>


    </insert>

    <update id="update">
        update tp_etl_plan_finish
        set act_value =#{actValue}
        <where>
            statistical_date = #{statisticalDate}

            <if test="lineCode !=null and lineCode !=''">
                and line_code =#{lineCode}
            </if>
        </where>
    </update>
    <select id="getListByDate" resultType="com.hvisions.thirdparty.common.dto.ETLPlanFinishDTO">
        select * from tp_etl_plan_finish
        <where>
            statistical_date = #{date}
        </where>
    </select>
</mapper>