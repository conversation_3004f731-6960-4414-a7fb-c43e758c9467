<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.thirdparty.dao.ETLPlanQualityMapper">

    <insert id="insertBatch">
        insert into tp_etl_plan_quality
        (
        statistical_date,
        line_code,
        plan_quality,
        finish_quality

        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.statisticalDate},
            #{item.lineCode},
            #{item.planQuality},
            #{item.finishQuality}
            )
        </foreach>


    </insert>

    <update id="update">
        update tp_etl_plan_quality
        set  plan_quality = #{planQuality},
        finish_quality = #{finishQuality}
        <where>
            statistical_date = #{statisticalDate}

            <if test="lineCode !=null and lineCode !=''">
                and line_code =#{lineCode}
            </if>
        </where>
    </update>
    <select id="getListByDate" resultType="com.hvisions.thirdparty.common.dto.ETLPlanQualityDTO">
        select * from tp_etl_plan_quality
        <where>
            statistical_date = #{date}
        </where>
    </select>
</mapper>