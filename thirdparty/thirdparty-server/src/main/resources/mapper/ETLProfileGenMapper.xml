<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.thirdparty.dao.ETLProfileGenMapper">

    <insert id="insertBatch">
        insert into tp_etl_profile_gen
        (
        line_code,
        statistical_date,
        act_value
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.lineCode},
            #{item.statisticalDate},
            #{item.actValue}
            )
        </foreach>


    </insert>

    <update id="update">
        update tp_etl_profile_gen
        set  act_value = #{actValue}
        <where>
            statistical_date = #{statisticalDate}
            <if test="lineCode !=null and lineCode !=''">
                and line_code =#{lineCode}
            </if>
        </where>
    </update>

    <select id="getListByDate" resultType="com.hvisions.thirdparty.common.dto.ETLProfileGenDTO">
        select * from tp_etl_profile_gen
        <where>
            statistical_date = #{date}
        </where>
    </select>
</mapper>