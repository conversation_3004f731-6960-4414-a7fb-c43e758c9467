<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.thirdparty.dao.ETLStockTurnoverRateMapper">

    <insert id="insertBatch">
        insert into tp_etl_stock_turnover_rate
        (
        statistical_date,
        material_type,
        act_value
        )
        values
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item.statisticalDate},
            #{item.materialType},
            #{act_value}
            }
        </foreach>


    </insert>

    <update id="update">
        update tp_etl_stock_turnover_rate
        set act_value = #{actValue}
        <where>
            statistical_date = #{statisticalDate}
            <if test="materialType !=null and materialType !=''">
                and material_type =#{materialType}
            </if>
        </where>
    </update>
</mapper>