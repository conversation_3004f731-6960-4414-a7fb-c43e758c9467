<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.thirdparty.dao.ThirdpartySystemMapper">


    <select id="getthirdpartySystemPage"
            resultType="com.hvisions.thirdparty.entity.ThirdpartySystem">
        select * from tp_system
        <where>
            <if test="query.systemCode != null and query.systemCode !=''">
                and system_code like concat('%',#{query.systemCode},'%')
            </if>
            <if test="query.systemName != null and query.systemName !=''">
                and system_name like concat('%',#{query.systemName},'%')
            </if>
            <if test="query.userCode != null and query.userCode !=''">
                and user_code like concat('%',#{query.userCode},'%')
            </if>
            <if test="query.encryptionType != null and query.encryptionType !=''">
                and encryption_type like concat('%',#{query.uencryptionType},'%')
            </if>
            <if test="query.creatorId != null and query.creatorId !=''">
                and creator_id like concat('%',#{query.creatorId},'%')
            </if>
            <if test="query.startTime != null and query.startTime != ''  and query.endTime != null and query.endTime != '' ">
                and create_time between #{query.startTime} and #{query.endTime}
            </if>
        </where>
    </select>
</mapper>