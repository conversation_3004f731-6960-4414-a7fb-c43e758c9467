package com.hvisions.thirdparty.common.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class LesOutfittingPalletArrivalTrayDTO {
    /**
     * 运送目标位置
     * 说明：实际送达区域
     * 错误消息：运送目标位置不能为空
     */
    @NotBlank(message = "运送目标位置不能为空")
    @Size(max = 100, message = "运送目标位置长度不能超过100个字符")
    @JsonAlias("targetLocation")
    private String targetLocation;

    /**
     * 实际送达时间
     * 说明：时间送达时间
     * 错误消息：实际送达时间不能为空
     */
    @NotBlank(message = "实际送达时间不能为空")
    @Size(max = 100, message = "实际送达时间长度不能超过100个字符")
    @JsonAlias("realFinishTime")
    private String realFinishTime;

    /**
     * 托盘编码
     * 说明：托盘的唯一标识
     * 错误消息：托盘编码不能为空
     */
    @NotBlank(message = "托盘编码不能为空")
    @Size(max = 50, message = "托盘编码长度不能超过50个字符")
    @JsonAlias("trayCode")
    private String trayCode;

    /**
     * 托盘名称
     * 说明：托盘的名称
     * 错误消息：托盘名称不能为空
     */
    @NotBlank(message = "托盘名称不能为空")
    @Size(max = 50, message = "托盘名称长度不能超过50个字符")
    @JsonAlias("trayName")
    private String trayName;

    /**
     * 托盘规格
     * 说明：托盘规格尺寸，载重相关信息
     */
    @Size(max = 200, message = "托盘规格长度不能超过200个字符")
    @JsonAlias("traySpecifications")
    private String traySpecifications;

    /**
     * 舱装件集合
     * 说明：包含多个舱装件的列表
     * 错误消息：舱装件集合不能为空
     */
    @NotNull(message = "舱装件集合不能为空")
    @JsonAlias("mList")
    private List<LesOutfittingPalletArrivalMaterialDTO> mList;
}
