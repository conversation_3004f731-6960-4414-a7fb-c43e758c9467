package com.hvisions.thirdparty.common.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
public class LesOutfittingSectionWorkOrderDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 工单编号
     * 业务说明：用于唯一标识一个工单的主键
     */
    @NotBlank(message = "工单编号不能为空")
    @Size(max = 50, message = "工单编号长度不能超过50个字符")
    private String workOrderNumber;

    /**
     * 项目号
     * 业务说明：关联到具体项目的标识符
     */
    @NotBlank(message = "项目号不能为空")
    @Size(max = 50, message = "项目号长度不能超过50个字符")
    private String projectCode;

    /**
     * 批次号
     * 业务说明：产品生产的批次标识
     */
    @NotBlank(message = "批次号不能为空")
    @Size(max = 50, message = "批次号长度不能超过50个字符")
    private String batch;

    /**
     * 分段号
     * 业务说明：产品分段制造的标识符
     */
    @NotBlank(message = "分段号不能为空")
    @Size(max = 50, message = "分段号长度不能超过50个字符")
    private String block;

    /**
     * 流向代码
     * 业务说明：指示产品的流向或目的地类型
     */
    @NotBlank(message = "流向代码不能为空")
    @Size(max = 32, message = "流向代码长度不能超过32个字符")
    private String flowCode;

    /**
     * 计划送达时间
     * 业务说明：计划的送达时间点，格式为yyyy-MM-dd HH:mm
     */
    @NotBlank(message = "计划送达时间不能为空")
    @Size(max = 32, message = "计划送达时间长度不能超过32个字符")
    private String planSendTime;

    /**
     * 计划生产时间
     * 业务说明：计划的开始生产时间点，格式为yyyy-MM-dd HH:mm
     */
    @NotBlank(message = "计划生产时间不能为空")
    @Size(max = 32, message = "计划生产时间长度不能超过32个字符")
    private String planProductTime;

    /**
     * 运送目标位置
     * 业务说明：产品需要运送到的具体位置
     */
    @NotBlank(message = "运送目标位置不能为空")
    @Size(max = 100, message = "运送目标位置长度不能超过100个字符")
    private String targetLocation;

    /**
     * 联系人
     * 业务说明：负责接收或处理该工单的人员姓名
     */
    @NotBlank(message = "联系人不能为空")
    @Size(max = 100, message = "联系人长度不能超过100个字符")
    private String contactName;

    /**
     * 联系人电话
     * 业务说明：联系人的电话号码
     */
    @NotBlank(message = "联系人电话不能为空")
    @Size(max = 100, message = "联系人电话长度不能超过100个字符")
    private String contactPhone;
}
