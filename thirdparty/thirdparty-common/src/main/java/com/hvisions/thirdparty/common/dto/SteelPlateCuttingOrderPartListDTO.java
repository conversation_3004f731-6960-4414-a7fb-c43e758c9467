package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class SteelPlateCuttingOrderPartListDTO {
    /**
     * 物料编码
     */
    @NotBlank(message = "物料编码不能为空")
    @Size(max = 32,message = "物料编码不能超过32个字符")
    private String matCode;

    /**
     * 物料名称
     */
    @Size(max = 32,message = "物料名称不能超过32个字符")
    private String matName;

    /**
     * 数量
     */
    @NotBlank(message = "数量不能为空")
    private Integer qty;

    /**
     * 零件图
     */
    @Size(max = 32,message = "零件图不能超过32个字符")
    @NotBlank(message = "零件图不能为空")
    private String partDrawing;

    /**
     * 零件长
     */
    @Size(max = 32,message = "零件长不能超过32个字符")
    private String partLen;

    /**
     * 零件宽
     */
    @Size(max = 32,message = "零件宽不能超过32个字符")
    private String partWid;

    /**
     * 零件厚
     */
    @Size(max = 32,message = "零件厚不能超过32个字符")
    private String partThick;

    /**
     * 零件规格1
     */
    @Size(max = 32,message = "零件规格1不能超过32个字符")
    private String partSpec1;

    /**
     * 零件规格2
     */
    @Size(max = 32,message = "零件规格2不能超过32个字符")
    private String partSpec2;

    /**
     * 零件规格3
     */
    @Size(max = 32,message = "零件规格3不能超过32个字符")
    private String partSpec3;

    /**
     * 零件切割顺序
     */
    @NotBlank(message = "零件切割顺序不能为空")
    private Integer cutOrder;

    /**
     * 零件序号
     */
    @NotBlank(message = "零件序号不能为空")
    @Size(max = 32,message = "零件序号不能超过32个字符")
    private String partSeq;

    /**
     *  分段
     */
    @Size(max = 32,message = "分段不能超过32个字符")
    @NotBlank(message = "分段不能为空")
    private String section;

    /**
     * 加工代码
     */
    @Size(max = 32,message = "加工代码不能超过32个字符")
    private String procCode;

    /**
     * 打磨
     */
    @Size(max = 32,message = "打磨不能超过32个字符")
    private String burnish;

    /**
     * 材质
     */
    @Size(max = 32,message = "材质不能超过32个字符")
    private String material;

    /**
     * 规格
     */
    @Size(max = 32,message = "规格不能超过32个字符")
    @NotBlank(message = "规格不能为空")
    private String spec;

    /**
     * 组立代码/流向1
     */
    @Size(max = 32,message = "组立代码/流向1不能超过32个字符")
    @NotBlank(message = "组立代码/流向1不能为空")
    private String assemblyCode1;

    /**
     * 组立代码/流向2
     */
    @Size(max = 32,message = "组立代码/流向2不能超过32个字符")
    @NotBlank(message = "组立代码/流向2不能为空")
    private String assemblyCode2;

    /**
     * 零件重量
     *
     */
    @Size(max = 32,message = "零件重量不能超过32个字符")
    @NotBlank(message = "零件重量不能为空")
    private String partWeight;

    /**
     * 坡口朝向
     * */
    @Size(max = 32,message = "坡口朝向不能超过32个字符")
    @NotBlank(message = "坡口朝向不能为空")
    private String bevelDir;


}
