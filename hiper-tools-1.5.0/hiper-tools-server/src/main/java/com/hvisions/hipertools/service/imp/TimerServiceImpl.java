package com.hvisions.hipertools.service.imp;

import cn.hutool.core.lang.Assert;
import com.hvisions.common.annotation.GlobalLock;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.hipertools.configuration.HiperToolsConfigProperty;
import com.hvisions.hipertools.configuration.SimpleMqTimerJob;
import com.hvisions.hipertools.consts.TimerConsts;
import com.hvisions.hipertools.dto.*;
import com.hvisions.hipertools.entity.HvBmTimer;
import com.hvisions.hipertools.entity.HvBmTimerExecutionInfo;
import com.hvisions.hipertools.enums.DayRepeatTypeEnum;
import com.hvisions.hipertools.enums.TimerExceptionEnum;
import com.hvisions.hipertools.enums.TimerTypeEnum;
import com.hvisions.hipertools.repository.TimerExecutionRepository;
import com.hvisions.hipertools.repository.TimerRepository;
import com.hvisions.hipertools.service.TimerHandler;
import com.hvisions.hipertools.service.TimerHandlerFactory;
import com.hvisions.hipertools.service.TimerService;
import com.hvisions.hipertools.utils.TimerUtil;
import com.hvisions.hipertools.utils.TimerValidator;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.quartz.*;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static org.quartz.JobBuilder.newJob;

/**
 * <p>Title: TimerServiceImpl</p>
 * <p>Description: 时间服务实现</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/3/19</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Service
@Slf4j
public class TimerServiceImpl implements TimerService {
    private final TimerRepository timerRepository;
    private final TimerExecutionRepository timerExecutionRepository;
    private final TimerHandlerFactory timerHandlerFactory;
    private final Scheduler schedulerSingle;

    private final RabbitTemplate rabbitTemplate;
    @Autowired
    HiperToolsConfigProperty property;

    @Autowired
    public TimerServiceImpl(TimerRepository timerRepository, TimerExecutionRepository timerExecutionRepository, TimerHandlerFactory timerHandlerFactory, Scheduler schedulerSingle, RabbitTemplate rabbitTemplate) throws SchedulerException {
        this.timerRepository = timerRepository;
        this.timerExecutionRepository = timerExecutionRepository;
        this.timerHandlerFactory = timerHandlerFactory;
        this.schedulerSingle = schedulerSingle;
        this.rabbitTemplate = rabbitTemplate;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Page<TimerDTO> getTimerByName(TimerQueryDTO timerQueryDTO) {
        Page<HvBmTimer> timers = timerRepository.findAllByTimerNameContainingAndTimerGroupContaining(timerQueryDTO.getTimerName(), timerQueryDTO.getTimerGroup(),
                timerQueryDTO.getRequest());
        List<TimerDTO> timerDTOS = timers.stream()
                .map(this::parseEntity)
                .collect(Collectors.toList());
        return new PageImpl<>(timerDTOS, timerQueryDTO.getRequest(), timers.getTotalElements());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createTimerAndStart(TimerDTO timerDTO) {
        HvBmTimer timer = parseDTO(timerDTO);
        TimerHandler handler = timerHandlerFactory.getTimerHandler(timerDTO);
        Trigger trigger = handler.getTrigger(timerDTO);
        timer.setActive(false);
        timerRepository.save(timer);
        startTimer(timer.getId());
        return timer.getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateTimer(TimerDTO timerDTO) {
        HvBmTimer origin = timerRepository.findById(timerDTO.getId())
                .orElseThrow(() -> new BaseKnownException("找不到对应的timer，请检查更新数据"));
        TimerHandler handler = timerHandlerFactory.getTimerHandler(timerDTO);
        Trigger trigger = handler.getTrigger(timerDTO);
        if (origin.getActive()) {
            throw new BaseKnownException(TimerExceptionEnum.TIMER_IS_RUNNING);
        }
        HvBmTimer timer = parseDTO(timerDTO);
        timer.setActive(false);

        return timerRepository.save(timer).getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTimerById(int timerId) {
        Optional<HvBmTimer> opt = timerRepository.findById(timerId);
        if (opt.isPresent()) {
            if (opt.get().getActive()) {
                throw new BaseKnownException(TimerExceptionEnum.TIMER_IS_RUNNING);
            }
        }
        stopTimer(timerId);
        timerRepository.deleteById(timerId);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void deleteTimerByIds(List<Integer> timerIds) {
        List<HvBmTimer> timers = timerRepository.findAllById(timerIds);
        for (HvBmTimer timer : timers) {
            if (timer.getActive()) {
                throw new BaseKnownException(TimerExceptionEnum.TIMER_IS_RUNNING);
            }
        }
        if (timers.stream().anyMatch(t -> t.getActive().equals(true))) {
            throw new BaseKnownException(TimerExceptionEnum.TIMER_IS_RUNNING);
        } else {
            timerRepository.deleteAll(timers);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalLock
    public void startTimer(int timerId) {
        HvBmTimer timerEntity = timerRepository.findById(timerId)
                .orElseThrow(() -> new BaseKnownException("找不到对应的timer信息，请检查id信息"));
        TimerDTO timerDTO = parseEntity(timerEntity);
        TimerValidator.validateCronEndTime(timerDTO.getCron(), timerDTO.getSimpleBeginTime(), timerDTO.getSimpleEndTime());
        TimerHandler handler = timerHandlerFactory.getTimerHandler(timerDTO);
        try {
            //创建scheduler
            Trigger trigger = handler.getTrigger(timerDTO);
            //定义Job
            JobDetail job = newJob(SimpleMqTimerJob.class)
                    //定义name/group
                    .withIdentity(timerDTO.getTimerCode())
                    .usingJobData("id", timerId)
                    .build();
            //加入这个调度
            schedulerSingle.scheduleJob(job, trigger);
            log.info("schedule start trigger !! timer id is:{}", timerDTO.getId());
            timerEntity.setActive(true);
            timerRepository.save(timerEntity);
        } catch (Exception e) {
            throw new BaseKnownException("启动失败：" + e.getMessage());
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stopTimer(int timerId) {
        HvBmTimer timer = timerRepository.findById(timerId)
                .orElseThrow(() -> new BaseKnownException("找不到对应的timer，请检查输入信息"));
        try {
            //创建scheduler
            TriggerKey triggerKey = TriggerKey.triggerKey(timer.getTimerCode());
            // 停止触发器
            schedulerSingle.pauseTrigger(triggerKey);
            schedulerSingle.unscheduleJob(triggerKey);
            schedulerSingle.deleteJob(JobKey.jobKey(timer.getTimerCode()));
            timer.setActive(false);
            timerRepository.save(timer);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            throw new BaseKnownException(TimerExceptionEnum.TIMER_FAIL_TO_STOP);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TimerDTO getByTimerId(int timerId) {
        return timerRepository.findById(timerId)
                .map(this::parseEntity)
                .orElse(null);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<TimerDTO> getByTimerIds(List<Integer> timerIds) {
        List<HvBmTimer> timers = timerRepository.findAllById(timerIds);
        return timers.stream().map(this::parseEntity).collect(Collectors.toList());

    }

    @Override
    public void triggerTimer(int id, Boolean force) {
        TimerDTO timer = getByTimerId(id);
        //如果不是强制触发要判断一下复杂的循环逻辑
        if (!force) {
            //如果是复杂循环
            if (timer.getTimerType().equals(TimerTypeEnum.REPEAT.getCode())) {
                //如果是每天间隔执行，要判断时间,不在时间的跳过
                if (timer.getDayRepeatType().equals(DayRepeatTypeEnum.REPEAT.getCode())) {
                    Date now = new Date();
                    if (TimerUtil.compareTime(now, timer.getDayRepeatBeginTime()) < 0 || TimerUtil.compareTime(now, timer.getDayRepeatEndTime()) > 0) {
                        return;
                    }
                }
            }
        }
        //发mq消息,只发送id
        sendMqMessage(timer.getId());
        //发送mq消息，发送timer的methodTrigger
        if (timer.getEnableMethodTrigger() != null && timer.getEnableMethodTrigger()) {
            sendMethodTrigger(timer);
        }
        //存日志
        try {
            saveTimerLog(timer);
        } catch (Exception e) {
            log.warn("存储timer触发日志异常,{}", e.getMessage());
        }
    }

    @Override
    public List<Date> getPredictionByCron(String cron) {
        List<Date> result = new ArrayList<>();
        TimerDTO timerDTO = new TimerDTO();
        timerDTO.setCron(cron);
        timerDTO.setTimerCode("tmp");
        timerDTO.setTimerType(TimerTypeEnum.CRON.getCode());
        timerDTO.setSimpleBeginTime(new Date());
        TimerHandler handler = timerHandlerFactory.getTimerHandler(timerDTO);
        Trigger trigger = handler.getTrigger(timerDTO);
        Date startTime = new Date();
        while (true) {
            //只拿5个
            if (result.size() > 5) {
                break;
            }
            Date nextFireTime;
            try {
                nextFireTime = handler.getNextFireTime(trigger, timerDTO, startTime);
            } catch (Exception ignore) {
                return Collections.emptyList();
            }
            if (nextFireTime == null) {
                break;
            }
            result.add(nextFireTime);
            startTime = nextFireTime;
        }
        return result;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String getCronByTimerId(Integer timerId) {
        HvBmTimer timer = timerRepository.getOne(timerId);
        return getCronByTimerDto(parseEntity(timer));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String getCronByTimerDto(TimerDTO timerDTO) {
        TimerHandler timerHandler = timerHandlerFactory.getTimerHandler(timerDTO);
        return timerHandler.getCron(timerDTO);
    }

    /**
     * 根据id查timer执行信息
     *
     * @param timerInfoQueryDTO 条件
     * @return timer执行信息
     */
    @Override
    public Page<TimerInfoDTO> getTimerInfoByQuery(TimerInfoQueryDTO timerInfoQueryDTO) {
        Page<HvBmTimerExecutionInfo> hvBmTimerExecutionInfos;
        HvBmTimerExecutionInfo convert = DtoMapper.convert(timerInfoQueryDTO, HvBmTimerExecutionInfo.class);
        Date executionTimeBefore = timerInfoQueryDTO.getExecutionTimeBefore();
        Date executionTimeAfter = timerInfoQueryDTO.getExecutionTimeAfter();
        if (executionTimeBefore == null && executionTimeAfter == null) {
            ExampleMatcher exampleMatcher = ExampleMatcher.matching()
                    .withMatcher("timerId", ExampleMatcher.GenericPropertyMatchers.ignoreCase());
            Example<HvBmTimerExecutionInfo> example = Example.of(convert, exampleMatcher);
            hvBmTimerExecutionInfos = timerExecutionRepository.findAll(example, timerInfoQueryDTO.getRequest());
            return DtoMapper.convertPage(hvBmTimerExecutionInfos, TimerInfoDTO.class);
        }
        if (executionTimeBefore != null && executionTimeAfter != null) {
            hvBmTimerExecutionInfos = timerExecutionRepository.findAllByTimerIdAndExecutionTimeBetween(timerInfoQueryDTO.getTimerId(), timerInfoQueryDTO.getExecutionTimeBefore(),
                    timerInfoQueryDTO.getExecutionTimeAfter(), timerInfoQueryDTO.getRequest());
            return DtoMapper.convertPage(hvBmTimerExecutionInfos, TimerInfoDTO.class);
        }
        if (executionTimeBefore != null) {
            executionTimeAfter = new Date();
            hvBmTimerExecutionInfos = timerExecutionRepository.findAllByTimerIdAndExecutionTimeBetween(timerInfoQueryDTO.getTimerId(), timerInfoQueryDTO.getExecutionTimeBefore(),
                    executionTimeAfter, timerInfoQueryDTO.getRequest());
            return DtoMapper.convertPage(hvBmTimerExecutionInfos, TimerInfoDTO.class);
        }
        hvBmTimerExecutionInfos = timerExecutionRepository.findAllByTimerIdAndExecutionTimeBefore(timerInfoQueryDTO.getTimerId(),
                timerInfoQueryDTO.getExecutionTimeAfter(), timerInfoQueryDTO.getRequest());
        return DtoMapper.convertPage(hvBmTimerExecutionInfos, TimerInfoDTO.class);
    }

    /**
     * 根据timerId获取下次执行时间
     *
     * @param timerId id
     * @return 执行信息
     */
    @Override
    public Date getNextExecutionTime(int timerId) {
        TimerDTO timer = getByTimerId(timerId);
        TriggerKey key = new TriggerKey(timer.getTimerCode());
        try {
            if (!schedulerSingle.checkExists(key)) {
                return null;
            }
            Trigger trigger = schedulerSingle.getTrigger(key);
            TimerHandler timerHandler = timerHandlerFactory.getTimerHandler(timer);
            return timerHandler.getNextFireTime(trigger, timer, new Date());
        } catch (SchedulerException e) {
            log.warn("error to get trigger");
        }
        return null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void startAllTimer() {
        log.info("开启所有合适状态的timer");
        List<HvBmTimer> timers = timerRepository.findAll().stream()
                .filter(HvBmTimer::getActive).collect(Collectors.toList());
        for (HvBmTimer timer : timers) {
            log.info("Timer id :{},Name: {}", timer.getId(), timer.getTimerName());
            try {
                startTimer(timer.getId());
            } catch (Exception e) {
                log.error("timer初始启动失败:{}", e.getMessage());
            }
        }
    }

    /**
     * 预测timer运行轨迹 通过timerId
     *
     * @param predictionTimerDTO 预测
     */
    @Override
    public List<Date> getPrediction(PredictionTimerDTO predictionTimerDTO) {
        //时间验证
        if (predictionTimerDTO.getExecutionTimeAfter() == null || predictionTimerDTO.getExecutionTimeBefore() == null) {
            throw new BaseKnownException("开始或者结束时间不能为空，请检查输入");
        }
        if (predictionTimerDTO.getExecutionTimeBefore().compareTo(predictionTimerDTO.getExecutionTimeAfter()) > 0) {
            throw new BaseKnownException("开始时间不能小于结束时间");
        }
        TimerDTO timer = getByTimerId(predictionTimerDTO.getTimerId());
        if (timer == null) {
            throw new BaseKnownException("找不到对应的timer，请检查输入数据");
        }
        TimerHandler handler = timerHandlerFactory.getTimerHandler(timer);
        Trigger trigger = handler.getTrigger(timer);
        List<Date> result = new ArrayList<>();
        Date startTime = predictionTimerDTO.getExecutionTimeBefore();
        while (true) {
            //防止数据过大
            if (result.size() > property.getMaxPrediction()) {
                break;
            }
            Date nextFireTime = handler.getNextFireTime(trigger, timer, startTime);
            if (nextFireTime == null) {
                break;
            }
            if (nextFireTime.compareTo(predictionTimerDTO.getExecutionTimeAfter()) > 0) {
                break;
            }
            result.add(nextFireTime);
            startTime = nextFireTime;
        }
        return result;
    }

    /**
     * 转换timer
     *
     * @param timerDTO timerDTO
     * @return 实体
     */
    private HvBmTimer parseDTO(TimerDTO timerDTO) {
        if (timerDTO.getEnableMethodTrigger()) {
            Assert.notBlank(timerDTO.getServiceName(), "需要填写服务名");
            Assert.notBlank(timerDTO.getMethodSrc(), "需要填写方法表达式");
        }
        HvBmTimer timer = DtoMapper.convert(timerDTO, HvBmTimer.class);
        if (timerDTO.getMonthDays() != null) {
            timer.setMonthDays(timerDTO.getMonthDays()
                    .stream()
                    .filter(Objects::nonNull)
                    .map(String::valueOf)
                    .collect(Collectors.joining(",")));
        } else {
            timer.setMonthDays(null);
        }
        if (timerDTO.getWeekDays() != null) {
            timer.setWeekDays(timerDTO.getWeekDays()
                    .stream()
                    .filter(Objects::nonNull)
                    .map(String::valueOf)
                    .collect(Collectors.joining(",")));
        } else {
            timer.setWeekDays(null);
        }
        return timer;
    }

    /**
     * 转换实体到DTO
     *
     * @param hvBmTimer 数据库实体
     * @return timerDTO
     */
    private TimerDTO parseEntity(HvBmTimer hvBmTimer) {
        TimerDTO timerDTO = DtoMapper.convert(hvBmTimer, TimerDTO.class);
        if (Strings.isNotBlank(hvBmTimer.getMonthDays())) {
            String[] array = hvBmTimer.getMonthDays().split(",");
            for (String i : array) {
                timerDTO.getMonthDays().add(Integer.parseInt(i));
            }
        }
        if (Strings.isNotBlank(hvBmTimer.getWeekDays())) {
            String[] weekDays = hvBmTimer.getWeekDays().split(",");
            for (String i : weekDays) {
                timerDTO.getWeekDays().add(Integer.parseInt(i));
            }
        }
        return timerDTO;
    }

    /**
     * 记录日志
     *
     * @param timer timer
     */
    private void saveTimerLog(TimerDTO timer) {
        log.info("发送消息结束，开始记录timer日志");
        HvBmTimerExecutionInfo hvBmTimerExecutionInfo = new HvBmTimerExecutionInfo();
        //timerId
        hvBmTimerExecutionInfo.setTimerId(timer.getId());
        //name
        hvBmTimerExecutionInfo.setTimerName(timer.getTimerName());
        //type
        hvBmTimerExecutionInfo.setTimerType(TimerTypeEnum.valueOf(timer.getTimerType()).getName());
        //group
        hvBmTimerExecutionInfo.setTimerGroup(timer.getTimerGroup());
        //执行时间
        hvBmTimerExecutionInfo.setExecutionTime(new Date());
        //描述
        hvBmTimerExecutionInfo.setDescription(timer.getDescription());
        try {
            timerExecutionRepository.save(hvBmTimerExecutionInfo);
        } catch (Exception ex) {
            log.error(ex.getMessage());
        }
        log.info("日志记录结束");
    }

    /**
     * 向消息队列发送消息
     *
     * @param id timerId
     */
    public void sendMqMessage(Integer id) {
        //普通发送，routingKey写死
        rabbitTemplate.convertAndSend(TimerConsts.TIMER_EXCHANGE,
                TimerConsts.TIMER_SIMPLE_TOPIC,
                id.toString());
        log.info("mq message is send,timer id is " + id);
    }

    private void sendMethodTrigger(TimerDTO timer) {
        rabbitTemplate.convertAndSend(TimerConsts.TIMER_EXCHANGE_V2, timer.getServiceName(), timer.getMethodSrc());
        log.info("methodTrigger message is send,timer method is {}", timer.getMethodSrc());
    }
}









